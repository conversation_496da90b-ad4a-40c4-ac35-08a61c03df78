# HRRS 人力资源系统

> 本项目是 HRRS 人力资源管理系统，提供员工信息、组织架构、绩效、招聘等核心人事管理功能。

该项目是一个基于 Spring Boot 的企业级 Web 应用程序。新的人事相关项目可在此基础上进行扩展。

## 目录

- [技术栈](#技术栈)
- [环境准备](#环境准备)
- [快速开始](#快速开始)
- [项目打包与部署](#项目打包与部署)
- [API接口文档](#api接口文档)
- [项目结构说明](#项目结构说明)

## 技术栈

- **核心框架**: Spring Boot 2.7.14
- **持久层框架**: MyBatis-Plus
- **数据库驱动**: SQL Server, PostgreSQL, MariaDB
- **数据库连接池**: HikariCP
- **缓存**: Redis (Lettuce)
- **消息队列**: Apache Kafka
- **API 文档**: SpringDoc (Swagger UI)
- **对象存储**: MinIO
- **构建工具**: Apache Maven
- **JDK 版本**: 17

## 环境准备

请确保您的开发环境中安装了以下软件，并配置好相应的环境变量：

- JDK >= 17
- Maven >= 3.5
- SQL Server (或其他兼容数据库)
- Redis
- Kafka (可选，如果需要使用消息队列功能)
- MinIO (可选，如果需要使用对象存储功能)
- IDE: IntelliJ IDEA (推荐)

## 快速开始

### 1. 初始化项目

1.  **修改项目标识 (如果需要)**:
    *   如果需要基于此项目创建新项目，建议全局搜索并替换 `hrrs` 为您的新项目名。
    *   将 `com.csci.hrrs` 包重构为您的项目包名。

2.  **修改开发环境配置**:
    *   打开 `src/main/resources/application-dev.properties` 文件。
    *   修改 `datasource.*` 相关配置，使其指向您的本地或开发数据库。
    *   根据需要修改 Redis, Kafka, MinIO 等中间件的连接配置。
    *   配置文件中的密码已使用 `Jasypt` 加密 (加密密钥在 `jasypt.encryptor.password`)，请按需修改。

3.  **初始化数据库**:
    *   连接到您的目标数据库。
    *   在项目根目录下找到 `db.sql` 和 `cdms物资损耗db.sql` 脚本。请根据需要执行它们来初始化数据库表结构和基础数据。

### 2. 运行项目

**通过IDE运行:**

-   找到并打开 `src/main/java/com/csci/hrrs/Application.java`。
-   右键点击并选择 `Run 'Application.main()'` 来启动项目。

**通过Maven命令运行:**

```bash
# 在项目根目录执行以下命令
mvn spring-boot:run
```

项目启动成功后，根据 `application-dev.properties` 中的配置，默认可以通过 `http://localhost:8091/hrrs-api` 访问。

## 项目打包与部署

### 打包

```bash
# 执行打包命令，生成可执行的 JAR 文件
# -DskipTests 会跳过测试阶段，加快打包速度
mvn clean package -DskipTests
```
打包成功后，会在 `target` 目录下生成 `hrrs-0.0.1-SNAPSHOT.jar` 文件。

### 部署

```bash
# 通过java命令后台运行jar包
nohup java -jar target/hrrs-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod > app.log 2>&1 &
```
> **注意**: `--spring.profiles.active=prod` 参数会加载 `application-prod.properties` 配置文件，请确保该文件存在且配置正确。

## API接口文档

项目已集成 SpringDoc。启动项目后，可通过以下地址访问 Swagger API 文档：

[http://localhost:8091/hrrs-api/swagger-ui/index.html](http://localhost:8091/hrrs-api/swagger-ui/index.html) (请根据实际 `context-path` 调整)

## 项目结构说明

```
hrrs
├── src/main/java/com/csci/hrrs
│   ├── annotation  // 自定义注解 (如权限控制)
│   ├── aspectj     // AOP 切面
│   ├── config      // 全局配置类 (如异步任务、WebSocket)
│   ├── configuration // 配置类（数据源等）
│   ├── controller  // API 控制器
│   ├── service     // 业务逻辑服务
│   ├── mapper      // MyBatis-Plus Mapper 接口
│   ├── model       // 数据库实体
│   ├── facade      // 封装复杂业务逻辑的门面层
│   ├── job         // 定时任务
│   ├── listener    // 事件监听器
│   ├── kafka       // Kafka 消费者和生产者
│   └── Application.java // Spring Boot 启动类
├── src/main/resources
│   ├── sqlmapper   // MyBatis XML 文件
│   ├── initdata    // 初始化数据
│   └── application-dev.properties // 开发环境配置
└── pom.xml         // Maven 项目配置
```

