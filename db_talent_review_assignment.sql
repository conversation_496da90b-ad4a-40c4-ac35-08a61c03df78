-- 人才盘点组织机构分配表创建脚本（SQL Server版本）
-- 创建时间：2025年

-- 创建人才盘点组织机构分配表
CREATE TABLE [talent_review_assignment] (
    [id] NVARCHAR(64) NOT NULL,
    [assignee_username] NVARCHAR(64) NOT NULL,
    [organization_code] NVARCHAR(64) NOT NULL,
    [create_user_id] NVARCHAR(64) NOT NULL,
    [creation_time] DATETIME2 NOT NULL DEFAULT GETDATE(),
    [last_update_time] DATETIME2 NOT NULL DEFAULT GETDATE(),
    [is_deleted] BIT NOT NULL DEFAULT 0,
    
    CONSTRAINT [PK_talent_review_assignment] PRIMARY KEY ([id]),
    CONSTRAINT [UK_talent_review_assignment_user_org] UNIQUE ([assignee_username], [organization_code])
);

-- 添加索引
CREATE INDEX [IX_talent_review_assignment_assignee] ON [talent_review_assignment] ([assignee_username]);
CREATE INDEX [IX_talent_review_assignment_org] ON [talent_review_assignment] ([organization_code]);
CREATE INDEX [IX_talent_review_assignment_create_user] ON [talent_review_assignment] ([create_user_id]);
CREATE INDEX [IX_talent_review_assignment_creation_time] ON [talent_review_assignment] ([creation_time]);
CREATE INDEX [IX_talent_review_assignment_deleted] ON [talent_review_assignment] ([is_deleted]);

-- 添加表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'人才盘点组织机构分配表，用于管理用户对组织机构的管理权限', 
    @level0type = N'SCHEMA', 
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'talent_review_assignment';

-- 添加字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'被分配人用户名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'assignee_username';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'组织机构编码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'organization_code';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'create_user_id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'creation_time';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'最后更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'last_update_time';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'是否删除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'talent_review_assignment', @level2type = N'COLUMN', @level2name = N'is_deleted';

-- 示例数据（可选）
-- INSERT INTO [talent_review_assignment] ([id], [assignee_username], [organization_code], [create_user_id], [creation_time], [last_update_time], [is_deleted])
-- VALUES 
--     (NEWID(), 'user1', 'ORG001', 'admin', GETDATE(), GETDATE(), 0),
--     (NEWID(), 'user1', 'ORG002', 'admin', GETDATE(), GETDATE(), 0),
--     (NEWID(), 'user2', 'ORG003', 'admin', GETDATE(), GETDATE(), 0); 