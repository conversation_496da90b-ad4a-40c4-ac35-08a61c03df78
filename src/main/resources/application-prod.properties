server.port=8091
logging.config=classpath:logback-prod.xml
openapi.request.host=https://api.csci.com.hk/hrrs-api
server.forward-headers-strategy=native
#springdoc.api-docs.path=/service/v3/api-docs
#springdoc.swagger-ui.path=/api/swagger-ui.html

#message center
datasource.hrrs.hikari.maximum-pool-size=100
datasource.hrrs.hikari.minimum-idle=5
datasource.hrrs.hikari.jdbc-url=*******************************************************;
datasource.hrrs.hikari.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.hikari.username=u_hrrs_prod
datasource.hrrs.hikari.password=ENC(yAOz3xawCtIyOqqAl4k3nmHEP6iyFSGI)
datasource.hrrs.hikari.connection-timeout=60000
datasource.hrrs.hikari.connection-test-query=select 1
#datasource.hrrs.hikari.leak-detection-threshold=10000

#???
datasource.hrrs.dw.maximum-pool-size=100
datasource.hrrs.dw.minimum-idle=5
datasource.hrrs.dw.jdbc-url=***************************************************;
datasource.hrrs.dw.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.dw.username=u_hrrs_prod
datasource.hrrs.dw.password=ENC(yAOz3xawCtIyOqqAl4k3nmHEP6iyFSGI)
datasource.hrrs.dw.connection-timeout=60000
datasource.hrrs.dw.connection-test-query=select 1
#datasource.hrrs.dw.leak-detection-threshold=10000

# bi_stg
datasource.bistg.dw.maximum-pool-size=100
datasource.bistg.dw.minimum-idle=5
datasource.bistg.dw.jdbc-url=****************************************************;
datasource.bistg.dw.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.bistg.dw.username=bistg_read_stdpos
datasource.bistg.dw.password=ENC(npi+L0N7igkY6pbkhptZ7vJMN34foeV2iVyuZul47rE=)
datasource.bistg.dw.connection-timeout=60000
datasource.bistg.dw.connection-test-query=select 1

# 37
datasource.hrrs.hr37.maximum-pool-size=100
datasource.hrrs.hr37.minimum-idle=5
datasource.hrrs.hr37.jdbc-url=**************************************************;
datasource.hrrs.hr37.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.hr37.username=bi
datasource.hrrs.hr37.password=ENC(TpG0sc7DXFJnRWxsj3+/8nXTvlpb//3A)
datasource.hrrs.hr37.connection-timeout=60000
datasource.hrrs.hr37.connection-test-query=select 1

# 37:fangwu_cockpit
datasource.hr37.fangwu.maximum-pool-size=50
datasource.hr37.fangwu.minimum-idle=5
datasource.hr37.fangwu.jdbc-url=***********************************************************;
datasource.hr37.fangwu.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hr37.fangwu.username=bi
datasource.hr37.fangwu.password=ENC(TpG0sc7DXFJnRWxsj3+/8nXTvlpb//3A)
datasource.hr37.fangwu.connection-timeout=60000
datasource.hr37.fangwu.connection-test-query=select 1

# FIS
datasource.fis.hikari.maximum-pool-size=20
datasource.fis.hikari.minimum-idle=5
datasource.fis.hikari.jdbc-url=*****************************************************************;
datasource.fis.hikari.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.fis.hikari.username=bifi
datasource.fis.hikari.password=ENC(Ik+jBR1QdTrXmoTQKPU9a2jkm5rcXmRG)
datasource.fis.hikari.connection-timeout=60000
datasource.fis.hikari.connection-test-query=select 1
datasource.fis.hikari.leak-detection-threshold=10000

datasource.gcdsj.fin.maximum-pool-size=20
datasource.gcdsj.fin.minimum-idle=5
datasource.gcdsj.fin.jdbc-url=*********************************************************************
datasource.gcdsj.fin.driver-class-name=org.postgresql.Driver
datasource.gcdsj.fin.username=operating_data
datasource.gcdsj.fin.password=ENC(7As304aB7eygpXpbsNJ/dM5RRZhlYR/sJGIIZvvx+ms=)
datasource.gcdsj.fin.connection-timeout=60000
datasource.gcdsj.fin.connection-test-query=select 1
datasource.gcdsj.fin.leak-detection-threshold=10000

# Oauth2 config
oauth2.url=https://auth.csci.com.hk/api/token?
oauth2.url.accessToken=grant_type=password&appid={0}&secret={1}&username={2}&password={3}
oauth2.url.validateToken=grant_type=validate&appid={0}&secret={1}&access_token={2}
oauth2.url.refreshToken=grant_type=refresh_token&appid={0}&secret={1}&refresh_token={2}
oauth2.appid=111
oauth2.appSecret=cptbtptpbcptdtptp

home.page=https://api.csci.com.hk/hrrs

# feishu sso config
feishu.sso.app.url=https://api.biz.3311csci.com/zhtappsso/api/Token
feishu.sso.app.validate=https://api.biz.3311csci.com/zhtappsso/api/ValidateToken
feishu.sso.app.code=hr-analysis
feishu.sso.app.secret=zjS2GplH4yWjpSZYYTmGuel02VD1uWGC

# baidu ocr config
baidu.ocr.url=https://aip.baidubce.com/oauth/2.0/token
baidu.ocr.clientId=Gf9lOhMBGRGZ7Pk8BafM3XiB
baidu.ocr.clientSecret=fYGOElGvsWmMAdPrR72dHC91T8LA2v11
baidu.ocr.grantType=client_credentials

app.redisKey=hrrs

# REDIS (RedisProperties)
spring.redis.database=0
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=Csci@redis#2023
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active= 100
spring.redis.lettuce.pool.max-wait= -1
spring.redis.lettuce.pool.min-idle= 10
spring.redis.lettuce.pool.max-idle= 50

feishu.message.url=https://msg.csci.com.hk
feishu.message.token=088B1A70B44641BC881597736B6BE43B

# rpa robot
rpa.robot.active=true

# user session timeout in minutes; 7200=7 days
user.session.timeout=10080

# max file size limit
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# user session manager
user.session.active.count=5

server.servlet.context-path=/hrrs-api

# ????????????
openapi.key=484D41B7747F4CA3B92C23839279C278

# general api call
general-api.url=https://api.csci.com.hk
general-api.secret=58BC0C339216484C9DBAAD7569E1D6E5
# ?????
general-api.auth.code.hrds=40FD9039875741E0A2A712DB04A6FB7C

# ??????????????
dash.dispay.wailao=false

# OA relative
oa.url=https://sys.biz.3311csci.com/a9gateway
oa.api.appKey=373ae7f8526d449ebdb371c668cb0d6d
oa.api.appSecret=443bbec1276c49738b37e0e54c8b12d9
oa.api.tenantId=6655778040853515916
oa.api.path.accessToken=/main/token/accessToken
oa.api.path.start=/main/case/start
oa.api.path.userInfo=/main/user/queryUserByLoginName
oa.wedata.url=https://wedata.3311csci.com/open/dw/open
oa.wedata.api.appId=69e3ff74a1dc4b3d8d6f53010bd1552a
oa.wedata.api.appSecret=78f5fa0fdaaa4740818f16ce0d836168
oa.wedata.api.issuedAt=2025-11-12 12:11:11
oa.wedata.api.path.accessToken=/business/data_access_app/getToken
oa.wedata.api.path.getOrgGuid=/api/maindata/main_midorgs_api

# kafka ??
spring.kafka.bootstrap-servers=10.148.2.92:9092
spring.kafka.consumer.group-id=hrp-hr-request-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

# hiAgent
hiagent.url=https://hiagent.3311csci.com
hiagent.get-app-config=/api/proxy/api/v1/get_app_config_preview
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get-suggested-questions=/api/proxy/api/v1/get_suggested_questions
hiagent.roster.apikey=cvhtmbjbg4roomp6p460
hiagent.staff-plan.apikey=cvihgsrbg4roomp75bf0
hiagent.roster.luo.apikey=cv7pk9hhst5dm9brr650
hiagent.visa.apikey=d1egbh16lovqj1705ms0
hiagent.get_message_info=/api/proxy/api/v1/get_message_info

#minio
minio.server=http://***********
minio.port=31681
minio.accessKey=jfYfYgeDsz1V13tccfE
minio.secretKey=iZePMF1ss1U4risse
minio.bucket=hr-rpt-srv-prod
minio.urlPrefix=/minio/



#fis
fis.auth.key=1B88A3E3932E4E8E884D7C9AC5E71F9E
fis.output.value.url=https://api.csci.com.hk/fis-api/cost-online/getIncomeData

# ehr
ehr.api.host=https://hcm.cohl.com
ehr.api.secret=Xc8rWq01RcPdLpTY

# EHRç³»ç»å¯é¥
ehr.secret=047F9ECA171B47FFB5FCCD77A6D8677A

#EHR sso redirect path template
ehr.api.redirect.path.template=/pm/changePosition/03311/changePositionModify?guid={guid}&act=query