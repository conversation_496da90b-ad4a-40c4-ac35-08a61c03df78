server.port=8091
logging.config=classpath:logback-dev.xml
#openapi.request.host=http://127.0.0.1:${server.port}
openapi.request.host=
server.servlet.context-path=/hrrs-api
server.forward-headers-strategy=native

#message center
datasource.hrrs.hikari.maximum-pool-size=50
datasource.hrrs.hikari.minimum-idle=5
datasource.hrrs.hikari.jdbc-url=********************************************************;
datasource.hrrs.hikari.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.hikari.username=devroster
datasource.hrrs.hikari.password=ENC(HHQRnSkJTYQ5tFaK0RzyY1owlcV0XjQh)
datasource.hrrs.hikari.connection-timeout=60000
datasource.hrrs.hikari.connection-test-query=select 1
datasource.hrrs.hikari.leak-detection-threshold=30000

#ï¿½Ð¼ï¿½ï¿½
datasource.hrrs.dw.maximum-pool-size=50
datasource.hrrs.dw.minimum-idle=5
datasource.hrrs.dw.jdbc-url=******************************************************;
datasource.hrrs.dw.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.dw.username=devroster
datasource.hrrs.dw.password=ENC(HHQRnSkJTYQ5tFaK0RzyY1owlcV0XjQh)
datasource.hrrs.dw.connection-timeout=60000
datasource.hrrs.dw.connection-test-query=select 1
datasource.hrrs.dw.leak-detection-threshold=30000

# bi_stg
datasource.bistg.dw.maximum-pool-size=100
datasource.bistg.dw.minimum-idle=5
datasource.bistg.dw.jdbc-url=****************************************************;
datasource.bistg.dw.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.bistg.dw.username=u_bistg_prod
datasource.bistg.dw.password=ENC(CatFAQcSSCy5pFVB28trEXSvVQJncG+IEgW08Ug11gU=)
datasource.bistg.dw.connection-timeout=60000
datasource.bistg.dw.connection-test-query=select 1

# 37
datasource.hrrs.hr37.maximum-pool-size=50
datasource.hrrs.hr37.minimum-idle=5
datasource.hrrs.hr37.jdbc-url=**************************************************;
datasource.hrrs.hr37.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hrrs.hr37.username=bi
datasource.hrrs.hr37.password=ENC(/t9C9CJB0cIWG3XE58xfmH6wHGmLe6l3)
datasource.hrrs.hr37.connection-timeout=60000
datasource.hrrs.hr37.connection-test-query=select 1

# 37:fangwu_cockpit
datasource.hr37.fangwu.maximum-pool-size=50
datasource.hr37.fangwu.minimum-idle=5
datasource.hr37.fangwu.jdbc-url=***********************************************************;
datasource.hr37.fangwu.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.hr37.fangwu.username=bi
datasource.hr37.fangwu.password=ENC(/t9C9CJB0cIWG3XE58xfmH6wHGmLe6l3)
datasource.hr37.fangwu.connection-timeout=60000
datasource.hr37.fangwu.connection-test-query=select 1

# FIS
datasource.fis.hikari.maximum-pool-size=20
datasource.fis.hikari.minimum-idle=5
datasource.fis.hikari.jdbc-url=**********************************************************************;
datasource.fis.hikari.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.fis.hikari.username=bifi
datasource.fis.hikari.password=ENC(Dckb04L8gv1WickmPrhPRivJF2cvP6sh)
datasource.fis.hikari.connection-timeout=60000
datasource.fis.hikari.connection-test-query=select 1
datasource.fis.hikari.leak-detection-threshold=10000

## engineering big data
datasource.gcdsj.fin.maximum-pool-size=20
datasource.gcdsj.fin.minimum-idle=5
datasource.gcdsj.fin.jdbc-url=*********************************************************************
datasource.gcdsj.fin.driver-class-name=org.postgresql.Driver
datasource.gcdsj.fin.username=operating_data
datasource.gcdsj.fin.password=h37CO68u5t1a5DQ5
datasource.gcdsj.fin.connection-timeout=60000
datasource.gcdsj.fin.connection-test-query=select 1
datasource.gcdsj.fin.leak-detection-threshold=10000

# Oauth2 config
oauth2.url=https://auth.csci.com.hk/api/token?
oauth2.url.accessToken=grant_type=password&appid={0}&secret={1}&username={2}&password={3}
oauth2.url.validateToken=grant_type=validate&appid={0}&secret={1}&access_token={2}
oauth2.url.refreshToken=grant_type=refresh_token&appid={0}&secret={1}&refresh_token={2}
oauth2.appid=111
oauth2.appSecret=cptbtptpbcptdtptp
jasypt.encryptor.password=Csci##3311!!

home.page=http://localhost:8080

# feishu sso config
feishu.sso.app.url=https://api.csci.com.hk/zhtappsso-test/api/Token
feishu.sso.app.validate=https://api.csci.com.hk/zhtappsso-test/api/ValidateToken
feishu.sso.app.code=fin-stat
feishu.sso.app.secret=FSal5YzYz0ZjSZXtYnrWZejs5SodHSZ8

# baidu ocr config
baidu.ocr.url=https://aip.baidubce.com/oauth/2.0/token
baidu.ocr.clientId=Gf9lOhMBGRGZ7Pk8BafM3XiB
baidu.ocr.clientSecret=fYGOElGvsWmMAdPrR72dHC91T8LA2v11
baidu.ocr.grantType=client_credentials

app.redisKey=hrrs

# REDIS (RedisProperties)
spring.redis.database=0
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=Passw0rd
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active= 100
spring.redis.lettuce.pool.max-wait= -1
spring.redis.lettuce.pool.min-idle= 10
spring.redis.lettuce.pool.max-idle= 50

feishu.message.url=https://msg.csci.com.hk
feishu.message.token=088B1A70B44641BC881597736B6BE43B

# rpa robot
rpa.robot.active=true

# user session timeout in minutes
user.session.timeout=10080

# max file size limit
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# user session manager
user.session.active.count=5

# ï¿½ï¿½ï¿½ï¿½Ó¦ï¿½Ãµï¿½ï¿½Ã´Ë·ï¿½ï¿½ï¿½Ê±Ê¹ï¿½ï¿½
openapi.key=484D41B7747F4CA3B92C23839279C278

# general api call
general-api.url=https://goon.csci.com.hk
general-api.secret=0179BF6CBF6F4066ABF26CBD56AD356D
# ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ê»ï¿½ï¿½
general-api.auth.code.hrds=1040F767E75C488C86D07E125B8A8C33

# ï¿½ï¿½Û±ï¿½ï¿½Æ´ï¿½ï¿½ï¿½ï¿½Ç·ï¿½ï¿½ï¿½Ê¾ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
dash.dispay.wailao=false

# OA relative
oa.url=https://entryzjxgdev7-vip.dev.3311csci.com/a9gateway
oa.api.appKey=f338565bb3bc49c9bf221a68ebcdc07a
oa.api.appSecret=f93347f91f2946e192f790eab48dea3b
oa.api.tenantId=6655778040853515916
oa.api.path.accessToken=/main/token/accessToken
oa.api.path.start=/main/case/start
oa.api.path.userInfo=/main/user/queryUserByLoginName
oa.wedata.url=https://wedata.3311csci.com/open/dw/open
oa.wedata.api.appId=69e3ff74a1dc4b3d8d6f53010bd1552a
oa.wedata.api.appSecret=78f5fa0fdaaa4740818f16ce0d836168
oa.wedata.api.issuedAt=2025-11-12 12:11:11
oa.wedata.api.path.accessToken=/business/data_access_app/getToken
oa.wedata.api.path.getOrgGuid=/api/maindata/main_midorgs_api

# kafka ï¿½ï¿½ï¿½ï¿½
spring.kafka.bootstrap-servers=**********:9092
spring.kafka.consumer.group-id=hrp-hr-request-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

# hiAgent
hiagent.url=https://hiagent.3311csci.com
hiagent.get-app-config=/api/proxy/api/v1/get_app_config_preview
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get-suggested-questions=/api/proxy/api/v1/get_suggested_questions
hiagent.roster.apikey=cvhtmbjbg4roomp6p460
hiagent.staff-plan.apikey=cvihgsrbg4roomp75bf0
hiagent.roster.luo.apikey=cv7pk9hhst5dm9brr650
hiagent.visa.apikey=d1egbh16lovqj1705ms0
hiagent.get_message_info=/api/proxy/api/v1/get_message_info

#minio
minio.server=http://10.148.2.15
minio.port=31681
minio.accessKey=jfYfYgeDsz1V13tccfE
minio.secretKey=iZePMF1ss1U4risse
minio.bucket=hr-rpt-srv-dev
minio.urlPrefix=/minio/

# hrrs-api
fis.auth.key=4AC0E601BEF640B09F97475623FE31F4
fis.output.value.url=https://goon.csci.com.hk/fis-api/cost-online/getIncomeData

# EHRç³»ç»å¯é¥
ehr.secret=1C59B352928E4C4BAC0FE0DF70C1961E

#EHR sso redirect path template
ehr.api.redirect.path.template=/pm/changePosition/03311/changePositionModify?guid={guid}&act=query