<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.hrrs.apply.mapper.ApplyEmpInfoMapper">

    <resultMap id="BaseResultMap" type="com.csci.hrrs.apply.model.ApplyEmpInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="staff_no" jdbcType="VARCHAR" property="staffNo"/>
        <result column="traditional_name" jdbcType="VARCHAR" property="traditionalName"/>
        <result column="hometown" jdbcType="VARCHAR" property="hometown"/>
        <result column="birth_place" jdbcType="VARCHAR" property="birthPlace"/>
        <result column="location_of_archive" jdbcType="VARCHAR" property="locationOfArchive"/>
        <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime"/>
        <result column="main_info_submit_count" jdbcType="INTEGER" property="mainInfoSubmitCount"/>
        <result column="other_info_submit_count" jdbcType="INTEGER" property="otherInfoSubmitCount"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="mainland_id_card" jdbcType="VARCHAR" property="mainlandIdCard"/>
        <result column="cert_expiration_date" jdbcType="TIMESTAMP" property="certExpirationDate"/>
        <result column="endor_expiration_date" jdbcType="TIMESTAMP" property="endorExpirationDate"/>
    </resultMap>

    <select id="selectForExport" resultMap="BaseResultMap">
        select t.staff_no,
               t.traditional_name,
               t.hometown,
               t.birth_place,
               t.location_of_archive
        from t_apply_emp_info t
                 join v_send_apply_emp_roster v
                      on t.staff_no = v.pernr
        where t.is_deleted = 0
          <if test="platform != null and platform != ''">and t.platform = #{platform}</if>
          <if test="staffClass3311 != null and staffClass3311 != ''">and v.staff_class3311 = #{staffClass3311}</if>
          and t.creation_time &gt; #{startDate}
          and t.creation_time &lt; #{endDate}
          and t.main_info_submit_count > 0
          and t.other_info_submit_count > 0
        order by t.staff_no
    </select>

    <select id="selectAddressForExport" resultMap="BaseResultMap">
        select t.staff_no,
               t.traditional_name,
               t.mainland_address,
               t.hk_address,
               t.macau_address
        from t_apply_emp_info t
                 join v_send_apply_emp_roster v
                      on t.staff_no = v.pernr
        where t.is_deleted = 0
          <if test="platform != null and platform != ''">and t.platform = #{platform}</if>
          <if test="staffClass3311 != null and staffClass3311 != ''">and v.staff_class3311 = #{staffClass3311}</if>
          and t.creation_time &gt; #{startDate}
          and t.creation_time &lt; #{endDate}
          and t.main_info_submit_count > 0
          and t.other_info_submit_count > 0
        order by t.staff_no
    </select>

    <select id="selectBankAccountForExport" resultMap="BaseResultMap">
        select t.staff_no,
               t.traditional_name,
               t.bank_account
        from t_apply_emp_info t
                 join v_send_apply_emp_roster v
                      on t.staff_no = v.pernr
        where t.is_deleted = 0
          <if test="platform != null and platform != ''">and t.platform = #{platform}</if>
          <if test="staffClass3311 != null and staffClass3311 != ''">and v.staff_class3311 = #{staffClass3311}</if>
          and t.creation_time &gt; #{startDate}
          and t.creation_time &lt; #{endDate}
          and t.main_info_submit_count > 0
          and t.other_info_submit_count > 0
        order by t.staff_no
    </select>

    <select id="selectCertificateForExport" resultMap="BaseResultMap">
        select t.staff_no,
               t.traditional_name,
               t.mainland_id_card,
               t.cert_expiration_date,
               t.endor_expiration_date
        from t_apply_emp_info t
                 join v_send_apply_emp_roster v on t.staff_no = v.pernr
        where t.is_deleted = 0
          <if test="platform != null and platform != ''">and t.platform = #{platform}</if>
          <if test="staffClass3311 != null and staffClass3311 != ''">and v.staff_class3311 = #{staffClass3311}</if>
          and t.creation_time &gt; #{startDate}
          and t.creation_time &lt; #{endDate}
          and t.main_info_submit_count > 0
          and t.other_info_submit_count > 0
        order by t.traditional_name
    </select>

</mapper> 