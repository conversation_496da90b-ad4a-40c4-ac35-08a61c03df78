<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.hrrs.talent.mapper.TalentReviewAssignmentMapper">
    
    <!-- 根据用户名查询分配的组织机构列表 -->
    <select id="selectOrganizationCodesByUsername" resultType="java.lang.String">
        SELECT organization_code
        FROM talent_review_assignment
        WHERE assignee_username = #{assigneeUsername}
          AND is_deleted = 0
        ORDER BY creation_time DESC
    </select>
    
    <!-- 根据组织机构编码查询分配的用户列表 -->
    <select id="selectUsernamesByOrganizationCode" resultType="java.lang.String">
        SELECT assignee_username
        FROM talent_review_assignment
        WHERE organization_code = #{organizationCode}
          AND is_deleted = 0
        ORDER BY creation_time DESC
    </select>
    
</mapper> 