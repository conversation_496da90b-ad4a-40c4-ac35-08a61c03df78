<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.hrrs.mapper.ParttimeJobMapper">
  <resultMap id="BaseResultMap" type="com.csci.hrrs.model.ParttimeJob">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="person_id" jdbcType="NVARCHAR" property="personId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="organization_name" jdbcType="NVARCHAR" property="organizationName" />
    <result column="parttime_position" jdbcType="NVARCHAR" property="parttimePosition" />
    <result column="job_description" jdbcType="NVARCHAR" property="jobDescription" />
    <result column="industry" jdbcType="NVARCHAR" property="industry" />
    <result column="is_paid" jdbcType="BIT" property="isPaid" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, person_id, start_date, end_date, organization_name, parttime_position, job_description, 
    industry, is_paid, is_deleted, creation_time, create_user_id, create_username, last_update_time, 
    last_update_user_id, last_update_username, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.hrrs.model.ParttimeJobExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_pi_ext_parttime_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_pi_ext_parttime_job
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_pi_ext_parttime_job
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.hrrs.model.ParttimeJobExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_pi_ext_parttime_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.hrrs.model.ParttimeJob">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_pi_ext_parttime_job (id, person_id, start_date, 
      end_date, organization_name, parttime_position, 
      job_description, industry, is_paid, 
      is_deleted, creation_time, create_user_id, 
      create_username, last_update_time, last_update_user_id, 
      last_update_username, last_update_version)
    values (#{id,jdbcType=NVARCHAR}, #{personId,jdbcType=NVARCHAR}, #{startDate,jdbcType=DATE}, 
      #{endDate,jdbcType=DATE}, #{organizationName,jdbcType=NVARCHAR}, #{parttimePosition,jdbcType=NVARCHAR}, 
      #{jobDescription,jdbcType=NVARCHAR}, #{industry,jdbcType=NVARCHAR}, #{isPaid,jdbcType=BIT}, 
      #{isDeleted,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=NVARCHAR}, 
      #{createUsername,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.hrrs.model.ParttimeJob">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_pi_ext_parttime_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="personId != null">
        person_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="organizationName != null">
        organization_name,
      </if>
      <if test="parttimePosition != null">
        parttime_position,
      </if>
      <if test="jobDescription != null">
        job_description,
      </if>
      <if test="industry != null">
        industry,
      </if>
      <if test="isPaid != null">
        is_paid,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=NVARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="organizationName != null">
        #{organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="parttimePosition != null">
        #{parttimePosition,jdbcType=NVARCHAR},
      </if>
      <if test="jobDescription != null">
        #{jobDescription,jdbcType=NVARCHAR},
      </if>
      <if test="industry != null">
        #{industry,jdbcType=NVARCHAR},
      </if>
      <if test="isPaid != null">
        #{isPaid,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.hrrs.model.ParttimeJobExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from t_pi_ext_parttime_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_pi_ext_parttime_job
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=NVARCHAR},
      </if>
      <if test="row.personId != null">
        person_id = #{row.personId,jdbcType=NVARCHAR},
      </if>
      <if test="row.startDate != null">
        start_date = #{row.startDate,jdbcType=DATE},
      </if>
      <if test="row.endDate != null">
        end_date = #{row.endDate,jdbcType=DATE},
      </if>
      <if test="row.organizationName != null">
        organization_name = #{row.organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="row.parttimePosition != null">
        parttime_position = #{row.parttimePosition,jdbcType=NVARCHAR},
      </if>
      <if test="row.jobDescription != null">
        job_description = #{row.jobDescription,jdbcType=NVARCHAR},
      </if>
      <if test="row.industry != null">
        industry = #{row.industry,jdbcType=NVARCHAR},
      </if>
      <if test="row.isPaid != null">
        is_paid = #{row.isPaid,jdbcType=BIT},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=BIT},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_pi_ext_parttime_job
    set id = #{row.id,jdbcType=NVARCHAR},
      person_id = #{row.personId,jdbcType=NVARCHAR},
      start_date = #{row.startDate,jdbcType=DATE},
      end_date = #{row.endDate,jdbcType=DATE},
      organization_name = #{row.organizationName,jdbcType=NVARCHAR},
      parttime_position = #{row.parttimePosition,jdbcType=NVARCHAR},
      job_description = #{row.jobDescription,jdbcType=NVARCHAR},
      industry = #{row.industry,jdbcType=NVARCHAR},
      is_paid = #{row.isPaid,jdbcType=BIT},
      is_deleted = #{row.isDeleted,jdbcType=BIT},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_user_id = #{row.createUserId,jdbcType=NVARCHAR},
      create_username = #{row.createUsername,jdbcType=NVARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_username = #{row.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.hrrs.model.ParttimeJob">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_pi_ext_parttime_job
    <set>
      <if test="personId != null">
        person_id = #{personId,jdbcType=NVARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="organizationName != null">
        organization_name = #{organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="parttimePosition != null">
        parttime_position = #{parttimePosition,jdbcType=NVARCHAR},
      </if>
      <if test="jobDescription != null">
        job_description = #{jobDescription,jdbcType=NVARCHAR},
      </if>
      <if test="industry != null">
        industry = #{industry,jdbcType=NVARCHAR},
      </if>
      <if test="isPaid != null">
        is_paid = #{isPaid,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.hrrs.model.ParttimeJob">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_pi_ext_parttime_job
    set person_id = #{personId,jdbcType=NVARCHAR},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      organization_name = #{organizationName,jdbcType=NVARCHAR},
      parttime_position = #{parttimePosition,jdbcType=NVARCHAR},
      job_description = #{jobDescription,jdbcType=NVARCHAR},
      industry = #{industry,jdbcType=NVARCHAR},
      is_paid = #{isPaid,jdbcType=BIT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>