<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.hrrs.mapper.RosterDetailMapper">
    <resultMap id="BaseResultMap" type="com.csci.hrrs.model.RosterDetail">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="NVARCHAR" property="id" />
        <result column="head_id" jdbcType="NVARCHAR" property="headId" />
        <result column="pernr" jdbcType="NVARCHAR" property="pernr" />
        <result column="name" jdbcType="NVARCHAR" property="name" />
        <result column="pinyin_name" jdbcType="NVARCHAR" property="pinyinName" />
        <result column="eng_name" jdbcType="NVARCHAR" property="engName" />
        <result column="eng_name_display" jdbcType="NVARCHAR" property="engNameDisplay" />
        <result column="platform" jdbcType="NVARCHAR" property="platform" />
        <result column="platform_trad" jdbcType="NVARCHAR" property="platformTrad" />
        <result column="platform_eng" jdbcType="NVARCHAR" property="platformEng" />
        <result column="subsidiary" jdbcType="NVARCHAR" property="subsidiary" />
        <result column="subsidiary_trad" jdbcType="NVARCHAR" property="subsidiaryTrad" />
        <result column="subsidiary_eng" jdbcType="NVARCHAR" property="subsidiaryEng" />
        <result column="sub_project" jdbcType="NVARCHAR" property="subProject" />
        <result column="sub_project_trad" jdbcType="NVARCHAR" property="subProjectTrad" />
        <result column="sub_project_eng" jdbcType="NVARCHAR" property="subProjectEng" />
        <result column="primary_position" jdbcType="NVARCHAR" property="primaryPosition" />
        <result column="conc_position" jdbcType="NVARCHAR" property="concPosition" />
        <result column="person_in_charge" jdbcType="NVARCHAR" property="personInCharge" />
        <result column="person_in_charge_trad" jdbcType="NVARCHAR" property="personInChargeTrad" />
        <result column="person_in_charge_eng" jdbcType="NVARCHAR" property="personInChargeEng" />
        <result column="employee_category" jdbcType="NVARCHAR" property="employeeCategory" />
        <result column="employee_category_name" jdbcType="NVARCHAR" property="employeeCategoryName" />
        <result column="work_place" jdbcType="NVARCHAR" property="workPlace" />
        <result column="position_type" jdbcType="NVARCHAR" property="positionType" />
        <result column="position_type_trad" jdbcType="NVARCHAR" property="positionTypeTrad" />
        <result column="position_type_eng" jdbcType="NVARCHAR" property="positionTypeEng" />
        <result column="expertise" jdbcType="NVARCHAR" property="expertise" />
        <result column="position_level" jdbcType="NVARCHAR" property="positionLevel" />
        <result column="job_grade" jdbcType="NVARCHAR" property="jobGrade" />
        <result column="hk_job_grade" jdbcType="NVARCHAR" property="hkJobGrade" />
        <result column="gender" jdbcType="INTEGER" property="gender" />
        <result column="start_work_time" jdbcType="DATE" property="startWorkTime" />
        <result column="join_cohl_time" jdbcType="DATE" property="joinCohlTime" />
        <result column="join_3311_time" jdbcType="DATE" property="join3311Time" />
        <result column="start_overseas_time" jdbcType="DATE" property="startOverseasTime" />
        <result column="duration_current_pos" jdbcType="DATE" property="durationCurrentPos" />
        <result column="duration_current_level" jdbcType="DATE" property="durationCurrentLevel" />
        <result column="duration_cur_job_grade" jdbcType="DATE" property="durationCurJobGrade" />
        <result column="birthdate" jdbcType="DATE" property="birthdate" />
        <result column="age" jdbcType="INTEGER" property="age" />
        <result column="ethnicity" jdbcType="NVARCHAR" property="ethnicity" />
        <result column="ethnicity_trad" jdbcType="NVARCHAR" property="ethnicityTrad" />
        <result column="ethnicity_eng" jdbcType="NVARCHAR" property="ethnicityEng" />
        <result column="hometown" jdbcType="NVARCHAR" property="hometown" />
        <result column="birth_place" jdbcType="NVARCHAR" property="birthPlace" />
        <result column="residence" jdbcType="NVARCHAR" property="residence" />
        <result column="marital_status" jdbcType="NVARCHAR" property="maritalStatus" />
        <result column="child_status" jdbcType="NVARCHAR" property="childStatus" />
        <result column="education" jdbcType="NVARCHAR" property="education" />
        <result column="major" jdbcType="NVARCHAR" property="major" />
        <result column="job_title" jdbcType="NVARCHAR" property="jobTitle" />
        <result column="credentials" jdbcType="NVARCHAR" property="credentials" />
        <result column="public_office" jdbcType="NVARCHAR" property="publicOffice" />
        <result column="source" jdbcType="NVARCHAR" property="source" />
        <result column="mobile" jdbcType="NVARCHAR" property="mobile" />
        <result column="email" jdbcType="NVARCHAR" property="email" />
        <result column="remark" jdbcType="NVARCHAR" property="remark" />
        <result column="remark_trad" jdbcType="NVARCHAR" property="remarkTrad" />
        <result column="remark_eng" jdbcType="NVARCHAR" property="remarkEng" />
        <result column="organization_id" jdbcType="NVARCHAR" property="organizationId" />
        <result column="company_code" jdbcType="NVARCHAR" property="companyCode" />
        <result column="salary_code" jdbcType="NVARCHAR" property="salaryCode" />
        <result column="personnel_scope" jdbcType="NVARCHAR" property="personnelScope" />
        <result column="personnel_subscope" jdbcType="NVARCHAR" property="personnelSubscope" />
        <result column="employee_group" jdbcType="NVARCHAR" property="employeeGroup" />
        <result column="employee_group_text" jdbcType="NVARCHAR" property="employeeGroupText" />
        <result column="employee_group_text_trad" jdbcType="NVARCHAR" property="employeeGroupTextTrad" />
        <result column="employee_group_text_eng" jdbcType="NVARCHAR" property="employeeGroupTextEng" />
        <result column="employee_subgroup" jdbcType="NVARCHAR" property="employeeSubgroup" />
        <result column="employee_subgroup_text" jdbcType="NVARCHAR" property="employeeSubgroupText" />
        <result column="employee_subgroup_text_trad" jdbcType="NVARCHAR" property="employeeSubgroupTextTrad" />
        <result column="employee_subgroup_text_eng" jdbcType="NVARCHAR" property="employeeSubgroupTextEng" />
        <result column="mainland_id_card" jdbcType="NVARCHAR" property="mainlandIdCard" />
        <result column="foreign_id_card" jdbcType="NVARCHAR" property="foreignIdCard" />
        <result column="hk_mo_passport" jdbcType="NVARCHAR" property="hkMoPassport" />
        <result column="hk_mo_end_expiry" jdbcType="DATE" property="hkMoEndExpiry" />
        <result column="employment_mode" jdbcType="NVARCHAR" property="employmentMode" />
        <result column="employment_mode_trad" jdbcType="NVARCHAR" property="employmentModeTrad" />
        <result column="employment_mode_eng" jdbcType="NVARCHAR" property="employmentModeEng" />
        <result column="hzz_generation" jdbcType="NVARCHAR" property="hzzGeneration" />
        <result column="hzz_sequence" jdbcType="NVARCHAR" property="hzzSequence" />
        <result column="political_status" jdbcType="NVARCHAR" property="politicalStatus" />
        <result column="political_status_trad" jdbcType="NVARCHAR" property="politicalStatusTrad" />
        <result column="political_status_eng" jdbcType="NVARCHAR" property="politicalStatusEng" />
        <result column="party_joining_date" jdbcType="DATE" property="partyJoiningDate" />
        <result column="specialty" jdbcType="NVARCHAR" property="specialty" />
        <result column="spouse_location" jdbcType="NVARCHAR" property="spouseLocation" />
        <result column="parents_location" jdbcType="NVARCHAR" property="parentsLocation" />
        <result column="blood_type" jdbcType="NVARCHAR" property="bloodType" />
        <result column="blood_type_trad" jdbcType="NVARCHAR" property="bloodTypeTrad" />
        <result column="blood_type_eng" jdbcType="NVARCHAR" property="bloodTypeEng" />
        <result column="health_condition" jdbcType="NVARCHAR" property="healthCondition" />
        <result column="health_condition_trad" jdbcType="NVARCHAR" property="healthConditionTrad" />
        <result column="health_condition_eng" jdbcType="NVARCHAR" property="healthConditionEng" />
        <result column="interests_hobbies" jdbcType="NVARCHAR" property="interestsHobbies" />
        <result column="archive_company" jdbcType="NVARCHAR" property="archiveCompany" />
        <result column="res_loc" jdbcType="NVARCHAR" property="resLoc" />
        <result column="res_type" jdbcType="NVARCHAR" property="resType" />
        <result column="res_type_trad" jdbcType="NVARCHAR" property="resTypeTrad" />
        <result column="res_type_eng" jdbcType="NVARCHAR" property="resTypeEng" />
        <result column="seq" jdbcType="INTEGER" property="seq" />
        <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
        <result column="is_sync" jdbcType="BIT" property="isSync" />
        <result column="years_in_current_position" jdbcType="DECIMAL" property="yearsInCurrentPosition" />
        <result column="years_in_current_job_level" jdbcType="DECIMAL" property="yearsInCurrentJobLevel" />
        <result column="years_in_current_rank" jdbcType="DECIMAL" property="yearsInCurrentRank" />
        <result column="talent_inventory_placement" jdbcType="NVARCHAR" property="talentInventoryPlacement" />
        <result column="appraisal_result_year1" jdbcType="NVARCHAR" property="appraisalResultYear1" />
        <result column="appraisal_result_year2" jdbcType="NVARCHAR" property="appraisalResultYear2" />
        <result column="appraisal_result_year3" jdbcType="NVARCHAR" property="appraisalResultYear3" />
        <result column="photo" jdbcType="NVARCHAR" property="photo" />
        <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
        <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
        <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
        <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
        <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
        <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
        <result column="has_overseas_work_exp" jdbcType="BIT" property="hasOverseasWorkExp" />
        <result column="position_level_code" jdbcType="NVARCHAR" property="positionLevelCode" />
        <result column="social_service" jdbcType="NVARCHAR" property="socialService" />
        <result column="province" jdbcType="NVARCHAR" property="province" />
        <result column="is_on_job" jdbcType="TINYINT" property="isOnJob" />
        <result column="work_seniority" jdbcType="DECIMAL" property="workSeniority" />
        <result column="cohl_seniority" jdbcType="DECIMAL" property="cohlSeniority" />
        <result column="organization_code" jdbcType="NVARCHAR" property="organizationCode" />
        <result column="platform_company_code" jdbcType="VARCHAR" property="platformCompanyCode" />
        <result column="hire_type" jdbcType="NVARCHAR" property="hireType" />
        <result column="manage_type" jdbcType="NVARCHAR" property="manageType" />
        <result column="is_cadre" jdbcType="BIT" property="isCadre" />
        <result column="is_hzz" jdbcType="BIT" property="isHzz" />
        <result column="graduation_date" jdbcType="NVARCHAR" property="graduationDate" />
        <result column="special_line" jdbcType="NVARCHAR" property="specialLine" />
        <result column="position_level_code_hk" jdbcType="NVARCHAR" property="positionLevelCodeHk" />
        <result column="birthdate_month" jdbcType="INTEGER" property="birthdateMonth" />
        <result column="birthdate_day" jdbcType="INTEGER" property="birthdateDay" />
        <result column="position_level_name" jdbcType="NVARCHAR" property="positionLevelName" />
        <result column="phone_overseas" jdbcType="NVARCHAR" property="phoneOverseas" />
        <result column="edu_all" jdbcType="NVARCHAR" property="eduAll" />
        <result column="school_all" jdbcType="NVARCHAR" property="schoolAll" />
        <result column="major_all" jdbcType="NVARCHAR" property="majorAll" />
        <result column="sex" jdbcType="NVARCHAR" property="sex" />
        <result column="staff_class3311" jdbcType="NVARCHAR" property="staffClass3311" />
        <result column="staff_class" jdbcType="NVARCHAR" property="staffClass" />
        <result column="dimission_date" jdbcType="DATE" property="dimissionDate" />
        <result column="primary_position_pinyin" jdbcType="NVARCHAR" property="primaryPositionPinyin" />
        <result column="hire_type_query" jdbcType="NVARCHAR" property="hireTypeQuery" />
        <result column="adname" jdbcType="NVARCHAR" property="adname" />
        <result column="domain" jdbcType="NVARCHAR" property="domain" />
        <result column="username" jdbcType="NVARCHAR" property="username" />
        <result column="staff_class_macau" jdbcType="NVARCHAR" property="staffClassMacau" />
        <result column="nationality" jdbcType="NVARCHAR" property="nationality" />
        <result column="job_level_name" jdbcType="NVARCHAR" property="jobLevelName" />
        <result column="job_level_pilot" jdbcType="NVARCHAR" property="jobLevelPilot" />
        <result column="name_simplified" jdbcType="NVARCHAR" property="nameSimplified" />
        <result column="school_type" jdbcType="NVARCHAR" property="schoolType" />
        <result column="personal_email" jdbcType="NVARCHAR" property="personalEmail" />
        <result column="hk_mo_cert_expiry_date" jdbcType="DATE" property="hkMoCertExpiryDate" />
        <result column="height" jdbcType="DECIMAL" property="height" />
        <result column="job_type_hk" jdbcType="NVARCHAR" property="jobTypeHk" />
        <result column="bank_account" jdbcType="NVARCHAR" property="bankAccount" />
        <result column="party_regular_date" jdbcType="DATE" property="partyRegularDate" />
        <result column="eng_name_origin" jdbcType="NVARCHAR" property="engNameOrigin" />
    </resultMap>

    <sql id="addAuthCondition">
        join (select distinct user_id, organization_code
               from t_user_organization
               where is_deleted = 0
                 and user_id = #{userId}
        ) u
        on u.organization_code = r.organization_code join
        (select distinct t2.code from t_user_sub_group t1 join t_employee_sub_group t2 on t1.sub_group_id
            = t2.id where t2.is_deleted = 0 and t1.user_id = #{userId}) usg on usg.code = r.employee_subgroup
    </sql>

    <!--增加一个sql片段，用来放置一些花名册表的查询条件-->
    <sql id="dashboardRosterDetailCondition">
        and r.is_deleted = 0
        <!--组织机构权限-->
        <!--and exists(select 1 from t_user_organization where user_id = #{userId} and is_deleted = 0 and
        organization_code = r.organization_code)-->
        <!--員工子组权限-->
        <!--and exists(select 1 from t_user_sub_group usg
        left join t_employee_sub_group g on usg.sub_group_id = g.id
        where g.is_deleted = 0
        and user_id = #{userId}
        and is_deleted = 0
        and g.code = r.employee_subgroup)-->
        <if test="rosterHeadId != null and rosterHeadId != ''">
            and r.head_id = #{rosterHeadId}
        </if>
        <if test="keyword != null and keyword != ''">
            and (r.name like #{keyword} or r.pernr like #{keyword})
        </if>
        <if test="provinceList != null and provinceList.size() > 0">
            and r.province in
            <foreach collection="provinceList" item="province" open="(" close=")" separator=",">
                #{province}
            </foreach>
        </if>
        <if test="province != null and province != ''">
            and r.province = #{province}
        </if>
        <!--(List<String> platformOrgCodeList不为null且platformOrgCodeList的size > 0) 并且(deptCodeList为null或者size=0)-->
        <if test="platformOrgCodeList != null and platformOrgCodeList.size() > 0 and (deptCodeList == null or deptCodeList.size() == 0)">
            and
            <foreach collection="platformOrgNoList" item="no" open="(" close=")" separator="or">
                exists(select 1 from t_organization b where b.code = r.organization_code and b.no
                like #{no})
            </foreach>
        </if>
        <!--deptCodeList不为空时，r.ORGEH作为条件-->
        <if test="deptCodeList != null and deptCodeList.size() > 0">
            and r.organization_code in
            <foreach collection="deptCodeList" item="deptCode" open="(" close=")" separator=",">
                #{deptCode}
            </foreach>
        </if>
        <!--employeeSubgroupTextList不为空时,r.PERSKTEXT-->
        <if test="employeeSubgroupTextList != null and employeeSubgroupTextList.size() > 0">
            and r.employee_subgroup_text in
            <foreach collection="employeeSubgroupTextList" item="employeeSubgroupText" open="(" close=")"
                     separator=",">
                #{employeeSubgroupText}
            </foreach>
        </if>
        <!--hireTypeList 为不空时-->
        <if test="hireTypeList != null and hireTypeList.size() > 0">
            and r.hire_type_query in
            <foreach collection="hireTypeList" item="hireType" open="(" close=")" separator=",">
                #{hireType}
            </foreach>
        </if>
        <!--manageType不为空时，r.manage_type-->
        <if test="manageType != null and manageType != ''">
            and r.manage_type = #{manageType}
        </if>
        <!--manageTypeList不为空时-->
        <!--<if test="manageTypeList != null and manageTypeList.size() > 0">
            and r.manage_type in
            <foreach collection="manageTypeList" item="manageType" open="(" close=")" separator=",">
                #{manageType}
            </foreach>
        </if>-->

        <!--employeeCategoryList不为空时，r.staff_class3311-->
        <if test="employeeCategoryList != null and employeeCategoryList.size() > 0">
            and r.employee_category_name in
            <foreach collection="employeeCategoryList" item="employeeCategory" open="(" close=")" separator=",">
                #{employeeCategory}
            </foreach>
        </if>
        <!--employeeTeam 不为空 且employeeTeam=干部 时，r.is_carders = 1，employeeTeam=海之子 时 r.is_hzz = 1-->
        <if test="employeeTeam != null and employeeTeam != ''">
            <if test="employeeTeam == '干部'">
                and r.is_cadre = 1
            </if>
            <if test="employeeTeam == '海之子'">
                and r.is_hzz = 1
            </if>
        </if>
        <!--positionTypeList不为空时，r.job_type-->
        <if test="positionTypeList != null and positionTypeList.size() > 0">
            and r.position_type in
            <foreach collection="positionTypeList" item="positionType" open="(" close=")" separator=",">
                #{positionType}
            </foreach>
        </if>
        <!--fromDate不为空时，BEGDA 需>fromDate-->
        <!--<if test="fromDate != null and fromDate != ''">
            and t.BeginDate <![CDATA[ >= ]]> #{fromDate}
        </if>-->
        <!--toDate不为空时，BeginDate <= toDate-->
        <!--<if test="toDate != null and toDate != ''">
            and t.BeginDate <![CDATA[ <= ]]> #{toDate}
        </if>-->
        <!--majorTypeList, r.special_line-->
        <if test="majorTypeList != null and majorTypeList.size() > 0">
            and r.special_line in
            <foreach collection="majorTypeList" item="majorType" open="(" close=")" separator=",">
                #{majorType}
            </foreach>
        </if>
        <!--jobLevel为list，, r.position_level_code-->
        <if test="jobLevel != null and jobLevel.size() > 0">
            and r.position_level_code in
            <foreach collection="jobLevel" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!--hkJobLevel为list，对应r.position_level_code_hk-->
        <if test="hkJobLevel != null and hkJobLevel.size() > 0">
            and r.position_level_code_hk in
            <foreach collection="hkJobLevel" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!--eventName, t.staff_change_refined-->
        <!--<if test="eventName != null and eventName != ''">
            and t.ChangeTypeRefined = #{eventName}
        </if>-->
        <!--jobLevelName, t.job_level_refined-->
        <!--<if test="jobLevelName != null and jobLevelName != ''">
            and t.job_level_refined = #{jobLevelName}
        </if>-->
        <if test="appraisalGradeList != null and appraisalGradeList.size() > 0">
            and r.appraisal_result_year1 in
            <foreach collection="appraisalGradeList" item="appraisalGrade" open="(" close=")" separator=",">
                #{appraisalGrade}
            </foreach>
        </if>
        <!--hzzGenerationList-->
        <if test="hzzGenerationList != null and hzzGenerationList.size() > 0">
            and r.hzz_generation in
            <foreach collection="hzzGenerationList" item="hzzGeneration" open="(" close=")" separator=",">
                #{hzzGeneration}
            </foreach>
        </if>
        <!--minAge-->
        <if test="minAge != null and minAge != ''">
            and r.age <![CDATA[ >= ]]> #{minAge}
        </if>
        <!--maxAge-->
        <if test="maxAge != null and maxAge != ''">
            and r.age <![CDATA[ <= ]]> #{maxAge}
        </if>
        <!--educationList-->
        <if test="educationList != null and educationList.size() > 0">
            and r.education in
            <foreach collection="educationList" item="education" open="(" close=")" separator=",">
                #{education}
            </foreach>
        </if>
    </sql>
    <select id="getNoById" resultType="com.csci.hrrs.dto.RosterDetailDTO">
        SELECT t.id, o.no as organization_no, t.adname from t_roster_detail t left join t_organization o on o.code = t.organization_code
        where t.id = #{id} and t.head_id = (SELECT top 1 id FROM t_roster_head WHERE is_deleted = 0 ORDER BY stat_date DESC)
        and t.is_deleted = 0 and o.is_deleted = 0
        <if test="platformCompanyCode!= null and platformCompanyCode != ''">
            and t.platform_company_code = #{platformCompanyCode}
         </if>
    </select>

    <select id="getNameByName" resultMap="BaseResultMap">
        select id,name, eng_name, adname from
        t_roster_detail t where
        t.head_id = (SELECT top 1 id FROM t_roster_head WHERE is_deleted = 0 ORDER BY stat_date DESC)
        <if test="nameList != null and nameList.size() > 0">
            and (
            <foreach collection="nameList" item="name" separator=" OR ">
                (t.name LIKE concat('%', #{name}, '%') OR t.adname LIKE concat('%', #{name}, '%'))
            </foreach>
            )
        </if>
        and t.is_deleted = 0
        <if test="platformCompanyCode!= null and platformCompanyCode != ''">
            and t.platform_company_code = #{platformCompanyCode}
        </if>
    </select>


    <select id="selectPositionLevelDistribution" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(1) as count
        from (select isnull(c.JobLevelPilot, N'助理经理以下') as name, row_number() over (partition by r.pernr order by
        c.BeginDate desc) as rn
        from t_roster_detail r
        left join t_job_change c on r.pernr = c.PERNR
        <include refid="addAuthCondition">
        </include>
        <where>
            <include refid="dashboardRosterDetailCondition"/>
        </where>
        ) t
        where t.rn = 1
        and t.name is not null
        and t.name not in(N'助理总经理级', N'地盘合约')
        group by t.name
    </select>

    <select id="selectRosterDetailWithOrgInfo" resultType="com.csci.hrrs.vo.RosterWithOrgVO">
        select * from
        ( select r.id, r.name as name, r.pernr as pernr, o.name as orgName, o.code as orgCode, o.no as orgNo,
        (select isnull(c.JobLevelPilot, N'助理经理以下')) as positionLevelName,
        r.seq, r.is_hzz as isHzz, r.has_overseas_work_exp as hasOverseasWorkExp, r.employee_category as
        employeeCategory,
        <!--r.work_place as workPlace,-->
        r.primary_position as primaryPosition,
        r.hzz_generation as hzzGeneration,
        row_number() over (partition by r.pernr order by c.BeginDate desc) as rn
        from t_roster_detail r
        left join t_job_change c on r.pernr = c.PERNR
        left join t_organization o on r.organization_code = o.code
        <include refid="addAuthCondition"/>
        <where>
            <include refid="dashboardRosterDetailCondition"/>
        </where>
        ) t
        where t.rn = 1
        <if test="hzzGenerationList != null and hzzGenerationList.size() > 0">
            order by t.hzzGeneration, t.seq
        </if>
        <!--否则按t.seq排序-->
        <if test="hzzGenerationList == null or hzzGenerationList.size() == 0">
            order by t.seq
        </if>
    </select>

    <select id="selectRosterDetailByOrgNo" resultMap="BaseResultMap">
        select t.*
        from t_roster_detail t
                 join t_organization o on t.organization_code = o.code
        where t.is_deleted = 0
          and t.head_id = #{headId}
          and o.is_deleted = 0
          and o.no like #{orgNO}
    </select>

    <select id="getRosterCountByStaffClass" resultType="com.csci.hrrs.vo.StaffClassCountVO">
        select isnull(t.staff_class, N'未分类') as staffClass, count(*) as count
        from t_roster_detail t join t_organization o
        on t.organization_code = o.code
        where t.is_deleted = 0
          and t.head_id = #{headId}
          and o.is_deleted = 0
          and o.no like #{orgNo}
        group by t.staff_class
    </select>

    <select id="selectRosterCountByStaffClass" resultType="com.csci.hrrs.vo.StaffClassCountVO">
        select isnull(t.staff_class, N'未分类') as staffClass, count(*) as count
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and t.head_id = #{headId}
        and o.is_deleted = 0
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
        group by t.staff_class
    </select>

    <select id="selectAgeGenderDistribution" resultMap="BaseResultMap">
        select t.age, t.gender
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
    </select>

    <select id="selectPositionLevelCode" resultType="java.lang.String">
        select t.position_level_code
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type_query != N'外劳'
        </if>
    </select>

    <select id="selectPositionLevel" resultType="java.lang.String">
        select t.position_level
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type_query != N'外劳'
        </if>
    </select>

    <select id="selectAgeCohlSeniority" resultMap="BaseResultMap">
        select t.age, t.cohl_seniority
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
    </select>

    <select id="selectTalentInfo" resultMap="BaseResultMap">
        select talent_inventory_placement
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
    </select>

    <select id="selectFormerTalentCount" resultType="long">
        select count(t.talent_inventory_placement)
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and t.head_id = 'formerEmployee'
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and t.talent_inventory_placement is not null
        and t.talent_inventory_placement != ''
        and t.dimission_date between #{fromDate} and #{toDate}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
    </select>

    <select id="selectOnJobCountByMonth" resultType="com.csci.hrrs.vo.OnJobMonthCountVO">
        select h.stat_date as statDate, count(1) as count
        from t_roster_detail t
        join t_roster_head h on t.head_id = h.id
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and h.is_deleted = 0
        and o.is_deleted = 0
        and h.stat_date between #{fromDate} and #{toDate}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        group by h.stat_date;
    </select>

    <select id="selectFormerEmpByPositionType" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.position_type as name, count(1) as count
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = 'formerEmployee'
        and t.dimission_date between #{fromDate} and #{toDate}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
        group by t.position_type
        order by t.position_type
    </select>

    <select id="selectCountByEducation" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.education as name, count(1) as count
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
        group by t.education
    </select>

    <select id="selectOnJobCount" resultType="java.lang.Long">
        select isnull(count(*), 0) as count
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        FROM t_user_organization
        WHERE is_deleted = 0
        AND user_id = #{userId}) u
        ON u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="includeWailao == false">
            and t.hire_type != N'外劳'
        </if>
    </select>

    <select id="selectPositionTypeAndStaffClass" resultMap="BaseResultMap">
        select position_type, staff_class
        from t_roster_detail t
        join t_organization o
        on t.organization_code = o.code
        join (select distinct user_id, organization_code
        from t_user_organization
        where is_deleted = 0
        and user_id = #{userId}) u on u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
        <if test="displayWailao == false">
            and t.hire_type != N'外劳'
        </if>
    </select>
    <select id="selectRosterByHeadIdAndOrgCode" resultMap="BaseResultMap">
        select t.name, t.pernr, t.primary_position, t.platform, t.subsidiary
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        FROM t_user_organization
        WHERE is_deleted = 0
        AND user_id = #{userId}) u
        ON u.organization_code = o.code
        where t.is_deleted = 0
        and o.is_deleted = 0
        and t.head_id = #{headId}
        and o.code in
        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
            #{orgCode}
        </foreach>
    </select>

    <select id="selectRosterByDashboardQO" resultMap="BaseResultMap">
        select t.name, t.pernr, t.primary_position, t.platform, t.subsidiary
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        FROM t_user_organization
        WHERE is_deleted = 0
        AND user_id = #{userId}) u
        ON u.organization_code = o.code
        <where>
            t.is_deleted = 0
            and o.is_deleted = 0
            and t.head_id = #{headId}
            and o.code in
            <foreach collection="organizationCodeList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode}
            </foreach>
            <!--这里用两个if条件来分别处理查询核心人才和非核心人才的情况；当用户没有传isCoreTalent时，则表示默认查询所有-->
            <if test="isCoreTalent != null and isCoreTalent == true">
                and t.talent_inventory_placement is not null
                and t.talent_inventory_placement != ''
            </if>
            <if test="isCoreTalent != null and isCoreTalent == false">
                and (t.talent_inventory_placement is null or t.talent_inventory_placement = '')
            </if>
            <!--是否男女条件-->
            <if test="isMale != null and isMale == true">
                and t.sex = N'男'
            </if>
            <if test="isMale != null and isMale == false">
                and t.sex = N'女'
            </if>
            <!--education-->
            <if test="education != null and education != ''">
                and t.education = #{education}
            </if>
        </where>
        order by t.seq
    </select>

    <select id="selectDuplicateFormerEmployee" resultType="java.lang.String">
        select pernr
        from t_roster_detail
        where head_id = 'formerEmployee'
          and is_deleted = 0
        group by pernr
        having count(1) > 1
    </select>

    <select id="selectRosterForHKDash" resultMap="BaseResultMap">
        select t.*
        from t_roster_detail t
        join t_organization o on t.organization_code = o.code
        join (select distinct user_id, organization_code
        FROM t_user_organization
        WHERE is_deleted = 0
        AND user_id = #{userId}) u ON u.organization_code = o.code
        <where>
            t.is_deleted = 0
            and o.is_deleted = 0
            and t.head_id = #{headId}
            and o.code in
            <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode}
            </foreach>
            <if test="beginDate != null">
                and t.dimission_date <![CDATA[ >= ]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t.dimission_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="displayWailao == false">
                and t.hire_type != N'外劳'
            </if>
        </where>
    </select>

    <select id="selectRosterDetail" resultMap="BaseResultMap">
        select * from t_roster_detail r
        <include refid="addAuthCondition">
        </include>
        <include refid="dashboardRosterDetailCondition">
        </include>
    </select>

    <select id="selectProvinceList" resultType="com.csci.hrrs.vo.ProvincePersonCountVO">
        select case
        when province is null or province = '' then N'其他'
        else province
        end as province,
        count(1) as count
        from t_roster_detail r
        <include refid="addAuthCondition">
        </include>
        <include refid="dashboardRosterDetailCondition"/>
        group by case
        when province is null or province = '' then N'其他'
        else province
        end
    </select>
    <select id="selectPersonCountByHeadIdsAndOrgCode" resultType="com.csci.hrrs.vo.IdCountVO">
        select id, count(*)
        from t_roster_detail
        where is_deleted = 0
        and head_id in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
        <if test="organizationCode != null">
            and organization_code = #{organizationCode}
        </if>
        <if test="staffClass3311 != null">
            and staff_class3311 = #{staffClass3311}
        </if>
        group by id
    </select>

    <select id="selectHKPersonCountForOutputValue" resultType="com.csci.hrrs.vo.NameCountVO">
        select head_id as name, count(*) as count
        from t_roster_detail t
        <where>
            <include refid="conditionForOutputValue"/>
            <include refid="defaultHKRosterCondition"/>
            <!--and organization_code in ('111', '222')-->
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <!--and head_id in ('333', '444')-->
            <if test="headIds != null and headIds.size() > 0">
                and head_id in
                <foreach collection="headIds" item="headId" open="(" close=")" separator=",">
                    #{headId}
                </foreach>
            </if>
        </where>
        group by head_id
    </select>

    <select id="selectHKPersonCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select head_id as name, count(*) as count
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            <!--and organization_code in ('111', '222')-->
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <!--and head_id in ('333', '444')-->
            <if test="headIds != null and headIds.size() > 0">
                and head_id in
                <foreach collection="headIds" item="headId" open="(" close=")" separator=",">
                    #{headId}
                </foreach>
            </if>
        </where>
        group by head_id
    </select>

    <select id="selectHKRosterList" resultMap="BaseResultMap">
        select t.*
        <include refid="defaultHKRosterList"/>
    </select>

    <select id="selectHKManagerRosterList" resultMap="BaseResultMap">
        select t.*
        from t_roster_detail t
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKManagerRosterCondition"/>
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKRosterBySiteList" resultMap="BaseResultMap">
        select t.*
        <include refid="defaultHKRosterList"/>
        and o.is_project = 1 and t.subsidiary like '%公司%'
    </select>

    <sql id="defaultHKRosterList">
        from t_roster_detail t
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="isProject != null">
                and o.is_project = #{isProject}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="defaultHKRosterList4Factor">
        from t_roster_detail t
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition4Factor"/>
            and t.head_id = #{headId}
            <if test="isProject != null">
                and o.is_project = #{isProject}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectRosterDetailDistributionByCategory" resultMap="BaseResultMap">
        select * from
        (select t.*,
            CASE
            WHEN t.staff_class3311 = N'内地' THEN N'内地'
            WHEN t.staff_class3311 = N'内派' THEN N'大团队核心层'
            WHEN t.staff_class3311 = N'境外聘' AND
                 t.person_in_charge IN (N'中建香港-地盤部門負責人',N'中建香港-地盘部门负责人',N'內派負責人',N'負責人',N'内派负责人',N'负责人') THEN N'大团队核心层'
            WHEN t.employee_subgroup = N'24' THEN N'自有日薪'
            ELSE N'地盘基层'
        END AS personType
        <include refid="defaultHKRosterList" />
        and o.is_project = 1 and t.subsidiary like '%公司%') as subquery
        WHERE personType = #{personType} ORDER BY seq
        OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY;
    </select>

    <select id="selectRosterDetailDistributionByCategoryTotalCount" resultType="java.lang.Integer">
        select count(*) from
        (select t.*,
            CASE
            WHEN t.staff_class3311 = N'内地' THEN N'内地'
            WHEN t.staff_class3311 = N'内派' THEN N'大团队核心层'
            WHEN t.staff_class3311 = N'境外聘' AND
                t.person_in_charge IN (N'中建香港-地盤部門負責人',N'中建香港-地盘部门负责人',N'內派負責人',N'負責人',N'内派负责人',N'负责人') THEN N'大团队核心层'
            WHEN t.employee_subgroup = N'24' THEN N'自有日薪'
            ELSE N'地盘基层'
        END AS personType
        <include refid="defaultHKRosterList" />
         and o.is_project = 1 and t.subsidiary like '%公司%') as subquery
        WHERE personType = #{personType}
    </select>

    <select id="selectHKRosterTypeList" resultMap="BaseResultMap">
        select t.*,
        CASE
        WHEN t.staff_class3311 = N'内地' THEN N'内地'
        WHEN t.staff_class3311 = N'内派' THEN N'内派'
        WHEN t.staff_class3311 = N'境外聘' AND t.staff_class = N'专才' THEN N'专才'
        WHEN t.staff_class3311 = N'境外聘' AND t.staff_class != N'专才' THEN N'港聘'
        END AS person_type
        <include refid="defaultHKRosterType"/>
        ORDER BY seq
        OFFSET #{offset}  ROWS FETCH NEXT #{limit} ROWS ONLY;
    </select>

    <sql id="defaultHKRosterType">
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            AND (CASE
            WHEN t.staff_class3311 = N'内地' THEN N'内地'
            WHEN t.staff_class3311 = N'内派' THEN N'内派'
            WHEN t.staff_class3311 = N'境外聘' AND t.staff_class = N'专才' THEN N'专才'
            WHEN t.staff_class3311 = N'境外聘' AND t.staff_class != N'专才' THEN N'港聘'
            END) = #{personType}
        </where>
    </sql>


    <select id="getHKRosterTypeListTotalCount" resultType="java.lang.Integer">
        select COUNT(*)
        <include refid="defaultHKRosterType"/>
    </select>

    <select id="selectHKOutsourceRosterList" resultMap="BaseResultMap">
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.head_id = #{headId}
            <if test="outsourceTypeList != null and outsourceTypeList.size() > 0">
                and t.personnel_scope in
                <foreach collection="outsourceTypeList" item="outsourceType" open="(" close=")" separator=",">
                    #{outsourceType}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <sql id="defaultHKRosterCondition">
        and t.is_deleted = 0
        and t.platform_company_code = '00100017'
        and t.staff_class != N'外劳'
        and ((t.manage_type = N'管理人员' and (t.hire_type_query != N'外包' or (t.hire_type_query = N'外包' and t.staff_class3311 = N'内地'))) or t.staff_class3311 = N'内派')
        and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
        and t.organization_code != '50001743'
        and t.organization_code != '50002106'
        and t.employee_subgroup != N'24'
    </sql>

    <sql id="defaultHKRosterCondition2">
        and t.is_deleted = 0
        and t.platform_company_code = '00100017'
        and t.staff_class != N'外劳'
        and ((t.manage_type = N'管理人员' and (t.hire_type_query != N'外包' or (t.hire_type_query = N'外包' and t.staff_class3311 = N'内地'))) or t.staff_class3311 = N'内派')
        and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
        and t.organization_code != '50001743'
        and t.organization_code != '50002106'
    </sql>

    <sql id="defaultHKRosterCondition4Factor">
        and t.is_deleted = 0
        and t.platform_company_code = '00100017'
        and t.staff_class != N'外劳'
        and ((t.hire_type_query != N'外包' or (t.hire_type_query = N'外包' and t.staff_class3311 = N'内地')) or t.staff_class3311 = N'内派')
        and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
        and t.organization_code != '50001743'
        and t.organization_code != '50002106'
    </sql>

    <sql id="conditionForOutputValue">
        and (t.manage_type = N'管理人员')
    </sql>

    <sql id="defaultHKOutsourceRosterCondition">
        and t.is_deleted = 0
        and t.platform_company_code = '00100017'
        and t.staff_class != N'外劳'
        and (t.hire_type_query = N'外包')
        and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
        and t.organization_code != '50001743'
        and t.organization_code != '50002106'
    </sql>

    <sql id="defaultHKRosterNotHireTypeCondition">
        and t.is_deleted = 0
        and t.platform_company_code = '00100017'
        and t.staff_class != N'外劳'
        and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
        and t.organization_code != '50001743'
        and t.organization_code != '50002106'
    </sql>

    <sql id="defaultHKManagerRosterCondition">
        <include refid="defaultHKRosterCondition"/>
        and t.manage_type = N'管理人员'
    </sql>

    <select id="selectHKPersonCountBySubsidiary" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.subsidiary as name, count(*) as count
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="subsidiaryList != null and subsidiaryList.size() > 0">
                and t.subsidiary in
                <foreach collection="subsidiaryList" item="subsidiary" open="(" close=")" separator=",">
                    #{subsidiary}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            and h.stat_date <![CDATA[>=]]> #{startDate} and h.stat_date <![CDATA[<=]]> #{endDate}
        </where>
        group by t.subsidiary
    </select>


    <select id="selectHKPersonCountBySubsidiaryForOutputValue" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.subsidiary as name, count(*) as count
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="conditionForOutputValue"/>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="subsidiaryList != null and subsidiaryList.size() > 0">
                and t.subsidiary in
                <foreach collection="subsidiaryList" item="subsidiary" open="(" close=")" separator=",">
                    #{subsidiary}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            and h.stat_date <![CDATA[>=]]> #{startDate} and h.stat_date <![CDATA[<=]]> #{endDate}
        </where>
        group by t.subsidiary
    </select>

    <select id="selectHKManagerCountGroupBySubsidiaryForSalary" resultType="com.csci.hrrs.vo.NameCountVO">
        select o.sub_company_dept as name, sum(s.headcount) as count
        from t_sc_salary_record s
        left join t_organization o on s.organization_code = o.code and o.is_deleted = 0
        <where>
            and s.salary_year = year(#{startDate}) and s.salary_month = month(#{endDate})
            and (
            s.employee_category = N'内派'
            or (s.employee_category = N'港聘' and s.staff_type = N'管理人员')
            or (s.employee_category = N'内地' and s.staff_type = N'管理人员')
            or (s.employee_category = N'内地' and s.staff_type = N'中智外包')
            )
            and s.is_deleted = 0
            <if test="subsidiaryList != null and subsidiaryList.size() > 0">
                and o.sub_company_dept in
                <foreach collection="subsidiaryList" item="subsidiary" open="(" close=")" separator=",">
                    #{subsidiary}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and s.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        group by o.sub_company_dept
    </select>

    <select id="selectHKPersonMonthCountBySubsidiary" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.subsidiary as name, count(*) as count
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="subsidiaryList != null and subsidiaryList.size() > 0">
                and t.subsidiary in
                <foreach collection="subsidiaryList" item="subsidiary" open="(" close=")" separator=",">
                    #{subsidiary}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            and FORMAT(h.stat_date, 'yyyy-MM') = #{yearMonth}
        </where>
        group by t.subsidiary
    </select>

    <select id="selectHKOrgPersonCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <!--<![CDATA[ and h.stat_date >= #{startDate} and h.stat_date <= #{endDate} ]]>-->
            <if test="startDate != null">
                and h.stat_date <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by t.organization_code
    </select>

    <select id="selectHKOrgPersonCountForOutputValue" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="conditionForOutputValue"/>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <!--<![CDATA[ and h.stat_date >= #{startDate} and h.stat_date <= #{endDate} ]]>-->
            <if test="startDate != null">
                and h.stat_date <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by t.organization_code
    </select>

    <select id="selectFullHKOrgOnJobCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        LEFT JOIN t_organization o on o.code = t.organization_code
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            AND h.stat_date >= o.start_date
            <if test="endDate != null">
                and h.stat_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by t.organization_code
    </select>


    <select id="selectFullHKOrgOnJobCountForOutputValue" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        LEFT JOIN t_organization o on o.code = t.organization_code
        <where>
            <include refid="conditionForOutputValue"/>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            AND h.stat_date >= (
                case when o.start_date is not null then o.start_date
                else (select top 1 _po.start_date from t_organization _po where _po.is_deleted = 0 and _po.is_project = 1 and _po.code = o.parent_code) end
            )
            <if test="endDate != null">
                and h.stat_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by t.organization_code
    </select>

    <select id="selectHKPersonCountByOrgCode" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        group by t.organization_code
    </select>
    <select id="selectHKJoin3311Count" resultType="long">
        select count(*)
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="startDate != null">
                and t.join_3311_time <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                and t.join_3311_time <![CDATA[<=]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKOutsourceCount" resultType="long">
        select count(*)
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.hire_type = N'外包' AND t.staff_class3311 = N'境外聘'
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectZZOutsourceCount" resultType="long">
        select count(*)
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.personnel_scope in ('H053')
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKRegretLossCountByOrgCode" resultType="com.csci.hrrs.vo.NameCountVO">
        select t.organization_code as name, count(*) as count
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = 'formerEmployee'
            and t.dimission_date between #{startDate} and #{endDate}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="regretFlag == 1">
                and t.appraisal_result_year1 in ('A', 'B+')
            </if>
        </where>
        group by t.organization_code
    </select>


    <select id="selectSiteAppraisalResult" resultType="com.csci.hrrs.vo.AppraisalResultVO">
        select t.pernr, t.name, t.primary_position, t.appraisal_result_year1, t.appraisal_result_year2, t.appraisal_result_year3
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            and t.appraisal_result_year1 in (N'A', N'B-')
            <include refid="conditionForOutputValue"/>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            and h.stat_date <![CDATA[ >= ]]> #{startDate} and h.stat_date <![CDATA[ <= ]]> #{endDate}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        order by t.appraisal_result_year1, t.appraisal_result_year2, t.appraisal_result_year3, t.primary_position, t.name
    </select>

    <select id="selectSiteEmpType" resultType="com.csci.hrrs.vo.CodeNameCountVO">
        select code, name, count(*) as count
        from (select convert(varchar(7), h.stat_date, 120) as code,
        case
        when t.staff_class3311 = N'内地' then N'内地'
        when t.staff_class3311 = N'内派' then N'内派'
        when t.staff_class3311 = N'境外聘' and t.hk_job_grade in ('G13', 'G14', 'G15', 'G16', 'G17', 'G18', 'G19',
        'G20') then N'核心港聘'
        when t.employee_subgroup = N'24' then N'自有日薪'
        else N'地盘基层' end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition4Factor"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            and o.is_project = 1
            and t.subsidiary like '%公司%'
        </where>
        ) a
        group by code, name
    </select>

    <select id="selectEmpType" resultType="com.csci.hrrs.vo.CodeNameCountVO">
        select max(siteCode) siteCode,code, name, count(*) as count
        from (SELECT CONVERT(VARCHAR(7), h.stat_date, 120) AS code, o.code siteCode,
        CASE
            WHEN o.name LIKE CONCAT('%', N'中建香港领导', '%') THEN N'公司领导'
            WHEN o.is_project = 0 AND t.subsidiary LIKE '%公司%' THEN N'子公司写字楼'
            WHEN o.is_project = 1 AND t.subsidiary LIKE '%公司%' THEN N'地盘'
        ELSE N'职能部门'
        END AS name
        FROM t_roster_detail t
        LEFT JOIN t_organization o ON o.code = t.organization_code and o.is_deleted = 0
        LEFT JOIN t_roster_head h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0 and h.id != N'formerEmployee'
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        ) a
        group by code, name
    </select>

    <select id="selectCountByOrgCodeGroupByType" resultType="com.csci.hrrs.vo.NameCountVO">
        <!--BZ.01	内派
            BZ.02	港聘核心管理人员
            BZ.03	地盘基层管理人员
            BZ.04	内地中台
            BZ.05	日薪工勤
            BZ.06	外包人员
            -->
        select name, count(*) as count
        from
        (select case
        when t.staff_class3311 = N'内地' then N'BZ.04'
        when t.staff_class3311 = N'内派' then N'BZ.01'
        when t.staff_class3311 = N'境外聘' and t.hk_job_grade in ('G13', 'G14', 'G15', 'G16', 'G17', 'G18', 'G19',
        'G20') then N'BZ.02'
        else N'BZ.03' end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>

        UNION ALL

        select case
        when t.employee_subgroup = '24' then N'BZ.05'
        when t.hire_type_query = N'外包' then N'BZ.06'
        end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and h.is_deleted = 0
            and  ((t.hire_type = N'外包' AND t.staff_class3311 = N'境外聘') or t.employee_subgroup = '24')
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        ) a
        group by name
    </select>

    <select id="selectHKOverallCountByCategory" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(*) as count
        from
        (select case
        when t.staff_class3311 = N'内地' then N'内地'
        when t.staff_class3311 = N'内派' then N'内派'
        when t.staff_class3311 = N'境外聘' and t.work_place = N'海外' then N'海外'
        <!--when t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.staff_class = N'专才' then N'含专才'-->
        when t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.manage_type =
        N'管理人员' then N'管理人员'
        when t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.manage_type =
        N'非管理人员' then N'自有工人'
        else N'其他港聘' end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        ) a
        group by name
    </select>

    <select id="selectHKOverallTotalCount" resultType="java.lang.Long">
        select count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition4Factor"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKOverallManagerCount" resultType="java.lang.Long">
        select count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSpecialistCount" resultType="long">
        select count(*)
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            and t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.staff_class =
            N'专才'
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKStaffCountByYear" resultType="com.csci.hrrs.vo.YearCountVO">
        select h.stat_year as year, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="includeCurrentYear != null and includeCurrentYear = true">
                and (
                (h.stat_year in
                <foreach collection="yearList" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
                and h.stat_month = 12)
                or (h.stat_year = year(getdate()) and h.stat_month = month(getdate()))
                )
            </if>
            <if test="includeCurrentYear == null or includeCurrentYear == false">
                and h.stat_year in
                <foreach collection="yearList" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
                and h.stat_month = 12
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        group by h.stat_year
        order by h.stat_year
    </select>

    <select id="selectHKDeptStaffCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(*) as count
        from (select case
        when t.subsidiary in (N'中建资讯科技公司', N'海宏公司') then N'中资讯、海宏'
        when t.subsidiary in (N'基础公司', N'机械公司') then N'基础公司'
        when t.subsidiary = N'土木公司' then N'土木公司'
        when t.subsidiary = N'房屋公司' then N'房屋公司'
        when t.subsidiary = N'机电公司' then N'机电公司'
        when t.subsidiary = N'中建医疗公司' then N'中建医疗公司'
        else N'职能部门' end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
        ) a
        group by name;
    </select>

    <select id="selectHKOfficeStaffCount" resultType="long">
        select count(*)
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        left join t_organization o on o.code = t.organization_code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            and not (
                t.subsidiary like N'%公司%'
                and o.is_project = 1
            )
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="selectHKGenderCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(*) as count
        from (select
        case when t.gender = 1 then N'男'
        else N'女' end as name
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
        ) a
        group by name
    </select>

    <select id="selectHKEducationCount" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(*) as count
        from (select case
        when t.education in (N'博士', N'硕士') then N'硕士及以上'
        when t.education = N'本科' then N'本科'
        else N'大专及以下' end as name
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
        ) a
        group by name
    </select>

    <select id="selectHKAgeStat" resultType="com.csci.hrrs.vo.NameCountVO">
        select name, count(*) as count
        from (select case
        when t.age <![CDATA[>=]]> 20 and t.age <![CDATA[<]]> 30 then N'20-29岁'
        when t.age <![CDATA[>=]]> 30 and t.age <![CDATA[<]]> 40 then N'30-39岁'
        when t.age <![CDATA[>=]]> 40 and t.age <![CDATA[<]]> 50 then N'40-49岁'
        when t.age <![CDATA[>=]]> 50 and t.age <![CDATA[<]]> 60 then N'50-59岁'
        when t.age <![CDATA[>=]]> 60 then N'60岁以上'
        else N'未知' end as name
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
        ) a
        group by name
    </select>

    <select id="selectHKAvgAge" resultType="java.math.BigDecimal">
        WITH ValidRosterHeads AS (SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate})
        SELECT CAST(SUM(t.age) * 1.0 / COUNT(*) AS DECIMAL(10, 1)) AS avgAge
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKCohlSeniority" resultType="com.csci.hrrs.vo.NameCountVO">
        WITH ValidRosterHeads AS (
        SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate}
        )
        SELECT
        CASE
        WHEN t.cohl_seniority <![CDATA[ < ]]> 1 THEN N'小于1年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 3 THEN N'1-3年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 10 THEN N'3-10年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 20 THEN N'10-20年'
        ELSE N'20年以上'
        END AS name,
        COUNT(*) AS count
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        GROUP BY
        CASE
        WHEN t.cohl_seniority <![CDATA[ < ]]> 1 THEN N'小于1年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 3 THEN N'1-3年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 10 THEN N'3-10年'
        WHEN t.cohl_seniority <![CDATA[ < ]]> 20 THEN N'10-20年'
        ELSE N'20年以上'
        END
    </select>

    <select id="selectHKAvgCohlSeniority" resultType="java.math.BigDecimal">
        WITH ValidRosterHeads AS (SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate})
        -- 主查询进行计算平均司龄
        SELECT CAST(AVG(t.cohl_seniority) AS DECIMAL(10, 1)) AS average_seniority
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="manageFlag == 1">
                and t.manage_type = N'管理人员'
            </if>
            <if test="manageFlag == 0">
                and t.manage_type = N'非管理人员'
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKNationalityCount" resultType="com.csci.hrrs.vo.NameCountVO">
        WITH ValidRosterHeads AS (SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate})
        SELECT CASE WHEN t.nationality = 'HK' THEN N'中国香港'
        WHEN t.nationality = 'CN' AND t.staff_class = N'专才' THEN N'内地（专才）'
        WHEN t.nationality = 'CN' AND t.staff_class3311 = N'内派' THEN N'内地（内派）'
        WHEN t.nationality = 'CN' AND t.staff_class3311 != N'内派' AND t.staff_class != N'专才' THEN N'中国内地'
        WHEN t.nationality IN ('MO', 'TW') THEN N'澳台'
        ELSE N'海外' END AS name,
        COUNT(*) AS count
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        GROUP BY
        CASE WHEN t.nationality = 'HK' THEN N'中国香港'
        WHEN t.nationality = 'CN' AND t.staff_class = N'专才' THEN N'内地（专才）'
        WHEN t.nationality = 'CN' AND t.staff_class3311 = N'内派' THEN N'内地（内派）'
        WHEN t.nationality = 'CN' AND t.staff_class3311 != N'内派' AND t.staff_class != N'专才' THEN N'中国内地'
        WHEN t.nationality IN ('MO', 'TW') THEN N'澳台'
        ELSE N'海外' END
    </select>

    <select id="selectHKOverseasCount" resultType="com.csci.hrrs.vo.NameCountVO">
        WITH ValidRosterHeads AS (
        SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate}
        )
        SELECT
        CASE WHEN ISNULL(LTRIM(RTRIM(t.nationality)), '') = '' THEN N'未知'
        ELSE t.nationality END AS name,
        COUNT(*) AS count
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        GROUP BY
        CASE WHEN ISNULL(LTRIM(RTRIM(t.nationality)), '') = '' THEN N'未知'
        ELSE t.nationality END
        order by count desc
    </select>

    <select id="selectByUsername" resultMap="BaseResultMap">
        select *
        from t_roster_detail
        where username = #{username}
          and is_deleted = 0
          and head_id =
              (select top 1 id from t_roster_head where is_deleted = 0 and stat_date &lt;= #{endDate} order by stat_date desc)
    </select>

    <select id="selectHKStaffCount" resultType="long">
        with ValidRosterHeads as (
        select id
        from t_roster_head
        where is_deleted = 0
        and stat_date between #{startDate} and #{endDate}
        )
        select count(*)
        from t_roster_detail t
        inner join ValidRosterHeads h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKStaffCountForOutputValue" resultType="long">
        with ValidRosterHeads as (
        select id
        from t_roster_head
        where is_deleted = 0
        and stat_date between #{startDate} and #{endDate}
        )
        select count(*)
        from t_roster_detail t
        inner join ValidRosterHeads h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <include refid="conditionForOutputValue" />
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectHKRosterPersonCategoryCountList" resultType="com.csci.hrrs.vo.subsidiary.CountVO">
        SELECT type,COUNT(*) AS count
        from ( SELECT
        CASE
        WHEN o.name LIKE CONCAT('%', #{projectName}, '%') THEN 'leader'
        WHEN o.is_project = 0 AND t.subsidiary LIKE '%公司%' THEN 'office'
        WHEN o.is_project = 1 AND t.subsidiary LIKE '%公司%' THEN 'site'
        ELSE 'dept'
        END AS type
        <include refid="defaultHKRosterPersonCategoryCondition"/>
        ) as subquery
        GROUP BY type
    </select>

    <select id="selectHKRosterPersonCategoryPageList" resultMap="BaseResultMap">
        WITH RosterDetailWithCategory AS (
        SELECT t.*,
        CASE
        WHEN o.name LIKE CONCAT('%', #{projectName}, '%') THEN #{leaderName}
        WHEN o.is_project = 0 and t.subsidiary like N'%公司%' THEN #{officeName}
        WHEN o.is_project = 1 and t.subsidiary like N'%公司%' THEN N'地盘'
        ELSE N'职能部门'
        END AS personCategory
        <include refid="defaultHKRosterPersonCategoryCondition"/>
        )
        SELECT *
        FROM RosterDetailWithCategory
        WHERE personCategory = #{personType}
        ORDER BY seq
        OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY;
    </select>

    <select id="selectHKRosterPersonCategoryPageListTotalCount" resultType="java.lang.Integer">
        WITH RosterDetailWithCategory AS (
        SELECT t.*,
        CASE
        WHEN o.name LIKE CONCAT('%', #{projectName}, '%') THEN #{leaderName}
        WHEN o.is_project = 0 and t.subsidiary like N'%公司%' THEN #{officeName}
        WHEN o.is_project = 1 and t.subsidiary like N'%公司%' THEN N'地盘'
        ELSE N'职能部门'
        END AS personCategory
        <include refid="defaultHKRosterPersonCategoryCondition"/>
        )
        SELECT COUNT(*)
        FROM RosterDetailWithCategory
        WHERE personCategory = #{personType}
    </select>

    <select id="selectHKOutsourceRosterPersonCategoryPageList" resultMap="BaseResultMap">
        WITH RosterDetailWithCategory AS (
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.head_id = #{headId}
            and  ((t.hire_type = N'外包' AND t.staff_class3311 = N'境外聘') or t.employee_subgroup = '24')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        )
        SELECT *
        FROM RosterDetailWithCategory
        ORDER BY join_3311_time DESC
        OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY;
    </select>

    <select id="selectHKOutsourceRosterPersonCategoryPageListTotalCount" resultType="java.lang.Integer">
        WITH RosterDetailWithCategory AS (
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.head_id = #{headId}
            and ( ( hire_type = N'外包' AND staff_class3311 = N'境外聘')or t.employee_subgroup = '24')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        )
        SELECT COUNT(*)
        FROM RosterDetailWithCategory
    </select>

    <sql id="defaultHKRosterPersonCategoryCondition">
        FROM t_roster_detail t
        LEFT JOIN t_organization o ON o.code = t.organization_code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectHKOutsourceRosterPersonCategoryCountList" resultType="com.csci.hrrs.vo.subsidiary.CountVO">
        SELECT type, COUNT(*) AS count
        FROM ( SELECT
        CASE
        WHEN o.name LIKE CONCAT('%', #{projectName}, '%') THEN 'leader'
        WHEN o.is_project = 0 AND t.subsidiary LIKE '%公司%' THEN 'office'
        WHEN o.is_project = 1 AND t.subsidiary LIKE '%公司%' THEN 'site'
        ELSE 'dept'
        END AS type
        FROM t_roster_detail t
        LEFT JOIN t_organization o ON o.code = t.organization_code and o.is_deleted = 0
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>and t.head_id = #{headId}
            <if test="outsourceTypeList != null and outsourceTypeList.size() > 0">
                and t.personnel_scope in
                <foreach collection="outsourceTypeList" item="outsourceType" open="(" close=")" separator=",">
                    #{outsourceType}
                </foreach>
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        ) as subquery
        GROUP BY type;
    </select>

    <select id="selectHKPersonCountByOrgCodeAndDateRange" resultType="com.csci.hrrs.vo.NameCountVO">
        select o.name as name, count(*) as count
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
        group by o.name
    </select>

    <select id="selectHKDismissCountByOrgCodeAndDateRange" resultType="com.csci.hrrs.vo.NameCountVO">
        with JobChange as (
            select *, row_number() over (partition by pernr order by BeginDate desc) as rn
            from t_job_change
            where is_deleted = 0
              and ChangeType = N'离职'
              and BeginDate between #{startDate} and #{endDate}
        )
        select o.name, count(*) as count
        from t_roster_detail t
                 left join JobChange j on t.pernr = j.PERNR and j.rn = 1
                 left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKDismissRosterCondition"/>
        </where>
        group by o.name
    </select>

    <select id="selectHKCoreDismissCountByOrgCodeAndDateRange" resultType="com.csci.hrrs.vo.NameCountVO">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select o.name, count(*) as count
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKDismissRosterCondition"/>
            and t.talent_inventory_placement is not null and t.talent_inventory_placement != ''
        </where>
        group by o.name
    </select>


    <sql id="defaultHKDismissRosterCondition">
        <include refid="defaultHKRosterCondition" />
        and t.dimission_date between #{startDate} and #{endDate}
        and t.is_deleted = 0
        and t.head_id = 'formerEmployee'
        and o.is_deleted = 0
        and ISNULL(j.Lzlx,'') = N'主動離職'
        <if test="orgCodeList != null and orgCodeList.size() > 0">
            and t.organization_code in
            <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode}
            </foreach>
        </if>
        <if test="manageType != null">
            and t.manage_type = #{manageType}
        </if>
    </sql>


    <select id="selectHKDismissBreakdown1" resultType="com.csci.hrrs.vo.NameStringVO">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select N'总体流失人数' as name, count(*) as value
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>

    <select id="selectHKDismissBreakdown2" resultType="com.csci.hrrs.vo.NameStringVO">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select N'核心人才流失人数' as name, count(*) as value
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and t.talent_inventory_placement is not null and t.talent_inventory_placement != ''
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>

    <select id="selectHKDismissBreakdown3" resultType="com.csci.hrrs.vo.NameStringVO">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select N'青年人才流失人数' as name, count(*) as value
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and left(t.position_level, 2) in (N'E1', N'E2', N'E3')
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>

    <select id="selectHKDismissBreakdown4" resultType="com.csci.hrrs.vo.NameStringVO">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select N'绩优人才流失人数' as name, count(*) as value
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and t.appraisal_result_year1 = 'A'
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>

    <select id="selectHKRosterCountByOutsource" resultType="java.lang.Long">
        SELECT count(*)
        FROM t_roster_detail t
        LEFT JOIN t_organization o ON t.organization_code = o.code AND o.is_deleted = 0
        WHERE t.is_deleted = 0
        AND t.platform_company_code = '00100017'
        AND t.staff_class = N'外劳'
        AND ( t.hire_type_query != N'外包' )
        AND ( (t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark IS NULL )
        AND t.organization_code != '50001743'
        and t.organization_code != '50002106'
        AND t.head_id = #{headId}
        <if test="orgCodeList != null and orgCodeList.size() > 0">
            and t.organization_code in
            <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode}
            </foreach>
        </if>
    </select>

    <select id="selectRosterCategoryCount" resultType="com.csci.hrrs.vo.NameCountVO">
        with ValidRosterHeads as
                 (select id from t_roster_head where is_deleted = 0 and stat_date between #{startDate} and #{endDate})
        select name, count(*) as count
        from (select CASE
                         WHEN t.staff_class3311 = N'内地' THEN N'内地'
                         WHEN t.staff_class3311 = N'内派' THEN N'内派'
                         WHEN t.staff_class3311 = N'境外聘' AND t.staff_class = N'专才' THEN N'专才'
                         WHEN t.staff_class3311 = N'境外聘' and t.staff_class = N'外劳' then N'外劳'
                         ELSE N'港聘'
                         END AS name
              from t_roster_detail t
                       join ValidRosterHeads v on t.head_id = v.id
                <where>
                    t.is_deleted = 0
                    and (t.hire_type_query != N'外包')
                    and ((t.remark not like N'%见习生%' and t.remark not like N'%代RE/顾问公司聘请%') or t.remark is null)
                    <if test="orgCodeList != null and orgCodeList.size() > 0">
                        and t.organization_code in
                        <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                            #{orgCode}
                        </foreach>
                    </if>
                </where>
             ) subquery
        group by subquery.name
    </select>

    <select id="selectOutsourceCount" resultType="java.lang.Long">
        with ValidRosterHeads as
                 (select id from t_roster_head where is_deleted = 0 and stat_date between #{startDate} and #{endDate})
        select count(*)
        from t_roster_detail t
                 join ValidRosterHeads v on t.head_id = v.id
        <where>
         t.is_deleted = 0
        and t.hire_type_query != N'外包'
          <if test="orgCodeList != null and orgCodeList.size() > 0">
              and t.organization_code in
              <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                  #{orgCode}
              </foreach>
          </if>
        </where>
    </select>

    <select id="selectCountByDimension" resultType="com.csci.hrrs.vo.NameCountVO">
        with ValidRosterHeads as
                 (select id from t_roster_head where is_deleted = 0 and stat_date between #{startDate} and #{endDate})
        select name, count(*) as count from
            (select CASE
                        WHEN t.staff_class3311 = N'内地' THEN N'内地'
                        WHEN t.staff_class3311 = N'内派' THEN N'大团队核心层内派'
                        WHEN t.staff_class3311 = N'境外聘' AND
                             (TRY_CAST(SUBSTRING(t.hk_job_grade, 2, LEN(t.hk_job_grade)) AS int) >= 13) THEN N'大团队核心层港聘'
                        WHEN t.employee_subgroup = N'24' THEN N'自有日薪'
                        ELSE N'地盘基层'
                        END AS name
             from t_roster_detail t
                      join ValidRosterHeads v on t.head_id = v.id
                 and t.platform = N'中建香港'
             <where>
                 t.is_deleted = 0
                 <include refid="defaultHKRosterCondition">
                 </include>
                 <if test="orgCodeList != null and orgCodeList.size() > 0">
                     and t.organization_code in
                     <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                         #{orgCode}
                     </foreach>
                </if>
             </where>
            ) subquery
        group by subquery.name
    </select>

    <select id="selectAgeCohlSenioritySum" resultType="java.util.Map">
        with ValidRosterHeads as (
        select id
        from t_roster_head
        where is_deleted = 0
        and stat_date between #{startDate} and #{endDate}
        )
        select sum(t.age) as ageSum, sum(t.cohl_seniority) as cohlSenioritySum
        from t_roster_detail t
        inner join ValidRosterHeads h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMinStatDate" resultType="java.time.LocalDate">
        select min(h.stat_date)
        from t_roster_detail t left join t_roster_head h on t.head_id = h.id
        where t.is_deleted = 0
          and h.is_deleted = 0
          and t.organization_code = #{orgCode}
    </select>
    <select id="selectEmployeeSubGroup" resultType="com.csci.hrrs.vo.CodeNameVO">
        select distinct employee_subgroup as code, employee_subgroup_text as name
        from t_roster_detail
        where is_deleted = 0
        and employee_subgroup is not null
        and employee_subgroup_text is not null
        <if test="keyword != null">
            and (employee_subgroup like concat('%', #{keyword}, '%') or employee_subgroup_text like concat('%', #{keyword}, '%'))
        </if>
        order by employee_subgroup
    </select>
    <select id="exportHKRosterTypeList" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*,
        CASE
        WHEN t.staff_class3311 = N'内地' THEN N'内地'
        WHEN t.staff_class3311 = N'内派' THEN N'内派'
        WHEN t.staff_class3311 = N'境外聘' AND t.staff_class = N'专才' THEN N'专才'
        WHEN t.staff_class3311 = N'境外聘' AND t.staff_class != N'专才' THEN N'港聘'
        END AS person_type
        <include refid="defaultHKRosterType"/>
        ORDER BY seq
    </select>
    <select id="exportHKRosterPersonCategoryPageList" resultType="com.csci.hrrs.model.RosterDetail">
        WITH RosterDetailWithCategory AS (
        SELECT t.*,
        CASE
        WHEN o.name LIKE CONCAT('%', #{projectName}, '%') THEN #{leaderName}
        WHEN o.is_project = 0 and t.subsidiary like N'%公司%' THEN #{officeName}
        WHEN o.is_project = 1 and t.subsidiary like N'%公司%' THEN N'地盘'
        ELSE N'职能部门'
        END AS personCategory
        <include refid="defaultHKRosterPersonCategoryCondition"/>
        )
        SELECT *
        FROM RosterDetailWithCategory
        WHERE personCategory = #{personType}
        ORDER BY seq
    </select>
    <select id="exportRosterDetailDistributionByCategory" resultType="com.csci.hrrs.model.RosterDetail">
        select * from
        (select t.*,
        CASE
        WHEN t.staff_class3311 = N'内地' THEN N'内地'
        WHEN t.staff_class3311 = N'内派' THEN N'大团队核心层'
        WHEN t.staff_class3311 = N'境外聘' AND
        (TRY_CAST(SUBSTRING(t.hk_job_grade, 2, LEN(t.hk_job_grade)) AS int) >= 13) THEN N'大团队核心层'
        WHEN t.employee_subgroup = N'24' THEN N'自有日薪'
        ELSE N'地盘基层'
        END AS personType
        <include refid="defaultHKRosterList" />
        and o.is_project = 1 and t.subsidiary like '%公司%') as subquery
        WHERE personType = #{personType} ORDER BY seq
    </select>
    <select id="exportHKOutsourceRosterPersonCategoryPageList" resultType="com.csci.hrrs.model.RosterDetail">
        WITH RosterDetailWithCategory AS (
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKOutsourceRosterCondition"/>
            and t.head_id = #{headId}
            and ( (t.hire_type = N'外包' AND t.staff_class3311 = N'境外聘') or t.employee_subgroup = '24')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        )
        SELECT *
        FROM RosterDetailWithCategory
        ORDER BY join_3311_time DESC
    </select>
    <select id="selectHKOverallTotalList" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition4Factor"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKOverallManagerList" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKOverallListByCategory" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
           <if test="categories != null and categories.size() > 0">
                AND (CASE
                when t.staff_class3311 = N'内地' then N'内地'
                when t.staff_class3311 = N'内派' then N'内派'
                when t.staff_class3311 = N'境外聘' and t.work_place = N'海外' then N'海外'
                when t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.manage_type =
                N'管理人员' then N'管理人员'
                when t.staff_class3311 = N'境外聘' and (t.work_place != N'海外' or t.work_place is null) and t.manage_type =
                N'非管理人员' then N'自有工人'
                else N'其他港聘'
                END) in
            <foreach collection="categories" item="category" open="(" close=")" separator=",">
                #{category}
            </foreach>
            </if>
        </where>
    </select>
    <select id="selectSelfWorkerList" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition4Factor"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
        EXCEPT
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test="startDate != null">
                and h.stat_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and h.stat_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKStaffListByYear" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_roster_head h on t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            and h.is_deleted = 0
            <if test=" isCurrentYear == true">
                and (
                (h.stat_year = #{year}
                and h.stat_month = 12)
                or (h.stat_year = year(getdate()) and h.stat_month = month(getdate()))
                )
            </if>
            <if test="isCurrentYear == false">
                and h.stat_year = #{year}
                and h.stat_month = 12
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKManagerRosterListByDetermineJobLevelName"
            resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKManagerRosterCondition"/>
            and t.head_id = #{headId}
            and (CASE WHEN  UPPER(position_level_code) LIKE 'B%'
                OR UPPER(position_level_code) = 'C1'
                OR UPPER(position_level_code) = 'C2'
                OR UPPER(position_level_code) = 'C3'
                OR UPPER(position_level_code) = 'C4' then 'director'
                WHEN  UPPER(position_level_code) = 'D1'
                OR UPPER(position_level_code) = 'D2' then 'deputyDirector'
                WHEN UPPER(position_level_code) = 'D3'
                OR  UPPER(position_level_code) = 'D4'
                OR  UPPER(position_level_code) = 'E1' then 'deputyManager'
                WHEN UPPER(position_level_code) = 'E2'
                OR UPPER(position_level_code) = 'E3' then 'engineer'
                ELSE 'apprentice' END
                ) = #{determineJobLevelName}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKManagerRosterListByMajor" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        left join t_organization o on t.organization_code = o.code and o.is_deleted = 0
        <where>
            <include refid="defaultHKManagerRosterCondition"/>
            and t.head_id = #{headId}
            and t.job_type_hk = #{major}
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKNationlityList" resultType="com.csci.hrrs.model.RosterDetail">
        WITH ValidRosterHeads AS (SELECT id
        FROM t_roster_head
        WHERE is_deleted = 0
        AND stat_date BETWEEN #{startDate} AND #{endDate})
        SELECT t.*
        FROM t_roster_detail t
        INNER JOIN ValidRosterHeads h ON t.head_id = h.id
        <where>
            <include refid="defaultHKRosterCondition"/>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            AND (
            CASE WHEN t.nationality = 'HK' THEN N'中国香港'
            WHEN t.nationality = 'CN' AND t.staff_class = N'专才' THEN N'内地（专才）'
            WHEN t.nationality = 'CN' AND t.staff_class3311 = N'内派' THEN N'内地（内派）'
            WHEN t.nationality = 'CN' AND t.staff_class3311 != N'内派' AND t.staff_class != N'专才' THEN N'中国内地'
            WHEN t.nationality IN ('MO', 'TW') THEN N'澳台'
            ELSE N'海外' END
            ) = #{nationality}
        </where>
    </select>
    <select id="selectHKPersonList" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            <!--and organization_code in ('111', '222')-->
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <!--and head_id in ('333', '444')-->
            <if test="headId != null and headId != ''">
                and head_id = #{headId}
            </if>
        </where>
    </select>
    <select id="selectHKJoin3311List" resultType="com.csci.hrrs.model.RosterDetail">
        select t.*
        from t_roster_detail t
        <where>
            <include refid="defaultHKRosterCondition"/>
            and t.head_id = #{headId}
            <if test="startDate != null">
                and t.join_3311_time <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                and t.join_3311_time <![CDATA[<=]]> #{endDate}
            </if>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectHKDismissCountByOrgCodeAndDateList" resultType="com.csci.hrrs.model.RosterDetail">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select t.*
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKDismissRosterCondition"/>
        </where>
    </select>
    <select id="selectHKCoreDismissCountByOrgCodeAndDateList" resultType="com.csci.hrrs.model.RosterDetail">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select t.*
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKDismissRosterCondition"/>
            and t.talent_inventory_placement is not null and t.talent_inventory_placement != ''
        </where>
    </select>
    <select id="selectHKDismissBreakdownCoreList" resultType="com.csci.hrrs.model.RosterDetail">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select t.*
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and t.talent_inventory_placement is not null and t.talent_inventory_placement != ''
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>
    <select id="selectHKDismissBreakdownYouthList" resultType="com.csci.hrrs.model.RosterDetail">
        with JobChange as (
        select *, row_number() over (partition by pernr order by BeginDate desc) as rn
        from t_job_change
        where is_deleted = 0
        and ChangeType = N'离职'
        and BeginDate between #{startDate} and #{endDate}
        )
        select t.*
        from t_roster_detail t
        left join JobChange j on t.pernr = j.PERNR and j.rn = 1
        left join t_organization o on t.organization_code = o.code
        <where>
            <include refid="defaultHKRosterCondition">
            </include>
            and left(t.position_level, 2) in (N'E1', N'E2', N'E3')
            and t.dimission_date between #{startDate} and #{endDate}
            and t.is_deleted = 0
            and t.head_id = 'formerEmployee'
            and o.is_deleted = 0
            and j.MGTXT in (N'主动辞职', N'员工提出-劳动合同期满不续签')
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                and t.organization_code in
                <foreach collection="orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode}
                </foreach>
            </if>
            <if test="manageType != null">
                and t.manage_type = #{manageType}
            </if>
        </where>
    </select>
    <!-- 统计指定headId和组织机构下，满足条件的"公司合约员工"和"地盘月薪员工"人数之和 -->
    <select id="countApprovedOrgEmployee" resultType="int">
        SELECT COUNT(*)
        FROM t_roster_detail t
        WHERE t.is_deleted = 0
        AND t.head_id = #{headId}
        AND t.platform = N'中建香港'
        AND t.employee_subgroup IN ('14', '23')
        AND t.job_grade IN ('G09', 'G10', 'G11', 'G12', 'G13', 'G14', 'G15', 'G16', 'G17', 'G18', 'G19', 'G20')
        <if test="orgCodeList != null and orgCodeList.size() > 0">
            AND t.organization_code IN
            <foreach collection="orgCodeList" item="orgCode" open="(" separator="," close=")">
                #{orgCode}
            </foreach>
        </if>
    </select>

    <select id="getUsernameFromLatestRoster" resultType="java.lang.String">
        select top 1 username
        from t_roster_detail where is_deleted = 0 and head_id = (select top 1 id from t_roster_head where is_deleted = 0 order by stat_date desc)
        and pernr = #{staffNo}
    </select>
</mapper>