<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.hrrs.mapper.VehicleFuelUsageMapper">
  <resultMap id="BaseResultMap" type="com.csci.hrrs.model.VehicleFuelUsage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ambient_head_id" jdbcType="VARCHAR" property="ambientHeadId" />
    <result column="month_value" jdbcType="INTEGER" property="monthValue" />
    <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType" />
    <result column="vehicle_emission_standard" jdbcType="VARCHAR" property="vehicleEmissionStandard" />
    <result column="mileage" jdbcType="DECIMAL" property="mileage" />
    <result column="cylinder_capacity" jdbcType="DECIMAL" property="cylinderCapacity" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="rated_power" jdbcType="DECIMAL" property="ratedPower" />
    <result column="fuel_type" jdbcType="VARCHAR" property="fuelType" />
    <result column="fuel_use_amount" jdbcType="DECIMAL" property="fuelUseAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, ambient_head_id, month_value, vehicle_type, vehicle_emission_standard, mileage, 
    cylinder_capacity, weight, rated_power, fuel_type, fuel_use_amount, remark, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id, 
    last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.hrrs.model.VehicleFuelUsageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_ab_vehicle_fuel_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ab_vehicle_fuel_usage
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_ab_vehicle_fuel_usage
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.hrrs.model.VehicleFuelUsageExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_ab_vehicle_fuel_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.hrrs.model.VehicleFuelUsage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_ab_vehicle_fuel_usage (id, ambient_head_id, month_value, 
      vehicle_type, vehicle_emission_standard, mileage, 
      cylinder_capacity, weight, rated_power, 
      fuel_type, fuel_use_amount, remark, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, last_update_user_id, 
      last_update_version)
    values (#{id,jdbcType=VARCHAR}, #{ambientHeadId,jdbcType=VARCHAR}, #{monthValue,jdbcType=INTEGER}, 
      #{vehicleType,jdbcType=VARCHAR}, #{vehicleEmissionStandard,jdbcType=VARCHAR}, #{mileage,jdbcType=DECIMAL}, 
      #{cylinderCapacity,jdbcType=DECIMAL}, #{weight,jdbcType=DECIMAL}, #{ratedPower,jdbcType=DECIMAL}, 
      #{fuelType,jdbcType=VARCHAR}, #{fuelUseAmount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.hrrs.model.VehicleFuelUsage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_ab_vehicle_fuel_usage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ambientHeadId != null">
        ambient_head_id,
      </if>
      <if test="monthValue != null">
        month_value,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="vehicleEmissionStandard != null">
        vehicle_emission_standard,
      </if>
      <if test="mileage != null">
        mileage,
      </if>
      <if test="cylinderCapacity != null">
        cylinder_capacity,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="ratedPower != null">
        rated_power,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="fuelUseAmount != null">
        fuel_use_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="ambientHeadId != null">
        #{ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="monthValue != null">
        #{monthValue,jdbcType=INTEGER},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="vehicleEmissionStandard != null">
        #{vehicleEmissionStandard,jdbcType=VARCHAR},
      </if>
      <if test="mileage != null">
        #{mileage,jdbcType=DECIMAL},
      </if>
      <if test="cylinderCapacity != null">
        #{cylinderCapacity,jdbcType=DECIMAL},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
      <if test="ratedPower != null">
        #{ratedPower,jdbcType=DECIMAL},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="fuelUseAmount != null">
        #{fuelUseAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.hrrs.model.VehicleFuelUsageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from t_ab_vehicle_fuel_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ab_vehicle_fuel_usage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.ambientHeadId != null">
        ambient_head_id = #{record.ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue != null">
        month_value = #{record.monthValue,jdbcType=INTEGER},
      </if>
      <if test="record.vehicleType != null">
        vehicle_type = #{record.vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleEmissionStandard != null">
        vehicle_emission_standard = #{record.vehicleEmissionStandard,jdbcType=VARCHAR},
      </if>
      <if test="record.mileage != null">
        mileage = #{record.mileage,jdbcType=DECIMAL},
      </if>
      <if test="record.cylinderCapacity != null">
        cylinder_capacity = #{record.cylinderCapacity,jdbcType=DECIMAL},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=DECIMAL},
      </if>
      <if test="record.ratedPower != null">
        rated_power = #{record.ratedPower,jdbcType=DECIMAL},
      </if>
      <if test="record.fuelType != null">
        fuel_type = #{record.fuelType,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelUseAmount != null">
        fuel_use_amount = #{record.fuelUseAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ab_vehicle_fuel_usage
    set id = #{record.id,jdbcType=VARCHAR},
      ambient_head_id = #{record.ambientHeadId,jdbcType=VARCHAR},
      month_value = #{record.monthValue,jdbcType=INTEGER},
      vehicle_type = #{record.vehicleType,jdbcType=VARCHAR},
      vehicle_emission_standard = #{record.vehicleEmissionStandard,jdbcType=VARCHAR},
      mileage = #{record.mileage,jdbcType=DECIMAL},
      cylinder_capacity = #{record.cylinderCapacity,jdbcType=DECIMAL},
      weight = #{record.weight,jdbcType=DECIMAL},
      rated_power = #{record.ratedPower,jdbcType=DECIMAL},
      fuel_type = #{record.fuelType,jdbcType=VARCHAR},
      fuel_use_amount = #{record.fuelUseAmount,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.hrrs.model.VehicleFuelUsage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ab_vehicle_fuel_usage
    <set>
      <if test="ambientHeadId != null">
        ambient_head_id = #{ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="monthValue != null">
        month_value = #{monthValue,jdbcType=INTEGER},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="vehicleEmissionStandard != null">
        vehicle_emission_standard = #{vehicleEmissionStandard,jdbcType=VARCHAR},
      </if>
      <if test="mileage != null">
        mileage = #{mileage,jdbcType=DECIMAL},
      </if>
      <if test="cylinderCapacity != null">
        cylinder_capacity = #{cylinderCapacity,jdbcType=DECIMAL},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DECIMAL},
      </if>
      <if test="ratedPower != null">
        rated_power = #{ratedPower,jdbcType=DECIMAL},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="fuelUseAmount != null">
        fuel_use_amount = #{fuelUseAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.hrrs.model.VehicleFuelUsage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ab_vehicle_fuel_usage
    set ambient_head_id = #{ambientHeadId,jdbcType=VARCHAR},
      month_value = #{monthValue,jdbcType=INTEGER},
      vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      vehicle_emission_standard = #{vehicleEmissionStandard,jdbcType=VARCHAR},
      mileage = #{mileage,jdbcType=DECIMAL},
      cylinder_capacity = #{cylinderCapacity,jdbcType=DECIMAL},
      weight = #{weight,jdbcType=DECIMAL},
      rated_power = #{ratedPower,jdbcType=DECIMAL},
      fuel_type = #{fuelType,jdbcType=VARCHAR},
      fuel_use_amount = #{fuelUseAmount,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>