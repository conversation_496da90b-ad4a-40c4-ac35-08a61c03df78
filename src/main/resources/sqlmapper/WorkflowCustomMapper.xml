<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.hrrs.mapper.WorkflowCustomMapper">
    <select id="selectWorkflowNodeByWorkflowId" resultType="com.csci.hrrs.vo.WorkflowNodeVO">
        select n.id               as id,
               w.id               as workflowId,
               n.name             as name,
               n.previous_node_id as previousNodeId,
               n.is_begin_node    as isBeginNode,
        u.id as userId,
        u.name as userRealName
        from t_workflow w
        left join t_workflow_node n on w.id = n.workflow_id
        left join t_workflow_node_user nu on n.id = nu.node_id
        left join t_user u on nu.user_id = u.id
        where w.id = #{workflowId}
        <!--and n.is_begin_node = 0-->
        and w.is_active = 1
    </select>

    <select id="selectWorkflowControlOfSocialPerfOne" resultType="com.csci.hrrs.vo.WorkflowControlVO" parameterType="com.csci.hrrs.qo.WorkflowControlQO">
        select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name as organizationName,
        f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state, h.year, h.month,
        (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName
        from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
        left join t_organization o on o.id = w.organization_id
        left join t_form f on w.form_id = f.id
        <!--left join t_social_performance_head h on h.id = c.business_id-->
        left join t_workflow_node n on n.id = c.current_node_id
        left join t_social_performance_head h on h.id = c.business_id
        where c.is_active = 1
        <if test="organizationId != null">
            and w.organization_id = #{organizationId}
        </if>
        <if test="formId != null">
            and w.form_id = #{formId}
        </if>
        <if test="year != null">
            and h.year = #{year}
        </if>
        and exists(select 1 from t_social_performance_head sh where sh.id = c.business_id and sh.is_active = 1)
    </select>

    <select id="selectWorkflowControlOfSocialPerfTwo" resultType="com.csci.hrrs.vo.WorkflowControlVO">
        select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name as
        organizationName,
        f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state,
        h.year, h.month,
        (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName
        from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
        left join t_organization o on o.id = w.organization_id
        left join t_form f on w.form_id = f.id
        <!--left join t_social_performance_head h on h.id = c.business_id-->
        left join t_workflow_node n on n.id = c.current_node_id
        left join t_social_perf_two_head h on h.id = c.business_id and h.is_active = 1
        where c.is_active = 1
        <if test="organizationId != null">
            and w.organization_id = #{organizationId}
        </if>
        <if test="formId != null">
            and w.form_id = #{formId}
        </if>
        <if test="year != null">
            and h.year = #{year}
        </if>
        and exists(select 1 from t_social_perf_two_head sh where sh.id = c.business_id and sh.is_active = 1)
    </select>

    <select id="selectWorkflowControlOfAmbient" resultType="com.csci.hrrs.vo.WorkflowControlVO">
        select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name as
        organizationName,
        f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state,
        ah.year, ah.month, u.id as createUserId, u.name as createUserRealName,
        (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName
        from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
        left join t_organization o on o.id = w.organization_id
        left join t_form f on w.form_id = f.id
        <!--left join t_social_performance_head h on h.id = c.business_id-->
        left join t_workflow_node n on n.id = c.current_node_id
        left join t_ambient_head ah on ah.id = c.business_id
        left join t_user u on c.create_user_id = u.id
        where c.is_active = 1
        and ah.is_active = 1
        <if test="organizationId != null">
            and w.organization_id = #{organizationId}
        </if>
        <if test='organizationId == null or organizationId == "" '>
            and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
        </if>
        <if test="formId != null">
            and w.form_id = #{formId}
        </if>
        <if test="year != null">
            and ah.year = #{year}
        </if>
        and exists(select 1 from t_ambient_head ah where ah.id = c.business_id and ah.is_active = 1)
    </select>

    <select id="selectUnSubmitWorkflowOfAmbient" resultType="com.csci.hrrs.vo.WorkflowControlVO">
        select w.id as workflowId,
        o.id as organizationId,
        o.name as organizationName,
        f.id as formId,
        f.name as formName
        from t_user_organization uo
        left join t_organization o on uo.organization_id = o.id
        left join t_workflow w on w.organization_id = uo.organization_id
        left join t_form f on w.form_id = f.id
        where
        o.is_deleted = 0
        and uo.user_id = #{userId}
        <if test='formId != null'>
            and w.form_id = #{formId}
        </if>
        and not exists(select 1
        from t_workflow_control c
        left join t_ambient_head h on c.business_id = h.id
        where c.workflow_id = w.id
        and h.organization_id = o.id
        and c.is_active = 1
        and h.is_active = 1
        <if test="year != null">
            and h.year = #{year}
        </if>
        )
    </select>

    <select id="selectUnSubmitOrganizationOfAmbient" resultType="com.csci.hrrs.vo.OrganizationExportData">
        select *
        from (select o.id as id,
                     o.name as name,
                     o.no as no,
             (select name from t_organization where id = o.parent_id) as parentName,
             (select id
              from t_workflow
              where is_active = 1 and organization_id = o.id and form_id = #{formId}) as workflowId
              from t_organization o
              where o.is_deleted = 0
              order by o.name) as t
        where (t.workflowId is null
           or not exists(select 1
                         from t_workflow_control c
                                  left join t_ambient_head h on
                             c.business_id = h.id
                         where c.workflow_id = t.workflowId
                           and c.is_active = 1
                           and h.is_active = 1
                           and h.year = #{year}))
          and not exists(select 1 from t_organization where parent_id = t.id and is_deleted = 0)
        order by no
    </select>

    <select id="selectUnSubmitOrganizationOfSocialPerfOne" resultType="com.csci.hrrs.vo.OrganizationExportData">
        select *
        from (select o.id as id,
                     o.name as name,
                     o.no as no,
             (select name from t_organization where id = o.parent_id) as parentName,
             (select id
              from t_workflow
              where is_active = 1 and organization_id = o.id and form_id = #{formId}) as workflowId
              from t_organization o
              where o.is_deleted = 0
              order by o.name) as t
        where (t.workflowId is null
           or not exists(select 1
                         from t_workflow_control c
                                  left join t_social_performance_head h on
                             c.business_id = h.id
                         where c.workflow_id = t.workflowId
                           and c.is_active = 1
                           and h.is_active = 1
                           and h.year = #{year}))
          and not exists(select 1 from t_organization where parent_id = t.id and is_deleted = 0)
        order by no
    </select>

    <select id="selectUnSubmitOrganizationOfSocialPerfTwo" resultType="com.csci.hrrs.vo.OrganizationExportData">
        select *
        from (select o.id as id,
                     o.name as name,
                     o.no as no,
             (select name from t_organization where id = o.parent_id) as parentName,
             (select id
              from t_workflow
              where is_active = 1 and organization_id = o.id and form_id = #{formId}) as workflowId
              from t_organization o
              where o.is_deleted = 0
              order by o.name) as t
        where (t.workflowId is null
           or not exists(select 1
                         from t_workflow_control c
                                  left join t_social_perf_two_head h on
                             c.business_id = h.id
                         where c.workflow_id = t.workflowId
                           and c.is_active = 1
                           and h.is_active = 1
                           and h.year = #{year}))
          and not exists(select 1 from t_organization where parent_id = t.id and is_deleted = 0)
        order by no
    </select>

    <select id="selectWorkflowControlOfBaseStatInfo" resultType="com.csci.hrrs.vo.WorkflowControlVO">
        select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name as organizationName,
        f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state, year(h.stat_date) as year, month(h.stat_date) as month,
        (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName
        from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
        left join t_organization o on o.id = w.organization_id
        left join t_form f on w.form_id = f.id
        <!--left join t_social_performance_head h on h.id = c.business_id-->
        left join t_workflow_node n on n.id = c.current_node_id
        left join t_hr_base_stat_info_head h on h.id = c.business_id
        where c.is_active = 1
        <if test="organizationId != null">
            and w.organization_id = #{organizationId}
        </if>
        <if test="formId != null">
            and w.form_id = #{formId}
        </if>
        <if test="year != null">
            and year(h.stat_date) = #{year}
        </if>
        and exists(select 1 from t_hr_base_stat_info_head sh where sh.id = c.business_id and sh.is_deleted = 0)
    </select>
</mapper>