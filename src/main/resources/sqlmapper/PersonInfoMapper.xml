<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.hrrs.mapper.PersonInfoMapper">
  <resultMap id="BaseResultMap" type="com.csci.hrrs.model.PersonInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="head_id" jdbcType="NVARCHAR" property="headId" />
    <result column="pernr" jdbcType="NVARCHAR" property="pernr" />
    <result column="name" jdbcType="NVARCHAR" property="name" />
    <result column="pinyin_name" jdbcType="NVARCHAR" property="pinyinName" />
    <result column="eng_name" jdbcType="NVARCHAR" property="engName" />
    <result column="eng_name_display" jdbcType="NVARCHAR" property="engNameDisplay" />
    <result column="platform" jdbcType="NVARCHAR" property="platform" />
    <result column="platform_trad" jdbcType="NVARCHAR" property="platformTrad" />
    <result column="platform_eng" jdbcType="NVARCHAR" property="platformEng" />
    <result column="subsidiary" jdbcType="NVARCHAR" property="subsidiary" />
    <result column="subsidiary_trad" jdbcType="NVARCHAR" property="subsidiaryTrad" />
    <result column="subsidiary_eng" jdbcType="NVARCHAR" property="subsidiaryEng" />
    <result column="sub_project" jdbcType="NVARCHAR" property="subProject" />
    <result column="sub_project_trad" jdbcType="NVARCHAR" property="subProjectTrad" />
    <result column="sub_project_eng" jdbcType="NVARCHAR" property="subProjectEng" />
    <result column="primary_position" jdbcType="NVARCHAR" property="primaryPosition" />
    <result column="conc_position" jdbcType="NVARCHAR" property="concPosition" />
    <result column="person_in_charge" jdbcType="NVARCHAR" property="personInCharge" />
    <result column="person_in_charge_trad" jdbcType="NVARCHAR" property="personInChargeTrad" />
    <result column="person_in_charge_eng" jdbcType="NVARCHAR" property="personInChargeEng" />
    <result column="employee_category" jdbcType="NVARCHAR" property="employeeCategory" />
    <result column="employee_category_name" jdbcType="NVARCHAR" property="employeeCategoryName" />
    <result column="work_place" jdbcType="NVARCHAR" property="workPlace" />
    <result column="position_type" jdbcType="NVARCHAR" property="positionType" />
    <result column="position_type_trad" jdbcType="NVARCHAR" property="positionTypeTrad" />
    <result column="position_type_eng" jdbcType="NVARCHAR" property="positionTypeEng" />
    <result column="expertise" jdbcType="NVARCHAR" property="expertise" />
    <result column="position_level" jdbcType="NVARCHAR" property="positionLevel" />
    <result column="job_grade" jdbcType="NVARCHAR" property="jobGrade" />
    <result column="hk_job_grade" jdbcType="NVARCHAR" property="hkJobGrade" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="start_work_time" jdbcType="DATE" property="startWorkTime" />
    <result column="join_cohl_time" jdbcType="DATE" property="joinCohlTime" />
    <result column="join_3311_time" jdbcType="DATE" property="join3311Time" />
    <result column="start_overseas_time" jdbcType="DATE" property="startOverseasTime" />
    <result column="duration_current_pos" jdbcType="DATE" property="durationCurrentPos" />
    <result column="duration_current_level" jdbcType="DATE" property="durationCurrentLevel" />
    <result column="duration_cur_job_grade" jdbcType="DATE" property="durationCurJobGrade" />
    <result column="birthdate" jdbcType="DATE" property="birthdate" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="ethnicity" jdbcType="NVARCHAR" property="ethnicity" />
    <result column="ethnicity_trad" jdbcType="NVARCHAR" property="ethnicityTrad" />
    <result column="ethnicity_eng" jdbcType="NVARCHAR" property="ethnicityEng" />
    <result column="hometown" jdbcType="NVARCHAR" property="hometown" />
    <result column="birth_place" jdbcType="NVARCHAR" property="birthPlace" />
    <result column="residence" jdbcType="NVARCHAR" property="residence" />
    <result column="marital_status" jdbcType="NVARCHAR" property="maritalStatus" />
    <result column="child_status" jdbcType="NVARCHAR" property="childStatus" />
    <result column="education" jdbcType="NVARCHAR" property="education" />
    <result column="major" jdbcType="NVARCHAR" property="major" />
    <result column="job_title" jdbcType="NVARCHAR" property="jobTitle" />
    <result column="credentials" jdbcType="NVARCHAR" property="credentials" />
    <result column="public_office" jdbcType="NVARCHAR" property="publicOffice" />
    <result column="source" jdbcType="NVARCHAR" property="source" />
    <result column="mobile" jdbcType="NVARCHAR" property="mobile" />
    <result column="email" jdbcType="NVARCHAR" property="email" />
    <result column="remark" jdbcType="NVARCHAR" property="remark" />
    <result column="remark_trad" jdbcType="NVARCHAR" property="remarkTrad" />
    <result column="remark_eng" jdbcType="NVARCHAR" property="remarkEng" />
    <result column="organization_id" jdbcType="NVARCHAR" property="organizationId" />
    <result column="company_code" jdbcType="NVARCHAR" property="companyCode" />
    <result column="salary_code" jdbcType="NVARCHAR" property="salaryCode" />
    <result column="personnel_scope" jdbcType="NVARCHAR" property="personnelScope" />
    <result column="personnel_subscope" jdbcType="NVARCHAR" property="personnelSubscope" />
    <result column="employee_group" jdbcType="NVARCHAR" property="employeeGroup" />
    <result column="employee_group_text" jdbcType="NVARCHAR" property="employeeGroupText" />
    <result column="employee_group_text_trad" jdbcType="NVARCHAR" property="employeeGroupTextTrad" />
    <result column="employee_group_text_eng" jdbcType="NVARCHAR" property="employeeGroupTextEng" />
    <result column="employee_subgroup" jdbcType="NVARCHAR" property="employeeSubgroup" />
    <result column="employee_subgroup_text" jdbcType="NVARCHAR" property="employeeSubgroupText" />
    <result column="employee_subgroup_text_trad" jdbcType="NVARCHAR" property="employeeSubgroupTextTrad" />
    <result column="employee_subgroup_text_eng" jdbcType="NVARCHAR" property="employeeSubgroupTextEng" />
    <result column="mainland_id_card" jdbcType="NVARCHAR" property="mainlandIdCard" />
    <result column="foreign_id_card" jdbcType="NVARCHAR" property="foreignIdCard" />
    <result column="hk_mo_passport" jdbcType="NVARCHAR" property="hkMoPassport" />
    <result column="hk_mo_end_expiry" jdbcType="NVARCHAR" property="hkMoEndExpiry" />
    <result column="employment_mode" jdbcType="NVARCHAR" property="employmentMode" />
    <result column="employment_mode_trad" jdbcType="NVARCHAR" property="employmentModeTrad" />
    <result column="employment_mode_eng" jdbcType="NVARCHAR" property="employmentModeEng" />
    <result column="hzz_generation" jdbcType="NVARCHAR" property="hzzGeneration" />
    <result column="hzz_sequence" jdbcType="NVARCHAR" property="hzzSequence" />
    <result column="political_status" jdbcType="NVARCHAR" property="politicalStatus" />
    <result column="political_status_trad" jdbcType="NVARCHAR" property="politicalStatusTrad" />
    <result column="political_status_eng" jdbcType="NVARCHAR" property="politicalStatusEng" />
    <result column="party_joining_date" jdbcType="DATE" property="partyJoiningDate" />
    <result column="specialty" jdbcType="NVARCHAR" property="specialty" />
    <result column="spouse_location" jdbcType="NVARCHAR" property="spouseLocation" />
    <result column="parents_location" jdbcType="NVARCHAR" property="parentsLocation" />
    <result column="blood_type" jdbcType="NVARCHAR" property="bloodType" />
    <result column="blood_type_trad" jdbcType="NVARCHAR" property="bloodTypeTrad" />
    <result column="blood_type_eng" jdbcType="NVARCHAR" property="bloodTypeEng" />
    <result column="health_condition" jdbcType="NVARCHAR" property="healthCondition" />
    <result column="health_condition_trad" jdbcType="NVARCHAR" property="healthConditionTrad" />
    <result column="health_condition_eng" jdbcType="NVARCHAR" property="healthConditionEng" />
    <result column="interests_hobbies" jdbcType="NVARCHAR" property="interestsHobbies" />
    <result column="archive_company" jdbcType="NVARCHAR" property="archiveCompany" />
    <result column="res_loc" jdbcType="NVARCHAR" property="resLoc" />
    <result column="res_type" jdbcType="NVARCHAR" property="resType" />
    <result column="res_type_trad" jdbcType="NVARCHAR" property="resTypeTrad" />
    <result column="res_type_eng" jdbcType="NVARCHAR" property="resTypeEng" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="is_sync" jdbcType="BIT" property="isSync" />
    <result column="years_in_current_position" jdbcType="DECIMAL" property="yearsInCurrentPosition" />
    <result column="years_in_current_job_level" jdbcType="DECIMAL" property="yearsInCurrentJobLevel" />
    <result column="years_in_current_rank" jdbcType="DECIMAL" property="yearsInCurrentRank" />
    <result column="talent_inventory_placement" jdbcType="NVARCHAR" property="talentInventoryPlacement" />
    <result column="appraisal_result_year1" jdbcType="NVARCHAR" property="appraisalResultYear1" />
    <result column="appraisal_result_year2" jdbcType="NVARCHAR" property="appraisalResultYear2" />
    <result column="appraisal_result_year3" jdbcType="NVARCHAR" property="appraisalResultYear3" />
    <result column="photo" jdbcType="NVARCHAR" property="photo" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
</mapper>