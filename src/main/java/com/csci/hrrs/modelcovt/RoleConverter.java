package com.csci.hrrs.modelcovt;

import com.csci.hrrs.model.Role;
import com.csci.hrrs.vo.RoleVO;
import org.springframework.beans.BeanUtils;

public class RoleConverter {
    public static RoleVO convert(Role source) {
        RoleVO roleVO = new RoleVO();
        BeanUtils.copyProperties(source, roleVO);
        return roleVO;
    }

    public static Role convert(RoleVO source) {
        Role role = new Role();
        BeanUtils.copyProperties(source, role);
        return role;
    }
}
