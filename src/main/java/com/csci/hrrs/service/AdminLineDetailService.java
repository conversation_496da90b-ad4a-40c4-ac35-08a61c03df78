package com.csci.hrrs.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.mapper.AdminLineDetailMapper;
import com.csci.hrrs.model.AdminLineDetail;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@LogMethod
public class AdminLineDetailService extends ServiceImpl<AdminLineDetailMapper, AdminLineDetail> implements IAdminLineDetailBase {

    @Resource
    private AdminLineDetailMapper adminLineDetailMapper;

    /**
     * 代理了 AdminLineDetailMapper.deleteByPrimaryKey() 方法
     *
     * @param record 行政线索明细
     * @return 影响行数
     */
    public int updateByPrimaryKeySelective(AdminLineDetail record) {
        return adminLineDetailMapper.updateById(record);
    }

}
