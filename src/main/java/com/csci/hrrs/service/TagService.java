package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.TagMapper;
import com.csci.hrrs.model.Tag;
import com.csci.hrrs.vo.TagNodeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class TagService extends ServiceImpl<TagMapper, Tag> {

    public TagNodeVO getRootTag() {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getIsDeleted, false).isNull(Tag::getParentId);
        Tag tag = getOne(queryWrapper);
        if (tag == null) {
            throw new ServiceException("未找到根标签");
        }
        TagNodeVO tagNodeVO = new TagNodeVO();
        tagNodeVO.setId(tag.getId());
        tagNodeVO.setCode(tag.getCode());
        tagNodeVO.setName(tag.getName());
        tagNodeVO.setParentId(tag.getParentId());
        return tagNodeVO;
    }

    public List<TagNodeVO> listByParentId(String parentId) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getIsDeleted, false).eq(Tag::getParentId, parentId);
        List<Tag> lstTag = list(queryWrapper);
        return lstTag.stream().map(tag -> {
            TagNodeVO tagNodeVO = new TagNodeVO();
            tagNodeVO.setId(tag.getId());
            tagNodeVO.setCode(tag.getCode());
            tagNodeVO.setName(tag.getName());
            tagNodeVO.setParentId(tag.getParentId());
            if (StringUtils.isBlank(tag.getParentId()) || StringUtils.equalsIgnoreCase(tag.getParentId(), getRootTag().getId())) {
                tagNodeVO.setType("dir");
            }
            return tagNodeVO;
        }).collect(Collectors.toList());
    }

    public TagNodeVO getTagTree() {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getIsDeleted, Boolean.FALSE);
        List<Tag> lstTag = list(queryWrapper);

        // find root tag
        List<Tag> lstRoots = lstTag.stream().filter(tag -> StringUtils.isBlank(tag.getParentId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstRoots)) {
            throw new ServiceException("未找到根标签");
        }
        if (lstRoots.size() > 1) {
            throw new ServiceException("数据错误，找到多个根标签");
        }
        Tag rootTag = lstRoots.get(0);

        TagNodeVO rootTagNode = new TagNodeVO();
        rootTagNode.setId(rootTag.getId());
        rootTagNode.setCode(rootTag.getCode());
        rootTagNode.setName(rootTag.getName());
        rootTagNode.setParentId(rootTag.getParentId());
        rootTagNode.setType("dir");

        buildTree(rootTagNode, lstTag);

        return rootTagNode;
    }

    private void buildTree(TagNodeVO parent, List<Tag> tagList) {
        Objects.requireNonNull(parent);
        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }
        List<Tag> children = tagList.stream().filter(tag -> StringUtils.equals(tag.getParentId(), parent.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (Tag child : children) {
            TagNodeVO childNode = new TagNodeVO();
            childNode.setId(child.getId());
            childNode.setCode(child.getCode());
            childNode.setName(child.getName());
            childNode.setParentId(child.getParentId());
            parent.getChildren().add(childNode);
            buildTree(childNode, tagList);
        }
    }
}
