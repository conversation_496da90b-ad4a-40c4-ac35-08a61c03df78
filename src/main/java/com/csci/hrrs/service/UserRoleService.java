package com.csci.hrrs.service;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.UserMapper;
import com.csci.hrrs.mapper.UserRoleMapper;
import com.csci.hrrs.model.Role;
import com.csci.hrrs.model.User;
import com.csci.hrrs.model.UserRole;
import com.csci.hrrs.model.UserRoleExample;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.vo.UserRoleVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.csci.hrrs.service.ServiceHelper.checkExist;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class UserRoleService {

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private UserMapper userMapper;

    public List<UserRole> selectByExample(UserRoleExample example) {
        return userRoleMapper.selectByExample(example);
    }

    /**
     * 用戶角色關係 数据列表
     *
     * @param
     * @return
     */
    public List<UserRoleVO> listUserRoleByUsername(String username) {
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        if (user == null) throw new ServiceException("找不到用戶");

        return ServiceHelper.listUserRole(user.getId(), userRoleMapper).stream().map(UserRoleVO::fromModel).toList();
    }
    
    

	
    /**
     * 用戶角色關係 数据列表
     *
     * @param userId
     * @return
     */
    public List<UserRole> listUserRole(String userId) {
        return ServiceHelper.listUserRole(userId, userRoleMapper);
    }

    /**
     * 保存 用戶角色關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userRoleLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<UserRoleVO> saveUserRoleList(List<UserRoleVO> userRoleLst) {
        List<UserRoleVO> idLst = new ArrayList<>();
        for (UserRoleVO userRoleVO : userRoleLst) {
            idLst.add(this.saveUserRole(userRoleVO));
        }
        return idLst;
    }

    /**
     * 保存 用戶角色關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userRoleVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UserRoleVO saveUserRole(UserRoleVO userRoleVO) {

        UserRole record = userRoleVO.toModel();

        if (StringUtils.isBlank(record.getId())) {
            record.setDeleted(false);
            userRoleMapper.insertSelective(record);
        } else {
            userRoleMapper.updateByPrimaryKeySelective(record);
        }
        return UserRoleVO.fromModel(record);
    }

    /**
     * 逻辑删除
     *
     * @param id 用戶角色關係 id
     * @return 删除的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserRole(String id) {
        UserRole record = new UserRole();
        record.setId(id);
        record.setDeleted(true);
        return userRoleMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 刪除 用戶角色關係 信息
     *
     * @param username
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserRoleByUsername(String username) {
        int result = 0;
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        if (user == null) {
            return 0;
        }
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(user.getId());
        userRoleMapper.deleteByExample(example);
        return result;
    }

    /**
     * 判断指定用户是否有指定角色
     *
     * @param username
     * @param roleCode 角色编码
     * @return
     */
    public boolean hasRole(String username, String roleCode) {
        checkExist(username, "username不能为空");
        checkExist(roleCode, "roleCode不能为空");
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        checkExist(user, "找不到用户");
        Role role = ServiceHelper.getRoleByCode(roleCode);
        checkExist(role, "角色不存在");
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(user.getId()).andRoleIdEqualTo(role.getId());
        return userRoleMapper.countByExample(example) > 0;
    }

    /**
     * 获取指定用户的角色id列表
     *
     * @param userId 用户id
     * @return 用户的角色id列表
     */
    public static List<String> listRoleIdByUserId(String userId) {
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(userId).andDeletedEqualTo(false);
        UserRoleMapper userRoleMapper = SpringContextHolder.getBean(UserRoleMapper.class);
        List<UserRole> userRoleList = userRoleMapper.selectByExample(example);
        return userRoleList.stream().map(UserRole::getRoleId).toList();
    }
}
