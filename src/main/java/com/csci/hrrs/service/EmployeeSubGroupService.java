package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.EmployeeSubGroupMapper;
import com.csci.hrrs.mapper.SampleCustomMapper;
import com.csci.hrrs.model.EmployeeSubGroup;
import com.csci.hrrs.vo.EmployeeSubGroupVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class EmployeeSubGroupService extends ServiceImpl<EmployeeSubGroupMapper, EmployeeSubGroup> {

    @Resource
    private EmployeeSubGroupMapper employeeSubGroupMapper;

    @Resource
    private SampleCustomMapper sampleCustomMapper;

    /**
     * 根据员工子组编码判断记录是否存在
     *
     * @param code 员工子组编码
     * @return
     */
    public boolean isExistByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        /* EmployeeSubGroupExample example = new EmployeeSubGroupExample();
        example.createCriteria().andIsDeletedEqualTo(Boolean.FALSE).andCodeEqualTo(code); */
        LambdaQueryWrapper<EmployeeSubGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeSubGroup::getIsDeleted, Boolean.FALSE).eq(EmployeeSubGroup::getCode, code);
        // return employeeSubGroupMapper.countByExample(example) > 0;
        return count(queryWrapper) > 0;
    }

    /**
     * @return 查询所有的员工子组
     */
    public List<EmployeeSubGroupVO> selectAll() {
        // return employeeSubGroupMapper.selectByExample(new EmployeeSubGroupExample()).stream().map(EmployeeSubGroupVO::fromModel).toList();
        return list().stream().map(EmployeeSubGroupVO::fromModel).toList();
    }

    /**
     * @return 查询所有的员工子组
     */
    public List<Map<String, String>> sourceEmployeeSubGroups() {
        return sampleCustomMapper.selectEmployeeSubGroups();
    }

    /**
     * 查询所有的员工子组
     *
     * @return 查询所有的员工子组
     */
    public List<EmployeeSubGroupVO> listEmployeeSubGroup() {
        // EmployeeSubGroupExample example = new EmployeeSubGroupExample();
        // example.createCriteria().andIsDeletedEqualTo(Boolean.FALSE);
        // example.setOrderByClause("code asc");
        // return employeeSubGroupMapper.selectByExample(example).stream().map(EmployeeSubGroupVO::fromModel).toList();
        LambdaQueryWrapper<EmployeeSubGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeSubGroup::getIsDeleted, Boolean.FALSE).orderByAsc(EmployeeSubGroup::getCode);
        return list(queryWrapper).stream().map(EmployeeSubGroupVO::fromModel).toList();
    }

    public EmployeeSubGroup findByCode(String code) {
        // EmployeeSubGroupExample example = new EmployeeSubGroupExample();
        // example.createCriteria().andIsDeletedEqualTo(Boolean.FALSE).andCodeEqualTo(code);
        // return employeeSubGroupMapper.selectByExample(example).stream().findFirst().orElse(null);
        LambdaQueryWrapper<EmployeeSubGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeSubGroup::getIsDeleted, Boolean.FALSE).eq(EmployeeSubGroup::getCode, code);
        return getOne(queryWrapper, false);
    }
}
