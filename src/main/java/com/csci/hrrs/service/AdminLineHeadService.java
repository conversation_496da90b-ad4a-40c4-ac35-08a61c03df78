package com.csci.hrrs.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.constant.SortDefCodeConsts;
import com.csci.hrrs.converter.AdminLineDetailConverter;
import com.csci.hrrs.converter.AdminLineHeadConverter;
import com.csci.hrrs.mapper.AdminLineDetailMapper;
import com.csci.hrrs.mapper.AdminLineHeadMapper;
import com.csci.hrrs.model.AdminLineDetail;
import com.csci.hrrs.model.AdminLineHead;
import com.csci.hrrs.model.RptSpecialLine;
import com.csci.hrrs.model.SortDef;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.vo.AdminLineDetailVO;
import com.csci.hrrs.vo.AdminLineHeadVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.csci.hrrs.service.ServiceHelper.checkExist;

@Service
@LogMethod
@Slf4j
@DS(DatasourceContextEnum.HRRS)
public class AdminLineHeadService extends ServiceImpl<AdminLineHeadMapper, AdminLineHead> implements IAdminLineDetailBase {

    @Resource
    private AdminLineHeadMapper adminLineHeadMapper;

    @Resource
    private AdminLineDetailMapper adminLineDetailMapper;


    /**
     * 更新人力行政线基本情况
     *
     * @param adminLineHeadVO 行政线信息
     * @return 更新完成的行政线信息
     */
    public AdminLineHeadVO updateWithDetail(AdminLineHeadVO adminLineHeadVO) {
        checkExist(adminLineHeadVO, "行政线信息不能为空");
        checkExist(adminLineHeadVO.getId(), "行政线id不能为空");
        // checkExist(adminLineHeadVO.getOrganizationId(), "组织机构id不能为空");
        checkExist(adminLineHeadVO.getStatDate(), "统计日期不能为空");
        checkExist(adminLineHeadVO.getDetails(), "行政线明细不能为空");

        // update head
        AdminLineHeadVO resultVO = updateHead(adminLineHeadVO);

        // update detail
        List<AdminLineDetailVO> lstDetailVO = updateDetails(adminLineHeadVO);
        resultVO.setDetails(lstDetailVO);

        return resultVO;
    }

    private AdminLineHeadVO updateHead(AdminLineHeadVO adminLineHeadVO) {
        AdminLineHead updateRecord = AdminLineHeadConverter.convert(adminLineHeadVO);
        LambdaUpdateWrapper<AdminLineHead> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AdminLineHead::getId, adminLineHeadVO.getId()).eq(AdminLineHead::getLastUpdateVersion, adminLineHeadVO.getLastUpdateVersion())
                .eq(AdminLineHead::getIsDeleted, false);

        int updateCount = adminLineHeadMapper.update(updateRecord, updateWrapper);
        if (updateCount != 1) {
            throw new ServiceException("更新行政线头部失败");
        }
        return AdminLineHeadConverter.convert(updateRecord);
    }

    /**
     * 更新人力行政线明细数据
     * 1. 删除旧数据
     * 2. 添加新数据
     *
     * @param adminLineHeadVO 行政线头部信息
     * @return
     */
    private List<AdminLineDetailVO> updateDetails(AdminLineHeadVO adminLineHeadVO) {
        // delete old record
        // AdminLineDetailExample deleteExample = new AdminLineDetailExample();
        // deleteExample.or().andDeletedEqualTo(false).andHeadIdEqualTo(adminLineHeadVO.getId());
        LambdaUpdateWrapper<AdminLineDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AdminLineDetail::getIsDeleted, false).eq(AdminLineDetail::getHeadId, adminLineHeadVO.getId());

        AdminLineDetail deleteRecord = new AdminLineDetail();
        deleteRecord.setIsDeleted(true);
        deleteRecord.setDeleteVersion(adminLineHeadVO.getLastUpdateVersion());
        adminLineDetailMapper.update(deleteRecord, updateWrapper);

        List<AdminLineDetailVO> lstDetailVO = new ArrayList<>();
        // add new record
        for (AdminLineDetailVO detailVO : adminLineHeadVO.getDetails()) {
            AdminLineDetail detailRecord = AdminLineDetailConverter.convert(detailVO);
            detailRecord.setId(null);
            detailRecord.setHeadId(adminLineHeadVO.getId());
            adminLineDetailMapper.insert(detailRecord);
            lstDetailVO.add(AdminLineDetailConverter.convert(detailRecord));
        }

        // return
        return lstDetailVO;
    }

    /**
     * 初始化行政线明细
     *
     * @param headId     头部id
     * @param configId   配置id
     * @param configName 配置名称
     * @param seq        序号
     * @return 行政线明细
     */
    private AdminLineDetailVO initDetail(String headId, String configId, String configName, Integer seq) {
        AdminLineDetail adminLineDetail = new AdminLineDetail();
        // adminLineDetail.setId("");
        adminLineDetail.setHeadId(headId);
        adminLineDetail.setConfigId(configId);
        adminLineDetail.setConfigName(configName);
        adminLineDetail.setHrHeadCount(0);
        adminLineDetail.setHrTwoLevelAbv(0);
        adminLineDetail.setHrSeniorMgr(0);
        adminLineDetail.setHrManager(0);
        adminLineDetail.setHrDeputyMgr(0);
        adminLineDetail.setHrAssisMgr(0);
        adminLineDetail.setHrOther(0);
        adminLineDetail.setAdminHeadCount(0);
        adminLineDetail.setAdminTwoLevelAbv(0);
        adminLineDetail.setAdminSeniorMgr(0);
        adminLineDetail.setAdminManager(0);
        adminLineDetail.setAdminDeputyMgr(0);
        adminLineDetail.setAdminAssisMgr(0);
        adminLineDetail.setAdminOther(0);
        adminLineDetail.setAdminOffice(0);
        adminLineDetail.setSeq(seq);
        adminLineDetail.setIsDeleted(false);
        adminLineDetail.setDeleteVersion(0);
        adminLineDetailMapper.insert(adminLineDetail);
        return AdminLineDetailConverter.convert(adminLineDetail);
    }

    /**
     * 根据组织机构id和统计日期查询行政线索头部信息
     *
     * @param organizationCode
     * @param statDate       统计日期
     * @return 行政线索头部信息
     */
    public AdminLineHeadVO findBy(String organizationCode, LocalDate statDate) {
        checkExist(organizationCode, "组织机构编码不能为空");
        checkExist(statDate, "统计日期不能为空");

        // AdminLineHeadExample example = new AdminLineHeadExample();
        // example.or().andDeletedEqualTo(false).andOrganizationIdEqualTo(organizationId).andStatDateEqualTo(statDate);
        LambdaQueryWrapper<AdminLineHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminLineHead::getIsDeleted, false).eq(AdminLineHead::getOrganizationCode, organizationCode).eq(AdminLineHead::getStatDate, statDate);

        AdminLineHeadVO adminLineHeadVO = Optional.ofNullable(getOne(queryWrapper, false)).map(AdminLineHeadConverter::convert).orElse(null);

        if (Objects.nonNull(adminLineHeadVO)) {
            List<AdminLineDetailVO> lstDetailVO = listAdminLineByHeadId(adminLineHeadVO.getId());
            adminLineHeadVO.setDetails(lstDetailVO);
        }

        return adminLineHeadVO;
    }

    /**
     * 根据行政线id查询行政线信息
     * 如果id为空，返回null
     * 未找到返回null
     *
     * @param id 行政线id
     * @return 行政线信息
     */
    public AdminLineHeadVO findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        AdminLineHead adminLineHead = getById(id);
        if (adminLineHead == null) {
            return null;
        }
        AdminLineHeadVO adminLineHeadVO = AdminLineHeadConverter.convert(adminLineHead);
        adminLineHeadVO.setDetails(listAdminLineByHeadId(id));
        return adminLineHeadVO;
    }

    /**
     * 根据行政线id删除行政线信息
     *
     * @param id                行政线id
     * @param lastUpdateVersion 版本号
     */
    public void deleteById(String id, Integer lastUpdateVersion) {
        checkExist(id, "行政线id不能为空");
        checkExist(lastUpdateVersion, "版本号不能为空");

        // AdminLineHeadExample example = new AdminLineHeadExample();
        // AdminLineHeadExample.Criteria criteria = example.createCriteria().andDeletedEqualTo(false).andIdEqualTo(id);
        LambdaQueryWrapper<AdminLineHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminLineHead::getIsDeleted, false).eq(AdminLineHead::getId, id);
        AdminLineHead existedRecord = getOne(queryWrapper, false);
        if (Objects.isNull(existedRecord)) {
            log.warn("执行删除操作时，未找到行政线信息，id={}", id);
            return;
        }

        // criteria.andLastUpdateVersionEqualTo(lastUpdateVersion);
        queryWrapper.eq(AdminLineHead::getLastUpdateVersion, lastUpdateVersion);
        AdminLineHead record = new AdminLineHead();
        record.setIsDeleted(true);
        record.setLastUpdateVersion(lastUpdateVersion + 1);
        int updateCount = adminLineHeadMapper.update(record, queryWrapper);
        if (updateCount == 0) {
            throw new ServiceException("删除失败，行政线信息已被修改，请刷新后重试");
        }
    }
}
