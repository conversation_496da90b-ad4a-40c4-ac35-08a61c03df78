package com.csci.hrrs.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.UserSubGroupMapper;
import com.csci.hrrs.model.UserSubGroup;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class UserSubGroupService extends ServiceImpl<UserSubGroupMapper, UserSubGroup> {

    @Resource
    private UserSubGroupMapper userSubGroupMapper;

    public List<String> listUserSubGroupCodesByUserId(String userId) {
        return userSubGroupMapper.listUserSubGroupCodesByUserId(userId);
    }
}
