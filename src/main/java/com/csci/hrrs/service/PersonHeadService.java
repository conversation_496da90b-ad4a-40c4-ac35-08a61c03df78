package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.mapper.PersonHeadMapper;
import com.csci.hrrs.model.PersonHead;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.vo.PersonHeadVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

import static com.csci.hrrs.service.ServiceHelper.checkExist;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class PersonHeadService extends ServiceImpl<PersonHeadMapper, PersonHead> implements IPersonHeadBase {


    public PersonHead findById(String id) {
        checkExist(id, "id不能爲空");
        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getId, id);
        return getOne(queryWrapper);
    }

    /**
     * 查询指定日期之前最近一次提交的人员头表信息
     *
     * @param statDate 统计日期
     * @return 人员头表信息
     */
    public PersonHead findLastSubmittedHead(LocalDate statDate) {
        // PersonHeadExample example = new PersonHeadExample();
        // example.createCriteria().andIsDeletedEqualTo(Boolean.FALSE).andStatDateLessThanOrEqualTo(statDate).andStatusEqualTo(HrrsConsts.BillStatus.SUBMITTED);
        // example.setOrderByClause("stat_date desc");
        // PageHelper.startPage(1, 1);
        // return personHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).le(PersonHead::getStatDate, statDate).eq(PersonHead::getStatus, HrrsConsts.BillStatus.SUBMITTED);
        queryWrapper.orderByDesc(PersonHead::getStatDate);
        return getOne(queryWrapper, false);
    }

    public boolean existsById(String id) {
        checkExist(id, "id不能爲空");
        // PersonHeadExample example = new PersonHeadExample();
        // example.createCriteria().andIsDeletedEqualTo(Boolean.FALSE).andIdEqualTo(id);
        // return personHeadMapper.countByExample(example) > 0;
        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getId, id);
        return exists(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public String createPersonHead(String organizationCode, LocalDate statDate) {
        checkExist(organizationCode, "organizationId不能爲空");
        checkExist(statDate, "statDate不能爲空");

        String lock = "lock:" + organizationCode + ":" + statDate.toString();

        if (RedisLockUtil.lock(lock)) {
            try {
                return doCreate(organizationCode, statDate);
            } finally {
                RedisLockUtil.unlock(lock);
            }
        } else {
            throw new ServiceException("创建人员头表信息失败, 请稍后重试");
        }

    }

    /**
     * 创建人员头表信息, 实际的创建逻辑写在这里
     *
     * @param organizationCode
     * @param statDate         统计日期
     * @return 人员头表id
     */
    private String doCreate(String organizationCode, LocalDate statDate) {
        LocalDate firstDayOfMonth = statDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = statDate.withDayOfMonth(statDate.lengthOfMonth());
        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getOrganizationCode, organizationCode)
                .ge(PersonHead::getStatDate, firstDayOfMonth).le(PersonHead::getStatDate, lastDayOfMonth);
        if (exists(queryWrapper)) {
            throw new ServiceException("该组织已存在该月份的人员头表信息");
        }

        PersonHead record = new PersonHead();
        record.setStatDate(statDate);
        record.setOrganizationCode(organizationCode);
        record.setStatus(HrrsConsts.BillStatus.DRAFT);

        save(record);
        return record.getId();
    }

    /**
     * 根据组织id和年月获取人员头表信息
     *
     * @param organizationId 组织id
     * @param statDate       统计日期
     * @return PersonHeadVO
     */
    public PersonHeadVO findByOrgIdAndStatDate(String organizationId, LocalDate statDate) {
        checkExist(organizationId, "organizationId不能爲空");
        checkExist(statDate, "statDate不能爲空");

        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getOrganizationId, organizationId).eq(PersonHead::getStatDate, statDate);
        PersonHead personHead = getOne(queryWrapper, Boolean.FALSE);
        return PersonHeadVO.fromModel(personHead);
    }

    public PersonHead findByOrgCodeAndStatDate(String orgCode, LocalDate statDate) {
        checkExist(orgCode, "orgCode不能爲空");
        checkExist(statDate, "statDate不能爲空");

        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getOrganizationCode, orgCode).eq(PersonHead::getStatDate, statDate);
        return getOne(queryWrapper, Boolean.FALSE);
    }

    /**
     * 查询指定组织指定日期之前最近一次提交的人员头表信息
     *
     * @param orgCode  组织编码
     * @param statDate 统计日期
     * @return 人员头表信息
     */
    public PersonHead findLatestByOrgCodeAndStatDate(String orgCode, LocalDate statDate) {
        checkExist(orgCode, "orgCode不能爲空");
        checkExist(statDate, "statDate不能爲空");

        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getOrganizationCode, orgCode).le(PersonHead::getStatDate, statDate);
        queryWrapper.orderByDesc(PersonHead::getStatDate);
        return getOne(queryWrapper, Boolean.FALSE);
    }

    public PersonHead findInMontyByOrgCodeAndStatDate(String orgCode, LocalDate statDate) {
        checkExist(orgCode, "orgCode不能爲空");
        checkExist(statDate, "statDate不能爲空");

        LocalDate firstDayOfMonth = statDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = statDate.withDayOfMonth(statDate.lengthOfMonth());
        LambdaQueryWrapper<PersonHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonHead::getIsDeleted, Boolean.FALSE).eq(PersonHead::getOrganizationCode, orgCode).ge(PersonHead::getStatDate, firstDayOfMonth).le(PersonHead::getStatDate, lastDayOfMonth);
        queryWrapper.orderByDesc(PersonHead::getStatDate);
        return getOne(queryWrapper, Boolean.FALSE);
    }

}
