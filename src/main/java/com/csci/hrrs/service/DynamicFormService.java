package com.csci.hrrs.service;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.DynamicFormDetailMapper;
import com.csci.hrrs.mapper.DynamicFormHeadMapper;
import com.csci.hrrs.model.AuditNode;
import com.csci.hrrs.model.DynamicFormDetail;
import com.csci.hrrs.model.DynamicFormHead;
import com.csci.hrrs.model.DynamicFormDetailExample;
import com.csci.hrrs.model.DynamicFormHeadExample;
import com.csci.hrrs.model.DynamicFormHeadExample.Criteria;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.model.User;
import com.csci.hrrs.qo.DynamicFormPageableQO;
import com.csci.hrrs.qo.DynamicFormQO;
import com.csci.hrrs.util.IntegerUtils;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;
import com.csci.hrrs.vo.DynamicFormDetailVO;
import com.csci.hrrs.vo.DynamicFormVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class DynamicFormService {

    @Autowired
    private AuditNodeService auditNodeService;

    @Autowired
    private UserService userService;

    @Autowired
    private DynamicFormHeadMapper headMapper;

    @Autowired
    private DynamicFormDetailMapper detailMapper;

    public List<DynamicFormHead> selectByExample(DynamicFormHeadExample example) {
        return headMapper.selectByExample(example);
    }

    public List<DynamicFormDetail> selectByExample(DynamicFormDetailExample example) {
        return detailMapper.selectByExample(example);
    }

    /**
     * 動態表單 数据列表
     *
     * @param dynamicFormQO
     * @return
     */
    public ResultPage<DynamicFormVO> listDynamicForm(DynamicFormPageableQO dynamicFormQO) {
        // 查询头行信息
        DynamicFormHeadExample headExample = new DynamicFormHeadExample();
        
        Criteria criteria = headExample.or();
        if(StringUtils.isNotBlank(dynamicFormQO.getOrganizationId())) {
        	criteria.andOrganizationIdEqualTo(dynamicFormQO.getOrganizationId());
        }
        if(IntegerUtils.isNotNullOrZero(dynamicFormQO.getYear())) {
        	criteria.andReportingYearEqualTo(dynamicFormQO.getYear());
        }
        if(IntegerUtils.isNotNullOrZero(dynamicFormQO.getMonth())) {
        	criteria.andReportingMonthEqualTo(dynamicFormQO.getMonth());
        }
        if(StringUtils.isNotBlank(dynamicFormQO.getFormCode())) {
        	criteria.andFormCodeEqualTo(dynamicFormQO.getFormCode());
        }
        criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");
        
        PageHelper.startPage(dynamicFormQO.getCurPage(), dynamicFormQO.getPageSize());
        
        List<DynamicFormHead> lstHead = headMapper.selectByExample(headExample);

        Page<DynamicFormVO> lstResultVO = new Page<>();
        for(DynamicFormHead head: lstHead) {
            DynamicFormVO resultVO = new DynamicFormVO();
            BeanUtils.copyProperties(head, resultVO);
	        
	        AuditNode auditNode = auditNodeService.getAuditNode(head.getId(), head.getFormCode(), head.getAuditNode());
	        User user = userService.getUser(auditNode == null ? "" : auditNode.getUserId());
	        resultVO.setCurrentAuditorUsername(user == null ? "" : user.getUsername());

            // 根据headId查询出对应的明细行，detail表
            DynamicFormDetailExample detailExample = new DynamicFormDetailExample();
            detailExample.or().andHeadIdEqualTo(head.getId());

            List<DynamicFormDetail> lstDetail = detailMapper.selectByExample(detailExample);
            List<DynamicFormDetailVO> lstDetailVO = lstDetail.stream().map(x -> {
                DynamicFormDetailVO vo = new DynamicFormDetailVO();
                BeanUtils.copyProperties(x, vo);
                return vo;
            }).collect(Collectors.toList());
            
            resultVO.setLstDetail(lstDetailVO);
            lstResultVO.add(resultVO);
        }
        ResultPage<DynamicFormVO> resultPage = new ResultPage<>(lstHead);
        resultPage.setList(lstResultVO);
        
        return resultPage;
    }
    
    /**
     * 動態表單 数据
     *
     * @param dynamicFormQO
     * @return
     */
    public DynamicFormVO getDynamicForm(DynamicFormQO dynamicFormQO) {
        // 查询头行信息
        DynamicFormHeadExample headExample = new DynamicFormHeadExample();
        
        Criteria criteria = headExample.or();
        if(StringUtils.isNotBlank(dynamicFormQO.getOrganizationId())) {
        	criteria.andOrganizationIdEqualTo(dynamicFormQO.getOrganizationId());
        }
        if(IntegerUtils.isNotNullOrZero(dynamicFormQO.getYear())) {
        	criteria.andReportingYearEqualTo(dynamicFormQO.getYear());
        }
        if(IntegerUtils.isNotNullOrZero(dynamicFormQO.getMonth())) {
        	criteria.andReportingMonthEqualTo(dynamicFormQO.getMonth());
        }
        if(StringUtils.isNotBlank(dynamicFormQO.getFormCode())) {
        	criteria.andFormCodeEqualTo(dynamicFormQO.getFormCode());
        }
        criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");

        List<DynamicFormHead> lstHead = headMapper.selectByExample(headExample);

        if (lstHead.size() == 0)
        	return null;

        DynamicFormHead head = lstHead.get(0);
        DynamicFormVO resultVO = new DynamicFormVO();
        BeanUtils.copyProperties(head, resultVO);
        
        AuditNode auditNode = auditNodeService.getAuditNode(head.getId(), head.getFormCode(), head.getAuditNode());
        User user = userService.getUser(auditNode == null ? "" : auditNode.getUserId());
        resultVO.setCurrentAuditorUsername(user == null ? "" : user.getUsername());

        // 根据headId查询出对应的明细行，detail表
        DynamicFormDetailExample detailExample = new DynamicFormDetailExample();
        detailExample.or().andHeadIdEqualTo(head.getId());

        List<DynamicFormDetail> lstDetail = detailMapper.selectByExample(detailExample);
        List<DynamicFormDetailVO> lstDetailVO = lstDetail.stream().map(x -> {
            DynamicFormDetailVO vo = new DynamicFormDetailVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        resultVO.setLstDetail(lstDetailVO);
        return resultVO;
    }

    /**
     * 驗證 動態表單 VO 信息, 如有錯誤返回相關資訊
     *
     * @param dynamicFormVO
     * @return
     */
    public String checkDynamicForm(DynamicFormVO dynamicFormVO) {
    	DynamicFormPageableQO dynamicFormQO = new DynamicFormPageableQO();
    	dynamicFormQO.setOrganizationId(dynamicFormVO.getOrganizationId());
    	dynamicFormQO.setFormCode(dynamicFormVO.getFormCode());
    	dynamicFormQO.setYear(dynamicFormVO.getReportingYear());
    	dynamicFormQO.setMonth(dynamicFormVO.getReportingMonth());
    	dynamicFormQO.setCurPage(0);
    	dynamicFormQO.setPageSize(0);
    	ResultPage<DynamicFormVO> resultPage = this.listDynamicForm(dynamicFormQO);
    	
    	if(resultPage.getSize() > 0) {
            return "該月份已提交記錄，不能重覆提交。";
    	}
    	else {
    		return "";
    	}
    }

    /**
     * 提交 動態表單 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param dynamicFormVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String submitDynamicForm(DynamicFormVO dynamicFormVO) {
    	String id;
    	String validMsg = checkDynamicForm(dynamicFormVO);
    	if(StringUtils.isNotBlank(validMsg)) {
    		throw new ServiceException(validMsg);
    	}
    	dynamicFormVO.setIsSubmitted(true);
		dynamicFormVO.setIsAudited(false);
		dynamicFormVO.setAuditNode(0);
		dynamicFormVO.setIsDeleted(false);
    	id = saveDynamicForm(dynamicFormVO);
    	auditNodeService.generateAuditNodes(dynamicFormVO.getOrganizationId(), dynamicFormVO.getFormCode(), id);
    	return id;
    }

    /**
     * 保存 動態表單 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param dynamicFormVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveDynamicFormWithStatus(DynamicFormVO dynamicFormVO) {
    	dynamicFormVO.setIsSubmitted(false);
		dynamicFormVO.setIsAudited(false);
		dynamicFormVO.setAuditNode(0);
		dynamicFormVO.setIsDeleted(false);
		String id = saveDynamicForm(dynamicFormVO);
    	auditNodeService.generateAuditNodes(dynamicFormVO.getOrganizationId(), dynamicFormVO.getFormCode(), id);
    	return id;
    }
    
    /**
     * 保存 動態表單 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param dynamicFormVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveDynamicForm(DynamicFormVO dynamicFormVO) {
        DynamicFormHead dynamicFormHead = new DynamicFormHead();
        BeanUtils.copyProperties(dynamicFormVO, dynamicFormHead);
        String headId = saveDynamicFormHead(dynamicFormHead);

        List<DynamicFormDetail> lstDetail = dynamicFormVO.getLstDetail().stream().map(vo -> {
        	DynamicFormDetail x = new DynamicFormDetail();
            BeanUtils.copyProperties(vo, x);
            x.setHeadId(headId);
            return x;
        }).collect(Collectors.toList());
        for(DynamicFormDetail dynamicFormDetail1: lstDetail) {
            saveDynamicFormDetail1(dynamicFormDetail1);
        }
        
        return headId;
    }
    
    /**
     * 保存 動態表單 头行信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param dynamicFormHead
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveDynamicFormHead(DynamicFormHead dynamicFormHead) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(dynamicFormHead.getId())) {
        	dynamicFormHead.setCreationTime(now);
        	dynamicFormHead.setCreateUsername(currentUser.getUsername());
        	dynamicFormHead.setLastUpdateTime(now);
        	dynamicFormHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.insertSelective(dynamicFormHead);
        } else {
        	dynamicFormHead.setLastUpdateTime(now);
        	dynamicFormHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.updateByPrimaryKeySelective(dynamicFormHead);
        }
        return dynamicFormHead.getId();
    }

    /**
     * 保存 動態表單 明细行信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param dynamicFormdetail
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveDynamicFormDetail1(DynamicFormDetail dynamicFormdetail) {
        if (StringUtils.isBlank(dynamicFormdetail.getId())) {
        	detailMapper.insertSelective(dynamicFormdetail);
        } else {
        	detailMapper.updateByPrimaryKeySelective(dynamicFormdetail);
        }
        return dynamicFormdetail.getId();
    }
    
}
