package com.csci.hrrs.service;

import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.FamilySocialRelationMapper;
import com.csci.hrrs.model.FamilySocialRelation;
import com.csci.hrrs.model.FamilySocialRelationExample;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class FamilySocialRelationService {

    @Resource
    private FamilySocialRelationMapper familySocialRelationMapper;

    /**
     * this method is delegated to FamilySocialRelationMapper.insertSelective
     *
     * @param record 待插入的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FamilySocialRelation record) {
        return familySocialRelationMapper.insertSelective(record);
    }

    /**
     * this method is delegated to FamilySocialRelationMapper.updateByPrimaryKeySelective
     *
     * @param record 待更新的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(FamilySocialRelation record) {
        return familySocialRelationMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * this method is delegated to FamilySocialRelationMapper.selectByExample
     *
     * @param example 查询条件
     * @return List<FamilySocialRelation>
     */
    public List<FamilySocialRelation> selectByExample(FamilySocialRelationExample example) {
        return familySocialRelationMapper.selectByExample(example);
    }

}
