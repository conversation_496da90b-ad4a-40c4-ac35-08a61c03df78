package com.csci.hrrs.service;

import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.KpiMapper;
import com.csci.hrrs.model.Kpi;
import com.csci.hrrs.model.KpiExample;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class KpiService {

    @Resource
    private KpiMapper kpiMapper;

    /**
     * this method is delegated to KpiMapper.insertSelective
     *
     * @param record 待插入的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(Kpi record) {
        return kpiMapper.insertSelective(record);
    }

    /**
     * this method is delegated to KpiMapper.updateByPrimaryKeySelective
     *
     * @param record 待更新的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(Kpi record) {
        return kpiMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * this method is delegated to KpiMapper.selectByExample
     *
     * @param example 查询条件
     * @return List<Kpi>
     */
    public List<Kpi> selectByExample(KpiExample example) {
        return kpiMapper.selectByExample(example);
    }

}
