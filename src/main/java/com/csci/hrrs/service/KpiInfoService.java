package com.csci.hrrs.service;

import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.KpiInfoMapper;
import com.csci.hrrs.model.KpiInfo;
import com.csci.hrrs.model.KpiInfoExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HR_DW)
public class KpiInfoService {

    @Resource
    private KpiInfoMapper kpiInfoMapper;

    /**
     * 根据条件查询KPI信息, 代理了KpiInfoMapper.selectByExample方法
     *
     * @param example 查询条件
     * @return KPI信息列表
     */
    public List<KpiInfo> selectByExample(KpiInfoExample example) {
        return kpiInfoMapper.selectByExample(example);
    }

    public long countByExample(KpiInfoExample example) {
        return kpiInfoMapper.countByExample(example);
    }

}
