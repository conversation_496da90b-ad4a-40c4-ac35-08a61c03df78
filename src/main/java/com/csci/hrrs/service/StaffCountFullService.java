package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.StaffCountFullMapper;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.vo.CodeNameCountVO;
import com.csci.hrrs.vo.NameCountVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class StaffCountFullService extends ServiceImpl<StaffCountFullMapper, StaffCountFull> {

    @Autowired
    private StaffCountFullMapper staffCountFullMapper;

    public List<StaffCountFull> listByOrgCodes(List<String> orgCodes) {
        return lambdaQuery()
                .eq(StaffCountFull::getIsDeleted, false)
                //.isNotNull(StaffCountFull::getStartDate) 开工日期通过组织机构的start_date来定义
                .isNull(StaffCountFull::getImportPlanId)
                .in(CollectionUtils.isNotEmpty(orgCodes), StaffCountFull::getOrganizationCode, orgCodes).list();
    }

    public List<Map<String, String>> listByImportPlanIdAsMap(String importPlanId) {
        return staffCountFullMapper.selectByImportPlanIdAsMap(importPlanId);
    }

    public List<StaffCountFull> listByImportPlanId(String importPlanId) {
        return staffCountFullMapper.selectByImportPlanId(importPlanId);
    }

    public long selectManagerCount(List<String> orgCodeList) {
        QueryWrapper<StaffCountFull> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", false);
        queryWrapper.in(CollectionUtils.isNotEmpty(orgCodeList), "organization_code", orgCodeList);
        queryWrapper.select("count(distinct manager_username) as count");
        return listObjs(queryWrapper, o -> Long.parseLong(o.toString())).get(0);
    }

    public List<String> listStaffCountOrgCodes() {
        return lambdaQuery()
                .eq(StaffCountFull::getIsDeleted, false)
                .select(StaffCountFull::getOrganizationCode)
                .list().stream().map(StaffCountFull::getOrganizationCode).toList();
    }

    public List<StaffCountFull> listByOrgCodeAndPlans(String orgCodes, String importPlanId) {
        return lambdaQuery()
                .eq(StaffCountFull::getIsDeleted, false)
                .eq(StaffCountFull::getImportPlanId, importPlanId)
                .eq(StringUtils.isNotEmpty(orgCodes), StaffCountFull::getOrganizationCode, orgCodes).list();
    }

    public List<String> keepOnlyHasDataOrgList(List<String> orgList) {
        if (CollectionUtils.isEmpty(orgList)) {
            return orgList;
        }
        List<String> existOrgList = listStaffCountOrgCodes();
        return orgList.stream().filter(existOrgList::contains).collect(Collectors.toList());
    }

    public List<NameCountVO> selectOnGoingProjectCountBySubsidiary(List<String> orgCodeList, LocalDate statDate) {
        return baseMapper.selectOnGoingProjectCountBySubsidiary(orgCodeList, statDate);
    }

    public List<NameCountVO> selectManagerCountBySubsidiary(List<String> orgCodeList, LocalDate statDate) {
        return staffCountFullMapper.selectManagerCountBySubsidiary(orgCodeList, statDate);
    }

    public List<CodeNameCountVO> selectSubsidiaryManagerDistrByPosition(List<String> orgCodeList, LocalDate startDate, LocalDate endDate) {
        return staffCountFullMapper.selectSubsidiaryManagerDistrByPosition(orgCodeList, startDate, endDate);
    }

}
