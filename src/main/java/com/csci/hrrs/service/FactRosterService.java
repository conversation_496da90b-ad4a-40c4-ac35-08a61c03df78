package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.model.PageableVO;
import com.csci.common.model.ResultPage;
import com.csci.common.util.CommonUtils;
import com.csci.common.util.DateUtils;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.FactRosterMapper;
import com.csci.hrrs.model.FactRoster;
import com.csci.hrrs.vo.EmployeeVO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Service
@DS(DatasourceContextEnum.HR_DW)
@LogMethod
@Slf4j
public class FactRosterService extends ServiceImpl<FactRosterMapper, FactRoster> {

    public ResultPage<FactRoster> listByPage(PageableVO pageableVO) {
        return listByPage(pageableVO, getMaxRecoverDate());
    }

    public ResultPage<FactRoster> listByPage(PageableVO pageableVO, String recoveryDate) {
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactRoster::getIsonjob, 1);
        if (StringUtils.isNotBlank(recoveryDate)) {
            queryWrapper.eq(FactRoster::getRecoverydate, recoveryDate);
        }
        queryWrapper.orderByAsc(FactRoster::getSort);
        PageHelper.startPage(pageableVO.getCurPage(), pageableVO.getPageSize());
        List<FactRoster> lst = list(queryWrapper);
        return new ResultPage<>(lst, Function.identity());
    }


    /**
     * 查询离职员工
     *
     * @param pageableVO 分页参数
     * @return 离职员工
     */
    public ResultPage<FactRoster> listFormerEmployee(PageableVO pageableVO) {
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactRoster::getIsonjob, 0);
        String recoveryDate = getMaxRecoverDate();
        if (StringUtils.isNotBlank(recoveryDate)) {
            queryWrapper.eq(FactRoster::getRecoverydate, recoveryDate);
        }
        queryWrapper.orderByAsc(FactRoster::getSort);
        PageHelper.startPage(pageableVO.getCurPage(), pageableVO.getPageSize());
        List<FactRoster> lst = list(queryWrapper);
        return new ResultPage<>(lst, Function.identity());
    }

    public String getMaxRecoverDate() {
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(FactRoster::getRecoverydate);
        PageHelper.startPage(1, 1);
        return Optional.ofNullable(getOne(queryWrapper, Boolean.FALSE)).map(FactRoster::getRecoverydate).orElse(null);
    }

    /**
     * 查询指定日期所在的月份的最后一天的数据同步日期
     *
     * @param date 日期
     * @return
     */
    public String getRecoverydateInMonth(LocalDate date) {
        String firstDay = DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date.withDayOfMonth(1));
        String lastDay = DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date.withDayOfMonth(date.lengthOfMonth()));
        QueryWrapper<FactRoster> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(recoverydate) as recoverydate").between("recoverydate", firstDay, lastDay);
        return Optional.ofNullable(getOne(queryWrapper)).map(FactRoster::getRecoverydate).orElse(null);
    }

    public String getLatestRecoverydate(LocalDate date) {
        String lastDayOfMonth = DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date.withDayOfMonth(date.lengthOfMonth()));
        QueryWrapper<FactRoster> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(recoverydate) as recoverydate").lt("recoverydate", lastDayOfMonth);
        return Optional.ofNullable(getOne(queryWrapper, false)).map(FactRoster::getRecoverydate).orElse(null);
    }

    /**
     * 根据工号查询花名册
     *
     * @param employeeNo 工号
     * @return FactRoster
     */
    public FactRoster selectByEmployeeNo(String employeeNo) {
        /*FactRosterExample example = new FactRosterExample();
        example.or().andPernrEqualTo(employeeNo).andRecoverydateEqualTo(getMaxRecoverDate());
        example.setOrderByClause("recoverydate desc");
        PageHelper.startPage(1, 1);
        return factRosterMapper.selectByExample(example).stream().findFirst().orElse(null);*/
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactRoster::getPernr, employeeNo).eq(FactRoster::getRecoverydate, getMaxRecoverDate());
        return getOne(queryWrapper, Boolean.FALSE);
    }

    public LocalDate getMaxRecoverDateLocalDate() {
        String maxRecoverDate = getMaxRecoverDate();
        if (StringUtils.isNotBlank(maxRecoverDate)) {
            return DateUtils.toLocalDateTime(maxRecoverDate).toLocalDate();
        }
        return null;
    }

    /**
     * 判断是否存在花名册记录
     *
     * @return 是否有花名册记录
     */
    public boolean hasRecord() {
        /*FactRosterExample example = new FactRosterExample();
        example.or().andPernrIsNotNull();
        return factRosterMapper.countByExample(example) > 0;*/
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(FactRoster::getPernr);
        return exists(queryWrapper);
    }

    /**
     * 获取花名册中间库最大同步日期，判断是否是当日，如果是当日则同步到本地库，否则不同步
     *
     * @return 是否需要同步
     */
    public boolean isAllowToSync() {
        if (!hasRecord()) {
            log.info("花名册中间库没有数据，不需要同步");
            return false;
        }
        if (!LocalDate.now().isEqual(getMaxRecoverDateLocalDate())) {
            log.info("花名册中间库最大同步日期不是当日，不需要同步");
            return false;
        }
        return true;
    }

    public EmployeeVO getEmployeeByUsername(String username) {
        // 去掉域名
        String removeDomainUsername = CommonUtils.splitDomainUsername2Entry(username).getValue();

        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(FactRoster::getAdname, username).eq(FactRoster::getRecoverydate, getMaxRecoverDate());
        queryWrapper.select(FactRoster::getPernr, FactRoster::getName, FactRoster::getAdname, FactRoster::getEmail);
        List<FactRoster> lstFactRoster = list(queryWrapper);
        for (FactRoster factRoster : lstFactRoster) {
            // 去掉域名
            String adNameWithoutDomain = CommonUtils.splitDomainUsername2Entry(factRoster.getAdname()).getValue();
            if (StringUtils.equalsIgnoreCase(adNameWithoutDomain, removeDomainUsername)) {
                return EmployeeVO.fromFactRoster(factRoster);
            }
        }
        return null;
    }

    /**
     * 根据工号查询员工类别字段值
     *
     * @param pernr 工号
     * @param date  日期
     * @return
     */
    public String getStaffClassMacauByPernr(String pernr, LocalDate date) {
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactRoster::getPernr, pernr).le(FactRoster::getRecoverydate, DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date));
        queryWrapper.orderByDesc(FactRoster::getRecoverydate);
        return Optional.ofNullable(getOne(queryWrapper, Boolean.FALSE)).map(FactRoster::getStaffclassMacau).orElse(null);
    }

    public FactRoster findByPernr(String pernr, LocalDate date) {
        String startDate = DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date.withDayOfMonth(1));
        String endDate = DateTimeFormatter.ofPattern("yyyy.MM.dd").format(date.withDayOfMonth(date.lengthOfMonth()));
        QueryWrapper<FactRoster> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pernr", pernr).between("recoverydate", startDate, endDate);
        queryWrapper.orderByDesc("recoverydate");
        return getOne(queryWrapper, false);
    }

    public FactRoster getFactRosterByPernr(String pernr){
        LambdaQueryWrapper<FactRoster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactRoster::getPernr,pernr);
        queryWrapper.orderByDesc(FactRoster::getRecoverydate);
        return getOne(queryWrapper,false);
    }

}
