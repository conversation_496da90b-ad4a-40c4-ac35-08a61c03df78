package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.AiReportMapper;
import com.csci.hrrs.model.AiReport;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class AiReportService extends ServiceImpl<AiReportMapper, AiReport> {
    /**
     * 更新AI报告信息，对列表中的每个AI报告进行删除后重新插入操作
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer update(List<AiReport> aiReports) {
        Integer count = 0;
        LocalDate currentDate = LocalDate.now();
        for (AiReport aiReport : aiReports) {
            if (ObjectUtils.isEmpty(aiReport.getStatYear())
                    || ObjectUtils.isEmpty(aiReport.getStatMonth())
                    || ObjectUtils.isEmpty(aiReport.getOrganizationCode())) {
                throw new IllegalArgumentException("沒有提供必要的參數");
            }

            LambdaQueryWrapper<AiReport> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiReport:: getStatYear, aiReport.getStatYear());
            queryWrapper.eq(AiReport:: getStatMonth, aiReport.getStatMonth());
            queryWrapper.eq(AiReport::getOrganizationCode, aiReport.getOrganizationCode());
            this.remove(queryWrapper);

            aiReport.setId(UUID.randomUUID().toString());
            aiReport.setCreationTime(currentDate);
            if(this.save(aiReport)) {
                count++;
            }
        }
        return count;
    }
}
