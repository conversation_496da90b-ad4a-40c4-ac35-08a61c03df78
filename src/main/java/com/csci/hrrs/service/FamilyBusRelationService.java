package com.csci.hrrs.service;

import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.FamilyBusRelationMapper;
import com.csci.hrrs.model.FamilyBusRelation;
import com.csci.hrrs.model.FamilyBusRelationExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class FamilyBusRelationService {

    @Resource
    private FamilyBusRelationMapper familyBusRelationMapper;

    /**
     * this method is delegated to FamilyBusRelationMapper.insertSelective
     *
     * @param record FamilyBusRelation
     * @return
     */
    // insertSelective
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FamilyBusRelation record) {
        return familyBusRelationMapper.insertSelective(record);
    }

    /**
     * this method is delegated to FamilyBusRelationMapper.updateByPrimaryKeySelective
     *
     * @param record FamilyBusRelation
     * @return 受影响的行数
     */
    // updateByPrimaryKeySelective
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(FamilyBusRelation record) {
        return familyBusRelationMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * this method is delegated to FamilyBusRelationMapper.selectByExample
     *
     * @param example 查询条件
     * @return List<FamilyBusRelation>
     */
    // selectByExample
    public List<FamilyBusRelation> selectByExample(FamilyBusRelationExample example) {
        return familyBusRelationMapper.selectByExample(example);
    }
}
