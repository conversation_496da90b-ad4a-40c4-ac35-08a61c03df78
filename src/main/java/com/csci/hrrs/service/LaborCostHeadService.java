package com.csci.hrrs.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.converter.LaborCostDetailConverter;
import com.csci.hrrs.converter.LaborCostHeadConverter;
import com.csci.hrrs.mapper.LaborCostDetailMapper;
import com.csci.hrrs.mapper.LaborCostHeadMapper;
import com.csci.hrrs.model.LaborCostDetail;
import com.csci.hrrs.model.LaborCostDetailExample;
import com.csci.hrrs.model.LaborCostHead;
import com.csci.hrrs.model.LaborCostHeadExample;
import com.csci.hrrs.util.DateUtils;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.vo.LaborCostDetailVO;
import com.csci.hrrs.vo.LaborCostHeadVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.csci.hrrs.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class LaborCostHeadService implements ILaborCostDetailBase, ILaborCostHeadBase {

    @Resource
    private LaborCostHeadMapper laborCostHeadMapper;

    @Resource
    private LaborCostDetailMapper laborCostDetailMapper;

    /**
     * 获取或初始化
     *
     * @param organizationId 组织机构代码
     * @param statDate       统计日期
     * @return LaborCostHeadVO
     */
    @Transactional(rollbackFor = Exception.class)
    public LaborCostHeadVO getOrInit(String organizationId, LocalDate statDate) {
        checkExist(organizationId, "组织机构代码不能为空");
        checkExist(statDate, "统计日期不能为空");

        LaborCostHeadVO headVO = findBy(organizationId, statDate);

        if (Objects.isNull(headVO)) {
            // do init
            String key = "lock:createLaborCost:" + organizationId + ":" + DateUtils.toDateString(statDate);
            try {
                if (RedisLockUtil.lock(key)) {
                    LaborCostHead head = new LaborCostHead();
                    head.setOrganizationId(organizationId);
                    head.setStatDate(statDate);
                    head.setLastUpdateVersion(0);
                    laborCostHeadMapper.insertSelective(head);
                    return LaborCostHeadConverter.convert(head);
                } else {
                    throw new ServiceException("正在初始化，请稍后再试");
                }
            } finally {
                RedisLockUtil.unlock(key);
            }
        } else {
            return headVO;
        }
    }

    /**
     * 根据组织机构代码和统计日期查询
     *
     * @param organizationId 组织机构代码
     * @param statDate       统计日期
     * @return LaborCostHeadVO
     */
    public LaborCostHeadVO findBy(String organizationId, LocalDate statDate) {
        checkExist(organizationId, "组织机构代码不能为空");
        checkExist(statDate, "统计日期不能为空");
        LaborCostHeadExample example = new LaborCostHeadExample();
        example.or().andDeletedEqualTo(false).andOrganizationIdEqualTo(organizationId).andStatDateEqualTo(statDate);
        LaborCostHeadVO laborCostHeadVO = laborCostHeadMapper.selectByExample(example).stream().findFirst().map(LaborCostHeadConverter::convert).orElse(null);
        if (Objects.isNull(laborCostHeadVO)) {
            return null;
        }
        laborCostHeadVO.setDetails(listLaborCostDetail(laborCostHeadVO.getId()));
        return laborCostHeadVO;
    }

    /**
     * 更新人工成本
     *
     * @param laborCostHeadVO LaborCostHeadVO
     * @return LaborCostHeadVO
     */
    public LaborCostHeadVO updateWithDetail(LaborCostHeadVO laborCostHeadVO) {
        checkExist(laborCostHeadVO.getId(), "id不能为空");
        checkExist(laborCostHeadVO.getLastUpdateVersion(), "lastUpdateVersion不能为空");
        checkExist(laborCostHeadVO.getDetails(), "details不能为空");

        LaborCostHead head = LaborCostHeadConverter.convert(laborCostHeadVO);
        LaborCostHeadExample headExample = new LaborCostHeadExample();
        headExample.or().andIdEqualTo(laborCostHeadVO.getId()).andDeletedEqualTo(false).andLastUpdateVersionEqualTo(laborCostHeadVO.getLastUpdateVersion());
        int updateCount = laborCostHeadMapper.updateByExampleSelective(head, headExample);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，数据已被修改");
        }

        LaborCostHeadVO resultVO = LaborCostHeadConverter.convert(head);

        List<LaborCostDetailVO> lstDetailVO = updateDetails(laborCostHeadVO);
        resultVO.setDetails(lstDetailVO);

        return resultVO;

    }

    List<LaborCostDetailVO> updateDetails(LaborCostHeadVO laborCostHeadVO) {

        LaborCostDetailExample detailExample = new LaborCostDetailExample();
        detailExample.or().andHeadIdEqualTo(laborCostHeadVO.getId()).andDeletedEqualTo(false);
        LaborCostDetail deleteRecord = new LaborCostDetail();
        deleteRecord.setDeleted(true);
        laborCostDetailMapper.updateByExampleSelective(deleteRecord, detailExample);

        List<LaborCostDetail> lstDetail = new ArrayList<>();
        int iSeq = 1;
        for (LaborCostDetailVO laborCostDetailVO : laborCostHeadVO.getDetails()) {
            LaborCostDetail detail = LaborCostDetailConverter.convert(laborCostDetailVO);
            detail.setId(null);
            detail.setHeadId(laborCostHeadVO.getId());
            detail.setSeq(iSeq++);
            laborCostDetailMapper.insertSelective(detail);
            lstDetail.add(detail);
        }

        return lstDetail.stream().map(LaborCostDetailConverter::convert).toList();
    }

    /**
     * 根据头id查询明细并导出excel
     *
     * @param headId   头id
     * @param response HttpServletResponse
     */
    public void exportExcel(String headId, HttpServletResponse response) {
        checkExist(headId, "headId不能为空");

        List<LaborCostDetailVO> lstDetailVO = listLaborCostDetail(headId);
        if (CollectionUtils.isEmpty(lstDetailVO)) {
            throw new ServiceException("没有数据");
        }

        String filename = MessageFormat.format("人工成本情况表-{0}.xlsx", DateUtils.toDatetimeString(LocalDateTime.now()));
        filename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);

        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream("template/hr/hr-labor-cost-template.xlsx"); ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(lstDetailVO, fillConfig, writeSheet);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public LaborCostHeadVO findById(String Id) {
        checkExist(Id, "Id不能为空");
        LaborCostHead head = laborCostHeadMapper.selectByPrimaryKey(Id);
        if (Objects.isNull(head)) {
            return null;
        }
        LaborCostHeadVO headVO = LaborCostHeadConverter.convert(head);
        headVO.setDetails(listLaborCostDetail(headVO.getId()));
        return headVO;
    }

    /**
     * 根据 id 及 lastUpdateVersion 执行逻辑删除
     *
     * @param id                id
     * @param lastUpdateVersion 最后更新版本号
     * @return 删除的记录数
     */
    public int deleteById(String id, Integer lastUpdateVersion) {
        checkExist(id, "id不能为空");
        checkExist(lastUpdateVersion, "lastUpdateVersion不能为空");
        LaborCostHeadExample example = new LaborCostHeadExample();
        example.or().andIdEqualTo(id).andDeletedEqualTo(false).andLastUpdateVersionEqualTo(lastUpdateVersion);
        LaborCostHead head = new LaborCostHead();
        head.setDeleted(true);
        head.setLastUpdateVersion(lastUpdateVersion);

        int updateCount = laborCostHeadMapper.updateByExampleSelective(head, example);
        if (updateCount == 0) {
            throw new ServiceException("数据已被修改,请刷新后重试");
        }
        return updateCount;
    }
}
