package com.csci.hrrs.service;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.model.*;
import com.csci.hrrs.qo.*;
import com.csci.hrrs.util.ExcelUtils;
import com.csci.hrrs.vo.*;

import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class ExcelService {

    @Autowired
    private ResourceLoader resourceLoader;
    
    @Autowired
    private DynamicFormService dynamicFormService;


	/**
	 * 社會積效二 數據導入
	 *
	 * @param file, dynamicFormHead
	 * @return
	 */
	public DynamicFormVO importSociologyOneExcel(MultipartFile file, DynamicFormHead dynamicFormHead) {
		dynamicFormHead.setIsDeleted(false);
		dynamicFormHead.setIsSubmitted(false);
		DynamicFormVO dynamicFormVO = this.excelToSociologyOneData(file, dynamicFormHead);
		String id = dynamicFormService.saveDynamicForm(dynamicFormVO);
		dynamicFormVO.setId(id);
		return dynamicFormVO;
	}

	/**
	 * 社會積效一 數據導出
	 *
	 * @param dynamicFormExportQO
	 * @return
	 */
	public ByteArrayInputStream exportSociologyOneExcel(DynamicFormExportQO dynamicFormExportQO) {
		DynamicFormVO dynamicFormVO = dynamicFormService.getDynamicForm(dynamicFormExportQO.getDynamicFormQO());
		if(dynamicFormVO == null) throw new ServiceException("找不到數據");
		return this.dataToSociologyOneExcel(dynamicFormVO);
	}

	/**
	 * 社會積效一 Excel轉換成数据
	 *
	 * @param file, dynamicFormHead
	 * @return
	 */
	private DynamicFormVO excelToSociologyOneData(MultipartFile file, DynamicFormHead dynamicFormHead) {
        
		try (InputStream is = file.getInputStream();
                Workbook workbook = ExcelUtils.getWorkbookFromUrl(is, file.getOriginalFilename())) {
        	
			DynamicFormVO dynamicFormVO = new DynamicFormVO();
			BeanUtils.copyProperties(dynamicFormHead, dynamicFormVO);

			Sheet sheet = workbook.getSheetAt(0);
			List<DynamicFormDetailVO> lstDynamicFormDetailVO = new ArrayList<>();

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeBelow30M", ExcelUtils.getCellValueByRef(sheet, "D3"), "员工人数 - 男性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAge31To40M", ExcelUtils.getCellValueByRef(sheet, "E3"), "员工人数 - 男性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAge41low50M", ExcelUtils.getCellValueByRef(sheet, "F3"), "员工人数 - 男性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeAbove51M", ExcelUtils.getCellValueByRef(sheet, "G3"), "员工人数 - 男性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeRemarkM", ExcelUtils.getCellValueByRef(sheet, "J3"), "员工人数 - 男性 - 按年龄划分(備註)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeBelow30F", ExcelUtils.getCellValueByRef(sheet, "D4"), "员工人数 - 女性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAge31To40F", ExcelUtils.getCellValueByRef(sheet, "E4"), "员工人数 - 女性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAge41low50F", ExcelUtils.getCellValueByRef(sheet, "F4"), "员工人数 - 女性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeAbove51F", ExcelUtils.getCellValueByRef(sheet, "G4"), "员工人数 - 女性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByAgeRemarkF", ExcelUtils.getCellValueByRef(sheet, "J4"), "员工人数 - 女性 - 按年龄划分(備註)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelHighM", ExcelUtils.getCellValueByRef(sheet, "D6"), "员工人数 - 男性 - 按职级划分(高层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelMiddleM", ExcelUtils.getCellValueByRef(sheet, "E6"), "员工人数 - 男性 - 按职级划分(中层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelBaseM", ExcelUtils.getCellValueByRef(sheet, "F6"), "员工人数 - 男性 - 按职级划分(基层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G6"), "员工人数 - 男性 - 按职级划分(一般员工)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelRemarkM", ExcelUtils.getCellValueByRef(sheet, "J6"), "员工人数 - 男性 - 按职级划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelHighF", ExcelUtils.getCellValueByRef(sheet, "D7"), "员工人数 - 女性 - 按职级划分(高层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelMiddleF", ExcelUtils.getCellValueByRef(sheet, "E7"), "员工人数 - 女性 - 按职级划分(中层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelBaseF", ExcelUtils.getCellValueByRef(sheet, "F7"), "员工人数 - 女性 - 按职级划分(基层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G7"), "员工人数 - 女性 - 按职级划分(一般员工)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByLevelRemarkF", ExcelUtils.getCellValueByRef(sheet, "J7"), "员工人数 - 女性 - 按职级划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractPermanentM", ExcelUtils.getCellValueByRef(sheet, "D9"), "员工人数 - 男性 - 按雇佣合同划分(无限期或永久)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTemporaryM", ExcelUtils.getCellValueByRef(sheet, "E9"), "员工人数 - 男性 - 按雇佣合同划分(固定期限或临时)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractRemarkM", ExcelUtils.getCellValueByRef(sheet, "J9"), "员工人数 - 男性 - 按雇佣合同划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractPermanentF", ExcelUtils.getCellValueByRef(sheet, "D10"), "员工人数 - 女性 - 按雇佣合同划分(无限期或永久)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTemporaryF", ExcelUtils.getCellValueByRef(sheet, "E10"), "员工人数 - 女性 - 按雇佣合同划分(固定期限或临时)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractRemarkF", ExcelUtils.getCellValueByRef(sheet, "J10"), "员工人数 - 女性 - 按雇佣合同划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypeFullTimeM", ExcelUtils.getCellValueByRef(sheet, "D12"), "员工人数 - 男性 - 按雇佣类型划分(全职)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypePartTimeM", ExcelUtils.getCellValueByRef(sheet, "E12"), "员工人数 - 男性 - 按雇佣类型划分(兼职)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypeRemarkM", ExcelUtils.getCellValueByRef(sheet, "J12"), "员工人数 - 男性 - 按雇佣类型划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypeFullTimeF", ExcelUtils.getCellValueByRef(sheet, "D13"), "员工人数 - 女性 - 按雇佣类型划分(全职)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypePartTimeF", ExcelUtils.getCellValueByRef(sheet, "E13"), "员工人数 - 女性 - 按雇佣类型划分(兼职)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByContractTypeRemarkF", ExcelUtils.getCellValueByRef(sheet, "J13"), "员工人数 - 女性 - 按雇佣类型划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeBelow30M", ExcelUtils.getCellValueByRef(sheet, "D15"), "员工人数 - 男性 - 月薪制員工人數－按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAge31To40M", ExcelUtils.getCellValueByRef(sheet, "E15"), "员工人数 - 男性 - 月薪制員工人數－按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAge41low50M", ExcelUtils.getCellValueByRef(sheet, "F15"), "员工人数 - 男性 - 月薪制員工人數－按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeAbove51M", ExcelUtils.getCellValueByRef(sheet, "G15"), "员工人数 - 男性 - 月薪制員工人數－按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeRemarkM", ExcelUtils.getCellValueByRef(sheet, "J15"), "员工人数 - 男性 - 月薪制員工人數－按年龄划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeBelow30F", ExcelUtils.getCellValueByRef(sheet, "D16"), "员工人数 - 女性 - 月薪制員工人數－按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAge31To40F", ExcelUtils.getCellValueByRef(sheet, "E16"), "员工人数 - 女性 - 月薪制員工人數－按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAge41low50F", ExcelUtils.getCellValueByRef(sheet, "F16"), "员工人数 - 女性 - 月薪制員工人數－按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeAbove51F", ExcelUtils.getCellValueByRef(sheet, "G16"), "员工人数 - 女性 - 月薪制員工人數－按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByAgeRemarkF", ExcelUtils.getCellValueByRef(sheet, "J16"), "员工人数 - 女性 - 月薪制員工人數－按年龄划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelHighM", ExcelUtils.getCellValueByRef(sheet, "D18"), "员工人数 - 男性 - 月薪制員工人數－按职级划分(高层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelMiddleM", ExcelUtils.getCellValueByRef(sheet, "E18"), "员工人数 - 男性 - 月薪制員工人數－按职级划分(中层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelBaseM", ExcelUtils.getCellValueByRef(sheet, "F18"), "员工人数 - 男性 - 月薪制員工人數－按职级划分(基层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G18"), "员工人数 - 男性 - 月薪制員工人數－按职级划分(一般员工)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelRemarkM", ExcelUtils.getCellValueByRef(sheet, "J18"), "员工人数 - 男性 - 月薪制員工人數－按职级划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelHighF", ExcelUtils.getCellValueByRef(sheet, "D19"), "员工人数 - 女性 - 月薪制員工人數－按职级划分(高层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelMiddleF", ExcelUtils.getCellValueByRef(sheet, "E19"), "员工人数 - 女性 - 月薪制員工人數－按职级划分(中层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelBaseF", ExcelUtils.getCellValueByRef(sheet, "F19"), "员工人数 - 女性 - 月薪制員工人數－按职级划分(基层)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G19"), "员工人数 - 女性 - 月薪制員工人數－按职级划分(一般员工)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoWithMonthlySalaryByLevelRemarkF", ExcelUtils.getCellValueByRef(sheet, "J19"), "员工人数 - 女性 - 月薪制員工人數－按职级划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionChinaM", ExcelUtils.getCellValueByRef(sheet, "D21"), "员工人数 - 男性 - 按地区划分(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionHongKongM", ExcelUtils.getCellValueByRef(sheet, "E21"), "员工人数 - 男性 - 按地区划分(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionMacaoM", ExcelUtils.getCellValueByRef(sheet, "F21"), "员工人数 - 男性 - 按地区划分(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionAmericaM", ExcelUtils.getCellValueByRef(sheet, "G21"), "员工人数 - 男性 - 按地区划分(美国)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionCanadaM", ExcelUtils.getCellValueByRef(sheet, "H21"), "员工人数 - 男性 - 按地区划分(加拿大)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionOtherM", ExcelUtils.getCellValueByRef(sheet, "I21"), "员工人数 - 男性 - 按地区划分(其他)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionRemarkM", ExcelUtils.getCellValueByRef(sheet, "J21"), "员工人数 - 男性 - 按地区划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionChinaF", ExcelUtils.getCellValueByRef(sheet, "D22"), "员工人数 - 女性 - 按地区划分(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionHongKongF", ExcelUtils.getCellValueByRef(sheet, "E22"), "员工人数 - 女性 - 按地区划分(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionMacaoF", ExcelUtils.getCellValueByRef(sheet, "F22"), "员工人数 - 女性 - 按地区划分(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionAmericaF", ExcelUtils.getCellValueByRef(sheet, "G22"), "员工人数 - 女性 - 按地区划分(美国)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionCanadaF", ExcelUtils.getCellValueByRef(sheet, "H22"), "员工人数 - 女性 - 按地区划分(加拿大)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionOtherF", ExcelUtils.getCellValueByRef(sheet, "I22"), "员工人数 - 女性 - 按地区划分(其他)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "staffNoByRegionRemarkF", ExcelUtils.getCellValueByRef(sheet, "J22"), "员工人数 - 女性 - 按地区划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerType1", ExcelUtils.getCellValueByRef(sheet, "D24"), "其他工作者 - 男性 - 其他工作者 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerNatureAndArea1", ExcelUtils.getCellValueByRef(sheet, "E24"), "其他工作者 - 男性 - 工作性质及范围 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal1M", ExcelUtils.getCellValueByRef(sheet, "F24"), "其他工作者 - 男性 - 其他工作者总人数(注：全年平均数) 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark1M", ExcelUtils.getCellValueByRef(sheet, "J24"), "其他工作者 - 男性 - 备注 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal1F", ExcelUtils.getCellValueByRef(sheet, "F25"), "其他工作者 - 女性 - 其他工作者总人数(注：全年平均数) 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark1F", ExcelUtils.getCellValueByRef(sheet, "J25"), "其他工作者 - 女性 - 备注 1"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerType2", ExcelUtils.getCellValueByRef(sheet, "D26"), "其他工作者 - 男性 - 工作性质及范围 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerNatureAndArea2", ExcelUtils.getCellValueByRef(sheet, "E26"), "其他工作者 - 男性 - 工作性质及范围 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal2M", ExcelUtils.getCellValueByRef(sheet, "F26"), "其他工作者 - 男性 - 其他工作者总人数(注：全年平均数) 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark2M", ExcelUtils.getCellValueByRef(sheet, "J26"), "其他工作者 - 男性 - 备注 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal2F", ExcelUtils.getCellValueByRef(sheet, "F27"), "其他工作者 - 女性 - 其他工作者总人数(注：全年平均数) 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark2F", ExcelUtils.getCellValueByRef(sheet, "J27"), "其他工作者 - 女性 - 备注 2"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerType3", ExcelUtils.getCellValueByRef(sheet, "D28"), "其他工作者 - 男性 - 工作性质及范围 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerNatureAndArea3", ExcelUtils.getCellValueByRef(sheet, "E28"), "其他工作者 - 男性 - 工作性质及范围 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal3M", ExcelUtils.getCellValueByRef(sheet, "F28"), "其他工作者 - 男性 - 其他工作者总人数(注：全年平均数) 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark3M", ExcelUtils.getCellValueByRef(sheet, "J28"), "其他工作者 - 男性 - 备注 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerTotal3F", ExcelUtils.getCellValueByRef(sheet, "F29"), "其他工作者 - 女性 - 其他工作者总人数(注：全年平均数) 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "otherWorkerRemark3F", ExcelUtils.getCellValueByRef(sheet, "J29"), "其他工作者 - 女性 - 备注 3"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelHighM", ExcelUtils.getCellValueByRef(sheet, "D31"), "员工总薪酬 - 男性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelMiddleM", ExcelUtils.getCellValueByRef(sheet, "E31"), "员工总薪酬 - 男性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelBaseM", ExcelUtils.getCellValueByRef(sheet, "F31"), "员工总薪酬 - 男性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G31"), "员工总薪酬 - 男性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelRemarkM", ExcelUtils.getCellValueByRef(sheet, "J31"), "员工总薪酬 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelHighF", ExcelUtils.getCellValueByRef(sheet, "D32"), "员工总薪酬 - 女性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelMiddleF", ExcelUtils.getCellValueByRef(sheet, "E32"), "员工总薪酬 - 女性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelBaseF", ExcelUtils.getCellValueByRef(sheet, "F32"), "员工总薪酬 - 女性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G32"), "员工总薪酬 - 女性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalSalaryByLevelRemarkF", ExcelUtils.getCellValueByRef(sheet, "J32"), "员工总薪酬 - 女性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeBelow30M", ExcelUtils.getCellValueByRef(sheet, "D34"), "新入职员工人数 - 男性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAge31To40M", ExcelUtils.getCellValueByRef(sheet, "E34"), "新入职员工人数 - 男性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAge41low50M", ExcelUtils.getCellValueByRef(sheet, "F34"), "新入职员工人数 - 男性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeAbove51M", ExcelUtils.getCellValueByRef(sheet, "G34"), "新入职员工人数 - 男性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeRemarkM", ExcelUtils.getCellValueByRef(sheet, "J34"), "新入职员工人数 - 男性 - 按年龄划分(备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeBelow30F", ExcelUtils.getCellValueByRef(sheet, "D35"), "新入职员工人数 - 女性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAge31To40F", ExcelUtils.getCellValueByRef(sheet, "E35"), "新入职员工人数 - 女性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAge41low50F", ExcelUtils.getCellValueByRef(sheet, "F35"), "新入职员工人数 - 女性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeAbove51F", ExcelUtils.getCellValueByRef(sheet, "G35"), "新入职员工人数 - 女性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "newStaffNoByAgeRemarkF", ExcelUtils.getCellValueByRef(sheet, "J35"), "新入职员工人数 - 女性 - 按年龄划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeBelow30M", ExcelUtils.getCellValueByRef(sheet, "D37"), "流失员工人数 - 男性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAge31To40M", ExcelUtils.getCellValueByRef(sheet, "E37"), "流失员工人数 - 男性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAge41low50M", ExcelUtils.getCellValueByRef(sheet, "F37"), "流失员工人数 - 男性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeAbove51M", ExcelUtils.getCellValueByRef(sheet, "G37"), "流失员工人数 - 男性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeRemarkM", ExcelUtils.getCellValueByRef(sheet, "J37"), "流失员工人数 - 男性 - 按年龄划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeBelow30F", ExcelUtils.getCellValueByRef(sheet, "D38"), "流失员工人数 - 女性 - 按年龄划分(30岁及以下)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAge31To40F", ExcelUtils.getCellValueByRef(sheet, "E38"), "流失员工人数 - 女性 - 按年龄划分(31-40岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAge41low50F", ExcelUtils.getCellValueByRef(sheet, "F38"), "流失员工人数 - 女性 - 按年龄划分(41-50岁)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeAbove51F", ExcelUtils.getCellValueByRef(sheet, "G38"), "流失员工人数 - 女性 - 按年龄划分(51岁及以上)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByAgeRemarkF", ExcelUtils.getCellValueByRef(sheet, "J38"), "流失员工人数 - 女性 - 按年龄划分(备注)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionChinaM", ExcelUtils.getCellValueByRef(sheet, "D40"), "流失员工人数 - 男性 - 按地区划分(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionHongKongM", ExcelUtils.getCellValueByRef(sheet, "E40"), "流失员工人数 - 男性 - 按地区划分(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionMacaoM", ExcelUtils.getCellValueByRef(sheet, "F40"), "流失员工人数 - 男性 - 按地区划分(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionAmericaM", ExcelUtils.getCellValueByRef(sheet, "G40"), "流失员工人数 - 男性 - 按地区划分(美国)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionCanadaM", ExcelUtils.getCellValueByRef(sheet, "H40"), "流失员工人数 - 男性 - 按地区划分(加拿大)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionOtherM", ExcelUtils.getCellValueByRef(sheet, "I40"), "流失员工人数 - 男性 - 按地区划分(其他)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionRemarkM", ExcelUtils.getCellValueByRef(sheet, "J40"), "流失员工人数 - 男性 - 按地区划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionChinaF", ExcelUtils.getCellValueByRef(sheet, "D41"), "流失员工人数 - 女性 - 按地区划分(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionHongKongF", ExcelUtils.getCellValueByRef(sheet, "E41"), "流失员工人数 - 女性 - 按地区划分(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionMacaoF", ExcelUtils.getCellValueByRef(sheet, "F41"), "流失员工人数 - 女性 - 按地区划分(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionAmericaF", ExcelUtils.getCellValueByRef(sheet, "G41"), "流失员工人数 - 女性 - 按地区划分(美国)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionCanadaF", ExcelUtils.getCellValueByRef(sheet, "H41"), "流失员工人数 - 女性 - 按地区划分(加拿大)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionOtherF", ExcelUtils.getCellValueByRef(sheet, "I41"), "流失员工人数 - 女性 - 按地区划分(其他)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossStaffNoByRegionRemarkF", ExcelUtils.getCellValueByRef(sheet, "J41"), "流失员工人数 - 女性 - 按地区划分(备注)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "minNoticePeriod", ExcelUtils.getCellValueByRef(sheet, "C43"), "运营变更通知 - 最短通知周数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "minNoticePeriodRemark", ExcelUtils.getCellValueByRef(sheet, "J43"), "运营变更通知 - 最短通知周数 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffDeathNoM", ExcelUtils.getCellValueByRef(sheet, "D45"), "员工-工伤 - 男性 - 工伤导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffSeriousConsequencesNoM", ExcelUtils.getCellValueByRef(sheet, "E45"), "员工-工伤 - 男性 - 严重后果工伤人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffRecordableNoM", ExcelUtils.getCellValueByRef(sheet, "F45"), "员工-工伤 - 男性 - 可记录因工伤亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffMainTypeM", ExcelUtils.getCellValueByRef(sheet, "G45"), "员工-工伤 - 男性 - 主要工伤类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffRemarkM", ExcelUtils.getCellValueByRef(sheet, "J45"), "员工-工伤 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffDeathNoF", ExcelUtils.getCellValueByRef(sheet, "D46"), "员工-工伤 - 女性 - 工伤导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffSeriousConsequencesNoF", ExcelUtils.getCellValueByRef(sheet, "E46"), "员工-工伤 - 女性 - 严重后果工伤人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffRecordableNoF", ExcelUtils.getCellValueByRef(sheet, "F46"), "员工-工伤 - 女性 - 可记录因工伤亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffMainTypeF", ExcelUtils.getCellValueByRef(sheet, "G46"), "员工-工伤 - 女性 - 主要工伤类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfStaffRemarkF", ExcelUtils.getCellValueByRef(sheet, "J46"), "员工-工伤 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffDeathNoM", ExcelUtils.getCellValueByRef(sheet, "D48"), "员工- 职业病 - 男性 - 职业病导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffRecordableNoM", ExcelUtils.getCellValueByRef(sheet, "E48"), "员工- 职业病 - 男性 - 可记录职业病人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffMainTypeM", ExcelUtils.getCellValueByRef(sheet, "F48"), "员工- 职业病 - 男性 - 主要职业病类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffRemarkM", ExcelUtils.getCellValueByRef(sheet, "J48"), "员工- 职业病 - 男性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffDeathNoF", ExcelUtils.getCellValueByRef(sheet, "D49"), "员工- 职业病 - 女性 - 职业病导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffRecordableNoF", ExcelUtils.getCellValueByRef(sheet, "E49"), "员工- 职业病 - 女性 - 可记录职业病人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffMainTypeF", ExcelUtils.getCellValueByRef(sheet, "F49"), "员工- 职业病 - 女性 - 主要职业病类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfStaffRemarkF", ExcelUtils.getCellValueByRef(sheet, "J49"), "员工- 职业病 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossWorkingDayOfStaffDueToWorkInjuryM", ExcelUtils.getCellValueByRef(sheet, "D51"), "员工- 因工伤损失工作日数及缺勤日数 - 男性 - 因工伤损失工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "absenceDayOfStaffM", ExcelUtils.getCellValueByRef(sheet, "E51"), "员工- 因工伤损失工作日数及缺勤日数 - 男性 - 缺勤日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "originalWorkingDayOfStaffM", ExcelUtils.getCellValueByRef(sheet, "F51"), "员工- 因工伤损失工作日数及缺勤日数 - 男性 - 原定工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalWorkingHourOfStaffM", ExcelUtils.getCellValueByRef(sheet, "G51"), "员工- 因工伤损失工作日数及缺勤日数 - 男性 - 總工作時数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workingDayOfStaffRemarkM", ExcelUtils.getCellValueByRef(sheet, "J51"), "员工- 因工伤损失工作日数及缺勤日数 - 男性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossWorkingDayOfStaffDueToWorkInjuryF", ExcelUtils.getCellValueByRef(sheet, "D52"), "员工- 因工伤损失工作日数及缺勤日数 - 女性 - 因工伤损失工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "absenceDayOfStaffF", ExcelUtils.getCellValueByRef(sheet, "E52"), "员工- 因工伤损失工作日数及缺勤日数 - 女性 - 缺勤日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "originalWorkingDayOfStaffF", ExcelUtils.getCellValueByRef(sheet, "F52"), "员工- 因工伤损失工作日数及缺勤日数 - 女性 - 原定工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalWorkingHourOfStaffF", ExcelUtils.getCellValueByRef(sheet, "G52"), "员工- 因工伤损失工作日数及缺勤日数 - 女性 - 總工作時数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workingDayOfStaffRemarkF", ExcelUtils.getCellValueByRef(sheet, "J52"), "员工- 因工伤损失工作日数及缺勤日数 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersDeathNoM", ExcelUtils.getCellValueByRef(sheet, "D54"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 男性 - 工伤导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersSeriousConsequencesNoM", ExcelUtils.getCellValueByRef(sheet, "E54"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 男性 - 严重后果工伤人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersRecordableNoM", ExcelUtils.getCellValueByRef(sheet, "F54"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 男性 - 可记录因工伤亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersMainTypeM", ExcelUtils.getCellValueByRef(sheet, "G54"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 男性 - 主要工伤类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersRemarkM", ExcelUtils.getCellValueByRef(sheet, "J54"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersDeathNoF", ExcelUtils.getCellValueByRef(sheet, "D55"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 女性 - 工伤导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersSeriousConsequencesNoF", ExcelUtils.getCellValueByRef(sheet, "E55"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 女性 - 严重后果工伤人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersRecordableNoF", ExcelUtils.getCellValueByRef(sheet, "F55"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 女性 - 可记录因工伤亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersMainTypeF", ExcelUtils.getCellValueByRef(sheet, "G55"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 女性 - 主要工伤类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workInjuryOfOthersRemarkF", ExcelUtils.getCellValueByRef(sheet, "J55"), "其他工作者（包括承包商/分包商/工厂工人）- 工伤 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersDeathNoM", ExcelUtils.getCellValueByRef(sheet, "D57"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 男性 - 职业病导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersRecordableNoM", ExcelUtils.getCellValueByRef(sheet, "E57"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 男性 - 可记录职业病人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersMainTypeM", ExcelUtils.getCellValueByRef(sheet, "F57"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 男性 - 主要职业病类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersRemarkM", ExcelUtils.getCellValueByRef(sheet, "J57"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 男性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersDeathNoF", ExcelUtils.getCellValueByRef(sheet, "D58"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 女性 - 职业病导致的死亡人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersRecordableNoF", ExcelUtils.getCellValueByRef(sheet, "E58"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 女性 - 可记录职业病人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersMainTypeF", ExcelUtils.getCellValueByRef(sheet, "F58"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 女性 - 主要职业病类型"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "occupationalDiseaseOfOthersRemarkF", ExcelUtils.getCellValueByRef(sheet, "J58"), "其他工作者（包括承包商/分包商／工厂工人） - 职业病 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossWorkingDayOfOthersDueToWorkInjuryM", ExcelUtils.getCellValueByRef(sheet, "D60"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 男性 - 因工伤损失工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "absenceDayOfOthersM", ExcelUtils.getCellValueByRef(sheet, "E60"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 男性 - 缺勤日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "originalWorkingDayOfOthersM", ExcelUtils.getCellValueByRef(sheet, "F60"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 男性 - 原定工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalWorkingHourOfOthersM", ExcelUtils.getCellValueByRef(sheet, "G60"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 男性 - 總工作時数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workingDayOfOthersRemarkM", ExcelUtils.getCellValueByRef(sheet, "J60"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 男性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lossWorkingDayOfOthersDueToWorkInjuryF", ExcelUtils.getCellValueByRef(sheet, "D61"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 女性 - 因工伤损失工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "absenceDayOfOthersF", ExcelUtils.getCellValueByRef(sheet, "E61"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 女性 - 缺勤日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "originalWorkingDayOfOthersF", ExcelUtils.getCellValueByRef(sheet, "F61"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 女性 - 原定工作日数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "totalWorkingHourOfOthersF", ExcelUtils.getCellValueByRef(sheet, "G61"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 女性 - 總工作時数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "workingDayOfOthersRemarkF", ExcelUtils.getCellValueByRef(sheet, "J61"), "其他工作者（包括承包商/分包商／工厂工人） - 因工伤损失工作日数及缺勤日数 - 女性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTopic1", ExcelUtils.getCellValueByRef(sheet, "C63"), "培训活动 - 培训主题"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingGoal1", ExcelUtils.getCellValueByRef(sheet, "D63"), "培训活动 - 培训目标"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingContent1", ExcelUtils.getCellValueByRef(sheet, "E63"), "培训活动 - 主要内容"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTarget1", ExcelUtils.getCellValueByRef(sheet, "F63"), "培训活动 - 培训对象"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTotal1", ExcelUtils.getCellValueByRef(sheet, "G63"), "培训活动 - 总参与人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingRemark1", ExcelUtils.getCellValueByRef(sheet, "J63"), "培训活动 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTopic2", ExcelUtils.getCellValueByRef(sheet, "C64"), "培训活动 - 培训主题"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingGoal2", ExcelUtils.getCellValueByRef(sheet, "D64"), "培训活动 - 培训目标"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingContent2", ExcelUtils.getCellValueByRef(sheet, "E64"), "培训活动 - 主要内容"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTarget2", ExcelUtils.getCellValueByRef(sheet, "F64"), "培训活动 - 培训对象"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTotal2", ExcelUtils.getCellValueByRef(sheet, "G64"), "培训活动 - 总参与人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "traningRemark2", ExcelUtils.getCellValueByRef(sheet, "J64"), "培训活动 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTopic3", ExcelUtils.getCellValueByRef(sheet, "C65"), "培训活动 - 培训主题"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingGoal3", ExcelUtils.getCellValueByRef(sheet, "D65"), "培训活动 - 培训目标"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingContent3", ExcelUtils.getCellValueByRef(sheet, "E65"), "培训活动 - 主要内容"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTarget3", ExcelUtils.getCellValueByRef(sheet, "F65"), "培训活动 - 培训对象"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingTotal3", ExcelUtils.getCellValueByRef(sheet, "G65"), "培训活动 - 总参与人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "traningRemark3", ExcelUtils.getCellValueByRef(sheet, "J65"), "培训活动 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfHighLevelM", ExcelUtils.getCellValueByRef(sheet, "D67"), "培训人数 - 男性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfMiddleLevelM", ExcelUtils.getCellValueByRef(sheet, "E67"), "培训人数 - 男性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfBaseLevelM", ExcelUtils.getCellValueByRef(sheet, "F67"), "培训人数 - 男性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G67"), "培训人数 - 男性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoRemarkM", ExcelUtils.getCellValueByRef(sheet, "J67"), "培训人数 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfHighLevelF", ExcelUtils.getCellValueByRef(sheet, "D68"), "培训人数 - 女性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfMiddleLevelF", ExcelUtils.getCellValueByRef(sheet, "E68"), "培训人数 - 女性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfBaseLevelF", ExcelUtils.getCellValueByRef(sheet, "F68"), "培训人数 - 女性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoOfNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G68"), "培训人数 - 女性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingPeopleNoRemarkF", ExcelUtils.getCellValueByRef(sheet, "J68"), "培训人数 - 女性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "saftyTrainingStaffNo", ExcelUtils.getCellValueByRef(sheet, "C70"), "培训人数 - 接受安全与健康培训员工人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "saftyTrainingStaffNoRemark", ExcelUtils.getCellValueByRef(sheet, "J70"), "培训人数 - 接受安全与健康培训员工人数 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedNewStaffNoOfLowCarbonTraining", ExcelUtils.getCellValueByRef(sheet, "C72"), "培训人数 - 曾接受低碳培训的相关新入职员工人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedNewStaffNo", ExcelUtils.getCellValueByRef(sheet, "D72"), "培训人数 - 相关新入职员工人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedStaffNoOfLowCarbonTraining", ExcelUtils.getCellValueByRef(sheet, "E70"), "培训人数 - 曾接受低碳培训的员工人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedTotalStaffNo", ExcelUtils.getCellValueByRef(sheet, "F70"), "培训人数 - 相关员工总人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "lowCarbonTrainingRemark", ExcelUtils.getCellValueByRef(sheet, "J70"), "培训人数 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedStaffNoOfInnovationTraining", ExcelUtils.getCellValueByRef(sheet, "C74"), "培训人数 - 曾接受创新概念或工具培训的相关人员人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "relatedStaffNo", ExcelUtils.getCellValueByRef(sheet, "D74"), "培训人数 - 科技相关人员总数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "InnovationTrainingRemark", ExcelUtils.getCellValueByRef(sheet, "J74"), "培训人数 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfHighLevelM", ExcelUtils.getCellValueByRef(sheet, "D76"), "培训时数 - 男性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfMiddleLevelM", ExcelUtils.getCellValueByRef(sheet, "E76"), "培训时数 - 男性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfBaseLevelM", ExcelUtils.getCellValueByRef(sheet, "F76"), "培训时数 - 男性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G76"), "培训时数 - 男性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourRemarkM", ExcelUtils.getCellValueByRef(sheet, "J76"), "培训时数 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfHighLevelF", ExcelUtils.getCellValueByRef(sheet, "D77"), "培训时数 - 女性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfMiddleLevelF", ExcelUtils.getCellValueByRef(sheet, "E77"), "培训时数 - 女性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfBaseLevelF", ExcelUtils.getCellValueByRef(sheet, "F77"), "培训时数 - 女性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourOfNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G77"), "培训时数 - 女性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "trainingHourRemarkF", ExcelUtils.getCellValueByRef(sheet, "J77"), "培训时数 - 女性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "noOfBoardMember", ExcelUtils.getCellValueByRef(sheet, "D79"), "反贪污政策传达与培训 - 3311董事局 - 董事局成员总数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionInfoNoOfBoardMember", ExcelUtils.getCellValueByRef(sheet, "E79"), "反贪污政策传达与培训 - 3311董事局 - 接受反贪污政策和程序信息传达的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingNoOfBoardMember", ExcelUtils.getCellValueByRef(sheet, "F79"), "反贪污政策传达与培训 - 3311董事局 - 接受反贪污培训的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingHourOfBoardMember", ExcelUtils.getCellValueByRef(sheet, "G79"), "反贪污政策传达与培训 - 3311董事局 - 总培训时数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingRemarkOfBoardMember", ExcelUtils.getCellValueByRef(sheet, "J79"), "反贪污政策传达与培训 - 3311董事局 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionInfoStaffNoOfHighLevel", ExcelUtils.getCellValueByRef(sheet, "E81"), "反贪污政策传达与培训 - 其他员工 - 高层 - 接受反贪污政策和程序信息传达的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingStaffNoOfHighLevel", ExcelUtils.getCellValueByRef(sheet, "F81"), "反贪污政策传达与培训 - 其他员工 - 高层 - 接受反贪污培训的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingHourOfHighLevel", ExcelUtils.getCellValueByRef(sheet, "G81"), "反贪污政策传达与培训 - 其他员工 - 高层 - 总培训时数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingRemarkOfHighLevel", ExcelUtils.getCellValueByRef(sheet, "J81"), "反贪污政策传达与培训 - 其他员工 - 高层 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionInfoStaffNoOfMiddleLevel", ExcelUtils.getCellValueByRef(sheet, "E82"), "反贪污政策传达与培训 - 其他员工 - 中层 - 接受反贪污政策和程序信息传达的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingStaffNoOfMiddleLevel", ExcelUtils.getCellValueByRef(sheet, "F82"), "反贪污政策传达与培训 - 其他员工 - 中层 - 接受反贪污培训的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingHourOfMiddleLevel", ExcelUtils.getCellValueByRef(sheet, "G82"), "反贪污政策传达与培训 - 其他员工 - 中层 - 总培训时数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingRemarkOfMiddleLevel", ExcelUtils.getCellValueByRef(sheet, "J82"), "反贪污政策传达与培训 - 其他员工 - 中层 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionInfoStaffNoOfBaseLevel", ExcelUtils.getCellValueByRef(sheet, "E83"), "反贪污政策传达与培训 - 其他员工 - 基层 - 接受反贪污政策和程序信息传达的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingStaffNoOfBaseLevel", ExcelUtils.getCellValueByRef(sheet, "F83"), "反贪污政策传达与培训 - 其他员工 - 基层 - 接受反贪污培训的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingHourOfBaseLevel", ExcelUtils.getCellValueByRef(sheet, "G83"), "反贪污政策传达与培训 - 其他员工 - 基层 - 总培训时数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingRemarkOfBaseLevel", ExcelUtils.getCellValueByRef(sheet, "J83"), "反贪污政策传达与培训 - 其他员工 - 基层 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionInfoStaffNoOfNormalStaff", ExcelUtils.getCellValueByRef(sheet, "E84"), "反贪污政策传达与培训 - 其他员工 - 一般员工 - 接受反贪污政策和程序信息传达的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingStaffNoOfNormalStaff", ExcelUtils.getCellValueByRef(sheet, "F84"), "反贪污政策传达与培训 - 其他员工 - 一般员工 - 接受反贪污培训的人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingHourOfNormalStaff", ExcelUtils.getCellValueByRef(sheet, "G84"), "反贪污政策传达与培训 - 其他员工 - 一般员工 - 总培训时数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "antiCorruptionTrainingRemarkOfNormalStaff", ExcelUtils.getCellValueByRef(sheet, "J84"), "反贪污政策传达与培训 - 其他员工 - 一般员工 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfHighLevelM", ExcelUtils.getCellValueByRef(sheet, "D86"), "接受定期绩效及职业发展检视的人数 - 男性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfMiddleLevelM", ExcelUtils.getCellValueByRef(sheet, "E86"), "接受定期绩效及职业发展检视的人数 - 男性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfBaseLevelM", ExcelUtils.getCellValueByRef(sheet, "F86"), "接受定期绩效及职业发展检视的人数 - 男性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfNormalStaffM", ExcelUtils.getCellValueByRef(sheet, "G86"), "接受定期绩效及职业发展检视的人数 - 男性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoRemarkM", ExcelUtils.getCellValueByRef(sheet, "J86"), "接受定期绩效及职业发展检视的人数 - 男性 - 备注"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfHighLevelF", ExcelUtils.getCellValueByRef(sheet, "D87"), "接受定期绩效及职业发展检视的人数 - 女性 - 高层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfMiddleLevelF", ExcelUtils.getCellValueByRef(sheet, "E87"), "接受定期绩效及职业发展检视的人数 - 女性 - 中层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfBaseLevelF", ExcelUtils.getCellValueByRef(sheet, "F87"), "接受定期绩效及职业发展检视的人数 - 女性 - 基层"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoOfNormalStaffF", ExcelUtils.getCellValueByRef(sheet, "G87"), "接受定期绩效及职业发展检视的人数 - 女性 - 一般员工"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "regularKpiStaffNoRemarkF", ExcelUtils.getCellValueByRef(sheet, "J87"), "接受定期绩效及职业发展检视的人数 - 女性 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "kpiAndSalaryRelatedStaffNo", ExcelUtils.getCellValueByRef(sheet, "C89"), "个人绩效评估与薪酬待遇挂钩 - 绩效考核与待遇挂钩的月薪制员工人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "kpiAndSalaryRelatedStaffNoRemark", ExcelUtils.getCellValueByRef(sheet, "J89"), "个人绩效评估与薪酬待遇挂钩 - 绩效考核与待遇挂钩的月薪制员工人数 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentArea1", ExcelUtils.getCellValueByRef(sheet, "C91"), "社区投资 - 贡献领域 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentType1", ExcelUtils.getCellValueByRef(sheet, "D91"), "社区投资 - 项目形式 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentProjectTitleAndTarget1", ExcelUtils.getCellValueByRef(sheet, "E91"), "社区投资 - 项目名称及受惠对象 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentStaffNo1", ExcelUtils.getCellValueByRef(sheet, "F91"), "社区投资 - 义工人次 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentHour1", ExcelUtils.getCellValueByRef(sheet, "G91"), "社区投资 - 义工时数（小时） 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentInvestedAmount1", ExcelUtils.getCellValueByRef(sheet, "H91"), "社区投资 - 投资金额（港币） 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestment1", ExcelUtils.getCellValueByRef(sheet, "J91"), "社区投资 - 备注 1"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentArea2", ExcelUtils.getCellValueByRef(sheet, "C92"), "社区投资 - 贡献领域 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentType2", ExcelUtils.getCellValueByRef(sheet, "D92"), "社区投资 - 项目形式 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentProjectTitleAndTarget2", ExcelUtils.getCellValueByRef(sheet, "E92"), "社区投资 - 项目名称及受惠对象 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentStaffNo2", ExcelUtils.getCellValueByRef(sheet, "F92"), "社区投资 - 义工人次 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentHour2", ExcelUtils.getCellValueByRef(sheet, "G92"), "社区投资 - 义工时数（小时） 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentInvestedAmount2", ExcelUtils.getCellValueByRef(sheet, "H92"), "社区投资 - 投资金额（港币） 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestment2", ExcelUtils.getCellValueByRef(sheet, "J92"), "社区投资 - 备注 2"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentArea3", ExcelUtils.getCellValueByRef(sheet, "C93"), "社区投资 - 贡献领域 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentType3", ExcelUtils.getCellValueByRef(sheet, "D93"), "社区投资 - 项目形式 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentProjectTitleAndTarget3", ExcelUtils.getCellValueByRef(sheet, "E93"), "社区投资 - 项目名称及受惠对象 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentStaffNo3", ExcelUtils.getCellValueByRef(sheet, "F93"), "社区投资 - 义工人次 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentHour3", ExcelUtils.getCellValueByRef(sheet, "G93"), "社区投资 - 义工时数（小时） 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestmentInvestedAmount3", ExcelUtils.getCellValueByRef(sheet, "H93"), "社区投资 - 投资金额（港币） 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "communityInvestment3", ExcelUtils.getCellValueByRef(sheet, "J93"), "社区投资 - 备注 3"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "reservedSubsidizedApprenticeNo", ExcelUtils.getCellValueByRef(sheet, "C95"), "弱势群体及年轻人就业机会 - 资助学徒进修预留名额"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "actualSubsidizedApprenticeNo", ExcelUtils.getCellValueByRef(sheet, "D95"), "弱势群体及年轻人就业机会 - 实际资助学徒进修人数"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "subsidizedApprenticeRemark", ExcelUtils.getCellValueByRef(sheet, "J95"), "弱势群体及年轻人就业机会 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_1", ExcelUtils.getCellValueByRef(sheet, "C97"), "合规性 - 与雇佣相关的违法或违规个案数目 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_2", ExcelUtils.getCellValueByRef(sheet, "D97"), "合规性 - 与雇佣相关的违法或违规个案数目 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_3", ExcelUtils.getCellValueByRef(sheet, "E97"), "合规性 - 与雇佣相关的违法或违规个案数目 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_4", ExcelUtils.getCellValueByRef(sheet, "F97"), "合规性 - 与雇佣相关的违法或违规个案数目 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_5", ExcelUtils.getCellValueByRef(sheet, "G97"), "合规性 - 与雇佣相关的违法或违规个案数目, 与雇佣相关的违法或违规个案，所引致之行政处罚 - 有重大影响的相关法律及规例"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_6", ExcelUtils.getCellValueByRef(sheet, "H97"), "合规性 - 与雇佣相关的违法或违规个案数目, 与雇佣相关的违法或违规个案，所引致之行政处罚 - 相关法律及规例对公司的影响"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_7", ExcelUtils.getCellValueByRef(sheet, "J97"), "合规性 - 与雇佣相关的违法或违规个案数目 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_1", ExcelUtils.getCellValueByRef(sheet, "C98"), "合规性 - 与雇佣相关的违法或违规个案，所引致之行政处罚 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_2", ExcelUtils.getCellValueByRef(sheet, "D98"), "合规性 - 与雇佣相关的违法或违规个案，所引致之行政处罚 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_3", ExcelUtils.getCellValueByRef(sheet, "E98"), "合规性 - 与雇佣相关的违法或违规个案，所引致之行政处罚 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_4", ExcelUtils.getCellValueByRef(sheet, "F98"), "合规性 - 与雇佣相关的违法或违规个案，所引致之行政处罚 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_5", ExcelUtils.getCellValueByRef(sheet, "J98"), "合规性 - 与雇佣相关的违法或违规个案，所引致之行政处罚 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_1", ExcelUtils.getCellValueByRef(sheet, "C99"), "合规性 - 与健康与安全相关的违法或违规个案数目 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_2", ExcelUtils.getCellValueByRef(sheet, "D99"), "合规性 - 与健康与安全相关的违法或违规个案数目 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_3", ExcelUtils.getCellValueByRef(sheet, "E99"), "合规性 - 与健康与安全相关的违法或违规个案数目 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_4", ExcelUtils.getCellValueByRef(sheet, "F99"), "合规性 - 与健康与安全相关的违法或违规个案数目 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_5", ExcelUtils.getCellValueByRef(sheet, "G99"), "合规性 - 与健康与安全相关的违法或违规个案数目, 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 有重大影响的相关法律及规例"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_6", ExcelUtils.getCellValueByRef(sheet, "H99"), "合规性 - 与健康与安全相关的违法或违规个案数目, 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 相关法律及规例对公司的影响"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_7", ExcelUtils.getCellValueByRef(sheet, "J99"), "合规性 - 与健康与安全相关的违法或违规个案数目 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_1", ExcelUtils.getCellValueByRef(sheet, "C100"), "合规性 - 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_2", ExcelUtils.getCellValueByRef(sheet, "D100"), "合规性 - 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_3", ExcelUtils.getCellValueByRef(sheet, "E100"), "合规性 - 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_4", ExcelUtils.getCellValueByRef(sheet, "F100"), "合规性 - 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_5", ExcelUtils.getCellValueByRef(sheet, "J100"), "合规性 - 与健康与安全相关的违法或违规个案，所引致之行政处罚 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_1", ExcelUtils.getCellValueByRef(sheet, "C101"), "合规性 - 与劳工准则相关的违法或违规个案数目 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_2", ExcelUtils.getCellValueByRef(sheet, "D101"), "合规性 - 与劳工准则相关的违法或违规个案数目 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_3", ExcelUtils.getCellValueByRef(sheet, "E101"), "合规性 - 与劳工准则相关的违法或违规个案数目 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_4", ExcelUtils.getCellValueByRef(sheet, "F101"), "合规性 - 与劳工准则相关的违法或违规个案数目 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_5", ExcelUtils.getCellValueByRef(sheet, "G101"), "合规性 - 与劳工准则相关的违法或违规个案数目, 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 有重大影响的相关法律及规例"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_6", ExcelUtils.getCellValueByRef(sheet, "H101"), "合规性 - 与劳工准则相关的违法或违规个案数目, 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 相关法律及规例对公司的影响"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_7", ExcelUtils.getCellValueByRef(sheet, "J101"), "合规性 - 与劳工准则相关的违法或违规个案数目 - 备注"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_1", ExcelUtils.getCellValueByRef(sheet, "C102"), "合规性 - 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 合规情况"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_2", ExcelUtils.getCellValueByRef(sheet, "D102"), "合规性 - 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 补充说明"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_3", ExcelUtils.getCellValueByRef(sheet, "E102"), "合规性 - 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 单位"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_4", ExcelUtils.getCellValueByRef(sheet, "F102"), "合规性 - 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 数目"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_5", ExcelUtils.getCellValueByRef(sheet, "J102"), "合规性 - 与劳工准则相关的违法或违规个案，所引致之行政处罚 - 备注"));
			
			dynamicFormVO.setLstDetail(lstDynamicFormDetailVO);
			
			workbook.close();
			return dynamicFormVO;
		} catch (IOException e) {
			throw new RuntimeException("fail to parse Excel file: " + e.getMessage());
		}
	}

	/**
	 * 社會積效一 数据轉換成Excel
	 *
	 * @param dynamicFormVO
	 * @return
	 */
	private ByteArrayInputStream dataToSociologyOneExcel(DynamicFormVO dynamicFormVO) {
		Map<String, DynamicFormDetailVO> mapDynamicFormDetailVO = dynamicFormVO.getLstDetail().stream().collect(
                Collectors.toMap(DynamicFormDetailVO::getCode, vo -> vo));
		
		try (ByteArrayOutputStream out = new ByteArrayOutputStream();
			Workbook workbook = WorkbookFactory.create(resourceLoader.getResource("classpath:template/sociology_one_template.xlsx").getFile())) {
			Sheet sheet = workbook.getSheetAt(0);
			ExcelUtils.setCellValueByRef(sheet, "D3", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeBelow30M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E3", mapDynamicFormDetailVO.getOrDefault("staffNoByAge31To40M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F3", mapDynamicFormDetailVO.getOrDefault("staffNoByAge41low50M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G3", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeAbove51M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J3", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D4", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeBelow30F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E4", mapDynamicFormDetailVO.getOrDefault("staffNoByAge31To40F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F4", mapDynamicFormDetailVO.getOrDefault("staffNoByAge41low50F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G4", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeAbove51F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J4", mapDynamicFormDetailVO.getOrDefault("staffNoByAgeRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D6", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelHighM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E6", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelMiddleM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F6", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelBaseM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G6", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J6", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D7", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelHighF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E7", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelMiddleF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F7", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelBaseF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G7", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J7", mapDynamicFormDetailVO.getOrDefault("staffNoByLevelRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D9", mapDynamicFormDetailVO.getOrDefault("staffNoByContractPermanentM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E9", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTemporaryM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J9", mapDynamicFormDetailVO.getOrDefault("staffNoByContractRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D10", mapDynamicFormDetailVO.getOrDefault("staffNoByContractPermanentF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E10", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTemporaryF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J10", mapDynamicFormDetailVO.getOrDefault("staffNoByContractRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D12", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypeFullTimeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E12", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypePartTimeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J12", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypeRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D13", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypeFullTimeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E13", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypePartTimeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J13", mapDynamicFormDetailVO.getOrDefault("staffNoByContractTypeRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D15", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeBelow30M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E15", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAge31To40M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F15", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAge41low50M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G15", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeAbove51M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J15", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D16", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeBelow30F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E16", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAge31To40F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F16", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAge41low50F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G16", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeAbove51F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J16", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByAgeRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D18", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelHighM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E18", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelMiddleM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F18", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelBaseM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G18", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J18", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D19", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelHighF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E19", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelMiddleF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F19", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelBaseF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G19", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J19", mapDynamicFormDetailVO.getOrDefault("staffNoWithMonthlySalaryByLevelRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionChinaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionHongKongM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionMacaoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionAmericaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionCanadaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "I21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionOtherM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J21", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionChinaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionHongKongF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionMacaoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionAmericaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionCanadaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "I22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionOtherF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J22", mapDynamicFormDetailVO.getOrDefault("staffNoByRegionRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D24", mapDynamicFormDetailVO.getOrDefault("otherWorkerType1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E24", mapDynamicFormDetailVO.getOrDefault("otherWorkerNatureAndArea1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F24", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal1M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J24", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark1M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F25", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal1F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J25", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark1F", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D26", mapDynamicFormDetailVO.getOrDefault("otherWorkerType2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E26", mapDynamicFormDetailVO.getOrDefault("otherWorkerNatureAndArea2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F26", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal2M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J26", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark2M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F27", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal2F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J27", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark2F", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D28", mapDynamicFormDetailVO.getOrDefault("otherWorkerType3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E28", mapDynamicFormDetailVO.getOrDefault("otherWorkerNatureAndArea3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F28", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal3M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J28", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark3M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F29", mapDynamicFormDetailVO.getOrDefault("otherWorkerTotal3F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J29", mapDynamicFormDetailVO.getOrDefault("otherWorkerRemark3F", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D31", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelHighM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E31", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelMiddleM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F31", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelBaseM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G31", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J31", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D32", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelHighF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E32", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelMiddleF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F32", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelBaseF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G32", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J32", mapDynamicFormDetailVO.getOrDefault("totalSalaryByLevelRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D34", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeBelow30M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E34", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAge31To40M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F34", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAge41low50M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G34", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeAbove51M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J34", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D35", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeBelow30F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E35", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAge31To40F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F35", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAge41low50F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G35", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeAbove51F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J35", mapDynamicFormDetailVO.getOrDefault("newStaffNoByAgeRemarkF", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D37", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeBelow30M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E37", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAge31To40M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F37", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAge41low50M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G37", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeAbove51M", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J37", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeRemarkM", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "D38", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeBelow30F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E38", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAge31To40F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F38", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAge41low50F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G38", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeAbove51F", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J38", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByAgeRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionChinaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionHongKongM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionMacaoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionAmericaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionCanadaM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "I40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionOtherM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J40", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionChinaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionHongKongF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionMacaoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionAmericaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionCanadaF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "I41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionOtherF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J41", mapDynamicFormDetailVO.getOrDefault("lossStaffNoByRegionRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C43", mapDynamicFormDetailVO.getOrDefault("minNoticePeriod", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J43", mapDynamicFormDetailVO.getOrDefault("minNoticePeriodRemark", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D45", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffDeathNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E45", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffSeriousConsequencesNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F45", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffRecordableNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G45", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffMainTypeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J45", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D46", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffDeathNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E46", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffSeriousConsequencesNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F46", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffRecordableNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G46", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffMainTypeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J46", mapDynamicFormDetailVO.getOrDefault("workInjuryOfStaffRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D48", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffDeathNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E48", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffRecordableNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F48", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffMainTypeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J48", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D49", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffDeathNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E49", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffRecordableNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F49", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffMainTypeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J49", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfStaffRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D51", mapDynamicFormDetailVO.getOrDefault("lossWorkingDayOfStaffDueToWorkInjuryM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E51", mapDynamicFormDetailVO.getOrDefault("absenceDayOfStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F51", mapDynamicFormDetailVO.getOrDefault("originalWorkingDayOfStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G51", mapDynamicFormDetailVO.getOrDefault("totalWorkingHourOfStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J51", mapDynamicFormDetailVO.getOrDefault("workingDayOfStaffRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D52", mapDynamicFormDetailVO.getOrDefault("lossWorkingDayOfStaffDueToWorkInjuryF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E52", mapDynamicFormDetailVO.getOrDefault("absenceDayOfStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F52", mapDynamicFormDetailVO.getOrDefault("originalWorkingDayOfStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G52", mapDynamicFormDetailVO.getOrDefault("totalWorkingHourOfStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J52", mapDynamicFormDetailVO.getOrDefault("workingDayOfStaffRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D54", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersDeathNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E54", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersSeriousConsequencesNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F54", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersRecordableNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G54", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersMainTypeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J54", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D55", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersDeathNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E55", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersSeriousConsequencesNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F55", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersRecordableNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G55", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersMainTypeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J55", mapDynamicFormDetailVO.getOrDefault("workInjuryOfOthersRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D57", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersDeathNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E57", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersRecordableNoM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F57", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersMainTypeM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J57", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D58", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersDeathNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E58", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersRecordableNoF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F58", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersMainTypeF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J58", mapDynamicFormDetailVO.getOrDefault("occupationalDiseaseOfOthersRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D60", mapDynamicFormDetailVO.getOrDefault("lossWorkingDayOfOthersDueToWorkInjuryM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E60", mapDynamicFormDetailVO.getOrDefault("absenceDayOfOthersM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F60", mapDynamicFormDetailVO.getOrDefault("originalWorkingDayOfOthersM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G60", mapDynamicFormDetailVO.getOrDefault("totalWorkingHourOfOthersM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J60", mapDynamicFormDetailVO.getOrDefault("workingDayOfOthersRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D61", mapDynamicFormDetailVO.getOrDefault("lossWorkingDayOfOthersDueToWorkInjuryF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E61", mapDynamicFormDetailVO.getOrDefault("absenceDayOfOthersF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F61", mapDynamicFormDetailVO.getOrDefault("originalWorkingDayOfOthersF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G61", mapDynamicFormDetailVO.getOrDefault("totalWorkingHourOfOthersF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J61", mapDynamicFormDetailVO.getOrDefault("workingDayOfOthersRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C63", mapDynamicFormDetailVO.getOrDefault("trainingTopic1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D63", mapDynamicFormDetailVO.getOrDefault("trainingGoal1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E63", mapDynamicFormDetailVO.getOrDefault("trainingContent1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F63", mapDynamicFormDetailVO.getOrDefault("trainingTarget1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G63", mapDynamicFormDetailVO.getOrDefault("trainingTotal1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J63", mapDynamicFormDetailVO.getOrDefault("trainingRemark1", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C64", mapDynamicFormDetailVO.getOrDefault("trainingTopic2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D64", mapDynamicFormDetailVO.getOrDefault("trainingGoal2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E64", mapDynamicFormDetailVO.getOrDefault("trainingContent2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F64", mapDynamicFormDetailVO.getOrDefault("trainingTarget2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G64", mapDynamicFormDetailVO.getOrDefault("trainingTotal2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J64", mapDynamicFormDetailVO.getOrDefault("traningRemark2", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C65", mapDynamicFormDetailVO.getOrDefault("trainingTopic3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D65", mapDynamicFormDetailVO.getOrDefault("trainingGoal3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E65", mapDynamicFormDetailVO.getOrDefault("trainingContent3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F65", mapDynamicFormDetailVO.getOrDefault("trainingTarget3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G65", mapDynamicFormDetailVO.getOrDefault("trainingTotal3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J65", mapDynamicFormDetailVO.getOrDefault("traningRemark3", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D67", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfHighLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E67", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfMiddleLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F67", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfBaseLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G67", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J67", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D68", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfHighLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E68", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfMiddleLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F68", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfBaseLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G68", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoOfNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J68", mapDynamicFormDetailVO.getOrDefault("trainingPeopleNoRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C70", mapDynamicFormDetailVO.getOrDefault("saftyTrainingStaffNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J70", mapDynamicFormDetailVO.getOrDefault("saftyTrainingStaffNoRemark", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C72", mapDynamicFormDetailVO.getOrDefault("relatedNewStaffNoOfLowCarbonTraining", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D72", mapDynamicFormDetailVO.getOrDefault("relatedNewStaffNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E70", mapDynamicFormDetailVO.getOrDefault("relatedStaffNoOfLowCarbonTraining", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F70", mapDynamicFormDetailVO.getOrDefault("relatedTotalStaffNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J70", mapDynamicFormDetailVO.getOrDefault("lowCarbonTrainingRemark", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C74", mapDynamicFormDetailVO.getOrDefault("relatedStaffNoOfInnovationTraining", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D74", mapDynamicFormDetailVO.getOrDefault("relatedStaffNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J74", mapDynamicFormDetailVO.getOrDefault("InnovationTrainingRemark", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D76", mapDynamicFormDetailVO.getOrDefault("trainingHourOfHighLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E76", mapDynamicFormDetailVO.getOrDefault("trainingHourOfMiddleLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F76", mapDynamicFormDetailVO.getOrDefault("trainingHourOfBaseLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G76", mapDynamicFormDetailVO.getOrDefault("trainingHourOfNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J76", mapDynamicFormDetailVO.getOrDefault("trainingHourRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D77", mapDynamicFormDetailVO.getOrDefault("trainingHourOfHighLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E77", mapDynamicFormDetailVO.getOrDefault("trainingHourOfMiddleLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F77", mapDynamicFormDetailVO.getOrDefault("trainingHourOfBaseLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G77", mapDynamicFormDetailVO.getOrDefault("trainingHourOfNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J77", mapDynamicFormDetailVO.getOrDefault("trainingHourRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D79", mapDynamicFormDetailVO.getOrDefault("noOfBoardMember", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E79", mapDynamicFormDetailVO.getOrDefault("antiCorruptionInfoNoOfBoardMember", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F79", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingNoOfBoardMember", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G79", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingHourOfBoardMember", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J79", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingRemarkOfBoardMember", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "E81", mapDynamicFormDetailVO.getOrDefault("antiCorruptionInfoStaffNoOfHighLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F81", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingStaffNoOfHighLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G81", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingHourOfHighLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J81", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingRemarkOfHighLevel", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "E82", mapDynamicFormDetailVO.getOrDefault("antiCorruptionInfoStaffNoOfMiddleLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F82", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingStaffNoOfMiddleLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G82", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingHourOfMiddleLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J82", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingRemarkOfMiddleLevel", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "E83", mapDynamicFormDetailVO.getOrDefault("antiCorruptionInfoStaffNoOfBaseLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F83", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingStaffNoOfBaseLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G83", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingHourOfBaseLevel", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J83", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingRemarkOfBaseLevel", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "E84", mapDynamicFormDetailVO.getOrDefault("antiCorruptionInfoStaffNoOfNormalStaff", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F84", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingStaffNoOfNormalStaff", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G84", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingHourOfNormalStaff", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J84", mapDynamicFormDetailVO.getOrDefault("antiCorruptionTrainingRemarkOfNormalStaff", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D86", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfHighLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E86", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfMiddleLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F86", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfBaseLevelM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G86", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfNormalStaffM", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J86", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoRemarkM", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D87", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfHighLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E87", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfMiddleLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F87", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfBaseLevelF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G87", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoOfNormalStaffF", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J87", mapDynamicFormDetailVO.getOrDefault("regularKpiStaffNoRemarkF", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C89", mapDynamicFormDetailVO.getOrDefault("kpiAndSalaryRelatedStaffNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J89", mapDynamicFormDetailVO.getOrDefault("kpiAndSalaryRelatedStaffNoRemark", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentArea1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentType1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentProjectTitleAndTarget1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentStaffNo1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentHour1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H91", mapDynamicFormDetailVO.getOrDefault("communityInvestmentInvestedAmount1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J91", mapDynamicFormDetailVO.getOrDefault("communityInvestment1", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentArea2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentType2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentProjectTitleAndTarget2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentStaffNo2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentHour2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H92", mapDynamicFormDetailVO.getOrDefault("communityInvestmentInvestedAmount2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J92", mapDynamicFormDetailVO.getOrDefault("communityInvestment2", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentArea3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentType3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentProjectTitleAndTarget3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentStaffNo3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentHour3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H93", mapDynamicFormDetailVO.getOrDefault("communityInvestmentInvestedAmount3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J93", mapDynamicFormDetailVO.getOrDefault("communityInvestment3", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C95", mapDynamicFormDetailVO.getOrDefault("reservedSubsidizedApprenticeNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D95", mapDynamicFormDetailVO.getOrDefault("actualSubsidizedApprenticeNo", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J95", mapDynamicFormDetailVO.getOrDefault("subsidizedApprenticeRemark", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C97", mapDynamicFormDetailVO.getOrDefault("compliance_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D97", mapDynamicFormDetailVO.getOrDefault("compliance_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E97", mapDynamicFormDetailVO.getOrDefault("compliance_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F97", mapDynamicFormDetailVO.getOrDefault("compliance_1_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G97", mapDynamicFormDetailVO.getOrDefault("compliance_1_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H97", mapDynamicFormDetailVO.getOrDefault("compliance_1_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J97", mapDynamicFormDetailVO.getOrDefault("compliance_1_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C98", mapDynamicFormDetailVO.getOrDefault("compliance_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D98", mapDynamicFormDetailVO.getOrDefault("compliance_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E98", mapDynamicFormDetailVO.getOrDefault("compliance_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F98", mapDynamicFormDetailVO.getOrDefault("compliance_2_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J98", mapDynamicFormDetailVO.getOrDefault("compliance_2_5", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C99", mapDynamicFormDetailVO.getOrDefault("compliance_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D99", mapDynamicFormDetailVO.getOrDefault("compliance_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E99", mapDynamicFormDetailVO.getOrDefault("compliance_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F99", mapDynamicFormDetailVO.getOrDefault("compliance_3_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G99", mapDynamicFormDetailVO.getOrDefault("compliance_3_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H99", mapDynamicFormDetailVO.getOrDefault("compliance_3_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J99", mapDynamicFormDetailVO.getOrDefault("compliance_3_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C100", mapDynamicFormDetailVO.getOrDefault("compliance_4_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D100", mapDynamicFormDetailVO.getOrDefault("compliance_4_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E100", mapDynamicFormDetailVO.getOrDefault("compliance_4_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F100", mapDynamicFormDetailVO.getOrDefault("compliance_4_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J100", mapDynamicFormDetailVO.getOrDefault("compliance_4_5", new DynamicFormDetailVO()).getValue());
			
			ExcelUtils.setCellValueByRef(sheet, "C101", mapDynamicFormDetailVO.getOrDefault("compliance_5_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D101", mapDynamicFormDetailVO.getOrDefault("compliance_5_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E101", mapDynamicFormDetailVO.getOrDefault("compliance_5_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F101", mapDynamicFormDetailVO.getOrDefault("compliance_5_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G101", mapDynamicFormDetailVO.getOrDefault("compliance_5_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H101", mapDynamicFormDetailVO.getOrDefault("compliance_5_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J101", mapDynamicFormDetailVO.getOrDefault("compliance_5_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C102", mapDynamicFormDetailVO.getOrDefault("compliance_6_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D102", mapDynamicFormDetailVO.getOrDefault("compliance_6_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E102", mapDynamicFormDetailVO.getOrDefault("compliance_6_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F102", mapDynamicFormDetailVO.getOrDefault("compliance_6_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J102", mapDynamicFormDetailVO.getOrDefault("compliance_6_5", new DynamicFormDetailVO()).getValue());
			
			workbook.write(out);
			return new ByteArrayInputStream(out.toByteArray());
		} catch (Exception e) {
			throw new RuntimeException("fail to import data to Excel file: " + e.getMessage());
		}
	}
	
	/**
	 * 社會積效二 數據導入
	 *
	 * @param file, dynamicFormHead
	 * @return
	 */
	public DynamicFormVO importSociologyTwoExcel(MultipartFile file, DynamicFormHead dynamicFormHead) {
		dynamicFormHead.setIsDeleted(false);
		dynamicFormHead.setIsSubmitted(false);
		DynamicFormVO dynamicFormVO = this.excelToSociologyTwoData(file, dynamicFormHead);
		String id = dynamicFormService.saveDynamicForm(dynamicFormVO);
		dynamicFormVO.setId(id);
		return dynamicFormVO;
	}

	/**
	 * 社會積效二 數據導出
	 *
	 * @param dynamicFormExportQO
	 * @return
	 */
	public ByteArrayInputStream exportSociologyTwoExcel(DynamicFormExportQO dynamicFormExportQO) {
		DynamicFormVO dynamicFormVO = dynamicFormService.getDynamicForm(dynamicFormExportQO.getDynamicFormQO());
		if(dynamicFormVO == null) throw new ServiceException("找不到數據");
		return this.dataToSociologyTwoExcel(dynamicFormVO);
	}

	/**
	 * 社會積效二 Excel轉換成数据
	 *
	 * @param file, dynamicFormHead
	 * @return
	 */
	private DynamicFormVO excelToSociologyTwoData(MultipartFile file, DynamicFormHead dynamicFormHead) {
        
		try (InputStream is = file.getInputStream();
                Workbook workbook = ExcelUtils.getWorkbookFromUrl(is, file.getOriginalFilename())) {
        	
			DynamicFormVO dynamicFormVO = new DynamicFormVO();
			BeanUtils.copyProperties(dynamicFormHead, dynamicFormVO);

			Sheet sheet = workbook.getSheetAt(0);
			List<DynamicFormDetailVO> lstDynamicFormDetailVO = new ArrayList<>();

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_1_1", ExcelUtils.getCellValueByRef(sheet, "D3"), "供应商数目 - 提供之产品或服务(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_1_2", ExcelUtils.getCellValueByRef(sheet, "E3"), "供应商数目 - 供应商数目(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_1_3", ExcelUtils.getCellValueByRef(sheet, "F3"), "供应商数目 - 执行相关惯例的供应商百分比(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_1_4", ExcelUtils.getCellValueByRef(sheet, "J3"), "供应商数目 - 备注(中国内地)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_2_1", ExcelUtils.getCellValueByRef(sheet, "D4"), "供应商数目 - 提供之产品或服务(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_2_2", ExcelUtils.getCellValueByRef(sheet, "E4"), "供应商数目 - 供应商数目(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_2_3", ExcelUtils.getCellValueByRef(sheet, "F4"), "供应商数目 - 执行相关惯例的供应商百分比(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_2_4", ExcelUtils.getCellValueByRef(sheet, "J4"), "供应商数目 - 备注(香港)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_3_1", ExcelUtils.getCellValueByRef(sheet, "D5"), "供应商数目 - 提供之产品或服务(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_3_2", ExcelUtils.getCellValueByRef(sheet, "E5"), "供应商数目 - 供应商数目(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_3_3", ExcelUtils.getCellValueByRef(sheet, "F5"), "供应商数目 - 执行相关惯例的供应商百分比(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_3_4", ExcelUtils.getCellValueByRef(sheet, "J5"), "供应商数目 - 备注(澳门)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_4_1", ExcelUtils.getCellValueByRef(sheet, "D6"), "供应商数目 - 提供之产品或服务(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_4_2", ExcelUtils.getCellValueByRef(sheet, "E6"), "供应商数目 - 供应商数目(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_4_3", ExcelUtils.getCellValueByRef(sheet, "F6"), "供应商数目 - 执行相关惯例的供应商百分比(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "supplier_number_4_4", ExcelUtils.getCellValueByRef(sheet, "J6"), "供应商数目 - 备注(其他（请注明）)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_1_1", ExcelUtils.getCellValueByRef(sheet, "D8"), "童工及强制劳工 - 存在使用童工风险的运营点和供应商数量(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_1_2", ExcelUtils.getCellValueByRef(sheet, "E8"), "童工及强制劳工 - 存在使用年轻工作者从事危险工作风险的运营点和供应商数量(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_1_3", ExcelUtils.getCellValueByRef(sheet, "F8"), "童工及强制劳工 - 存在强制劳工风险的运营点和供应商数量(中国内地)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_1_4", ExcelUtils.getCellValueByRef(sheet, "J8"), "童工及强制劳工 - 备注(中国内地)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_2_1", ExcelUtils.getCellValueByRef(sheet, "D9"), "童工及强制劳工 - 存在使用童工风险的运营点和供应商数量(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_2_2", ExcelUtils.getCellValueByRef(sheet, "E9"), "童工及强制劳工 - 存在使用年轻工作者从事危险工作风险的运营点和供应商数量(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_2_3", ExcelUtils.getCellValueByRef(sheet, "F9"), "童工及强制劳工 - 存在强制劳工风险的运营点和供应商数量(香港)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_2_4", ExcelUtils.getCellValueByRef(sheet, "J9"), "童工及强制劳工 - 备注(香港)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_3_1", ExcelUtils.getCellValueByRef(sheet, "D10"), "童工及强制劳工 - 存在使用童工风险的运营点和供应商数量(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_3_2", ExcelUtils.getCellValueByRef(sheet, "E10"), "童工及强制劳工 - 存在使用年轻工作者从事危险工作风险的运营点和供应商数量(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_3_3", ExcelUtils.getCellValueByRef(sheet, "F10"), "童工及强制劳工 - 存在强制劳工风险的运营点和供应商数量(澳门)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_3_4", ExcelUtils.getCellValueByRef(sheet, "J10"), "童工及强制劳工 - 备注(澳门)"));
			
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_4_1", ExcelUtils.getCellValueByRef(sheet, "D11"), "童工及强制劳工 - 存在使用童工风险的运营点和供应商数量(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_4_2", ExcelUtils.getCellValueByRef(sheet, "E11"), "童工及强制劳工 - 存在使用年轻工作者从事危险工作风险的运营点和供应商数量(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_4_3", ExcelUtils.getCellValueByRef(sheet, "F11"), "童工及强制劳工 - 存在强制劳工风险的运营点和供应商数量(其他（请注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "child_and_compulsory_labour_4_4", ExcelUtils.getCellValueByRef(sheet, "J11"), "童工及强制劳工 - 备注(其他（请注明）)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_1_1", ExcelUtils.getCellValueByRef(sheet, "C13"), "反贪污政策传达与培训 - 业务合作伙伴(供应商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_1_2", ExcelUtils.getCellValueByRef(sheet, "E13"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(供应商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_1_3", ExcelUtils.getCellValueByRef(sheet, "F13"), "反贪污政策传达与培训 - 业务合作伙伴总数(供应商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_1_4", ExcelUtils.getCellValueByRef(sheet, "J13"), "反贪污政策传达与培训 - 备注(供应商)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_2_1", ExcelUtils.getCellValueByRef(sheet, "C14"), "反贪污政策传达与培训 - 业务合作伙伴(代理商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_2_2", ExcelUtils.getCellValueByRef(sheet, "E14"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(代理商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_2_3", ExcelUtils.getCellValueByRef(sheet, "F14"), "反贪污政策传达与培训 - 业务合作伙伴总数(代理商)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_2_4", ExcelUtils.getCellValueByRef(sheet, "J14"), "反贪污政策传达与培训 - 备注(代理商)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_3_1", ExcelUtils.getCellValueByRef(sheet, "C15"), "反贪污政策传达与培训 - 业务合作伙伴(合资企业)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_3_2", ExcelUtils.getCellValueByRef(sheet, "E15"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(合资企业)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_3_3", ExcelUtils.getCellValueByRef(sheet, "F15"), "反贪污政策传达与培训 - 业务合作伙伴总数(合资企业)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_3_4", ExcelUtils.getCellValueByRef(sheet, "J15"), "反贪污政策传达与培训 - 备注(合资企业)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_4_1", ExcelUtils.getCellValueByRef(sheet, "C16"), "反贪污政策传达与培训 - 业务合作伙伴(政府)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_4_2", ExcelUtils.getCellValueByRef(sheet, "E16"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(政府)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_4_3", ExcelUtils.getCellValueByRef(sheet, "F16"), "反贪污政策传达与培训 - 业务合作伙伴总数(政府)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_4_4", ExcelUtils.getCellValueByRef(sheet, "J16"), "反贪污政策传达与培训 - 备注(政府)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_5_1", ExcelUtils.getCellValueByRef(sheet, "C17"), "反贪污政策传达与培训 - 业务合作伙伴(客户)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_5_2", ExcelUtils.getCellValueByRef(sheet, "E17"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(客户)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_5_3", ExcelUtils.getCellValueByRef(sheet, "F17"), "反贪污政策传达与培训 - 业务合作伙伴总数(客户)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_5_4", ExcelUtils.getCellValueByRef(sheet, "J17"), "反贪污政策传达与培训 - 备注(客户)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_6_1", ExcelUtils.getCellValueByRef(sheet, "C18"), "反贪污政策传达与培训 - 业务合作伙伴(其他（请在备注注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_6_2", ExcelUtils.getCellValueByRef(sheet, "E18"), "反贪污政策传达与培训 - 接受反贪污政策和程序信息传达的伙伴数量(其他（请在备注注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_6_3", ExcelUtils.getCellValueByRef(sheet, "F18"), "反贪污政策传达与培训 - 业务合作伙伴总数(其他（请在备注注明）)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "anti_corruption_policy_communication_and_training_6_4", ExcelUtils.getCellValueByRef(sheet, "J18"), "反贪污政策传达与培训 - 备注(其他（请在备注注明）)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_1", ExcelUtils.getCellValueByRef(sheet, "C20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品类别 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_2", ExcelUtils.getCellValueByRef(sheet, "D20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收原因 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_3", ExcelUtils.getCellValueByRef(sheet, "E20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收次数 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_4", ExcelUtils.getCellValueByRef(sheet, "F20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收产品数目 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_5", ExcelUtils.getCellValueByRef(sheet, "G20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 同类型已售或已运送产品数量 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_6", ExcelUtils.getCellValueByRef(sheet, "H20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品单位 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_1_7", ExcelUtils.getCellValueByRef(sheet, "J20"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 备注 1"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_1", ExcelUtils.getCellValueByRef(sheet, "C21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品类别 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_2", ExcelUtils.getCellValueByRef(sheet, "D21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收原因 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_3", ExcelUtils.getCellValueByRef(sheet, "E21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收次数 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_4", ExcelUtils.getCellValueByRef(sheet, "F21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收产品数目 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_5", ExcelUtils.getCellValueByRef(sheet, "G21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 同类型已售或已运送产品数量 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_6", ExcelUtils.getCellValueByRef(sheet, "H21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品单位 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_2_7", ExcelUtils.getCellValueByRef(sheet, "J21"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 备注 2"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_1", ExcelUtils.getCellValueByRef(sheet, "C22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品类别 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_2", ExcelUtils.getCellValueByRef(sheet, "D22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收原因 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_3", ExcelUtils.getCellValueByRef(sheet, "E22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收次数 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_4", ExcelUtils.getCellValueByRef(sheet, "F22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 回收产品数目 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_5", ExcelUtils.getCellValueByRef(sheet, "G22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 同类型已售或已运送产品数量 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_6", ExcelUtils.getCellValueByRef(sheet, "H22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 产品单位 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "recycle_3_7", ExcelUtils.getCellValueByRef(sheet, "J22"), "已售或已运送产品总数中，因安全与健康理由而须回收的个案。 - 备注 3"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_1_1", ExcelUtils.getCellValueByRef(sheet, "C24"), "关于产品及客户的投诉数目。 - 涉及的产品或服务 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_1_2", ExcelUtils.getCellValueByRef(sheet, "D24"), "关于产品及客户的投诉数目。 - 投诉类别 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_1_3", ExcelUtils.getCellValueByRef(sheet, "E24"), "关于产品及客户的投诉数目。 - 投诉个案数目 1"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_1_4", ExcelUtils.getCellValueByRef(sheet, "J24"), "关于产品及客户的投诉数目。 - 备注 1"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_2_1", ExcelUtils.getCellValueByRef(sheet, "C25"), "关于产品及客户的投诉数目。 - 涉及的产品或服务 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_2_2", ExcelUtils.getCellValueByRef(sheet, "D25"), "关于产品及客户的投诉数目。 - 投诉类别 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_2_3", ExcelUtils.getCellValueByRef(sheet, "E25"), "关于产品及客户的投诉数目。 - 投诉个案数目 2"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_2_4", ExcelUtils.getCellValueByRef(sheet, "J25"), "关于产品及客户的投诉数目。 - 备注 2"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_3_1", ExcelUtils.getCellValueByRef(sheet, "C26"), "关于产品及客户的投诉数目。 - 涉及的产品或服务 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_3_2", ExcelUtils.getCellValueByRef(sheet, "D26"), "关于产品及客户的投诉数目。 - 投诉类别 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_3_3", ExcelUtils.getCellValueByRef(sheet, "E26"), "关于产品及客户的投诉数目。 - 投诉个案数目 3"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "report_number_3_4", ExcelUtils.getCellValueByRef(sheet, "J26"), "关于产品及客户的投诉数目。 - 备注 3"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_1", ExcelUtils.getCellValueByRef(sheet, "C28"), "合规性 - 合规情况(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_2", ExcelUtils.getCellValueByRef(sheet, "D28"), "合规性 - 补充说明(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_3", ExcelUtils.getCellValueByRef(sheet, "E28"), "合规性 - 单位(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_4", ExcelUtils.getCellValueByRef(sheet, "F28"), "合规性 - 数目(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_5", ExcelUtils.getCellValueByRef(sheet, "G28"), "合规性 - 有重大影响的相关法律及规例(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_6", ExcelUtils.getCellValueByRef(sheet, "H28"), "合规性 - 相关法律及规例对公司的影响(与产品责任相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_1_7", ExcelUtils.getCellValueByRef(sheet, "J28"), "合规性 - 备注(与产品责任相关的违法或违规个案数目)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_1", ExcelUtils.getCellValueByRef(sheet, "C29"), "合规性 - 合规情况(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_2", ExcelUtils.getCellValueByRef(sheet, "D29"), "合规性 - 补充说明(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_3", ExcelUtils.getCellValueByRef(sheet, "E29"), "合规性 - 单位(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_4", ExcelUtils.getCellValueByRef(sheet, "F29"), "合规性 - 数目(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_5", ExcelUtils.getCellValueByRef(sheet, "G29"), "合规性 - 有重大影响的相关法律及规例(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_6", ExcelUtils.getCellValueByRef(sheet, "H29"), "合规性 - 相关法律及规例对公司的影响(与产品责任相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_2_7", ExcelUtils.getCellValueByRef(sheet, "J29"), "合规性 - 备注(与产品责任相关的违法或违规个案，所引致之行政处罚)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_1", ExcelUtils.getCellValueByRef(sheet, "C30"), "合规性 - 合规情况(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_2", ExcelUtils.getCellValueByRef(sheet, "D30"), "合规性 - 补充说明(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_3", ExcelUtils.getCellValueByRef(sheet, "E30"), "合规性 - 单位(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_4", ExcelUtils.getCellValueByRef(sheet, "F30"), "合规性 - 数目(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_5", ExcelUtils.getCellValueByRef(sheet, "G30"), "合规性 - 有重大影响的相关法律及规例(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_6", ExcelUtils.getCellValueByRef(sheet, "H30"), "合规性 - 相关法律及规例对公司的影响(与反贪污相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_3_7", ExcelUtils.getCellValueByRef(sheet, "J30"), "合规性 - 备注(与反贪污相关的违法或违规个案数目)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_1", ExcelUtils.getCellValueByRef(sheet, "C31"), "合规性 - 合规情况(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_2", ExcelUtils.getCellValueByRef(sheet, "D31"), "合规性 - 补充说明(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_3", ExcelUtils.getCellValueByRef(sheet, "E31"), "合规性 - 单位(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_4", ExcelUtils.getCellValueByRef(sheet, "F31"), "合规性 - 数目(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_5", ExcelUtils.getCellValueByRef(sheet, "G31"), "合规性 - 有重大影响的相关法律及规例(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_6", ExcelUtils.getCellValueByRef(sheet, "H31"), "合规性 - 相关法律及规例对公司的影响(与反贪污相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_4_7", ExcelUtils.getCellValueByRef(sheet, "J31"), "合规性 - 备注(与反贪污相关的违法或违规个案，所引致之行政处罚)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_1", ExcelUtils.getCellValueByRef(sheet, "C32"), "合规性 - 合规情况(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_2", ExcelUtils.getCellValueByRef(sheet, "D32"), "合规性 - 补充说明(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_3", ExcelUtils.getCellValueByRef(sheet, "E32"), "合规性 - 单位(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_4", ExcelUtils.getCellValueByRef(sheet, "F32"), "合规性 - 数目(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_5", ExcelUtils.getCellValueByRef(sheet, "G32"), "合规性 - 有重大影响的相关法律及规例(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_6", ExcelUtils.getCellValueByRef(sheet, "H32"), "合规性 - 相关法律及规例对公司的影响(与不当竞争相关的违法或违规个案数目)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_5_7", ExcelUtils.getCellValueByRef(sheet, "J32"), "合规性 - 备注(与不当竞争相关的违法或违规个案数目)"));

			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_1", ExcelUtils.getCellValueByRef(sheet, "C33"), "合规性 - 合规情况(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_2", ExcelUtils.getCellValueByRef(sheet, "D33"), "合规性 - 补充说明(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_3", ExcelUtils.getCellValueByRef(sheet, "E33"), "合规性 - 单位(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_4", ExcelUtils.getCellValueByRef(sheet, "F33"), "合规性 - 数目(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_5", ExcelUtils.getCellValueByRef(sheet, "G33"), "合规性 - 有重大影响的相关法律及规例(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_6", ExcelUtils.getCellValueByRef(sheet, "H33"), "合规性 - 相关法律及规例对公司的影响(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));
			lstDynamicFormDetailVO.add(new DynamicFormDetailVO("", "compliance_6_7", ExcelUtils.getCellValueByRef(sheet, "J33"), "合规性 - 备注(与不当竞争相关的违法或违规个案，所引致之行政处罚)"));

			dynamicFormVO.setLstDetail(lstDynamicFormDetailVO);
			
			workbook.close();
			return dynamicFormVO;
		} catch (IOException e) {
			throw new RuntimeException("fail to parse Excel file: " + e.getMessage());
		}
	}

	/**
	 * 社會積效二 数据轉換成Excel
	 *
	 * @param dynamicFormVO
	 * @return
	 */
	private ByteArrayInputStream dataToSociologyTwoExcel(DynamicFormVO dynamicFormVO) {
		Map<String, DynamicFormDetailVO> mapDynamicFormDetailVO = dynamicFormVO.getLstDetail().stream().collect(
                Collectors.toMap(DynamicFormDetailVO::getCode, vo -> vo));
		
		try (ByteArrayOutputStream out = new ByteArrayOutputStream();
			Workbook workbook = WorkbookFactory.create(resourceLoader.getResource("classpath:template/sociology_two_template.xlsx").getFile())) {
			Sheet sheet = workbook.getSheetAt(0);
			ExcelUtils.setCellValueByRef(sheet, "D3", mapDynamicFormDetailVO.getOrDefault("supplier_number_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E3", mapDynamicFormDetailVO.getOrDefault("supplier_number_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F3", mapDynamicFormDetailVO.getOrDefault("supplier_number_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J3", mapDynamicFormDetailVO.getOrDefault("supplier_number_1_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D4", mapDynamicFormDetailVO.getOrDefault("supplier_number_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E4", mapDynamicFormDetailVO.getOrDefault("supplier_number_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F4", mapDynamicFormDetailVO.getOrDefault("supplier_number_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J4", mapDynamicFormDetailVO.getOrDefault("supplier_number_2_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D5", mapDynamicFormDetailVO.getOrDefault("supplier_number_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E5", mapDynamicFormDetailVO.getOrDefault("supplier_number_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F5", mapDynamicFormDetailVO.getOrDefault("supplier_number_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J5", mapDynamicFormDetailVO.getOrDefault("supplier_number_3_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D6", mapDynamicFormDetailVO.getOrDefault("supplier_number_4_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E6", mapDynamicFormDetailVO.getOrDefault("supplier_number_4_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F6", mapDynamicFormDetailVO.getOrDefault("supplier_number_4_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J6", mapDynamicFormDetailVO.getOrDefault("supplier_number_4_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D8", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E8", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F8", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J8", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_1_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D9", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E9", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F9", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J9", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_2_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D10", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E10", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F10", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J10", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_3_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "D11", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_4_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E11", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_4_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F11", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_4_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J11", mapDynamicFormDetailVO.getOrDefault("child_and_compulsory_labour_4_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C13", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E13", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F13", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J13", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_1_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C14", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E14", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F14", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J14", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_2_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C15", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E15", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F15", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J15", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_3_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C16", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_4_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E16", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_4_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F16", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_4_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J16", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_4_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C17", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_5_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E17", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_5_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F17", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_5_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J17", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_5_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C18", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_6_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E18", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_6_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F18", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_6_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J18", mapDynamicFormDetailVO.getOrDefault("anti_corruption_policy_communication_and_training_6_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C20", mapDynamicFormDetailVO.getOrDefault("recycle_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D20", mapDynamicFormDetailVO.getOrDefault("recycle_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E20", mapDynamicFormDetailVO.getOrDefault("recycle_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F20", mapDynamicFormDetailVO.getOrDefault("recycle_1_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G20", mapDynamicFormDetailVO.getOrDefault("recycle_1_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H20", mapDynamicFormDetailVO.getOrDefault("recycle_1_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J20", mapDynamicFormDetailVO.getOrDefault("recycle_1_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C21", mapDynamicFormDetailVO.getOrDefault("recycle_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D21", mapDynamicFormDetailVO.getOrDefault("recycle_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E21", mapDynamicFormDetailVO.getOrDefault("recycle_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F21", mapDynamicFormDetailVO.getOrDefault("recycle_2_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G21", mapDynamicFormDetailVO.getOrDefault("recycle_2_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H21", mapDynamicFormDetailVO.getOrDefault("recycle_2_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J21", mapDynamicFormDetailVO.getOrDefault("recycle_2_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C22", mapDynamicFormDetailVO.getOrDefault("recycle_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D22", mapDynamicFormDetailVO.getOrDefault("recycle_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E22", mapDynamicFormDetailVO.getOrDefault("recycle_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F22", mapDynamicFormDetailVO.getOrDefault("recycle_3_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G22", mapDynamicFormDetailVO.getOrDefault("recycle_3_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H22", mapDynamicFormDetailVO.getOrDefault("recycle_3_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J22", mapDynamicFormDetailVO.getOrDefault("recycle_3_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C24", mapDynamicFormDetailVO.getOrDefault("report_number_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D24", mapDynamicFormDetailVO.getOrDefault("report_number_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E24", mapDynamicFormDetailVO.getOrDefault("report_number_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J24", mapDynamicFormDetailVO.getOrDefault("report_number_1_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C25", mapDynamicFormDetailVO.getOrDefault("report_number_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D25", mapDynamicFormDetailVO.getOrDefault("report_number_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E25", mapDynamicFormDetailVO.getOrDefault("report_number_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J25", mapDynamicFormDetailVO.getOrDefault("report_number_2_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C26", mapDynamicFormDetailVO.getOrDefault("report_number_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D26", mapDynamicFormDetailVO.getOrDefault("report_number_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E26", mapDynamicFormDetailVO.getOrDefault("report_number_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J26", mapDynamicFormDetailVO.getOrDefault("report_number_3_4", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C28", mapDynamicFormDetailVO.getOrDefault("compliance_1_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D28", mapDynamicFormDetailVO.getOrDefault("compliance_1_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E28", mapDynamicFormDetailVO.getOrDefault("compliance_1_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F28", mapDynamicFormDetailVO.getOrDefault("compliance_1_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G28", mapDynamicFormDetailVO.getOrDefault("compliance_1_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H28", mapDynamicFormDetailVO.getOrDefault("compliance_1_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J28", mapDynamicFormDetailVO.getOrDefault("compliance_1_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C29", mapDynamicFormDetailVO.getOrDefault("compliance_2_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D29", mapDynamicFormDetailVO.getOrDefault("compliance_2_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E29", mapDynamicFormDetailVO.getOrDefault("compliance_2_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F29", mapDynamicFormDetailVO.getOrDefault("compliance_2_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G29", mapDynamicFormDetailVO.getOrDefault("compliance_2_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H29", mapDynamicFormDetailVO.getOrDefault("compliance_2_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J29", mapDynamicFormDetailVO.getOrDefault("compliance_2_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C30", mapDynamicFormDetailVO.getOrDefault("compliance_3_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D30", mapDynamicFormDetailVO.getOrDefault("compliance_3_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E30", mapDynamicFormDetailVO.getOrDefault("compliance_3_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F30", mapDynamicFormDetailVO.getOrDefault("compliance_3_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G30", mapDynamicFormDetailVO.getOrDefault("compliance_3_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H30", mapDynamicFormDetailVO.getOrDefault("compliance_3_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J30", mapDynamicFormDetailVO.getOrDefault("compliance_3_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C31", mapDynamicFormDetailVO.getOrDefault("compliance_4_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D31", mapDynamicFormDetailVO.getOrDefault("compliance_4_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E31", mapDynamicFormDetailVO.getOrDefault("compliance_4_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F31", mapDynamicFormDetailVO.getOrDefault("compliance_4_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G31", mapDynamicFormDetailVO.getOrDefault("compliance_4_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H31", mapDynamicFormDetailVO.getOrDefault("compliance_4_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J31", mapDynamicFormDetailVO.getOrDefault("compliance_4_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C32", mapDynamicFormDetailVO.getOrDefault("compliance_5_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D32", mapDynamicFormDetailVO.getOrDefault("compliance_5_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E32", mapDynamicFormDetailVO.getOrDefault("compliance_5_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F32", mapDynamicFormDetailVO.getOrDefault("compliance_5_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G32", mapDynamicFormDetailVO.getOrDefault("compliance_5_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H32", mapDynamicFormDetailVO.getOrDefault("compliance_5_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J32", mapDynamicFormDetailVO.getOrDefault("compliance_5_7", new DynamicFormDetailVO()).getValue());

			ExcelUtils.setCellValueByRef(sheet, "C33", mapDynamicFormDetailVO.getOrDefault("compliance_6_1", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "D33", mapDynamicFormDetailVO.getOrDefault("compliance_6_2", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "E33", mapDynamicFormDetailVO.getOrDefault("compliance_6_3", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "F33", mapDynamicFormDetailVO.getOrDefault("compliance_6_4", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "G33", mapDynamicFormDetailVO.getOrDefault("compliance_6_5", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "H33", mapDynamicFormDetailVO.getOrDefault("compliance_6_6", new DynamicFormDetailVO()).getValue());
			ExcelUtils.setCellValueByRef(sheet, "J33", mapDynamicFormDetailVO.getOrDefault("compliance_6_7", new DynamicFormDetailVO()).getValue());
			
			workbook.write(out);
			return new ByteArrayInputStream(out.toByteArray());
		} catch (Exception e) {
			throw new RuntimeException("fail to import data to Excel file: " + e.getMessage());
		}
	}
}
