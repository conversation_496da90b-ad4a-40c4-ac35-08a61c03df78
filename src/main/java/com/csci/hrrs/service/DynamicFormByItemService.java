package com.csci.hrrs.service;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.*;
import com.csci.hrrs.model.*;
import com.csci.hrrs.model.DynamicFormByItemHeadExample;
import com.csci.hrrs.model.DynamicFormByItemHeadExample.Criteria;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.qo.DynamicFormByItemExportQO;
import com.csci.hrrs.qo.DynamicFormByItemPageableQO;
import com.csci.hrrs.qo.DynamicFormByItemQO;
import com.csci.hrrs.util.ExcelUtils;
import com.csci.hrrs.util.IntegerUtils;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;
import com.csci.hrrs.vo.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class DynamicFormByItemService {

    @Autowired
    private AuditNodeService auditNodeService;

    @Autowired
    private UserService userService;

	@Autowired
	private DynamicFormByItemHeadMapper headMapper;

	@Autowired
	private DynamicFormByItemDetailMapper detailMapper;

	public List<DynamicFormByItemHead> selectByExample(DynamicFormByItemHeadExample example) {
		return headMapper.selectByExample(example);
	}

	public List<DynamicFormByItemDetail> selectByExample(DynamicFormByItemDetailExample example) {
		return detailMapper.selectByExample(example);
	}

	/**
	 * 動態表單(按項目) 數據導入
	 *
	 * @param file, dynamicFormByItemHead
	 * @return
	 */
	public DynamicFormByItemVO importExcel(MultipartFile file, DynamicFormByItemHead dynamicFormByItemHead) {
		DynamicFormByItemVO dynamicFormByItemVO = this.excelToData(file, dynamicFormByItemHead);
		String id = this.saveDynamicFormByItemWithStatus(dynamicFormByItemVO);
		dynamicFormByItemVO.setId(id);
		return dynamicFormByItemVO;
	}

	/**
	 * 動態表單(按項目) 數據導出
	 *
	 * @param dynamicFormByItemExportQO
	 * @return
	 */
	public ByteArrayInputStream exportExcel(DynamicFormByItemExportQO dynamicFormByItemExportQO) {
		DynamicFormByItemVO dynamicFormByItemVO = this.getDynamicFormByItem(dynamicFormByItemExportQO.getDynamicFormByItemQO());
		if(dynamicFormByItemVO == null) throw new ServiceException("找不到數據");
		return this.dataToExcel(dynamicFormByItemExportQO.getSheetTitles(), dynamicFormByItemExportQO.getHeaders(), dynamicFormByItemVO);
	}

	/**
	 * 動態表單(按項目) Excel轉換成数据
	 *
	 * @param file, dynamicFormByItemHead
	 * @return
	 */
	private DynamicFormByItemVO excelToData(MultipartFile file, DynamicFormByItemHead dynamicFormByItemHead) {
        
		try (InputStream is = file.getInputStream();
                Workbook workbook = ExcelUtils.getWorkbookFromUrl(is, file.getOriginalFilename())) {
        	
			DynamicFormByItemVO dynamicFormByItemVO = new DynamicFormByItemVO();
			BeanUtils.copyProperties(dynamicFormByItemHead, dynamicFormByItemVO);

			Sheet sheet = workbook.getSheetAt(0);
			
			Row headerRow = sheet.getRow(0);
			Iterator<Cell> hCellsInRow = headerRow.iterator();
			List<String> lstHeader = new ArrayList<>();
			while (hCellsInRow.hasNext()) {
				lstHeader.add(hCellsInRow.next().getStringCellValue());
			}
			
			Iterator<Row> rows = sheet.iterator();
			
			List<DynamicFormByItemDetailVO> lstDynamicFormByItemDetailVO = new ArrayList<>();
			int rowNumber = 0;
			while (rows.hasNext()) {
				Row currentRow = rows.next();
				if (rowNumber == 0) {
					rowNumber++;
					continue;
				}
				Iterator<Cell> cellsInRow = currentRow.iterator();
				
				DynamicFormByItemDetailVO dynamicFormByItemDetailVO = new DynamicFormByItemDetailVO();
				
				int cellIdx = 0;
				while (cellsInRow.hasNext()) {
					Cell currentCell = cellsInRow.next();
					switch (cellIdx) {
					case 0:
						dynamicFormByItemDetailVO.setDisplayName(currentCell.getStringCellValue());
						break;
					case 1:
						dynamicFormByItemDetailVO.setCode(currentCell.getStringCellValue());
						break;
					case 2:
						dynamicFormByItemDetailVO.setColName1(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue1(currentCell.getStringCellValue());
						break;
					case 3:
						dynamicFormByItemDetailVO.setColName2(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue2(currentCell.getStringCellValue());
						break;
					case 4:
						dynamicFormByItemDetailVO.setColName3(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue3(currentCell.getStringCellValue());
						break;
					case 5:
						dynamicFormByItemDetailVO.setColName4(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue4(currentCell.getStringCellValue());
						break;
					case 6:
						dynamicFormByItemDetailVO.setColName5(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue5(currentCell.getStringCellValue());
						break;
					case 7:
						dynamicFormByItemDetailVO.setColName6(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue6(currentCell.getStringCellValue());
						break;
					case 8:
						dynamicFormByItemDetailVO.setColName7(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue7(currentCell.getStringCellValue());
						break;
					case 9:
						dynamicFormByItemDetailVO.setColName8(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue8(currentCell.getStringCellValue());
						break;
					case 10:
						dynamicFormByItemDetailVO.setColName9(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue9(currentCell.getStringCellValue());
						break;
					case 11:
						dynamicFormByItemDetailVO.setColName10(cellIdx < lstHeader.size() ? lstHeader.get(cellIdx) : "");
						dynamicFormByItemDetailVO.setColValue10(currentCell.getStringCellValue());
						break;
					default:
						break;
					}
					cellIdx++;
				}
				lstDynamicFormByItemDetailVO.add(dynamicFormByItemDetailVO);
			}
			dynamicFormByItemVO.setLstDetail(lstDynamicFormByItemDetailVO);
			workbook.close();
			return dynamicFormByItemVO;
		} catch (IOException e) {
			throw new RuntimeException("fail to parse Excel file: " + e.getMessage());
		}
	}

	/**
	 * 動態表單(按項目) 数据轉換成Excel
	 *
	 * @param sheetTitles, headers, dynamicFormByItemVO
	 * @return
	 */
	private ByteArrayInputStream dataToExcel(String[] sheetTitles, String[][] headers, DynamicFormByItemVO dynamicFormByItemVO) {
		String sheetTitle = sheetTitles[0];

		try (Workbook workbook = new XSSFWorkbook(); ByteArrayOutputStream out = new ByteArrayOutputStream();) {
			Sheet sheet = workbook.createSheet(sheetTitle);
			Row headerRow = sheet.createRow(0);
			for (int col = 0; col < headers[0].length; col++) {
				Cell cell = headerRow.createCell(col);
                CellStyle style = workbook.createCellStyle(); 
                style.setWrapText(true); 
                cell.setCellStyle(style); 
				cell.setCellValue(headers[0][col]);
			}
			int rowIdx = 1;
			for (DynamicFormByItemDetailVO detailVO : dynamicFormByItemVO.getLstDetail()) {
				int cellIdx = 0;
				Row row = sheet.createRow(rowIdx++);
				row.createCell(cellIdx++).setCellValue(detailVO.getDisplayName());
				row.createCell(cellIdx++).setCellValue(detailVO.getCode());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue1());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue2());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue3());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue4());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue5());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue6());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue7());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue8());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue9());
				row.createCell(cellIdx++).setCellValue(detailVO.getColValue10());
			}
			ExcelUtils.autoSizeColumns(workbook, 22);
			workbook.write(out);
			return new ByteArrayInputStream(out.toByteArray());
		} catch (IOException e) {
			throw new RuntimeException("fail to import data to Excel file: " + e.getMessage());
		}
	}
	
	public DynamicFormByItemVO convert(SociologyVO sociologyVO) {
		if(sociologyVO == null) return null;
		
		DynamicFormByItemVO resultVO = new DynamicFormByItemVO();
		BeanUtils.copyProperties(sociologyVO, resultVO);
		List<SociologyDetailVO> lstSociologyDetailVO = sociologyVO.getLstDetail();
		
		if(lstSociologyDetailVO == null) throw new ServiceException("SociologyDetailVO 列表為NULL");
		
		List<DynamicFormByItemDetailVO> lstDynamicFormByItemDetailVO = new ArrayList<DynamicFormByItemDetailVO>();
		
		for(SociologyDetailVO sociologyDetailVO : lstSociologyDetailVO) {
			DynamicFormByItemDetailVO dynamicFormByItemDetailVO = new DynamicFormByItemDetailVO();
			
			dynamicFormByItemDetailVO.setColValue1(sociologyDetailVO.getCategory());
			dynamicFormByItemDetailVO.setColValue2(sociologyDetailVO.getReport());
			dynamicFormByItemDetailVO.setColValue3(sociologyDetailVO.getClassification().getClassification1());
			dynamicFormByItemDetailVO.setColValue4(sociologyDetailVO.getClassification().getClassification2());
			dynamicFormByItemDetailVO.setColValue5(sociologyDetailVO.getClassification().getClassification3());
			dynamicFormByItemDetailVO.setColValue6(sociologyDetailVO.getClassification().getClassification4());
			dynamicFormByItemDetailVO.setColValue7(sociologyDetailVO.getClassification().getClassification5());
			dynamicFormByItemDetailVO.setColValue8(sociologyDetailVO.getClassification().getClassification6());
			dynamicFormByItemDetailVO.setColValue9(sociologyDetailVO.getClassification().getClassification7());
			dynamicFormByItemDetailVO.setColValue10(sociologyDetailVO.getDesc());
			
			lstDynamicFormByItemDetailVO.add(dynamicFormByItemDetailVO);
		}
		resultVO.setLstDetail(lstDynamicFormByItemDetailVO);
		
		return resultVO;
	}
	
	public SociologyVO convertToSociologyVO(DynamicFormByItemVO dynamicFormByItemVO) {
		if(dynamicFormByItemVO == null) return null;
		
		SociologyVO resultVO = new SociologyVO();
		BeanUtils.copyProperties(dynamicFormByItemVO, resultVO);
		List<DynamicFormByItemDetailVO> lstDynamicFormByItemDetailVO = dynamicFormByItemVO.getLstDetail();
		
		if(lstDynamicFormByItemDetailVO == null) throw new ServiceException("DynamicFormByItemDetailVO 列表為NULL");
		
		List<SociologyDetailVO> lstSociologyDetailVO = new ArrayList<SociologyDetailVO>();
		
		for(DynamicFormByItemDetailVO dynamicFormByItemDetailVO : lstDynamicFormByItemDetailVO) {
			SociologyDetailVO sociologyDetailVO = new SociologyDetailVO();
			
			sociologyDetailVO.setCategory(dynamicFormByItemDetailVO.getColValue1());
			sociologyDetailVO.setReport(dynamicFormByItemDetailVO.getColValue2());
			SociologyClassificationDetailVO classification = new SociologyClassificationDetailVO();
			classification.setClassification1(dynamicFormByItemDetailVO.getColValue3());
			classification.setClassification2(dynamicFormByItemDetailVO.getColValue4());
			classification.setClassification3(dynamicFormByItemDetailVO.getColValue5());
			classification.setClassification4(dynamicFormByItemDetailVO.getColValue6());
			classification.setClassification5(dynamicFormByItemDetailVO.getColValue7());
			classification.setClassification6(dynamicFormByItemDetailVO.getColValue8());
			classification.setClassification7(dynamicFormByItemDetailVO.getColValue9());
			sociologyDetailVO.setClassification(classification);
			sociologyDetailVO.setDesc(dynamicFormByItemDetailVO.getColValue10());
			
			lstSociologyDetailVO.add(sociologyDetailVO);
		}
		resultVO.setLstDetail(lstSociologyDetailVO);
		
		return resultVO;
	}
	
	public Sociology2VO convertToSociology2VO(DynamicFormByItemVO dynamicFormByItemVO) {
		if(dynamicFormByItemVO == null) return null;
		
		Sociology2VO resultVO = new Sociology2VO();
		BeanUtils.copyProperties(dynamicFormByItemVO, resultVO);
		List<DynamicFormByItemDetailVO> lstDynamicFormByItemDetailVO = dynamicFormByItemVO.getLstDetail();
		
		if(lstDynamicFormByItemDetailVO == null) throw new ServiceException("DynamicFormByItemDetailVO 列表為NULL");
		
		List<SociologyDetail2VO> lstSociologyDetail2VO = new ArrayList<SociologyDetail2VO>();
		
		for(DynamicFormByItemDetailVO dynamicFormByItemDetail2VO : lstDynamicFormByItemDetailVO) {
			SociologyDetail2VO sociologyDetail2VO = new SociologyDetail2VO();
			
			sociologyDetail2VO.setCategory(dynamicFormByItemDetail2VO.getColValue1());
			sociologyDetail2VO.setReport(dynamicFormByItemDetail2VO.getColValue2());
			SociologyClassificationDetail2VO classification = new SociologyClassificationDetail2VO();
			classification.setClassification1(dynamicFormByItemDetail2VO.getColValue3());
			classification.setClassification2(dynamicFormByItemDetail2VO.getColValue4());
			classification.setClassification3(dynamicFormByItemDetail2VO.getColValue5());
			classification.setClassification4(dynamicFormByItemDetail2VO.getColValue6());
			classification.setClassification5(dynamicFormByItemDetail2VO.getColValue7());
			classification.setClassification6(dynamicFormByItemDetail2VO.getColValue8());
			sociologyDetail2VO.setClassification(classification);
			sociologyDetail2VO.setDesc(dynamicFormByItemDetail2VO.getColValue10());
			
			lstSociologyDetail2VO.add(sociologyDetail2VO);
		}
		resultVO.setLstDetail(lstSociologyDetail2VO);
		
		return resultVO;
	}

	/**
	 * 動態表單(按項目) 数据列表
	 *
	 * @param dynamicFormByItemQO
	 * @return
	 */
	public ResultPage<DynamicFormByItemVO> listDynamicFormByItem(DynamicFormByItemPageableQO dynamicFormByItemQO) {
		// 查询头行信息
		DynamicFormByItemHeadExample headExample = new DynamicFormByItemHeadExample();

		Criteria criteria = headExample.or();
		if (StringUtils.isNotBlank(dynamicFormByItemQO.getOrganizationId())) {
			criteria.andOrganizationIdEqualTo(dynamicFormByItemQO.getOrganizationId());
		}
		if (IntegerUtils.isNotNullOrZero(dynamicFormByItemQO.getYear())) {
			criteria.andReportingYearEqualTo(dynamicFormByItemQO.getYear());
		}
		if (IntegerUtils.isNotNullOrZero(dynamicFormByItemQO.getMonth())) {
			criteria.andReportingMonthEqualTo(dynamicFormByItemQO.getMonth());
		}
		if (StringUtils.isNotBlank(dynamicFormByItemQO.getFormCode())) {
			criteria.andFormCodeEqualTo(dynamicFormByItemQO.getFormCode());
		}
		criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");

		PageHelper.startPage(dynamicFormByItemQO.getCurPage(), dynamicFormByItemQO.getPageSize());

		List<DynamicFormByItemHead> lstHead = headMapper.selectByExample(headExample);

		Page<DynamicFormByItemVO> lstResultVO = new Page<>();
		for (DynamicFormByItemHead head : lstHead) {
			DynamicFormByItemVO resultVO = new DynamicFormByItemVO();
			BeanUtils.copyProperties(head, resultVO);
	        
	        AuditNode auditNode = auditNodeService.getAuditNode(head.getId(), head.getFormCode(), head.getAuditNode());
	        User user = userService.getUser(auditNode == null ? "" : auditNode.getUserId());
	        resultVO.setCurrentAuditorUsername(user == null ? "" : user.getUsername());

			// 根据headId查询出对应的明细行，detail表
			DynamicFormByItemDetailExample detailExample = new DynamicFormByItemDetailExample();
			DynamicFormByItemQO qo = new DynamicFormByItemQO();
			DynamicFormByItemDetailExample.Criteria detailCriteria = detailExample.or();
			detailCriteria.andHeadIdEqualTo(head.getId());
			detailExample.setOrderByClause("seq");
			
			List<DynamicFormByItemDetail> lstDetail = detailMapper.selectByExample(detailExample);
			List<DynamicFormByItemDetailVO> lstDetailVO = lstDetail.stream().map(x -> {
				DynamicFormByItemDetailVO vo = new DynamicFormByItemDetailVO();
				BeanUtils.copyProperties(x, vo);
				return vo;
			}).collect(Collectors.toList());

			resultVO.setLstDetail(lstDetailVO);
			if(lstDetailVO.size() > 0) {
				lstResultVO.add(resultVO);
			}
		}
		ResultPage<DynamicFormByItemVO> resultPage = new ResultPage<>(lstHead);
		resultPage.setList(lstResultVO);

		return resultPage;
	}
	

	/**
	 * 動態表單(按項目) 数据
	 *
	 * @param dynamicFormByItemQO
	 * @return
	 */
	public DynamicFormByItemVO getDynamicFormByItem(DynamicFormByItemQO dynamicFormByItemQO) {
		// 查询头行信息
		DynamicFormByItemHeadExample headExample = new DynamicFormByItemHeadExample();

		Criteria criteria = headExample.or();
		if (StringUtils.isNotBlank(dynamicFormByItemQO.getOrganizationId())) {
			criteria.andOrganizationIdEqualTo(dynamicFormByItemQO.getOrganizationId());
		}
		if (IntegerUtils.isNotNullOrZero(dynamicFormByItemQO.getYear())) {
			criteria.andReportingYearEqualTo(dynamicFormByItemQO.getYear());
		}
		if (IntegerUtils.isNotNullOrZero(dynamicFormByItemQO.getMonth())) {
			criteria.andReportingMonthEqualTo(dynamicFormByItemQO.getMonth());
		}
		if (StringUtils.isNotBlank(dynamicFormByItemQO.getFormCode())) {
			criteria.andFormCodeEqualTo(dynamicFormByItemQO.getFormCode());
		}
		criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");

		List<DynamicFormByItemHead> lstHead = headMapper.selectByExample(headExample);

		if (lstHead.size() == 0)
			return null;

		DynamicFormByItemHead head = lstHead.get(0);
		DynamicFormByItemVO resultVO = new DynamicFormByItemVO();
		BeanUtils.copyProperties(head, resultVO);
        
        AuditNode auditNode = auditNodeService.getAuditNode(head.getId(), head.getFormCode(), head.getAuditNode());
        User user = userService.getUser(auditNode == null ? "" : auditNode.getUserId());
        resultVO.setCurrentAuditorUsername(user == null ? "" : user.getUsername());

		// 根据headId查询出对应的明细行，detail表
		DynamicFormByItemDetailExample detailExample = new DynamicFormByItemDetailExample();
		DynamicFormByItemDetailExample.Criteria detailCriteria = detailExample.or();
		detailCriteria.andHeadIdEqualTo(head.getId());
		detailExample.setOrderByClause("seq");
		
		List<DynamicFormByItemDetail> lstDetail = detailMapper.selectByExample(detailExample);
		List<DynamicFormByItemDetailVO> lstDetailVO = lstDetail.stream().map(x -> {
			DynamicFormByItemDetailVO vo = new DynamicFormByItemDetailVO();
			BeanUtils.copyProperties(x, vo);
			return vo;
		}).collect(Collectors.toList());

		resultVO.setLstDetail(lstDetailVO);
		return resultVO;
	}

	/**
	 * 驗證 動態表單(按項目) VO 信息, 如有錯誤返回相關資訊
	 *
	 * @param dynamicFormByItemVO
	 * @return
	 */
	public String checkDynamicFormByItem(DynamicFormByItemVO dynamicFormByItemVO) {
		DynamicFormByItemPageableQO dynamicFormByItemQO = new DynamicFormByItemPageableQO();
		dynamicFormByItemQO.setOrganizationId(dynamicFormByItemVO.getOrganizationId());
		dynamicFormByItemQO.setFormCode(dynamicFormByItemVO.getFormCode());
		dynamicFormByItemQO.setYear(dynamicFormByItemVO.getReportingYear());
		dynamicFormByItemQO.setMonth(dynamicFormByItemVO.getReportingMonth());
		dynamicFormByItemQO.setCurPage(0);
		dynamicFormByItemQO.setPageSize(0);
		ResultPage<DynamicFormByItemVO> resultPage = this.listDynamicFormByItem(dynamicFormByItemQO);

		if (resultPage.getSize() > 0) {
			return "該月份已提交記錄，不能重覆提交。";
		} else {
			return "";
		}
	}

	/**
	 * 提交 動態表單(按項目) VO 信息 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param dynamicFormByItemVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String submitDynamicFormByItem(DynamicFormByItemVO dynamicFormByItemVO) {
		String validMsg = checkDynamicFormByItem(dynamicFormByItemVO);
		if (StringUtils.isNotBlank(validMsg)) {
			throw new ServiceException(validMsg);
		}
		dynamicFormByItemVO.setIsSubmitted(true);
    	dynamicFormByItemVO.setIsAudited(false);
    	dynamicFormByItemVO.setAuditNode(0);
    	dynamicFormByItemVO.setIsDeleted(false);
		return saveDynamicFormByItem(dynamicFormByItemVO);
	}

	/**
	 * 保存 動態表單(按項目) VO 信息 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param dynamicFormByItemVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String saveDynamicFormByItemWithStatus(DynamicFormByItemVO dynamicFormByItemVO) {
		dynamicFormByItemVO.setIsSubmitted(false);
    	dynamicFormByItemVO.setIsAudited(false);
    	dynamicFormByItemVO.setAuditNode(0);
    	dynamicFormByItemVO.setIsDeleted(false);
		return saveDynamicFormByItem(dynamicFormByItemVO);
	}

	/**
	 * 保存 動態表單(按項目) VO 信息 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param dynamicFormByItemVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String saveDynamicFormByItem(DynamicFormByItemVO dynamicFormByItemVO) {
		DynamicFormByItemHead dynamicFormByItemHead = new DynamicFormByItemHead();
		BeanUtils.copyProperties(dynamicFormByItemVO, dynamicFormByItemHead);
		String headId = saveDynamicFormByItemHead(dynamicFormByItemHead);

		List<DynamicFormByItemDetail> lstDetail = dynamicFormByItemVO.getLstDetail().stream().map(vo -> {
			DynamicFormByItemDetail x = new DynamicFormByItemDetail();
			BeanUtils.copyProperties(vo, x);
			x.setHeadId(headId);
			return x;
		}).collect(Collectors.toList());
        int seq = 0;
		for (DynamicFormByItemDetail dynamicFormByItemDetail1 : lstDetail) {
			dynamicFormByItemDetail1.setSeq(++seq);
			saveDynamicFormByItemDetail1(dynamicFormByItemDetail1);
		}

		return headId;
	}

	/**
	 * 保存 動態表單(按項目) 头行信息, 如果是已经存在的记录就保存，否则新增 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param dynamicFormByItemHead
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String saveDynamicFormByItemHead(DynamicFormByItemHead dynamicFormByItemHead) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(dynamicFormByItemHead.getId())) {
        	dynamicFormByItemHead.setCreationTime(now);
        	dynamicFormByItemHead.setCreateUsername(currentUser.getUsername());
        	dynamicFormByItemHead.setLastUpdateTime(now);
        	dynamicFormByItemHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.insertSelective(dynamicFormByItemHead);
        } else {
        	dynamicFormByItemHead.setLastUpdateTime(now);
        	dynamicFormByItemHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.updateByPrimaryKeySelective(dynamicFormByItemHead);
        }
		return dynamicFormByItemHead.getId();
	}

	/**
	 * 保存 動態表單(按項目) 明细行信息, 如果是已经存在的记录就保存，否则新增 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param dynamicFormByItemdetail
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String saveDynamicFormByItemDetail1(DynamicFormByItemDetail dynamicFormByItemdetail) {
		if (StringUtils.isBlank(dynamicFormByItemdetail.getId())) {
			detailMapper.insertSelective(dynamicFormByItemdetail);
		} else {
			detailMapper.updateByPrimaryKeySelective(dynamicFormByItemdetail);
		}
		return dynamicFormByItemdetail.getId();
	}

}
