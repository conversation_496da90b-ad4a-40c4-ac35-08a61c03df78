package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.dto.JobChangeDTO;
import com.csci.hrrs.mapper.InternalExperienceMapper;
import com.csci.hrrs.model.InternalExperience;
import com.csci.hrrs.qo.JobChangeQO;
import com.csci.hrrs.util.DateRangeDecider;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.vo.StringDateRangeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class InternalExperienceService extends ServiceImpl<InternalExperienceMapper, InternalExperience> {

    @Autowired
    private InternalExperienceMapper internalExperienceMapper;

    public long getMaxVersion() {
        QueryWrapper<InternalExperience> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(sync_version) as syncVersion");
        queryWrapper.lambda().eq(InternalExperience::getIsDeleted, Boolean.FALSE);
        return Optional.ofNullable(getOne(queryWrapper)).map(InternalExperience::getSyncVersion).orElse(0L);
    }

    @Transactional(rollbackFor = Exception.class)
    public void switchToNewData(long newVersion) {
        // 将newVersion的数据置为未删除
        lambdaUpdate().set(InternalExperience::getIsDeleted, Boolean.FALSE).eq(InternalExperience::getSyncVersion, newVersion).update();

        // 移除不等于newVersion的数据
        lambdaUpdate().ne(InternalExperience::getSyncVersion, newVersion).remove();
    }

    public List<JobChangeDTO> findByQO(JobChangeQO dashboardQO) {
        StringDateRangeVO stringDateRangeVO = DateRangeDecider.convertTypeToDateRange(dashboardQO.getRosterHeadId(), dashboardQO.getType(), "yyyy.MM.dd");
        dashboardQO.setFromDate(stringDateRangeVO.getFromDate());
        dashboardQO.setToDate(stringDateRangeVO.getToDate());
        FactJobChangeService.convertPlatformOrgCode(dashboardQO);
        dashboardQO.setUserId(ContextUtils.getCurrentUser().getId());
        return internalExperienceMapper.findByQO(dashboardQO);
    }
}
