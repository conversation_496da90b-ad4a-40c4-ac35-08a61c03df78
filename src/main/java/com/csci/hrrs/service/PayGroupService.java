package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultPage;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.PayGroupMapper;
import com.csci.hrrs.model.PayGroup;
import com.csci.hrrs.qo.KeywordPageQO;
import com.csci.hrrs.vo.PayGroupVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class PayGroupService extends ServiceImpl<PayGroupMapper, PayGroup> {

    @Autowired
    private PayGroupMapper payGroupMapper;

    /**
     * get pay group by code
     *
     * @param code pay group code
     * @return
     */
    public PayGroup getPayGroupByCode(String code) {
        LambdaQueryWrapper<PayGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayGroup::getCode, code).eq(PayGroup::getIsDeleted, false);
        return getOne(queryWrapper);
    }

    /**
     * list pay group by page， query by keyword
     *
     * @param qo 查询条件
     * @return
     */
    public ResultPage<PayGroupVO> listPayGroupByPage(KeywordPageQO qo) {
        LambdaQueryWrapper<PayGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayGroup::getIsDeleted, false);
        if (StringUtils.isNotBlank(qo.getKeyword())) {
            queryWrapper.and(i -> i.like(PayGroup::getCode, qo.getKeyword()).or().like(PayGroup::getName, qo.getKeyword()));
        }
        PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
        List<PayGroup> lstPayGroup = list(queryWrapper);
        return new ResultPage<>(lstPayGroup, PayGroupVO::fromPayGroup);
    }

    /**
     * add a new pay group
     *
     * @param payGroupVO 薪酬填报小组
     * @return new pay group id
     */
    public String addPayGroup(PayGroupVO payGroupVO) {
        PayGroup payGroup = payGroupVO.toPayGroup();
        try {
            if (save(payGroup)) {
                return payGroup.getId();
            }
        } catch (Exception e) {
            throw new ServiceException("新增薪酬填报小组失败", e);
        }
        throw new ServiceException("新增薪酬填报小组失败");
    }

    /**
     * update pay group, currently only name can be updated
     *
     * @param payGroupVO 薪酬填报小组
     * @return
     */
    public PayGroup updatePayGroup(PayGroupVO payGroupVO) {
        ServiceHelper.checkExist(payGroupVO.getId(), "更新薪酬填报小组时，id不能为空");
        ServiceHelper.checkExist(payGroupVO.getLastUpdateVersion(), "更新薪酬填报小组时，lastUpdateVersion不能为空");
        ServiceHelper.checkExist(payGroupVO.getName(), "更新薪酬填报小组时，name不能为空");

        PayGroup updateRecord = new PayGroup();
        updateRecord.setName(payGroupVO.getName());
        updateRecord.setLastUpdateVersion(payGroupVO.getLastUpdateVersion());

        LambdaUpdateWrapper<PayGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PayGroup::getIsDeleted, false).eq(PayGroup::getId, payGroupVO.getId()).eq(PayGroup::getLastUpdateVersion, payGroupVO.getLastUpdateVersion());
        if (update(updateRecord, updateWrapper)) {
            return getById(payGroupVO.getId());
        }
        throw new ServiceException("更新薪酬填报小组失败");
    }

    /**
     * delete pay group
     * <p>
     * 逻辑删除
     *
     * @param id                pay group id
     * @param lastUpdateVersion last update version
     */
    public void deletePayGroup(String id, int lastUpdateVersion) {
        LambdaUpdateWrapper<PayGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PayGroup::getIsDeleted, false).eq(PayGroup::getId, id).eq(PayGroup::getLastUpdateVersion, lastUpdateVersion);
        PayGroup payGroup = new PayGroup();
        payGroup.setIsDeleted(true);
        payGroup.setLastUpdateVersion(lastUpdateVersion);
        if (!update(payGroup, updateWrapper)) {
            throw new ServiceException("删除薪酬填报小组失败");
        }
    }

    public List<PayGroup> selectPayGroupByOrgCodeAndUserId(String orgCode, String userId) {
        return payGroupMapper.selectPayGroupByOrgCodeAndUserId(orgCode, userId);
    }
}
