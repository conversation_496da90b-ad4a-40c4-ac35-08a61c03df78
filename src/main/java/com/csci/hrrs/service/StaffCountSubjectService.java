package com.csci.hrrs.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.StaffCountSubjectMapper;
import com.csci.hrrs.model.StaffCountSubject;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class StaffCountSubjectService extends ServiceImpl<StaffCountSubjectMapper, StaffCountSubject> {

    /**
     * List all staff count subjects, order by sort.
     *
     * @return List<StaffCountSubject>
     */
    public List<StaffCountSubject> listAll() {
        LambdaQueryWrapper<StaffCountSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffCountSubject::getIsDeleted, false).orderByAsc(StaffCountSubject::getSort);
        return list(queryWrapper);
    }


    /**
     * 根据数据来源查询科目编码
     *
     * @param source 数据来源
     * @return List<String>
     */
    public List<String> listSubjectCodeBySource(String source) {
        LambdaQueryWrapper<StaffCountSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(source), StaffCountSubject::getSource, source)
                .eq(StaffCountSubject::getIsDeleted, false).orderByAsc(StaffCountSubject::getSort);
        return list(queryWrapper).stream().map(StaffCountSubject::getCode).distinct().toList();
    }

}
