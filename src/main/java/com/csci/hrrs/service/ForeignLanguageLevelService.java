package com.csci.hrrs.service;

import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.ForeignLanguageLevelMapper;
import com.csci.hrrs.model.ForeignLanguageLevel;
import com.csci.hrrs.model.ForeignLanguageLevelExample;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class ForeignLanguageLevelService {

    @Resource
    private ForeignLanguageLevelMapper foreignLanguageLevelMapper;


    /**
     * this method is delegated to ForeignLanguageLevelMapper.insertSelective
     *
     * @param record 待插入的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(ForeignLanguageLevel record) {
        return foreignLanguageLevelMapper.insertSelective(record);
    }

    /**
     * this method is delegated to ForeignLanguageLevelMapper.updateByPrimaryKeySelective
     *
     * @param record 待更新的记录
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(ForeignLanguageLevel record) {
        return foreignLanguageLevelMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * this method is delegated to ForeignLanguageLevelMapper.selectByExample
     *
     * @param example 查询条件
     * @return List<ForeignLanguageLevel>
     */
    public List<ForeignLanguageLevel> selectByExample(ForeignLanguageLevelExample example) {
        return foreignLanguageLevelMapper.selectByExample(example);
    }

}
