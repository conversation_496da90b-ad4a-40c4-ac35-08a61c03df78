package com.csci.hrrs.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.*;
import com.csci.hrrs.model.*;
import com.csci.hrrs.model.Permission;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class PermissionMenuService {
    
    @Autowired
    private PermissionMenuMapper mapper;
    
    @Autowired
    private PermissionMapper permissionMapper;

    public List<PermissionMenu> selectByExample(PermissionMenuExample example) {
        return mapper.selectByExample(example);
    }
	
    /**
     * 權限菜單關係 数据列表
     *
     * @param 
     * @return
     */
    public List<PermissionMenu> listPermissionMenuByCode(String permissionname) {
    	Permission permission = ServiceHelper.getPermissionByCode(permissionname, permissionMapper);
    	if(permission == null)
    		throw new ServiceException("找不到權限");
    	
        return ServiceHelper.listPermissionMenu(permission.getId(), mapper);
    }
    
    

	
    /**
     * 權限菜單關係 数据列表
     *
     * @param permissionId
     * @return
     */
    public List<PermissionMenu> listPermissionMenu(String permissionId) {
        return ServiceHelper.listPermissionMenu(permissionId, mapper);
    }

    /**
     * 保存 權限菜單關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permissionMenuLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> savePermissionMenuList(List<PermissionMenu> permissionMenuLst) {
    	List<String> idLst = new ArrayList<>();
    	for(PermissionMenu permissionMenu : permissionMenuLst) {
    		this.savePermissionMenu(permissionMenu);
            idLst.add(permissionMenu.getId());
    	}
        return idLst;
    }

    /**
     * 保存 權限菜單關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permissionMenu
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String savePermissionMenu(PermissionMenu permissionMenu) {
    	if(permissionMenu.getDeleted() == true) {
    		this.deletePermissionMenu(permissionMenu.getId());
    	} else {
	    	UserInfo currentUser = ContextUtils.getCurrentUser();
	    	LocalDateTime now = LocalDateTime.now();
	        if (StringUtils.isBlank(permissionMenu.getId())) {
	        	permissionMenu.setCreationTime(now);
	        	permissionMenu.setCreateUsername(currentUser.getUsername());
	        	permissionMenu.setLastUpdateTime(now);
	        	permissionMenu.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.insertSelective(permissionMenu);
	        } else {
	        	permissionMenu.setLastUpdateTime(now);
	        	permissionMenu.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.updateByPrimaryKeySelective(permissionMenu);
	        }
    	}
        return permissionMenu.getId();
    }

    /**
     * 刪除 權限菜單關係 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deletePermissionMenu(String id) {
        return mapper.deleteByPrimaryKey(id);
    }
}
