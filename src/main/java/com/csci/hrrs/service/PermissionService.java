package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.PermissionMapper;
import com.csci.hrrs.mapper.RoleMapper;
import com.csci.hrrs.mapper.RolePermissionMapper;
import com.csci.hrrs.model.Permission;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.model.Role;
import com.csci.hrrs.model.RolePermission;
import com.csci.hrrs.qo.PermissionPageableQO;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class PermissionService extends ServiceImpl<PermissionMapper, Permission> {

    @Autowired
    private PermissionMapper mapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    /**
     * 權限 数据列表
     *
     * @param permissionQO
     * @return
     */
    public ResultPage<Permission> listPermission(PermissionPageableQO permissionQO) {
        ResultPage<Permission> resultPage;

        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(permissionQO.getRoleCode())) {
            List<String> lstPermissionId = new ArrayList<>();
            Role role = roleMapper.selectOne(new LambdaQueryWrapper<Role>().eq(Role::getCode, permissionQO.getRoleCode()));
            if (role != null) {
                List<RolePermission> lstRolePermission = rolePermissionMapper.selectList(new LambdaQueryWrapper<RolePermission>().eq(RolePermission::getRoleId, role.getId()));
                for (RolePermission rolePermission : lstRolePermission) {
                    lstPermissionId.add(rolePermission.getPermissionId());
                }
                if (lstPermissionId.size() > 0) {
                    queryWrapper.in(Permission::getId, lstPermissionId);
                }
            }
        }

        PageHelper.startPage(permissionQO.getCurPage(), permissionQO.getPageSize());
        List<Permission> lstPermission = list(queryWrapper);
        resultPage = new ResultPage<>(lstPermission, true);
        return resultPage;
    }


    /**
     * 保存 權限 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> savePermissionList(List<Permission> permissionLst) {
        List<String> idLst = new ArrayList<>();
        for (Permission permission : permissionLst) {
            this.savePermission(permission);
            idLst.add(permission.getId());
        }
        return idLst;
    }

    /**
     * 保存 權限 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permission
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String savePermission(Permission permission) {
        UserInfo currentUser = ContextUtils.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(permission.getId())) {
            permission.setCreationTime(now);
            permission.setCreateUsername(currentUser.getUsername());
            permission.setLastUpdateTime(now);
            permission.setLastUpdateUsername(currentUser.getUsername());
            save(permission);
        } else {
            permission.setLastUpdateTime(now);
            permission.setLastUpdateUsername(currentUser.getUsername());
            updateById(permission);
        }
        return permission.getId();
    }

}
