package com.csci.hrrs.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.csci.common.exception.ServiceException;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.*;
import com.csci.hrrs.model.*;
import com.csci.hrrs.vo.*;
import com.csci.hrrs.model.AuditNodeExample.Criteria;
import com.csci.hrrs.qo.*;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;
import com.github.pagehelper.PageHelper;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class AuditNodeService {
	
    @Autowired
    private AuditNodeMapper mapper;

    @Autowired
    private AuditSettingMapper auditSettingMapper;
    
    @Autowired
    private AuditNodeLogMapper auditNodeLogMapper;
	
    @Autowired
    private AuditNodeCustomMapper customMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private AmbientHeadMapper ambientHeadMapper;

    @Autowired
    private BusinessTravelHeadMapper businessTravelHeadMapper;
    
    @Autowired
    private RawMaterialHeadMapper rawMaterialHeadMapper;
    
    @Autowired
    private OtherFactorsHeadMapper otherFactorsHeadMapper;

    @Autowired
    private ResourcesEnergyHeadMapper resourcesEnergyHeadMapper;
    
    @Autowired
    private WasteAndRecyclingHeadMapper wasteAndRecyclingHeadMapper;
    
    @Autowired
    private WaterResourcesHeadMapper waterResourcesHeadMapper;
    
    @Autowired
    private DynamicFormHeadMapper dynamicFormHeadMapper;
    
    public List<AuditNode> selectByExample(AuditNodeExample example) {
        return mapper.selectByExample(example);
    }
    
    /**
     * 審核節點 数据列表
     *
     * @param auditNodeQO
     * @return
     */
    public ResultPage<AuditNodeDetailVO> listAuditNode(AuditNodePageableQO auditNodeQO) {
    	User currentUser = ServiceHelper.getUserByUsername(ContextUtils.getCurrentUser().getUsername(), userMapper);
    	ResultPage<AuditNodeDetailVO> resultPage;
        PageHelper.startPage(auditNodeQO.getCurPage(), auditNodeQO.getPageSize());
        List<AuditNodeDetailVO> lstAuditNodeDetailVO = customMapper.listAuditNodeDetail(currentUser.getId());
	    resultPage = new ResultPage<>(lstAuditNodeDetailVO, true);
        return resultPage;
    }
    
    /**
     * 審核節點 数据
     *
     * @param formId, formCode, seq
     * @return
     */
    public AuditNode getAuditNode(String formId, String formCode, int seq) {
    	AuditNodeExample auditNodeExample = new AuditNodeExample();
    	auditNodeExample.or().andFormCodeEqualTo(formCode).andFormIdEqualTo(formId).andSeqEqualTo(seq);
    	List<AuditNode> lstAuditNode = mapper.selectByExample(auditNodeExample);
    	if(lstAuditNode == null || lstAuditNode.size() == 0) return null;
        return lstAuditNode.get(0);
    }
    
    
    /**
     * 進行 表單審核
     *
     * @param auditQO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer audit(AuditQO auditQO) {
    	int result = 0;
    	int iNode = 0;
    	List<AuditNode> lstAuditNode;
    	AuditNode auditNode;
    	AuditNodeExample auditNodeExample = new AuditNodeExample();
    	AuditNodeLog auditNodeLog;
    	Criteria auditNodeCriteria = auditNodeExample.or();
    	
    	User currentUser = ServiceHelper.getUserByUsername(ContextUtils.getCurrentUser().getUsername(), userMapper);
    	
    	if(auditQO.getIsPass()) {
        	//Update Form Head
            switch (auditQO.getFormCode()) {
            case "ambient":
            	/*AmbientHead ambientHead = ambientHeadMapper.selectByPrimaryKey(auditQO.getFormId());
            	if(ambientHead == null) throw new ServiceException("找不到相關表格。");
            	iNode = ambientHead.getAuditNode() >= 4 ? 4 : ambientHead.getAuditNode() + 1;
            	ambientHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		ambientHead.setIsAudited(true);
            	}
            	result += ambientHeadMapper.updateByPrimaryKey(ambientHead);
            	
            	BusinessTravelHeadExample businessTravelHeadExample = new BusinessTravelHeadExample();
            	businessTravelHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<BusinessTravelHead> lstBusinessTravelHead = businessTravelHeadMapper.selectByExample(businessTravelHeadExample);
            	if(lstBusinessTravelHead == null || lstBusinessTravelHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	BusinessTravelHead businessTravelHead = lstBusinessTravelHead.get(0);
            	iNode = businessTravelHead.getAuditNode() >= 4 ? 4 : businessTravelHead.getAuditNode() + 1;
            	businessTravelHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		businessTravelHead.setIsAudited(true);
            	}
            	result += businessTravelHeadMapper.updateByPrimaryKey(businessTravelHead);
            	
            	OtherFactorsHeadExample otherFactorsHeadExample = new OtherFactorsHeadExample();
            	otherFactorsHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<OtherFactorsHead> lstOtherFactorsHead = otherFactorsHeadMapper.selectByExample(otherFactorsHeadExample);
            	if(lstOtherFactorsHead == null || lstOtherFactorsHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	OtherFactorsHead otherFactorsHead = lstOtherFactorsHead.get(0);
            	iNode = otherFactorsHead.getAuditNode() >= 4 ? 4 : otherFactorsHead.getAuditNode() + 1;
            	otherFactorsHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		otherFactorsHead.setIsAudited(true);
            	}
            	result += otherFactorsHeadMapper.updateByPrimaryKey(otherFactorsHead);

            	RawMaterialHeadExample rawMaterialHeadExample = new RawMaterialHeadExample();
            	rawMaterialHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<RawMaterialHead> lstRawMaterialHead = rawMaterialHeadMapper.selectByExample(rawMaterialHeadExample);
            	if(lstRawMaterialHead == null || lstRawMaterialHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	RawMaterialHead rawMaterialHead = lstRawMaterialHead.get(0);
            	iNode = rawMaterialHead.getAuditNode() >= 4 ? 4 : rawMaterialHead.getAuditNode() + 1;
            	rawMaterialHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		rawMaterialHead.setIsAudited(true);
            	}
            	result += rawMaterialHeadMapper.updateByPrimaryKey(rawMaterialHead);

            	ResourcesEnergyHeadExample resourcesEnergyHeadExample = new ResourcesEnergyHeadExample();
            	resourcesEnergyHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<ResourcesEnergyHead> lstResourcesEnergyHead = resourcesEnergyHeadMapper.selectByExample(resourcesEnergyHeadExample);
            	if(lstResourcesEnergyHead == null || lstResourcesEnergyHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	ResourcesEnergyHead resourcesEnergyHead = lstResourcesEnergyHead.get(0);
            	iNode = resourcesEnergyHead.getAuditNode() >= 4 ? 4 : resourcesEnergyHead.getAuditNode() + 1;
            	resourcesEnergyHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		resourcesEnergyHead.setIsAudited(true);
            	}
            	result += resourcesEnergyHeadMapper.updateByPrimaryKey(resourcesEnergyHead);

            	WasteAndRecyclingHeadExample wasteAndRecyclingHeadExample = new WasteAndRecyclingHeadExample();
            	wasteAndRecyclingHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<WasteAndRecyclingHead> lstWasteAndRecyclingHead = wasteAndRecyclingHeadMapper.selectByExample(wasteAndRecyclingHeadExample);
            	if(lstWasteAndRecyclingHead == null || lstWasteAndRecyclingHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	WasteAndRecyclingHead wasteAndRecyclingHead = lstWasteAndRecyclingHead.get(0);
            	iNode = wasteAndRecyclingHead.getAuditNode() >= 4 ? 4 : wasteAndRecyclingHead.getAuditNode() + 1;
            	wasteAndRecyclingHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		wasteAndRecyclingHead.setIsAudited(true);
            	}
            	result += wasteAndRecyclingHeadMapper.updateByPrimaryKey(wasteAndRecyclingHead);*/
                break;
            case "sociologyOne":
            case "sociologyTwo":
            	DynamicFormHead dynamicFormHead = dynamicFormHeadMapper.selectByPrimaryKey(auditQO.getFormId());
            	if(dynamicFormHead == null) throw new ServiceException("找不到相關表格。");
            	iNode = dynamicFormHead.getAuditNode() >= 4 ? 4 : dynamicFormHead.getAuditNode() + 1;
            	dynamicFormHead.setAuditNode(iNode);
            	if(iNode == 4) {
            		dynamicFormHead.setIsAudited(true);
            	}
            	result += dynamicFormHeadMapper.updateByPrimaryKey(dynamicFormHead);
                break;
            default:
            	throw new ServiceException("表單編號不對。");
            }

        	//Update Audit Node
        	auditNodeCriteria.andFormIdEqualTo(auditQO.getFormId());
        	auditNodeCriteria.andFormCodeEqualTo(auditQO.getFormCode());
        	auditNodeCriteria.andSeqEqualTo(iNode);
        	lstAuditNode = mapper.selectByExample(auditNodeExample);
        	if(lstAuditNode.size() > 0) {
        		auditNode = lstAuditNode.get(0);
        		auditNode.setIsPass(auditQO.getIsPass());
        		auditNode.setRemark(auditQO.getRemark());
            	result += mapper.updateByPrimaryKey(auditNode);
        	} else {
        		auditNode = new AuditNode();
        		auditNode.setFormId(auditQO.getFormId());
        		auditNode.setFormCode(auditQO.getFormCode());
        		auditNode.setIsPass(auditQO.getIsPass());
        		auditNode.setRemark(auditQO.getRemark());
            	result += mapper.insertSelective(auditNode);
        	}
    	} else {
    		//Update Form Head
            switch (auditQO.getFormCode()) {
            case "ambient":
            	/*AmbientHead ambientHead = ambientHeadMapper.selectByPrimaryKey(auditQO.getFormId());
            	if(ambientHead == null) throw new ServiceException("找不到相關表格。");
            	ambientHead.setAuditNode(0);
            	ambientHead.setIsAudited(false);
            	ambientHead.setIsSubmitted(false);
            	result += ambientHeadMapper.updateByPrimaryKey(ambientHead);
            	
            	BusinessTravelHeadExample businessTravelHeadExample = new BusinessTravelHeadExample();
            	businessTravelHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<BusinessTravelHead> lstBusinessTravelHead = businessTravelHeadMapper.selectByExample(businessTravelHeadExample);
            	if(lstBusinessTravelHead == null || lstBusinessTravelHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	BusinessTravelHead businessTravelHead = lstBusinessTravelHead.get(0);
            	businessTravelHead.setAuditNode(0);
            	businessTravelHead.setIsAudited(false);
            	businessTravelHead.setIsSubmitted(false);
            	result += businessTravelHeadMapper.updateByPrimaryKey(businessTravelHead);
            	
            	OtherFactorsHeadExample otherFactorsHeadExample = new OtherFactorsHeadExample();
            	otherFactorsHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<OtherFactorsHead> lstOtherFactorsHead = otherFactorsHeadMapper.selectByExample(otherFactorsHeadExample);
            	if(lstOtherFactorsHead == null || lstOtherFactorsHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	OtherFactorsHead otherFactorsHead = lstOtherFactorsHead.get(0);
            	otherFactorsHead.setAuditNode(0);
            	otherFactorsHead.setIsAudited(false);
            	otherFactorsHead.setIsSubmitted(false);
            	result += otherFactorsHeadMapper.updateByPrimaryKey(otherFactorsHead);

            	RawMaterialHeadExample rawMaterialHeadExample = new RawMaterialHeadExample();
            	rawMaterialHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<RawMaterialHead> lstRawMaterialHead = rawMaterialHeadMapper.selectByExample(rawMaterialHeadExample);
            	if(lstRawMaterialHead == null || lstRawMaterialHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	RawMaterialHead rawMaterialHead = lstRawMaterialHead.get(0);
            	rawMaterialHead.setAuditNode(0);
            	rawMaterialHead.setIsAudited(false);
            	rawMaterialHead.setIsSubmitted(false);
            	result += rawMaterialHeadMapper.updateByPrimaryKey(rawMaterialHead);

            	ResourcesEnergyHeadExample resourcesEnergyHeadExample = new ResourcesEnergyHeadExample();
            	resourcesEnergyHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<ResourcesEnergyHead> lstResourcesEnergyHead = resourcesEnergyHeadMapper.selectByExample(resourcesEnergyHeadExample);
            	if(lstResourcesEnergyHead == null || lstResourcesEnergyHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	ResourcesEnergyHead resourcesEnergyHead = lstResourcesEnergyHead.get(0);
            	resourcesEnergyHead.setAuditNode(0);
            	resourcesEnergyHead.setIsAudited(false);
            	resourcesEnergyHead.setIsSubmitted(false);
            	result += resourcesEnergyHeadMapper.updateByPrimaryKey(resourcesEnergyHead);

            	WasteAndRecyclingHeadExample wasteAndRecyclingHeadExample = new WasteAndRecyclingHeadExample();
            	wasteAndRecyclingHeadExample.or().andHeadIdEqualTo(auditQO.getFormId());
            	List<WasteAndRecyclingHead> lstWasteAndRecyclingHead = wasteAndRecyclingHeadMapper.selectByExample(wasteAndRecyclingHeadExample);
            	if(lstWasteAndRecyclingHead == null || lstWasteAndRecyclingHead.size() == 0) throw new ServiceException("找不到相關表格。");
            	WasteAndRecyclingHead wasteAndRecyclingHead = lstWasteAndRecyclingHead.get(0);
            	wasteAndRecyclingHead.setAuditNode(0);
            	wasteAndRecyclingHead.setIsAudited(false);
            	wasteAndRecyclingHead.setIsSubmitted(false);
            	result += wasteAndRecyclingHeadMapper.updateByPrimaryKey(wasteAndRecyclingHead);*/
                break;
            case "sociologyOne":
            case "sociologyTwo":
            	DynamicFormHead dynamicFormHead = dynamicFormHeadMapper.selectByPrimaryKey(auditQO.getFormId());
            	if(dynamicFormHead == null) throw new ServiceException("找不到相關表格。");
            	dynamicFormHead.setAuditNode(0);
            	dynamicFormHead.setIsSubmitted(false);
            	result += dynamicFormHeadMapper.updateByPrimaryKey(dynamicFormHead);
                break;
            default:
            	throw new ServiceException("表單編號不對。");
            }

        	//Create Audit Node Log
        	auditNodeCriteria.andFormIdEqualTo(auditQO.getFormId());
        	auditNodeCriteria.andFormCodeEqualTo(auditQO.getFormCode());
        	lstAuditNode = mapper.selectByExample(auditNodeExample);
        	
        	for(AuditNode node: lstAuditNode) {
        		auditNodeLog = new AuditNodeLog();
        		BeanUtils.copyProperties(auditNodeLog, node);
        		auditNodeLog.setId(UUID.randomUUID().toString());
            	result += auditNodeLogMapper.insertSelective(auditNodeLog);
        	}

        	//Update Audit Node
        	auditNodeCriteria.andFormIdEqualTo(auditQO.getFormId());
        	auditNodeCriteria.andFormCodeEqualTo(auditQO.getFormCode());
        	auditNodeCriteria.andSeqEqualTo(iNode);
        	lstAuditNode = mapper.selectByExample(auditNodeExample);
        	for(AuditNode node: lstAuditNode) {
        		node.setIsPass(null);
        		node.setRemark(null);
            	result += mapper.updateByPrimaryKey(node);
        	}
    	}
        return result;
    }

    
    /**
     * 生成 審核節點
     *
     * @param organizationId, formCode, formId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int generateAuditNodes(String organizationId, String formCode, String formId) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
    	
    	int result = 0;
    	List<AuditSetting> lstAuditSetting = new ArrayList<>();
    	
    	AuditSettingExample auditSettingExample = new AuditSettingExample();
    	AuditSettingExample.Criteria auditSettingCriteria = auditSettingExample.or();
    	auditSettingCriteria.andOrganizationIdEqualTo(organizationId);
    	auditSettingCriteria.andFormCodeEqualTo(formCode);
    	lstAuditSetting = auditSettingMapper.selectByExample(auditSettingExample);
    	
    	for(AuditSetting setting : lstAuditSetting) {
    		AuditNode node = new AuditNode();
    		node.setName("");
    		node.setOrganizationId(organizationId);
    		node.setFormCode(formCode);
    		node.setFormId(formId);
    		node.setUserId(setting.getUserId());
    		node.setRemark(null);
    		node.setIsPass(null);
    		node.setSeq(setting.getSeq());
    		node.setCreateUsername(currentUser.getUsername());
    		node.setCreationTime(now);
    		node.setLastUpdateUsername(currentUser.getUsername());
    		node.setLastUpdateTime(now);
    	}
    	
    	return result;
    }
}
