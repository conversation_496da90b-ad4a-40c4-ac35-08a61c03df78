package com.csci.hrrs.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.mapper.StaffCountPlanMapper;
import com.csci.hrrs.model.StaffCountPlan;
import org.springframework.stereotype.Service;

@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class StaffCountPlanService extends ServiceImpl<StaffCountPlanMapper, StaffCountPlan> {
}
