package com.csci.hrrs.service;

import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.mapper.ConfigMapper;
import com.csci.hrrs.model.Config;
import com.csci.hrrs.model.ConfigExample;
import com.csci.hrrs.util.SpringContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
public class ConfigService {

    @Resource
    private ConfigMapper configMapper;

    public int insertSelective(Config record) {
        return configMapper.insertSelective(record);
    }

    public List<Config> selectByExample(ConfigExample example) {
        return configMapper.selectByExample(example);
    }

    /**
     * 根据配置编码查询配置列表
     *
     * @param code 配置编码
     * @return 配置列表
     */
    public static List<Config> listByCode(String code) {
        ConfigExample example = new ConfigExample();
        example.createCriteria().andCodeEqualTo(code).andDeletedEqualTo(false);
        example.setOrderByClause("seq asc");
        ConfigMapper configMapper = SpringContextHolder.getBean(ConfigMapper.class);
        return configMapper.selectByExample(example);
    }

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 删除的记录数
     */
    public int deleteById(String id) {
        // 执行逻辑删除，将删除标志置为1
        Config config = new Config();
        config.setId(id);
        config.setDeleted(true);
        return configMapper.updateByPrimaryKeySelective(config);
    }

    /**
     * 根据配置编码查询最大序号
     *
     * @param code 配置编码
     * @return 最大序号
     */
    public int getMaxSeqByCode(String code) {
        ConfigExample example = new ConfigExample();
        example.createCriteria().andCodeEqualTo(code).andDeletedEqualTo(false);
        example.setOrderByClause("seq desc");
        List<Config> configs = configMapper.selectByExample(example);
        if (configs.isEmpty()) {
            return 0;
        }
        return configs.get(0).getSeq();
    }

}
