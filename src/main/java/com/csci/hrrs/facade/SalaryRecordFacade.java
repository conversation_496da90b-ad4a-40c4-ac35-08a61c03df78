package com.csci.hrrs.facade;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.SalaryRecord;
import com.csci.hrrs.qo.SalaryRecordQO;
import com.csci.hrrs.service.OrganizationService;
import com.csci.hrrs.service.SalaryRecordService;
import com.csci.hrrs.util.ExportUtils;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.util.excel.ColumnDefVO;
import com.csci.hrrs.util.excel.GeneralExcelExporter;
import com.csci.hrrs.vo.SalaryRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.csci.hrrs.constant.HrrsConsts.SalaryRecordColumnName.*;

@Component
@LogMethod
@Slf4j
public class SalaryRecordFacade {

    @Resource
    private SalaryRecordService salaryRecordService;
    @Resource
    private OrganizationService organizationService;

    private static final int BATCH_SIZE = 1000;

    private static void validateSalaryRecord(List<SalaryRecord> batchList) {
        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        List<String> orgCodeList = organizationService.listAllOrgCodes();
        for (SalaryRecord salaryRecord : batchList) {
            if (!orgCodeList.contains(salaryRecord.getOrganizationCode())) {
                throw new ServiceException("组织机构编码不存在: " + salaryRecord.getOrganizationCode());
            }
            switch (salaryRecord.getEmployeeCategory()) {
                case "港聘" -> {
                    if (!salaryRecord.getStaffType().equals("管理人员") && !salaryRecord.getStaffType().equals("自有日薪") && !salaryRecord.getStaffType().equals("中智外包")) {
                        throw new ServiceException("员工类别为港聘时，人员类型只能为管理人员、自有日薪、中智外包");
                    }
                }
                case "内地" -> {
                    if (!salaryRecord.getStaffType().equals("管理人员") && !salaryRecord.getStaffType().equals("中智外包")) {
                        throw new ServiceException("员工类别为内地时，人员类型只能为管理人员、中智外包");
                    }
                }
                case "内派" -> {
                    if (!salaryRecord.getStaffType().equals("管理人员")) {
                        throw new ServiceException("员工类别为内派时，人员类型只能为管理人员");
                    }
                }
                default -> throw new ServiceException("员工类别只能为港聘、内地、内派");
            }
        }
    }

    private static void validateSalaryRecordNew(List<SalaryRecord> batchList) {
        // 1. 获取所有有效组织编码
        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        List<String> orgCodeList = organizationService.listAllOrgCodes();

        // 2. 收集错误信息（分步校验）
        List<String> errorMessages = new ArrayList<>();

        // ==================== 第一步：校验空编码 ====================
        List<Integer> emptyCodeRows = batchList.stream()
                .filter(record -> StringUtils.isBlank(record.getOrganizationCode()))
                .map(record -> record.getExcelRowNum() + 1)
                .sorted()
                .collect(Collectors.toList());

        if (!emptyCodeRows.isEmpty()) {
            errorMessages.add("存在组织编码为空的行：" +
                    emptyCodeRows.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining("、")));
        }

        // ==================== 第二步：校验无效编码 ====================
        Map<String, List<Integer>> invalidOrgCodeMap = batchList.stream()
                .filter(record -> StringUtils.isNotBlank(record.getOrganizationCode()))
                .filter(record -> !orgCodeList.contains(record.getOrganizationCode()))
                .collect(Collectors.groupingBy(
                        SalaryRecord::getOrganizationCode,
                        Collectors.mapping(
                                record -> record.getExcelRowNum() + 1,
                                Collectors.toList()
                        )
                ));

        if (!invalidOrgCodeMap.isEmpty()) {
            errorMessages.add("组织机构编码不存在：" +
                    invalidOrgCodeMap.entrySet().stream()
                            .map(e -> String.format("%s（行：%s）",
                                    e.getKey(),
                                    e.getValue().stream()
                                            .sorted()
                                            .map(String::valueOf)
                                            .collect(Collectors.joining("、"))))
                            .collect(Collectors.joining("、")));
        }

        // 如果前两步有错误，直接抛出
        if (!errorMessages.isEmpty()) {
            throw new ServiceException("导入校验失败：\n" + String.join("\n", errorMessages));
        }

        // ==================== 第三步：校验人员类型（前两步通过后才执行） ====================
        List<String> typeErrors = batchList.stream()
                .map(record -> {
                    String category = record.getEmployeeCategory();
                    String staffType = record.getStaffType();
                    int rowNum = record.getExcelRowNum() + 1;

                    switch (category) {
                        case "港聘":
                            if (!"管理人员".equals(staffType) && !"自有日薪".equals(staffType) && !"中智外包".equals(staffType)) {
                                return String.format("第%d行：港聘员工的人员类型只能为管理人员、自有日薪、中智外包", rowNum);
                            }
                            break;
                        case "内地":
                            if (!"管理人员".equals(staffType) && !"中智外包".equals(staffType)) {
                                return String.format("第%d行：内地员工的人员类型只能为管理人员、中智外包", rowNum);
                            }
                            break;
                        case "内派":
                            if (!"管理人员".equals(staffType)) {
                                return String.format("第%d行：内派员工的人员类型只能为管理人员", rowNum);
                            }
                            break;
                        default:
                            return String.format("第%d行：员工类别只能为港聘、内地、内派", rowNum);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!typeErrors.isEmpty()) {
            throw new ServiceException("导入校验失败：\n" + String.join("\n", typeErrors));
        }
    }

    public void importSalaryRecord(MultipartFile file, String employeeCategory, YearMonth yearMonth) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("导入文件不能为空");
        }
        if (StringUtils.isBlank(employeeCategory)) {
            throw new ServiceException("员工类别不能为空");
        }
        if (yearMonth == null) {
            throw new ServiceException("薪酬年月不能为空");
        }

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null || sheet.getPhysicalNumberOfRows() <= 1) {
                throw new ServiceException("Excel文件内容为空");
            }

            Map<String, Integer> headerMap = getHeaderMap(sheet.getRow(0));
            validateHeaders(headerMap, employeeCategory);

            List<SalaryRecord> batchList = new ArrayList<>();
            // 遍历数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (isEmptyRow(row)) {
                    continue;
                }

                SalaryRecordVO vo = createSalaryRecord(row, headerMap, employeeCategory, yearMonth);
                SalaryRecord record = vo.toModel();
                // 临时存储Excel行号
                record.setExcelRowNum(i);
                batchList.add(record);

                // 达到批量处理数量或最后一行时进行保存
                /*if (batchList.size() >= BATCH_SIZE || i == sheet.getLastRowNum()) {
                    salaryRecordService.saveBatchCustom(batchList);
                    batchList.clear();
                }*/
            }
            // validate employeeCategory and staffType
            validateSalaryRecordNew(batchList);
            salaryRecordService.saveBatchCustom(batchList);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导入薪酬记录失败", e);
            throw new ServiceException("导入薪酬记录失败: " + e.getMessage());
        }
    }

    private Map<String, Integer> getHeaderMap(Row headerRow) {
        if (headerRow == null) {
            throw new ServiceException("Excel文件格式错误：未找到表头");
        }

        Map<String, Integer> headerMap = new HashMap<>();
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                headerMap.put(cell.getStringCellValue(), i);
            }
        }
        return headerMap;
    }

    private void validateHeaders(Map<String, Integer> headerMap, String employeeCategory) {
        // 验证必需的基础列
        List<String> requiredHeaders = Arrays.asList(ORG_CODE, /*"组织代码", "组织机构名称",*/
                STAFF_TYPE, HEAD_COUNT, UNIT_CURRENCY);

        for (String header : requiredHeaders) {
            if (!headerMap.containsKey(header)) {
                throw new ServiceException("Excel文件格式错误：缺少必需列 " + header);
            }
        }

        // 验证港聘特有的列
        if (StringUtils.equals(employeeCategory, "港聘")) {
            List<String> hkHeaders = Arrays.asList(HrrsConsts.SalaryRecordColumnName.BONUS, HrrsConsts.SalaryRecordColumnName.EMPLOYEE_MC, HrrsConsts.SalaryRecordColumnName.EMPLOYEE_VC, HrrsConsts.SalaryRecordColumnName.NET_SALARY, HrrsConsts.SalaryRecordColumnName.PERFORMANCE_BONUS, SPECIAL_BONUS, PROTECTION_BONUS, SEVERANCE_PAYMENT);

            for (String header : hkHeaders) {
                if (!headerMap.containsKey(header)) {
                    throw new ServiceException("Excel文件格式错误：港聘类别缺少必需列 " + header);
                }
            }
        }

        // 验证内地特有的列
        if (StringUtils.equals(employeeCategory, "内地")) {
            List<String> hkHeaders = Arrays.asList(HrrsConsts.SalaryRecordColumnName.OVERTIME_PAY_RMB, HrrsConsts.SalaryRecordColumnName.TOTAL_SALARY_RMB);

            for (String header : hkHeaders) {
                if (!headerMap.containsKey(header)) {
                    throw new ServiceException("Excel文件格式错误：内地类别缺少必需列 " + header);
                }
            }
        } else {
            List<String> hkHeaders = Arrays.asList(HrrsConsts.SalaryRecordColumnName.OVERTIME_PAY, HrrsConsts.SalaryRecordColumnName.TOTAL_SALARY);

            for (String header : hkHeaders) {
                if (!headerMap.containsKey(header)) {
                    throw new ServiceException("Excel文件格式错误：内地类别缺少必需列 " + header);
                }
            }
        }

    }

    private boolean isEmptyRow(Row row) {
        return row == null || row.getPhysicalNumberOfCells() == 0;
    }

    private SalaryRecordVO createSalaryRecord(Row row, Map<String, Integer> headerMap,
                                              String employeeCategory, YearMonth yearMonth) {
        SalaryRecordVO vo = new SalaryRecordVO();
        vo.setEmployeeCategory(employeeCategory);
        vo.setSalaryYear(yearMonth.getYear());
        vo.setSalaryMonth(yearMonth.getMonthValue());

        // 读取基础列数据
        vo.setOrganizationCode(getCellAllValue(row, headerMap.get(ORG_CODE)));
        vo.setSiteCode(getCellAllValue(row, headerMap.get(SITE_CODE)));
        vo.setOrganizationName(getCellAllValue(row, headerMap.get(ORG_NAME)));
        vo.setStaffType(getCellAllValue(row, headerMap.get(STAFF_TYPE)));
        vo.setHeadcount(getCellIntValue(row, headerMap.get(HEAD_COUNT)));
        vo.setCurrency(getCellAllValue(row, headerMap.get(UNIT_CURRENCY)));
        vo.setOvertimePay(getCellBigDecimalValue(row, headerMap.get(OVERTIME_PAY)));
        vo.setTotalSalary(getCellBigDecimalValue(row, headerMap.get(TOTAL_SALARY)));

        // 如果是港聘类别，读取额外列数据
        if (StringUtils.equals(employeeCategory, "港聘")) {
            vo.setBonus(getCellBigDecimalValue(row, headerMap.get(HrrsConsts.SalaryRecordColumnName.BONUS)));
            vo.setEmployeeMc(getCellBigDecimalValue(row, headerMap.get(EMPLOYEE_MC)));
            vo.setEmployeeVc(getCellBigDecimalValue(row, headerMap.get(EMPLOYEE_VC)));
            vo.setNetSalary(getCellBigDecimalValue(row, headerMap.get(NET_SALARY)));
            vo.setPerformanceBonus(getCellBigDecimalValue(row, headerMap.get(HrrsConsts.SalaryRecordColumnName.PERFORMANCE_BONUS)));
            vo.setSpecialBonus(getCellBigDecimalValue(row, headerMap.get(SPECIAL_BONUS)));
            vo.setProtectionBonus(getCellBigDecimalValue(row, headerMap.get(PROTECTION_BONUS)));
            vo.setSeverancePayment(getCellBigDecimalValue(row, headerMap.get(SEVERANCE_PAYMENT)));
        }

        // 如果是内地类别，读取额外列数据
        if (StringUtils.equals(employeeCategory, "内地")) {
            vo.setTotalSalaryRmb(getCellBigDecimalValue(row, headerMap.get(TOTAL_SALARY_RMB)));
            vo.setOvertimePayRmb(getCellBigDecimalValue(row, headerMap.get(OVERTIME_PAY_RMB)));
        }

        return vo;
    }

    public void downloadTemplate(SalaryRecordQO qo, HttpServletResponse response) {
        // 1. 批量获取组织数据（减少数据库访问）
        List<Organization> organizations = organizationService.getByCodes(qo.getOrgCodeList());
        if (organizations.isEmpty()) {
            throw new IllegalArgumentException("未找到指定的组织数据");
        }

        // 2. 预加载配置模板（避免重复创建）
        TemplateConfig normalConfig = getTemplateConfig(qo.getEmployeeCategory());
        TemplateConfig config830 = get830TemplateConfig(qo.getEmployeeCategory());

        // 3. 使用并行流处理组织分类（大数据量时提升性能）
        Map<Boolean, List<Organization>> groupedOrgs = organizations.parallelStream()
                .collect(Collectors.groupingByConcurrent(
                        org -> org.getNo() != null && org.getNo().contains("00200002")
                ));

        // 4. 使用Stream API处理数据构建
        List<SalaryRecordVO> dataList = Stream.concat(
                // 处理830组织数据
                groupedOrgs.getOrDefault(true, Collections.emptyList()).stream()
                        .flatMap(org -> Arrays.stream(config830.getStaffTypes())
                                .map(staffType -> buildSalaryRecordVO(
                                        org, qo.getEmployeeCategory(), staffType, config830.isHongKongHire()))
                        ),

                // 处理非830组织数据
                groupedOrgs.getOrDefault(false, Collections.emptyList()).stream()
                        .flatMap(org -> Arrays.stream(normalConfig.getStaffTypes())
                                .map(staffType -> buildSalaryRecordVO(
                                        org, qo.getEmployeeCategory(), staffType, normalConfig.isHongKongHire()))
                        )
        ).collect(Collectors.toList());

        List<ColumnDefVO> columnDefs = buildCommonColumnDefs(normalConfig, qo.getEmployeeCategory());

        exportToExcel(response, columnDefs, dataList);
    }

    public void exportSalaryRecord(SalaryRecordQO qo, HttpServletResponse response) {
        // 根据员工类别获取对应的列定义和员工类型
        TemplateConfig config = getTemplateConfig(qo.getEmployeeCategory());

        // 构建通用列定义
        List<ColumnDefVO> columnDefs = buildCommonColumnDefs(config, qo.getEmployeeCategory());

        // 查詢數據
        List<SalaryRecordVO> dataList = salaryRecordService.listSalaryRecord(qo);

        // 导出数据
        exportToExcel(response, columnDefs, dataList);
    }

    private TemplateConfig getTemplateConfig(String employeeCategory) {
        if (StringUtils.equalsIgnoreCase(employeeCategory, "内派")) {
            return new TemplateConfig(new String[]{"管理人员"}, false);
        } else if (StringUtils.equals(employeeCategory, "内地")) {
            return new TemplateConfig(new String[]{"管理人员", "中智外包"}, false);
        } else if (StringUtils.equals(employeeCategory, "港聘")) {
            return new TemplateConfig(new String[]{"管理人员", "自有日薪", "中智外包"}, true);
        }
        throw new IllegalArgumentException("不支持的员工类别: " + employeeCategory);
    }

    private TemplateConfig get830TemplateConfig(String employeeCategory) {
        // 对于830的组织，只返回管理人员配置
        return new TemplateConfig(new String[]{"管理人员"}, false);
    }

    private List<ColumnDefVO> buildCommonColumnDefs(TemplateConfig config, String employeeCategory) {
        List<ColumnDefVO> columnDefs = new ArrayList<>();
        columnDefs.add(new ColumnDefVO(ORG_CODE, "organizationCode"));
        columnDefs.add(new ColumnDefVO(SITE_CODE, "siteCode"));
        columnDefs.add(new ColumnDefVO(ORG_NAME, "organizationName", 4000));
        columnDefs.add(new ColumnDefVO(STAFF_TYPE, "staffType"));
        columnDefs.add(new ColumnDefVO(HEAD_COUNT, "headcount"));
        columnDefs.add(new ColumnDefVO(UNIT_CURRENCY, "currency", 3500));
        // 添加特定类别的额外列定义
        columnDefs.addAll(config.getExtraColumnDefs());
        if (StringUtils.equals(employeeCategory, "内地")) {
            columnDefs.add(new ColumnDefVO(TOTAL_SALARY_RMB, "totalSalaryRmb"));
            columnDefs.add(new ColumnDefVO(OVERTIME_PAY_RMB, "overtimePayRmb"));
        } else {
            columnDefs.add(new ColumnDefVO(OVERTIME_PAY, "overtimePay"));
            columnDefs.add(new ColumnDefVO(TOTAL_SALARY, "totalSalary"));
        }
        return columnDefs;
    }


    private SalaryRecordVO buildSalaryRecordVO(Organization organization, String employeeCategory, String staffType, boolean isHongKongHire) {
        SalaryRecordVO vo = new SalaryRecordVO();
        vo.setEmployeeCategory(employeeCategory);
        vo.setOrganizationCode(organization.getCode());
        vo.setOrganizationName(SalaryRecordVO.coalesceOrgName(organization));
        vo.setSiteCode(organization.getSiteCode());
        vo.setStaffType(staffType);
        vo.setHeadcount(0);
        vo.setCurrency(StringUtils.equals(employeeCategory, "内地") ? "人民币" : "港元");
        vo.setOvertimePay(BigDecimal.ZERO);
        vo.setOvertimePayRmb(BigDecimal.ZERO);
        vo.setTotalSalaryRmb(BigDecimal.ZERO);

        if (isHongKongHire) {
            vo.setBonus(BigDecimal.ZERO);
            vo.setEmployeeMc(BigDecimal.ZERO);
            vo.setEmployeeVc(BigDecimal.ZERO);
            vo.setNetSalary(BigDecimal.ZERO);
            vo.setPerformanceBonus(BigDecimal.ZERO);
            vo.setSpecialBonus(BigDecimal.ZERO);
            vo.setProtectionBonus(BigDecimal.ZERO);
            vo.setSeverancePayment(BigDecimal.ZERO);
        }

        return vo;
    }

    private void exportToExcel(HttpServletResponse response, List<ColumnDefVO> columnDefs,
                               List<SalaryRecordVO> dataList) {
        ExportUtils.setResponseHeaders(response, "薪酬记录导入模板");
        try (OutputStream out = response.getOutputStream();
             GeneralExcelExporter exporter = new GeneralExcelExporter()) {
            exporter.export(dataList, columnDefs, out);
        } catch (Exception e) {
            throw new ServiceException("导出模板失败", e);
        }
    }

    /**
     * 获取单元格字符串值，转换失败返回空字符串
     */
    private String getCellStringValue(Row row, Integer columnIndex) {
        try {
            if (columnIndex == null || row.getCell(columnIndex) == null) {
                return "";
            }
            Cell cell = row.getCell(columnIndex);
            return cell.getStringCellValue();
        } catch (Exception e) {
            return "";
        }
    }

    private String getCellAllValue(Row row, Integer columnIndex) {
        if (columnIndex == null || row == null) {
            return "";
        }

        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        // 处理整数和小数
                        double num = cell.getNumericCellValue();
                        if (num == (long) num) {
                            return String.valueOf((long) num);
                        } else {
                            return String.valueOf(num);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    switch (cell.getCachedFormulaResultType()) {
                        case STRING:
                            return cell.getStringCellValue().trim();
                        case NUMERIC:
                            return String.valueOf(cell.getNumericCellValue());
                        case BOOLEAN:
                            return String.valueOf(cell.getBooleanCellValue());
                        default:
                            return "";
                    }
                case BLANK:
                    return "";
                default:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取单元格整数值，转换失败返回0
     */
    private Integer getCellIntValue(Row row, Integer columnIndex) {
        try {
            if (columnIndex == null || row.getCell(columnIndex) == null) {
                return 0;
            }
            Cell cell = row.getCell(columnIndex);
            return (int) cell.getNumericCellValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取单元格BigDecimal值，转换失败返回0
     */
    private BigDecimal getCellBigDecimalValue(Row row, Integer columnIndex) {
        try {
            if (columnIndex == null || row.getCell(columnIndex) == null) {
                return BigDecimal.ZERO;
            }
            Cell cell = row.getCell(columnIndex);
            return BigDecimal.valueOf(cell.getNumericCellValue());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    // 新增配置类
    private static class TemplateConfig {
        private final String[] staffTypes;
        private final boolean isHongKongHire;

        public TemplateConfig(String[] staffTypes, boolean isHongKongHire) {
            this.staffTypes = staffTypes;
            this.isHongKongHire = isHongKongHire;
        }

        public String[] getStaffTypes() {
            return staffTypes;
        }

        public boolean isHongKongHire() {
            return isHongKongHire;
        }

        public List<ColumnDefVO> getExtraColumnDefs() {
            if (!isHongKongHire) {
                return Collections.emptyList();
            }

            List<ColumnDefVO> extraColumns = new ArrayList<>();
            extraColumns.add(new ColumnDefVO(HrrsConsts.SalaryRecordColumnName.BONUS, "bonus"));
            extraColumns.add(new ColumnDefVO(HrrsConsts.SalaryRecordColumnName.EMPLOYEE_MC, "employeeMc"));
            extraColumns.add(new ColumnDefVO(HrrsConsts.SalaryRecordColumnName.EMPLOYEE_VC, "employeeVc"));
            extraColumns.add(new ColumnDefVO(HrrsConsts.SalaryRecordColumnName.NET_SALARY, "netSalary"));
            extraColumns.add(new ColumnDefVO(HrrsConsts.SalaryRecordColumnName.PERFORMANCE_BONUS, "performanceBonus"));
            extraColumns.add(new ColumnDefVO(SPECIAL_BONUS, "specialBonus"));
            extraColumns.add(new ColumnDefVO(PROTECTION_BONUS, "protectionBonus"));
            extraColumns.add(new ColumnDefVO(SEVERANCE_PAYMENT, "severancePayment"));
            return extraColumns;
        }
    }

    public static BigDecimal calculateSalary(List<SalaryRecordVO> salaryRecordList) {
        return salaryRecordList.stream()
                .filter(salaryRecordVO -> !("港聘".equals(salaryRecordVO.getEmployeeCategory()) && "中智外包".equals(salaryRecordVO.getStaffType())))
                .filter(salaryRecordVO -> !("港聘".equals(salaryRecordVO.getEmployeeCategory()) && "自有日薪".equals(salaryRecordVO.getStaffType())))
                .map(SalaryRecordVO::getTotalSalary)
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    /**
     * 计算指定员工类别的薪酬总额
     *
     * @param salaryRecordList
     * @return
     */
    public static BigDecimal calculateEmpCategorySalary(List<SalaryRecordVO> salaryRecordList, List<String> employeeCategorys) {
        return salaryRecordList.stream()
                .filter(salaryRecordVO -> (employeeCategorys.contains(salaryRecordVO.getEmployeeCategory())))
                .map(SalaryRecordVO::getTotalSalary)
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    public static Long calculatePaidPerson(List<SalaryRecordVO> salaryRecordVOS, List<String> employeeCategoryList) {
        return salaryRecordVOS.stream()
                .filter(salaryRecordVO -> (employeeCategoryList.contains(salaryRecordVO.getEmployeeCategory())))
                .mapToLong(SalaryRecordVO::getHeadcount)
                .sum();
    }

    public static BigDecimal calculateManagerSalary(List<SalaryRecordVO> salaryRecordList) {
        return salaryRecordList.stream()
                .filter(s ->
                        (
                                "内地_管理人员".equals(s.getEmployeeCategory() + "_" + s.getStaffType())
                                        || "内派".equals(s.getEmployeeCategory())
                                        || "港聘_管理人员".equals(s.getEmployeeCategory() + "_" + s.getStaffType())
                                        || "内地_中智外包".equals(s.getEmployeeCategory() + "_" + s.getStaffType())
                        ))
                .map(SalaryRecordVO::getTotalSalary)
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    public static BigDecimal calculateSalaryByOrgCode(String orgCode, List<SalaryRecordVO> salaryRecordVOList) {
        return salaryRecordVOList.stream()
                .filter(salaryRecordVO -> orgCode.equals(salaryRecordVO.getOrganizationCode()))
                .filter(salaryRecordVO -> !("港聘".equals(salaryRecordVO.getEmployeeCategory()) && "中智外包".equals(salaryRecordVO.getStaffType())))
                .filter(salaryRecordVO -> !("港聘".equals(salaryRecordVO.getEmployeeCategory()) && "自有日薪".equals(salaryRecordVO.getStaffType())))
                .map(SalaryRecordVO::getTotalSalary)
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }
}
