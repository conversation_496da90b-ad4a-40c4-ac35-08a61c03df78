package com.csci.hrrs.facade.hk;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.OrganizationFacade;
import com.csci.hrrs.facade.SalaryRecordFacade;
import com.csci.hrrs.mapper.RosterDetailMapper;
import com.csci.hrrs.model.*;
import com.csci.hrrs.model.fis.ProjectInfo;
import com.csci.hrrs.model.fis.RawRevenueSite;
import com.csci.hrrs.qo.*;
import com.csci.hrrs.service.*;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.util.StaffCountFullHelper;
import com.csci.hrrs.util.TranslateUtils;
import com.csci.hrrs.util.holder.HKRosterHolder;
import com.csci.hrrs.util.holder.OrganizationHolder;
import com.csci.hrrs.vo.*;
import com.csci.hrrs.vo.subsidiary.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@LogMethod
@Log4j2
public class HKDashFacade {

    private static final Map<String, String> JOB_LEVEL_MAP = new HashMap<>() {
        {
            put("C1", "助总及以上/总监Ⅰ");
            put("C2", "助总及以上/总监Ⅰ");
            put("C3", "助总及以上/总监Ⅰ");
            put("C4", "助总及以上/总监Ⅰ");
            put("D1", "总监Ⅱ/副总监/助理总监/高级经理/经理级");
            put("D2", "总监Ⅱ/副总监/助理总监/高级经理/经理级");
            put("D3", "副经理/助理经理/高级工程师");
            put("D4", "副经理/助理经理/高级工程师");
            put("E1", "副经理/助理经理/高级工程师");
            put("E2", "工程师/助理工程师");
            put("E3", "工程师/助理工程师");
        }
    };
    @Resource
    private ProjectInitiationService projectInitiationService;
    @Resource
    private OrganizationFacade organizationFacade;
    @Resource
    private RawRevenueSiteService rawRevenueSiteService;
    @Resource
    private HKRosterHolder hkRosterHolder;
    @Autowired
    private RosterHeadService rosterHeadService;
    @Autowired
    private RosterDetailService rosterDetailService;
    @Resource
    private BaseBusinessUnitService baseBusinessUnitService;
    @Resource
    private SubjectBalanceService subjectBalanceService;
    @Resource
    private FisProjectInfoService fisProjectInfoService;
    @Resource
    private RawRevenueService rawRevenueService;
    @Resource
    private OrganizationHolder organizationHolder;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private StaffCountFullService staffCountFullService;
    @Resource
    private SalaryRecordService salaryRecordService;


    /**
     * 将 additionalRevenue 的值累加到 sumToRevenue
     *
     * @param sumToRevenue      被累加的对象
     * @param additionalRevenue 需要累加的对象
     */
    private static void sumTo(RawRevenue sumToRevenue, RawRevenue additionalRevenue) {
        sumToRevenue.setIncAmount(CommonUtils.add(sumToRevenue.getIncAmount(), additionalRevenue.getIncAmount()));
        sumToRevenue.setAccumulateAmount(CommonUtils.add(sumToRevenue.getAccumulateAmount(), additionalRevenue.getAccumulateAmount()));
        sumToRevenue.setTotalAmount(CommonUtils.add(sumToRevenue.getTotalAmount(), additionalRevenue.getTotalAmount()));
    }

    public static BigDecimal getStaffCountFieldByDateAndCode(StaffCountFull staffCountFull, LocalDate date) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
        try {
            return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
        } catch (IllegalAccessException e) {
            return BigDecimal.ZERO;
        } catch (IllegalArgumentException e) {
            return BigDecimal.ZERO;
        }
    }


    public static LocalDate getStatDate(HKDashQO qo) {
        return Optional.ofNullable(qo.getStatDate()).orElse(LocalDate.now());
    }

    /**
     * 按要素分布
     * 内地：Staffclass3311=内地
     * 内派：Staffclass3311=内派
     * 港聘：Staffclass3311=境外聘&Staffclass!=专才
     * 专才：Staffclass3311=境外聘&Staffclass=专才
     *
     * @return
     */
    public ElementDistributionVO listElementDistribution(HKDashQO qo) {
        ElementDistributionVO elementDistributionVO = new ElementDistributionVO();
        // 获取人员详情列表
        List<RosterDetail> rosterDetailList = getHKRosterList(qo);

        // 如果组织代码列表不为空，按条件过滤
        if (CollectionUtils.isNotEmpty(qo.getOrganizationCodeList())) {
            rosterDetailList = rosterDetailList.stream()
                    .filter(rosterDetail -> qo.getOrganizationCodeList().contains(rosterDetail.getOrganizationCode()))
                    .toList();
        }

        // 计算总人数
        long total = rosterDetailList.size();

        // 分组统计不同类别的人数
        Map<String, Long> mapCount = rosterDetailList.stream().collect(
                Collectors.groupingBy(this::determineCategory, Collectors.counting())
        );

        // 定义类别与名称的对应关系
        List<String> categories = Arrays.asList("内地", "内派", "港聘", "专才");

        List<RateVO> rateVOS = categories.stream()
                .map(category -> createRateVO(category, mapCount.getOrDefault(category, 0L), total))
                .collect(Collectors.toList());

        elementDistributionVO.setRates(rateVOS);
        elementDistributionVO.setOutsource(getOutsourceCount(qo));
        return elementDistributionVO;
    }

    private Long getOutsourceCount(HKDashQO qo) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return 0L;
        }
        return rosterDetailService.selectHKRosterCountByOutsource(qo.getOrganizationCodeList(), headId);
    }

    /**
     * 根据 RosterDetail 的字段确定人员类别
     */
    private String determineCategory(RosterDetail rosterDetail) {
        if ("内地".equals(rosterDetail.getStaffClass3311())) {
            return "内地";
        } else if ("内派".equals(rosterDetail.getStaffClass3311())) {
            return "内派";
        } else if ("专才".equals(rosterDetail.getStaffClass())) {
            return "专才";
        } else {
            return "港聘";
        }
    }

    /**
     * 创建 RateVO 对象
     */
    private RateVO createRateVO(String name, long count, long total) {
        RateVO rateVO = new RateVO();
        rateVO.setName(name);
        rateVO.setCount(count);
        rateVO.setRate(DemoUtils.divide(count, total));
        return rateVO;
    }

    /**
     * 判断 RosterHeadIdByYearMonthCountVO 是否为当前年月
     */
    private boolean isCurrentYearAndMonth(RosterHeadIdByYearMonthCountVO rosterHead, LocalDate statDate) {
        return statDate.getYear() == rosterHead.getYear() && statDate.getMonthValue() == rosterHead.getMonth();
    }

    /**
     * 根据日期范围计算符合条件的记录数量
     */
    private long countByDateRange(List<RosterDetail> details,
                                  Function<RosterDetail, LocalDate> dateExtractor,
                                  LocalDate startDate,
                                  LocalDate endDate) {
        return details.stream()
                .map(dateExtractor)
                .filter(date -> date != null && !date.isBefore(startDate) && !date.isAfter(endDate))
                .count();
    }

    /**
     * 在职人数：Isonjob=1的人数
     * 当月入职人数：join3311date=所选年月的人数
     * 当月离职人数：dimissiondate=所选年月的人数
     * 年度平均人数：本年度截止到所选年月，每月在职人数之和/月份数
     * <p>
     * 调整：
     * 当月在职人数显示为：当月在职人数/编制数。下方用小字显示（满编率Z%）
     * 在职人数：当前值
     * 编制数：当月编制数
     * 满编率：当月在职人数/当月编制数
     *
     * @param qo
     * @return
     */
    public HKPersonCountVO getHKPersonCount(HKDashQO qo) {
        // 确定时间范围
        LocalDate statDate = getStatDate(qo);
        LocalDate startOfYear = statDate.withDayOfYear(1);
        LocalDate startOfMonth = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startOfYear, endDate);
        List<String> headIdList = rosterHeadIdList.stream()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .toList();

        // 获取香港人员统计信息并过滤 count > 0
        Map<String, Long> mapPersonCount = rosterDetailService
                .selectHKPersonCount(qo.getOrganizationCodeList(), headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        // 获取香港 roster 列表
        // List<RosterDetail> rosterDetailList = getHKRosterList(qo);

        // 获取当前年月的 headId
        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);

        // 获取编制信息
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        // 修改为计算当年12月份编制数
        LocalDate endYear = statDate.with(TemporalAdjusters.lastDayOfYear());
        long currentPlanCount = StaffCountFullHelper.calcStaffCountOfMonth(staffCountFullList, YearMonth.from(endYear));

        // 计算在职人数
        long onJobCount = currentHeadId != null ? mapPersonCount.getOrDefault(currentHeadId, 0L) : 0L;

        // 计算满编率 = 当月在职人数/当月编制数
        BigDecimal fullRate = DemoUtils.divide(onJobCount, currentPlanCount);

        // 计算本月新入职人数
        long thisMonthCount = rosterDetailService.selectHKJoin3311Count(qo.getOrganizationCodeList(), currentHeadId, startOfMonth, endDate);

        // 计算本月离职人数
        long dimissionCount = 0;
        List<RosterDetail> hkFormerRoster = getHKFormerRoster(qo, startOfMonth, endDate); // 获取离职人员信息
        if (CollectionUtils.isNotEmpty(hkFormerRoster)) {
            dimissionCount = countByDateRange(
                    hkFormerRoster,
                    RosterDetail::getDimissionDate,
                    startOfMonth,
                    endDate
            );
        }

        // 计算平均人数: 总数 / 月数
        long avgCount = mapPersonCount.isEmpty() ? 0 : mapPersonCount.values().stream().mapToLong(Long::longValue).sum() / mapPersonCount.size();

        long hkOutsourceCount = rosterDetailService.selectHKOutsourceCount(qo.getOrganizationCodeList(), currentHeadId);


        // 构建结果对象
        return buildHKPersonCountVO(onJobCount, currentPlanCount, fullRate, thisMonthCount, dimissionCount, avgCount, hkOutsourceCount);
    }

    public ResultPage<RosterDetailCategoryVO> getPersonList(HKClassificationQO qo,Integer pageNum,Integer pageSize){
        // 确定时间范围
        LocalDate statDate = getStatDate(qo);
        LocalDate startOfYear = statDate.withDayOfYear(1);
        LocalDate startOfMonth = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startOfYear, endDate);
        // 获取当前年月的 headId
        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);
        List<RosterDetail> rosterDetails = new ArrayList<>();
        PageHelper.startPage(pageNum,pageSize);
        switch (qo.getClassification()){
            case "onJobCount":
                if (StringUtils.isNotBlank(currentHeadId)){
                    rosterDetails = rosterDetailService.selectHKPersonList(qo.getOrganizationCodeList(),currentHeadId);
                }
                break;
            case "entryCount":
                if (StringUtils.isNotBlank(currentHeadId)){
                    rosterDetails =  rosterDetailService.selectHKJoin3311List(qo.getOrganizationCodeList(), currentHeadId, startOfMonth, endDate);
                }
                break;
            case "leaveCount":
                //rosterDetails = getHKFormerRoster(qo, startOfMonth, endDate);
                rosterDetails = rosterDetailService.getLeaveRoster(qo, pageNum, pageSize, startOfMonth, endDate);
                break;
        }
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);
        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());
        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }
    public void exportPersonList(HKClassificationQO qo,HttpServletResponse response) throws IOException {
        // 确定时间范围
        LocalDate statDate = getStatDate(qo);
        LocalDate startOfYear = statDate.withDayOfYear(1);
        LocalDate startOfMonth = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startOfYear, endDate);
        // 获取当前年月的 headId
        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);
        List<RosterDetail> rosterDetails = new ArrayList<>();
        switch (qo.getClassification()){
            case "onJobCount":
                if (StringUtils.isNotBlank(currentHeadId)){
                    rosterDetails = rosterDetailService.selectHKPersonList(qo.getOrganizationCodeList(),currentHeadId);
                }
                break;
            case "entryCount":
                if (StringUtils.isNotBlank(currentHeadId)){
                    rosterDetails =  rosterDetailService.selectHKJoin3311List(qo.getOrganizationCodeList(), currentHeadId, startOfMonth, endDate);
                }
                break;
            case "leaveCount":
                rosterDetails = rosterDetailService.getLeaveRoster(qo,1,10,startOfMonth, endDate);
                break;
        }
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }
    /**
     * 在职人数：Isonjob=1的人数
     * 当月入职人数：join3311date=所选年月的人数
     * 当月离职人数：dimissiondate=所选年月的人数
     * 年度平均人数：本年度截止到所选年月，每月在职人数之和/月份数
     * <p>
     * 调整：
     * 当月在职人数显示为：当月在职人数/编制数。下方用小字显示（满编率Z%）
     * 在职人数：当前值
     * 编制数：当月编制数
     * 满编率：当月在职人数/当月编制数
     *
     * @param qo
     * @return
     */
    public HKPersonCountVO getSUPersonCount(HKDashQO qo) {
        // 确定时间范围
        LocalDate statDate = getStatDate(qo);
        LocalDate startOfYear = statDate.withDayOfYear(1);
        LocalDate startOfMonth = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startOfYear, endDate);
        List<String> headIdList = rosterHeadIdList.stream()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .toList();

        // 获取香港人员统计信息并过滤 count > 0
        Map<String, Long> mapPersonCount = rosterDetailService
                .selectHKPersonCount(qo.getOrganizationCodeList(), headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        // 获取香港 roster 列表
        // List<RosterDetail> rosterDetailList = getHKRosterList(qo);

        // 获取当前年月的 headId
        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);

        // 获取编制信息
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        long currentPlanCount = StaffCountFullHelper.calcStaffCountOfMonth(staffCountFullList, YearMonth.from(statDate));

        // 计算在职人数
        long onJobCount = currentHeadId != null ? mapPersonCount.getOrDefault(currentHeadId, 0L) : 0L;

        // 计算满编率 = 当月在职人数/当月编制数
        BigDecimal fullRate = DemoUtils.divide(onJobCount, currentPlanCount);

        // 计算本月新入职人数
        long thisMonthCount = rosterDetailService.selectHKJoin3311Count(qo.getOrganizationCodeList(), currentHeadId, startOfMonth, endDate);

        // 计算本月离职人数
        long dimissionCount = 0;
        List<RosterDetail> hkFormerRoster = getHKFormerRoster(qo, startOfMonth, endDate); // 获取离职人员信息
        if (CollectionUtils.isNotEmpty(hkFormerRoster)) {
            dimissionCount = countByDateRange(
                    hkFormerRoster,
                    RosterDetail::getDimissionDate,
                    startOfMonth,
                    endDate
            );
        }

        // 计算平均人数: 总数 / 月数
        long avgCount = mapPersonCount.isEmpty() ? 0 : mapPersonCount.values().stream().mapToLong(Long::longValue).sum() / mapPersonCount.size();

        long hkOutsourceCount = rosterDetailService.selectHKOutsourceRosterPersonCategoryPageListTotalCount(qo.getOrganizationCodeList(), currentHeadId);


        // 构建结果对象
        return buildHKPersonCountVO(onJobCount, currentPlanCount, fullRate, thisMonthCount, dimissionCount, avgCount, hkOutsourceCount);
    }

    /**
     * 构建 HKPersonCountVO 对象
     */
    private HKPersonCountVO buildHKPersonCountVO(long onJobCount, long currentPlanCount, BigDecimal fullRate,
                                                 long entryCount, long leaveCount, long avgCount,
                                                 long hkOutsourceCount) {
        HKPersonCountVO hkPersonCountVO = new HKPersonCountVO();
        hkPersonCountVO.setOnJobCount(onJobCount);
        hkPersonCountVO.setStaffCount(currentPlanCount);
        hkPersonCountVO.setFullRate(fullRate);
        hkPersonCountVO.setEntryCount(entryCount);
        hkPersonCountVO.setLeaveCount(leaveCount);
        hkPersonCountVO.setAvgCount(avgCount);
        hkPersonCountVO.setHkOutsourceCount(hkOutsourceCount);
        return hkPersonCountVO;
    }

    /**
     * 获取香港离职人员信息
     *
     * @param qo 查询条件
     * @return
     */
    private List<RosterDetail> getHKFormerRoster(HKDashQO qo, @NonNull LocalDate startDate, @NonNull LocalDate endDate) {
        List<RosterDetail> formerRosterList = hkRosterHolder.getFormerDataList();
        if (CollectionUtils.isEmpty(qo.getOrganizationCodeList())) {
            return formerRosterList;
        }
        if(formerRosterList == null) {
            return new ArrayList<>();
        }
        return formerRosterList.stream()
                .filter(rosterDetail -> qo.getOrganizationCodeList().contains(rosterDetail.getOrganizationCode())
                        && rosterDetail.getDimissionDate() != null && !rosterDetail.getDimissionDate().isBefore(startDate) && !rosterDetail.getDimissionDate().isAfter(endDate))
                .toList();
    }

    /**
     * 大团队核心层：鼠标移到此处时，浮窗显示下列两类：
     * 核心港聘：S taffclass3311=境外聘&rank_hk>=G13  --> 修改为负责人标签is_responsible IN (中建香港-地盤部門負責人、中建香港-地盘部门负责人、內派負責人、負責人、内派负责人、负责人) AND Staffclass3311=境外聘
     * 内派：Staffclass3311=内派   --> 修改为 负责人标签is_responsible IN (中建香港-地盤部門負責人、中建香港-地盘部门负责人、內派負責人、負責人、内派负责人、负责人) AND Staffclass3311=内派
     * 内地：Staffclass3311=内地
     * 自有日薪：PERSKL=24，地盘日薪
     * 地盘基层：除上述情况以外的员工
     *
     * @param qo 查询条件
     * @return
     */
    public List<RateTreeVO> getDistributionByCategory(HKDashQO qo) {
        List<RosterDetail> rosterDetailList = getHKRosterBySiteList(qo);

        Map<String, Long> mapCount = rosterDetailList.stream()
                .collect(Collectors.groupingBy(this::determineType, Collectors.counting()));

        List<String> types = Arrays.asList("核心港聘", "内派", "内地", "自有日薪", "地盘基层");

        List<RateTreeVO> lstOut = new ArrayList<>();
        List<RateVO> lstInner = new ArrayList<>();
        for (String type : types) {
            RateTreeVO rateVO = new RateTreeVO();
            rateVO.setName(type);
            rateVO.setCount(mapCount.getOrDefault(type, 0L));
            rateVO.setRate(DemoUtils.divide(rateVO.getCount(), rosterDetailList.size()));
            if ("核心港聘".equals(type) || "内派".equals(type)) {
                lstInner.add(rateVO);
            } else {
                lstOut.add(rateVO);
            }
        }
        RateTreeVO out = new RateTreeVO();
        out.setName("大团队核心层");
        out.setChildren(lstInner);
        out.setCount(lstInner.stream().mapToLong(RateVO::getCount).sum());
        out.setRate(DemoUtils.divide(out.getCount(), rosterDetailList.size()));
        lstOut.add(out);
        return lstOut;
    }

    private List<RosterDetail> getHKRosterBySiteList(HKDashQO qo) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ArrayList<>();
        }
        return rosterDetailService.selectHKRosterBySiteList(qo.getOrganizationCodeList(), headId, null);
    }

    /**
     * 大团队核心层：鼠标移到此处时，浮窗显示下列两类：
     * *          核心港聘：S taffclass3311=境外聘&rank_hk>=G13  --> 修改为负责人标签is_responsible IN (中建香港-地盤部門負責人、中建香港-地盘部门负责人、內派負責人、負責人、内派负责人、负责人) AND Staffclass3311=境外聘
     * *          内派：Staffclass3311=内派  --> 修改为 负责人标签is_responsible IN (中建香港-地盤部門負責人、中建香港-地盘部门负责人、內派負責人、負責人、内派负责人、负责人) AND Staffclass3311=内派
     * * 内地：Staffclass3311=内地
     * * 自有日薪：PERSKL=24，地盘日薪
     * * 地盘基层：除上述情况以外的员工
     *
     * @param rosterDetail 人员详情
     * @return
     */
    private String determineType(RosterDetail rosterDetail) {
        if ("内地".equals(rosterDetail.getStaffClass3311())) {
            return "内地";
        } else if ("内派".equals(rosterDetail.getStaffClass3311())) {
            return "内派";
        } else if ("境外聘".equals(rosterDetail.getStaffClass3311()) && convertManagerFlag(rosterDetail)) {
            return "核心港聘";
        } else if ("24".equals(rosterDetail.getEmployeeSubgroup())) {
            return "自有日薪";
        } else {
            return "地盘基层";
        }
    }

    /**
     * 判断是否为负责人标签 personInCharge IN staffClassManager(List)
     * @param rosterDetail
     * @return
     */
    private boolean convertManagerFlag(RosterDetail rosterDetail) {
        if (StringUtils.isBlank(rosterDetail.getPersonInCharge()) && StringUtils.isBlank(rosterDetail.getStaffClass3311())) {
            return false;
        }
        if (StringUtils.isNotBlank(rosterDetail.getPersonInCharge())){
            if (HrrsConsts.staffClassManager.contains(rosterDetail.getPersonInCharge())){
                return true;
            }
        }
        return false;
    }

    /**
     * remove starting G, convert to int, remove prefix 0
     *
     * @param hkJobGrade 香港职级
     * @return
     */
    public int convertHKJobGrade(String hkJobGrade) {
        if (StringUtils.startsWithIgnoreCase(hkJobGrade, "G")) {
            try {
                return Integer.parseInt(hkJobGrade.substring(1).replaceFirst("^0*", ""));
            } catch (NumberFormatException e) {
                return -1;
            }
        } else {
            return -1;
        }
    }

    private long getOrgAvgStaffCountForOutputValue(List<String> orgCodes, LocalDate localDate) {
        if (localDate == null) {
            localDate = LocalDate.now();
        }
        // 确定时间范围
        LocalDate startDate = localDate.withDayOfYear(1);
        LocalDate endDate = localDate.withDayOfMonth(localDate.lengthOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startDate, endDate);
        List<String> headIdList = rosterHeadIdList.stream()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .toList();

        // 获取香港人员统计信息并过滤 count > 0
        Map<String, Long> mapPersonCount = rosterDetailService
                .selectHKPersonCountForOutputValue(orgCodes, headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        return mapPersonCount.isEmpty() ? 0 : mapPersonCount.values().stream().mapToLong(Long::longValue).sum() / mapPersonCount.size();
    }

    /**
     * 获取香港人员详情列表
     */
    public List<RosterDetail> getHKRosterList(HKDashQO qo, Integer isProject) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ArrayList<>();
        }
        return rosterDetailService.selectHKRosterList(qo.getOrganizationCodeList(), headId, isProject);
    }

    public List<RosterDetail> getHKRosterList(HKDashQO qo) {
        return getHKRosterList(qo, null);
    }

    /**
     * 获取香港外包人员详情列表
     */
    public List<RosterDetail> getHKOutsourceRosterList(HKDashOutsourceQO qo) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ArrayList<>();
        }
        return rosterDetailService.selectHKOutsourceRosterList(qo.getOrganizationCodeList(), qo.getOutsourceTypeList(), headId);
    }

    /**
     * 获取公司年累计产值:2024年之前，从RawRevenueSite表取，2024年之后，取中央厨房SubjectBalance表中取科目为工程收入的数据
     *
     * @param siteCodeList
     * @param localDate
     * @return
     */
    private BigDecimal getHKCumulativeValue(List<String> siteCodeList, List<String> projectIdList, LocalDate localDate) {
        /*
        if (localDate.getYear() > 2024) {
            // 2024年之后，取中央厨房SubjectBalance表中科目为工程收入的数据  TODO
            return subjectBalanceService.listCumulativeValueByProjectIdAndYearMonth(projectIdList, localDate.getYear(), localDate.getMonthValue()).stream()
                    .map(SubjectBalance::getAmountYear)
                    .reduce(CommonUtils::add)
                    .orElse(BigDecimal.ZERO);
        } else {
            // 2024年之前，从RawRevenueSite表取
            return rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(sietCodeList, localDate).stream()
                    .filter(Objects::nonNull)
                    .map(RawRevenueSite::getAmountYear)
                    .reduce(CommonUtils::add)
                    .orElse(BigDecimal.ZERO);
        }
         */
        return rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(siteCodeList, localDate).stream()
                .filter(Objects::nonNull)
                .map(RawRevenueSite::getAmountYear)
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
    }
    
    private BigDecimal getHKCumulativeValueYear(List<String> siteCodeList,LocalDate localDate){
        return rawRevenueSiteService.listRawRevenueBySiteCodeAndYear(siteCodeList,localDate).stream()
                .filter(Objects::nonNull)
                .map(RawRevenueSite::getAmountYear)
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 获取职级分布统计数据
     * 按照以下职级分类统计人数及占比:
     * - 助总及以上/总监Ⅰ
     * - 总监Ⅱ/副总监/助理总监/高级经理/经理级
     * - 副经理/助理经理/高级工程师
     * - 工程师/助理工程师
     * - 学徒/文员/司机
     *
     * @param qo 查询参数
     * @return 职级分布统计列表
     */
    public List<RateVO> getJobLevelDistribution(HKDashQO qo) {
        // 获取统计日期,默认为当前日期
        LocalDate statDate = Optional.ofNullable(qo.getStatDate()).orElse(LocalDate.now());
        
        // 获取最新花名册
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(statDate);
        
        // 获取管理层人员明细列表
        List<RosterDetail> rosterDetailList = rosterDetailService.selectHKManagerRosterList(
            qo.getOrganizationCodeList(), 
            rosterHead.getId()
        );

        // 按职级分组统计人数
        Map<String, Long> jobLevelCountMap = rosterDetailList.stream()
                .collect(Collectors.groupingBy(
                    this::determineJobLevelName,
                    Collectors.counting()
                ));

        // 定义职级顺序
        List<String> jobLevelNames = Arrays.asList(
            "助总及以上/总监Ⅰ",
            "总监Ⅱ/副总监/助理总监/高级经理/经理级", 
            "副经理/助理经理/高级工程师",
            "工程师/助理工程师",
            "学徒/文员/司机"
        );

        // 计算总人数
        long totalCount = rosterDetailList.size();

        // 构建返回结果
        return jobLevelNames.stream()
            .map(levelName -> new RateVO(
                levelName,
                jobLevelCountMap.getOrDefault(levelName, 0L),
                DemoUtils.divide(jobLevelCountMap.getOrDefault(levelName, 0L), totalCount)
            ))
            .collect(Collectors.toList());
    }

    public ResultPage<RosterDetailCategoryVO> selectHKManagerRosterListByDetermineJobLevelName(HKJobDistributionQO qo,Integer pageNum,Integer pageSize){
        // 获取统计日期,默认为当前日期
        LocalDate statDate = Optional.ofNullable(qo.getStatDate()).orElse(LocalDate.now());
        // 获取最新花名册
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(statDate);
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKManagerRosterListByDetermineJobLevelName(qo.getOrganizationCodeList(), rosterHead.getId(), qo.getDetermineJobLevelName());
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportHKManagerRosterListByDetermineJobLevelName(HKJobDistributionQO qo,HttpServletResponse response) throws IOException {
        // 获取统计日期,默认为当前日期
        LocalDate statDate = Optional.ofNullable(qo.getStatDate()).orElse(LocalDate.now());
        // 获取最新花名册
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(statDate);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKManagerRosterListByDetermineJobLevelName(qo.getOrganizationCodeList(), rosterHead.getId(), qo.getDetermineJobLevelName());
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    /**
     * 助总及以上/总监Ⅰ：C4以上
     * 总监Ⅱ/副总监/助理总监/高级经理/经理级：D1、D2
     * 副经理/助理经理/高级工程师：D3、D4、E1
     * 工程师/助理工程师：E2、E3
     * 学徒/文员/司机：其他
     *
     * @param rosterDetail
     * @return
     */
    private String determineJobLevelName(RosterDetail rosterDetail) {
        String jobLevelCode = Optional.ofNullable(rosterDetail.getPositionLevelCode()).orElse("").trim().toUpperCase();
        if (StringUtils.startsWithIgnoreCase(jobLevelCode, "B")) {
            return "助总及以上/总监Ⅰ";
        }
        return JOB_LEVEL_MAP.getOrDefault(jobLevelCode, "学徒/文员/司机");
    }

    /**
     * 根据专业统计：positionType、
     *
     * @param qo 查询条件
     * @return
     */
    public List<RateVO> getStatByMajor(HKDashQO qo) {
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(getStatDate(qo));
        List<RosterDetail> rosterDetailList = rosterDetailService.selectHKManagerRosterList(qo.getOrganizationCodeList(), rosterHead.getId());
        if (rosterDetailList == null) {
            rosterDetailList = Collections.emptyList();
        }
        Map<String, Long> mapCount = rosterDetailList.stream()
                .filter(rosterDetail -> rosterDetail.getJobTypeHk() != null)
                .collect(Collectors.groupingBy(RosterDetail::getJobTypeHk, Collectors.counting()));
        long total = rosterDetailList.size();
        List<RateVO> rateVOList = new ArrayList<>();
        for (Map.Entry<String, Long> entry : mapCount.entrySet()) {
            rateVOList.add(new RateVO(entry.getKey(), entry.getValue(), DemoUtils.divide(entry.getValue(), total)));
        }
        return rateVOList;
    }

    public ResultPage<RosterDetailCategoryVO> getStatByMajorList(HKMajorQO qo, Integer pageNum, Integer pageSize){
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(getStatDate(qo));
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKManagerRosterListByMajor(qo.getOrganizationCodeList(), rosterHead.getId(), qo.getMajor());
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);
        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }
    public void exportStatByMajor(HKMajorQO qo,HttpServletResponse response) throws IOException {
        RosterHead rosterHead = rosterHeadService.getLatestRosterHead(getStatDate(qo));
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKManagerRosterListByMajor(qo.getOrganizationCodeList(), rosterHead.getId(), qo.getMajor());
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    private boolean isCurrentYearMonth(LocalDate date) {
        return LocalDate.now().getYear() == date.getYear() && LocalDate.now().getMonthValue() == date.getMonthValue();
    }

    public GetSalaryVO getSalaryStat(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");
        //List<BaseBusinessUnit> lstBaseBusinessUnit = baseBusinessUnitService.listByCompanyName("中建香港", statDate);
        List<BaseBusinessUnit> lstBaseBusinessUnit = baseBusinessUnitService.listByOrgCode(qo.getOrganizationCodeList(), statDate);
        List<ProjectInfo> lstProject = fisProjectInfoService.listBySiteCodes(lstBaseBusinessUnit.stream().map(BaseBusinessUnit::getProjectCdmsCode).toList());
        List<String> projectIdList = lstProject.stream().map(ProjectInfo::getId).toList();
//        List<SubjectBalance> subjectBalanceList = subjectBalanceService.listSalaryByProjectIdAndYearMonth(
//                projectIdList, statDate.getYear(), statDate.getMonthValue());

        // 当年累计薪酬总额
        List<SalaryRecordVO> cumulativeTotalSalaryRecordVOList = salaryRecordService.listYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        BigDecimal cumulativeTotalSalary = SalaryRecordFacade.calculateSalary(cumulativeTotalSalaryRecordVOList);

        // 当月薪酬总额
        List<SalaryRecordVO> salaryRecordVOList = salaryRecordService.listCurrentMonthTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        BigDecimal currentMonthTotalSalary = SalaryRecordFacade.calculateSalary(salaryRecordVOList);
        List<StaffCountFull> lstStaffCountFull = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
//        BigDecimal salaryMonth = subjectBalanceList.stream().map(SubjectBalance::getAmountPeriodHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
//        BigDecimal excludeSalary = StaffCountFullHelper.calcStaffCountMonthByDateAndCode(lstStaffCountFull, statDate, "XZ.02");
//        BigDecimal salaryYear = subjectBalanceList.stream().map(SubjectBalance::getAmountYear).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
//        BigDecimal excludeSalaryYear = StaffCountFullHelper.calcStaffCountYearByDateAndCode(lstStaffCountFull, statDate, "XZ.02");

        GetSalaryVO getSalaryVO = new GetSalaryVO();
//        getSalaryVO.setCurrentMonthTotalSalary(CommonUtils.subtract(salaryMonth, excludeSalary));
//        getSalaryVO.setCumulativeTotalSalary(CommonUtils.subtract(salaryYear, excludeSalaryYear));
        getSalaryVO.setCurrentMonthTotalSalary(currentMonthTotalSalary);
        getSalaryVO.setCumulativeTotalSalary(cumulativeTotalSalary);
        // 新需求:预算统计默认只统计 XZYS.01 薪酬预算（内派）+ XZYS.02 薪酬预算（核心港聘）+ XZYS.03 薪酬预算（地盘基层）+ XZYS.04 薪酬预算（内派中台）
        List<StaffCountFull> salaryBudgetList = lstStaffCountFull.stream().filter(item -> StringUtils.isNotBlank(item.getSubjectCode()) && HrrsConsts.BUDGET_SALARY.contains(item.getSubjectCode())).collect(Collectors.toList());
        getSalaryVO.setCurrentMonthSalaryBudget(StaffCountFullHelper.calcSalaryBudget(salaryBudgetList, statDate.withDayOfMonth(1), statDate.with(TemporalAdjusters.lastDayOfMonth())));
        // 新需求修改为统计日期所选的整年薪酬预算
        getSalaryVO.setCumulativeSalaryBudget(StaffCountFullHelper.calcSalaryBudget(salaryBudgetList, statDate.withDayOfYear(1), statDate.with(TemporalAdjusters.lastDayOfYear())));
        // 预算使用率,1月至今实发薪酬/全年薪酬指标
        getSalaryVO.setBudgetUsedRatio(DemoUtils.divide(cumulativeTotalSalary,getSalaryVO.getCumulativeSalaryBudget(),4));

        long hKOrgOnJobCountForOutputValue = rosterDetailService.selectHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(), statDate.withDayOfYear(1), statDate).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum).orElse(0L);
        BigDecimal avgHKOrgOnJobCountForOutputValue = DemoUtils.divide(BigDecimal.valueOf(hKOrgOnJobCountForOutputValue),
                BigDecimal.valueOf(statDate.getMonthValue()), 4);

        //BigDecimal cumulativeValue = getHKCumulativeValue(organizationFacade.listAllHKSiteCode(), projectIdList, statDate);
        List<String> siteCode = baseBusinessUnitService.listCdmsCodeByOrgCode(qo.getOrganizationCodeList(), statDate);
        BigDecimal cumulativeValue = getHKCumulativeValue(siteCode, projectIdList, statDate);
        // 年总产值
        BigDecimal cumulativeValueYear = StaffCountFullHelper.calcTargetOutputValueYearByDate(lstStaffCountFull,statDate).multiply(BigDecimal.valueOf(10000));
        // 全年编制汇总
        BigDecimal totalYearStaffCount = StaffCountFullHelper.calcStaffCountYearByDate(lstStaffCountFull, statDate);
        // 目标值(成本)  所选年月全年（XZYS.01~04）的薪酬预算数汇总/目标产值汇总*100
        BigDecimal costTargetValue = BigDecimal.ZERO;
        if (cumulativeValueYear.compareTo(BigDecimal.ZERO) != 0){
            costTargetValue = DemoUtils.divide(getSalaryVO.getCumulativeSalaryBudget(),cumulativeValueYear,4).multiply(BigDecimal.valueOf(100));
        }

        // 目标值(工资)  所选年月全年目标产值汇总/全年（XZYS.01~04）的薪酬预算数汇总
        BigDecimal targetSalaryValue = BigDecimal.ZERO;
        if (getSalaryVO.getCumulativeSalaryBudget().compareTo(BigDecimal.ZERO) != 0){
            targetSalaryValue = DemoUtils.divide(cumulativeValueYear,getSalaryVO.getCumulativeSalaryBudget(),4);
        }
        getSalaryVO.setTargetSalaryValue(targetSalaryValue);
        getSalaryVO.setCostTargetValue(costTargetValue);
        // 目标值(产值) 所选年月全年的目标产值汇总/全年（BZ.01~04）编制数汇总 * 12个月
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        if (totalYearStaffCount.compareTo(BigDecimal.ZERO) != 0){
            targetOutputValue = DemoUtils.divide(cumulativeValueYear,totalYearStaffCount,4).multiply(BigDecimal.valueOf(12));
        }
        getSalaryVO.setTargetOutputValue(targetOutputValue);
        getSalaryVO.setPerCapitaOutput(DemoUtils.divide(cumulativeValue, avgHKOrgOnJobCountForOutputValue).setScale(1, RoundingMode.HALF_UP));
        getSalaryVO.setLaborCostPer100Output(DemoUtils.divide(getSalaryVO.getCumulativeTotalSalary(), cumulativeValue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));

        // 工资产值率 = 本年度截止所选年月累计【产值】/本年度截止所选年月累计【薪酬】
        getSalaryVO.setSalaryToAssetsRatio(DemoUtils.divide(cumulativeValue, getSalaryVO.getCumulativeTotalSalary(), 4));
        // 人均利润 TODO

        return getSalaryVO;
    }

    /**
     * 中建香港板块下查询公司人员编制概况
     * <p>
     * 工程公司：按6大公司级架构展示：房屋、土木、基础（含机械公司）、机电、中建医疗、海宏（含中资讯），按组织code排序
     * 平均在岗人数：本年度截止到所选年月，工程公司每月人数之和/月份数
     * 平均编制使用率：本年度截止到所选年月，工程公司(每月底人数之和)/（每月底编制人数之和）
     * 累计薪酬执行率：本年度截止到所选年月，工程公司累计薪酬总额/累计薪酬预算金额
     * 人均产值：本年度截止到所选年月，工程公司每月产值之和/【年度平均人数】
     * 百元产值人工成本：本年度截止到所选年月，工程公司累计薪酬总额/累计产值*100
     *
     * @param qo 查询条件
     * @return
     */
    public List<CompanyOverviewVO> getSubsidiaryOverview(HKDashQO qo) {

        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        List<NameCountVO> subsidiaryCountList = rosterDetailService.selectHKPersonCountGroupBySubsidiary(HrrsConsts.HK_SUBSIDIARY_LIST, startDate, endDate);
        Map<String, Long> mapCount = subsidiaryCountList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
        Map<String, Long> mapAvgCount = new HashMap<>();
        // mapCount 的每一个公司对应的人数都应该 除以 endDate 的月份，以得到平均数
        for (Map.Entry<String, Long> entry : mapCount.entrySet()) {
            mapAvgCount.put(entry.getKey(), entry.getValue() / endDate.getMonthValue());
        }

        List<Organization> orgList = organizationService.listByCompanyNameAndSubCompanyName(List.of("中建香港"), HrrsConsts.HK_SUBSIDIARY_LIST);
        Map<String, List<Organization>> subsidiaryOrgMap = orgList.stream().collect(Collectors.groupingBy(Organization::getSubCompanyDept));
        // subCompanyDept -> List<siteCode>
        Map<String, List<String>> mapSubCompanyDept = orgList.stream()
                .collect(Collectors.groupingBy(Organization::getSubCompanyDept, Collectors.mapping(Organization::getSiteCode, Collectors.toList())));
        Map<String, List<String>> mapSubCompanyDeptToCode = orgList.stream()
                .collect(Collectors.groupingBy(Organization::getSubCompanyDept, Collectors.mapping(Organization::getCode, Collectors.toList())));

        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        List<ProjectInfo> projectInfoList = fisProjectInfoService.listBySiteCodes(orgList.stream().map(Organization::getSiteCode).toList());
        Map<String, String> cdmsCodeToProjectIdMap = projectInfoList.stream().collect(Collectors.toMap(ProjectInfo::getProjectCode, ProjectInfo::getId, (k1, k2) -> k1));
        List<SubjectBalance> subjectBalanceList = subjectBalanceService.listSalaryByProjectIdAndYearMonth(projectInfoList.stream().map(ProjectInfo::getId).toList(), statDate.getYear(), statDate.getMonthValue());

        // 查询全部产值记录,只需查到指定年月的数据，去累计值即可
        int month = statDate.getMonthValue();
        // todo 改为使用 rawRevenueSiteService
        int maxMonth = rawRevenueService.getMaxMonthByYear(statDate.getYear());
        if (month > maxMonth) {
            month = maxMonth;
        }
        List<RawRevenue> rawRevenueList = rawRevenueService.listByCompanyNamesAndYearMonth(null, statDate.getYear(), month);
        /*基础（含机械），海宏（含中资讯）*/
        rawRevenueList = handleSubsidiaryRevenue(rawRevenueList);
        Map<String, RawRevenue> mapSubsidiaryRevenue = rawRevenueList.stream().collect(Collectors.toMap(r -> TranslateUtils.convertToPinyin(r.getCompanyName()), x -> x, (k1, k2) -> k1));

        List<CompanyOverviewVO> lstOverview = new ArrayList<>();
        for (String subsidiary : HrrsConsts.DISPLAY_SUBSIDIARY_LIST) {
            String pinyinSubsidiary = TranslateUtils.convertToPinyin(subsidiary);
            CompanyOverviewVO overviewVO = new CompanyOverviewVO();
            overviewVO.setCompanyName(subsidiary);

            // 通过 mapSubCompanyDept 取出 siteCodeList，再通过 cdmsCodeToProjectIdMap 取出 projectIdList
            List<String> subsidiaryProjectIdList = mapSubCompanyDept.get(subsidiary).stream().map(cdmsCodeToProjectIdMap::get).collect(Collectors.toList());
            List<String> subsidiaryOrgCodeList = subsidiaryOrgMap.get(subsidiary).stream().map(Organization::getCode).collect(Collectors.toList());

            long avgOnJobCount = mapAvgCount.getOrDefault(subsidiary, 0L);
            long onJobCount = mapCount.getOrDefault(subsidiary, 0L);
            // 取StaffCountFull的 organizationCode 应该包含在 [mapSubCompanyDept 根据 subsidiary 取出的 siteCodeList] 中
            List<StaffCountFull> subsidiaryStaffCountList = lstStaffCount.stream().filter(staffCountFull -> mapSubCompanyDeptToCode.get(subsidiary).contains(staffCountFull.getOrganizationCode())).collect(Collectors.toList());

            if (TranslateUtils.convertToPinyin("基础公司").equals(pinyinSubsidiary)) {
                avgOnJobCount += mapAvgCount.getOrDefault("机械公司", 0L);
                onJobCount += mapCount.getOrDefault("机械公司", 0L);
                subsidiaryStaffCountList.addAll(lstStaffCount.stream().filter(staffCountFull -> mapSubCompanyDeptToCode.get("机械公司").contains(staffCountFull.getOrganizationCode())).toList());
                subsidiaryProjectIdList.addAll(mapSubCompanyDept.get("机械公司").stream().map(cdmsCodeToProjectIdMap::get).toList());
                subsidiaryOrgCodeList.addAll(subsidiaryOrgMap.get("机械公司").stream().map(Organization::getCode).toList());
            } else if ("海宏公司".equals(subsidiary)) {
                avgOnJobCount += mapAvgCount.getOrDefault("中建资讯科技公司", 0L);
                onJobCount += mapCount.getOrDefault("中建资讯科技公司", 0L);
                subsidiaryStaffCountList.addAll(lstStaffCount.stream().filter(staffCountFull -> mapSubCompanyDeptToCode.get("中建资讯科技公司").contains(staffCountFull.getOrganizationCode())).toList());
                subsidiaryProjectIdList.addAll(mapSubCompanyDept.get("中建资讯科技公司").stream().map(cdmsCodeToProjectIdMap::get).toList());
                subsidiaryOrgCodeList.addAll(subsidiaryOrgMap.get("中建资讯科技公司").stream().map(Organization::getCode).toList());
            }
            List<SubjectBalance> subsidiarySubjectBalanceList = subjectBalanceList.stream().filter(subjectBalance -> subsidiaryProjectIdList.contains(subjectBalance.getProjectId())).toList();

            overviewVO.setAvgOnJobCount(avgOnJobCount);

            long totalStaffCountPlan = StaffCountFullHelper.calcStaffCountOfYear(subsidiaryStaffCountList, statDate);
            overviewVO.setAvgUsageRate(DemoUtils.divide(onJobCount, totalStaffCountPlan));

            overviewVO.setCumulativeSalaryAmount(subsidiarySubjectBalanceList.stream().map(SubjectBalance::getAmountYearHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));

            BigDecimal budget = StaffCountFullHelper.calcSalaryBudget(subsidiaryStaffCountList, startDate, endDate);
            log.info("cumulativeSalaryAmount is {},getSubsidiaryOverview budget is {} ", overviewVO.getCumulativeSalaryAmount(), budget);
            overviewVO.setCumulativeSalaryExecRate(DemoUtils.divide(overviewVO.getCumulativeSalaryAmount(), budget));

            BigDecimal cumulativeValue = Optional.ofNullable(mapSubsidiaryRevenue.get(pinyinSubsidiary)).map(RawRevenue::getAccumulateAmount).orElse(BigDecimal.ZERO);
            cumulativeValue = new BigDecimal("10000").multiply(cumulativeValue);
            overviewVO.setPerCapitaOutputValue(DemoUtils.divide(cumulativeValue, avgOnJobCount));

            overviewVO.setLaborCostPerHundredOutputValue(DemoUtils.divide(overviewVO.getCumulativeSalaryAmount(), cumulativeValue).multiply(BigDecimal.valueOf(100)));

            lstOverview.add(overviewVO);
        }
        return lstOverview;
    }

    public List<CompanyOverviewVO> getDepartmentOverview(HKDashQO hkDashQO) {
        LocalDate statDate = getStatDate(hkDashQO);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        List<NameCountVO> departmentCountList = rosterDetailService.selectHKPersonCountGroupBySubsidiary(HrrsConsts.HK_DEPARTMENT_LIST, startDate, endDate);
        Map<String, Long> mapCount = departmentCountList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
        Map<String, Long> mapAvgCount = new HashMap<>();
        for (Map.Entry<String, Long> entry : mapCount.entrySet()) {
            mapAvgCount.put(entry.getKey(), entry.getValue() / endDate.getMonthValue());
        }

        List<CompanyOverviewVO> lstOverview = new ArrayList<>();
        for (String department : HrrsConsts.HK_DEPARTMENT_LIST) {
            CompanyOverviewVO overviewVO = new CompanyOverviewVO();
            overviewVO.setCompanyName(department);

            long avgOnJobCount = mapAvgCount.getOrDefault(department, 0L);
            long onJobCount = mapCount.getOrDefault(department, 0L);

            // 获取该部门下的所有组织机构代码
            List<String> orgCodeList = getSubCompanyDeptsOrgCodeList(department);
            List<StaffCountFull> departmentStaffCountList = staffCountFullService.listByOrgCodes(orgCodeList);
            long totalStaffCountPlan = StaffCountFullHelper.calcStaffCountOfYear(departmentStaffCountList, statDate);
            overviewVO.setAvgOnJobCount(avgOnJobCount);
            overviewVO.setAvgUsageRate(DemoUtils.divide(onJobCount, totalStaffCountPlan));

            // 薪酬
            List<String> projectIdList = organizationFacade.listFisProjectIdByOrgCodes(orgCodeList);
            List<SubjectBalance> subjectBalanceList = subjectBalanceService.listSalaryByProjectIdAndYearMonth(projectIdList, statDate.getYear(), statDate.getMonthValue());
            BigDecimal cumulativeSalary = subjectBalanceList.stream().map(SubjectBalance::getAmountYearHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
            cumulativeSalary = CommonUtils.add(cumulativeSalary, calcSalaryByStaffCountFull(departmentStaffCountList, startDate, endDate));
            overviewVO.setCumulativeSalaryAmount(cumulativeSalary);

            BigDecimal budget = StaffCountFullHelper.calcSalaryBudget(departmentStaffCountList, startDate, endDate);
            overviewVO.setCumulativeSalaryExecRate(DemoUtils.divide(overviewVO.getCumulativeSalaryAmount(), budget));

            lstOverview.add(overviewVO);
        }
        return lstOverview;
    }

    /**
     * 房屋、土木、基础（含机械公司）、机电、中建医疗、海宏（含中资讯）
     * 将基础公司和机械公司、海宏公司和中建资讯科技公司合并
     *
     * @param rawRevenueList
     * @return
     */
    private List<RawRevenue> handleSubsidiaryRevenue(List<RawRevenue> rawRevenueList) {
        List<RawRevenue> lstRevenue = new ArrayList<>();
        Map<String, RawRevenue> mapRevenue = rawRevenueList.stream().collect(Collectors.toMap(a -> TranslateUtils.convertToPinyin(a.getCompanyName()), x -> x, (k1, k2) -> k1));
        for (String subsidiary : HrrsConsts.DISPLAY_SUBSIDIARY_LIST) {
            String pinyin = TranslateUtils.convertToPinyin(subsidiary);
            RawRevenue rawRevenue = new RawRevenue();
            RawRevenue subsidiaryRevenue = mapRevenue.get(pinyin);
            if (subsidiaryRevenue != null) {
                BeanUtils.copyProperties(subsidiaryRevenue, rawRevenue);
            }
            if (TranslateUtils.convertToPinyin("基础公司").equals(pinyin)) {
                RawRevenue mechanicalRevenue = mapRevenue.get(TranslateUtils.convertToPinyin("机械公司"));
                if (mechanicalRevenue != null) {
                    sumTo(rawRevenue, mechanicalRevenue);
                }
            } else if (TranslateUtils.convertToPinyin("海宏公司").equals(pinyin)) {
                RawRevenue informationRevenue = mapRevenue.get(TranslateUtils.convertToPinyin("中建资讯科技公司"));
                if (informationRevenue != null) {
                    sumTo(rawRevenue, informationRevenue);
                }
            }
            lstRevenue.add(rawRevenue);
        }
        return lstRevenue;
    }

    public SubsidiaryPersonCountVO getSubsidiaryPersonCount(HKDashQO qo) {
        if (CollectionUtils.isEmpty(qo.getOrganizationCodeList())) {
            // 设置所有地盘编码
            qo.setOrganizationCodeList(organizationService.listAllHKSubCompanyOrgs().stream().map(Organization::getCode).collect(Collectors.toList()));
        }
        LocalDate statDate = getStatDate(qo);
        // 确定时间范围
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        // 获取头信息列表及其 ID
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(startDate, endDate);
        List<String> headIdList = rosterHeadIdList.stream()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .toList();

        // 获取香港人员统计信息并过滤 count > 0
        Map<String, Long> mapPersonCount = rosterDetailService
                .selectHKPersonCount(qo.getOrganizationCodeList(), headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        // 计算平均人数
        long avgCount = mapPersonCount.isEmpty() ? 0 : mapPersonCount.values().stream().mapToLong(Long::longValue).sum() / mapPersonCount.size();

        // 构建结果对象
        SubsidiaryPersonCountVO subsidiaryPersonCountVO = new SubsidiaryPersonCountVO();
        subsidiaryPersonCountVO.setAvgOnJobCount(avgCount);
        // 本年度截止到所选年月，所选地盘每个月人数之和/编制人数之和
        long total = mapPersonCount.values().stream().mapToLong(Long::longValue).sum();
        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        long staffCountPlan = StaffCountFullHelper.calcStaffCountPlan(lstStaffCount, startDate, endDate);


        subsidiaryPersonCountVO.setStaffCount(staffCountPlan / endDate.getMonthValue());
        subsidiaryPersonCountVO.setStaffUtilRate(DemoUtils.divide(total, staffCountPlan));

        // List<String> cdmsCodeList = baseBusinessUnitService.listCdmsCodeByOrgCode(qo.getOrganizationCodeList(), statDate);
        List<String> siteCodeList = organizationService.listByCodes(qo.getOrganizationCodeList()).stream().map(Organization::getSiteCode).toList();
//        List<ProjectInfo> lstProject = fisProjectInfoService.listBySiteCodes(siteCodeList);
//        List<String> projectIdList = lstProject.stream().map(ProjectInfo::getId).toList();
//        List<SubjectBalance> lstSubjectBalance = subjectBalanceService.listSalaryByProjectIdAndYearMonth(projectIdList, startDate.getYear(), statDate.getMonthValue());
//        // 当前薪酬总额：本年度截止到所选年月，所选地盘薪酬总金额
//        BigDecimal currentTotalSalary = lstSubjectBalance.stream().map(SubjectBalance::getAmountYear).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);

        // 当年累计薪酬总额
        List<SalaryRecordVO> cumulativeTotalSalaryRecordVOList = salaryRecordService.listYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        BigDecimal cumulativeTotalSalary = SalaryRecordFacade.calculateSalary(cumulativeTotalSalaryRecordVOList);
        BigDecimal cumulativeManagerTotalSalary = SalaryRecordFacade.calculateManagerSalary(cumulativeTotalSalaryRecordVOList);
        // 当前薪酬总额
        subsidiaryPersonCountVO.setCurrentTotalSalary(cumulativeTotalSalary);

        // 取subjectType = 薪酬预算
        // 新需求，薪酬预算默认只算 XZYS.01 薪酬预算（内派）+ XZYS.02 薪酬预算（核心港聘）+ XZYS.03 薪酬预算（地盘基层）+ XZYS.04 薪酬预算（内派中台
        List<StaffCountFull> salaryBudgetCount = lstStaffCount.stream().filter(item -> StringUtils.isNotBlank(item.getSubjectCode()) && !HrrsConsts.BUDGET_EXCLUDE_SALARY.contains(item.getSubjectCode())).collect(Collectors.toList());
        BigDecimal salaryBudget = StaffCountFullHelper.calcSalaryBudget(salaryBudgetCount, startDate, endDate);
        subsidiaryPersonCountVO.setSalaryBudget(salaryBudget);
        // 本年度截止到所选年月，所选地盘薪酬总金额之和/薪酬预算金额之和
        subsidiaryPersonCountVO.setCurrentSalaryUtilRate(DemoUtils.divide(cumulativeTotalSalary, salaryBudget));

        // List<RawRevenue> lstRevenue = rawRevenueService.listByCompanyNamesAndYear(qo.getOrganizationCodeList(), startDate.getYear());
        List<RawRevenueSite> rawRevenueSiteList = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(siteCodeList, statDate);

        BigDecimal yearTotalRevenue = rawRevenueSiteList.stream().map(RawRevenueSite::getAmountYear).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
        long totalPersonCount = rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), startDate, endDate).stream().map(NameCountVO::getCount).reduce(Long::sum).orElse(0L);
        long hKOrgOnJobCountForOutputValue = rosterDetailService.selectHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(), statDate.withDayOfYear(1), statDate).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum).orElse(0L);
        BigDecimal avgHKOrgOnJobCountForOutputValue = DemoUtils.divide(BigDecimal.valueOf(hKOrgOnJobCountForOutputValue),
                BigDecimal.valueOf(statDate.getMonthValue()), 4);

        // 人均产值：本年度截止到所选年月，所选地盘每月产值之和/当前平均人数
        subsidiaryPersonCountVO.setPerCapitaOutput(DemoUtils.divide(yearTotalRevenue, avgHKOrgOnJobCountForOutputValue));
        // 管理人员平均薪酬（万港元/人/月）：取各工程公司【内派+港聘管理人员+内地管理人员+内地中智外包】四维度的薪酬总额汇总/发薪人数汇总
        subsidiaryPersonCountVO.setManagerAvgSalary(DemoUtils.divide(cumulativeManagerTotalSalary, totalPersonCount));
        // 本年度编制汇总
        BigDecimal totalYearStaffCount = StaffCountFullHelper.calcStaffCountYearByDate(lstStaffCount, startDate);
        // 所选日期年度总产值
        BigDecimal cumulativeValueYear = StaffCountFullHelper.calcTargetOutputValueYearByDate(lstStaffCount,statDate).multiply(BigDecimal.valueOf(10000));
        // 目标值 = 所选年月全年的目标产值汇总/全年（BZ.01~04）编制数汇总 * 12个月
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        if (totalYearStaffCount.compareTo(BigDecimal.ZERO) != 0){
            targetOutputValue = DemoUtils.divide(cumulativeValueYear,totalYearStaffCount,4).multiply(BigDecimal.valueOf(12));
        }
        subsidiaryPersonCountVO.setTargetOutputValue(targetOutputValue);
        // 所选日期年度薪酬预算
        LocalDate startYear = statDate.withDayOfYear(1);
        LocalDate endYear = statDate.with(TemporalAdjusters.lastDayOfYear());
        BigDecimal totalYearBudget = StaffCountFullHelper.calcSalaryBudget(salaryBudgetCount, startYear, endYear);
        // 目标值 = 所选年月全年（XZYS.01~04）的薪酬预算数汇总/目标产值汇总*100
        BigDecimal costTargetValue = BigDecimal.ZERO;
        if (cumulativeValueYear.compareTo(BigDecimal.ZERO) != 0){
            costTargetValue = DemoUtils.divide(totalYearBudget,cumulativeValueYear).multiply(BigDecimal.valueOf(100));
        }
        subsidiaryPersonCountVO.setCostTargetValue(costTargetValue);
        subsidiaryPersonCountVO.setLaborCostPerHundredOutput(DemoUtils.divide(cumulativeTotalSalary, yearTotalRevenue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));

        return subsidiaryPersonCountVO;
    }

    private BigDecimal calcSalaryByStaffCountFull(List<StaffCountFull> staffCountFullList, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(staffCountFullList)) {
            return BigDecimal.ZERO;
        }
        // XZ.01	管理人员薪金（内地）
        // XZ.03	内地外包人员薪金
        // 取subjectCode in (XZ.01, XZ.03)
        List<StaffCountFull> lstStaffCount = staffCountFullList.stream()
                .filter(staffCountFull -> "XZ.01".equalsIgnoreCase(staffCountFull.getSubjectCode()) || "XZ.03".equalsIgnoreCase(staffCountFull.getSubjectCode()))
                .toList();
        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : lstStaffCount) {
            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
                total = total.add(StaffCountFullHelper.getValueByDate(staffCountFull, date));
            }
        }
        return total;
    }

    private BigDecimal calcSalaryByStaffCountFullCurrentMonth(List<StaffCountFull> staffCountFullList, LocalDate startDate) {
        if (CollectionUtils.isEmpty(staffCountFullList)) {
            return BigDecimal.ZERO;
        }
        // XZ.01	管理人员薪金（内地）
        // XZ.03	内地外包人员薪金
        // 取subjectCode in (XZ.01, XZ.03)
        List<StaffCountFull> lstStaffCount = staffCountFullList.stream()
                .filter(staffCountFull -> "XZ.01".equalsIgnoreCase(staffCountFull.getSubjectCode()) || "XZ.03".equalsIgnoreCase(staffCountFull.getSubjectCode()))
                .toList();
        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : lstStaffCount) {
            total = total.add(StaffCountFullHelper.getValueByDate(staffCountFull, startDate));
        }
        return total;
    }

    /**
     * 地盘：按累计薪酬使用率降序排名
     * 累计在岗人数情况：地盘开工年月截止到所选年月，该地盘人数之和
     * 累计薪酬使用情况：地盘开工年月截止到所选年月，该地盘薪酬总金额之和
     * 累计薪酬使用率：地盘开工年月截止到所选年月，该地盘薪酬总金额之和/薪酬预算金额之和
     *
     * @param qo
     * @return
     */
    public List<PersonSalaryWarningVO> getPersonSalaryWarning(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        List<Organization> lstOrgs = organizationService.listByCodes(qo.getOrganizationCodeList());
        Map<String, Organization> mapOrgCodeOrg = lstOrgs.stream().collect(Collectors.toMap(Organization::getCode, Function.identity()));
        // List<BaseBusinessUnit> lstBusinessUnit = baseBusinessUnitService.listByCodeAndDate(qo.getOrganizationCodeList(), statDate);
        // Map<String, BaseBusinessUnit> mapOrgCodeBusinessUnit = lstBusinessUnit.stream().collect(Collectors.toMap(BaseBusinessUnit::getBucode, Function.identity()));
        List<String> siteCodeList = lstOrgs.stream().map(Organization::getSiteCode).toList();
        List<ProjectInfo> lstProject = fisProjectInfoService.listBySiteCodes(siteCodeList);
        Map<String, String> mapCdmsCodeProjectId = lstProject.stream().collect(Collectors.toMap(ProjectInfo::getProjectCode, ProjectInfo::getId, (a, b) -> a));

        List<SubjectBalance> allSubjectBalanceList = subjectBalanceService.listSalaryByProjectId(lstProject.stream().map(ProjectInfo::getId).toList());
        Map<String, List<SubjectBalance>> mapSubjectBalanceStore = allSubjectBalanceList.stream().collect(Collectors.groupingBy(SubjectBalance::getProjectId));

        // 这样做默认所有的编制都是填写的相同的期间日期
        LocalDate[] dateRange;
        StaffCountFull staffCountFull = lstStaffCount.stream()
                .findFirst()
                .orElse(null);
        if (staffCountFull == null) {
            dateRange = new LocalDate[]{statDate.withDayOfYear(1), statDate.withDayOfMonth(statDate.lengthOfMonth())};
        } else {
            dateRange = StaffCountHelper.getStartDateAndEndDate(staffCountFull);
            dateRange[0] = dateRange[0] == null ? statDate.withDayOfYear(1) : dateRange[0];
            dateRange[1] = dateRange[1] == null ? statDate.withDayOfMonth(statDate.lengthOfMonth()) : dateRange[1];
        }

        List<NameCountVO> lstPersonCountVO = rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), dateRange[0], dateRange[1]);
        Map<String, Long> mapOrgPersonCount = lstPersonCountVO.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<PersonSalaryWarningVO> lstWarning = new ArrayList<>();
        Long onJobTotal = 0L; // 累计在岗人数 -- 总计
        BigDecimal cumulativeSalaryTotal = BigDecimal.ZERO; // 累计薪酬总额 -- 总计
        BigDecimal[] budgetTotal = {BigDecimal.ZERO};  // 薪酬预算 -- 总计

        for (Organization org : lstOrgs) {
            PersonSalaryWarningVO personSalaryWarningVO = new PersonSalaryWarningVO();
            personSalaryWarningVO.setName(org.getName());

            Long onJobCount = mapOrgPersonCount.getOrDefault(org.getCode(), 0L);
            personSalaryWarningVO.setOnJobCount(onJobCount);
            onJobTotal = onJobTotal + onJobCount;
            String projectId = mapCdmsCodeProjectId.get(mapOrgCodeOrg.get(org.getCode()).getSiteCode());
            if (StringUtils.isNotBlank(projectId)) {
                List<SubjectBalance> lstSubjectBalance = mapSubjectBalanceStore.get(projectId);
                BigDecimal cumulativeSalary = calcCumulativeSalary(lstSubjectBalance, statDate);
                personSalaryWarningVO.setCumulativeSalary(cumulativeSalary);
                cumulativeSalaryTotal = cumulativeSalaryTotal.add(cumulativeSalary);
            }
            lstStaffCount.stream().filter(staffCount -> staffCount.getOrganizationCode().equals(org.getCode()) && "薪酬预算".equals(staffCount.getSubjectType()))
                    .findFirst()
                    .ifPresent(staffCount -> {
                        BigDecimal budget = StaffCountFullHelper.calcStaffCountFullValue(staffCount);
                        budgetTotal[0] = budgetTotal[0].add(budget);
                        log.info("org:{},budget: {}", staffCount.getOrganizationName(), budget);
                        personSalaryWarningVO.setCumulativeSalaryUsageRate(DemoUtils.divide(personSalaryWarningVO.getCumulativeSalary(), budget));
                    });

            lstWarning.add(personSalaryWarningVO);
        }
        // 按累计在岗人数倒叙，如果是null则排在最后
        lstWarning.sort(Comparator.comparing(PersonSalaryWarningVO::getCumulativeSalaryUsageRate, Comparator.nullsLast(Comparator.reverseOrder())));

        PersonSalaryWarningVO totalWarning = new PersonSalaryWarningVO();
        totalWarning.setName("总计");
        totalWarning.setOnJobCount(onJobTotal);
        totalWarning.setCumulativeSalary(cumulativeSalaryTotal);
        log.info("budgetTotal: {}", budgetTotal[0]);
        totalWarning.setCumulativeSalaryUsageRate(DemoUtils.divide(cumulativeSalaryTotal, budgetTotal[0])); // 累计薪酬总额/薪酬预算总额
        lstWarning.add(totalWarning);

        return lstWarning;
    }

    /**
     * 取每个subjectCode的最大年份和月份那条数据，这个最大年月需要小于参数日期的年月，进行求和
     *
     * @param subjectBalanceList
     * @return
     */
    private BigDecimal calcCumulativeSalary(List<SubjectBalance> subjectBalanceList, LocalDate statDate) {
        if (CollectionUtils.isEmpty(subjectBalanceList)) {
            return BigDecimal.ZERO;
        }
        // 先过滤掉大于参数日期的数据
        subjectBalanceList = subjectBalanceList.stream()
                .filter(subjectBalance -> {
                    LocalDate date = LocalDate.of(subjectBalance.getYear(), subjectBalance.getPeriod(), 1);
                    return !date.isAfter(statDate);
                })
                .toList();
        // 按subjectCode分组, 取每个subjectCode的最大年份和月份那条数据
        Map<String, SubjectBalance> mapSubjectCodeMaxDate = subjectBalanceList.stream()
                .collect(Collectors.groupingBy(SubjectBalance::getSubjectCode,
                        Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparingInt(SubjectBalance::getYear).thenComparingInt(SubjectBalance::getPeriod)),
                                Optional::get)));
        return mapSubjectCodeMaxDate.values().stream().filter(Objects::nonNull)
                .map(SubjectBalance::getAmountEndHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
    }


    /**
     * 人均产值：地盘开工年月截止到所选年月，每月产值之和/每月在职人数之和
     * <p>
     * 全周期人均效能排名：
     * <p>
     * 地盘：人均产值降序
     * 人均产值：地盘开工年月截止到所选年月，每月产值之和/每月在职人数之和
     * <p>
     * 地盘：百产成本降序
     * 百元产值人工成本：地盘开工年月截止到所选年月，累计薪酬总额/累计产值*100
     *
     * @param qo
     * @return
     */
    public EfficiencyRankingVO getEfficiencyRanking(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");

        List<StaffCountFull> lstStaffCountFull = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        LocalDate[] dateRange = CollectionUtils.isEmpty(lstStaffCountFull) ? null : StaffCountHelper.getStartDateAndEndDate(lstStaffCountFull.get(0));
        LocalDate startDate = dateRange != null ? dateRange[0] : null;
        LocalDate endDate = dateRange != null ? dateRange[1] : null;

        List<BaseBusinessUnit> baseBusinessUnitList = baseBusinessUnitService.listByCodeAndDate(qo.getOrganizationCodeList(), statDate);
        Map<String, BaseBusinessUnit> orgCodeToBaseBusinessUnitMap = baseBusinessUnitList.stream().collect(Collectors.toMap(BaseBusinessUnit::getBucode, Function.identity()));
        List<String> cdmsCodeList = baseBusinessUnitList.stream().map(BaseBusinessUnit::getProjectCdmsCode).toList();
        List<ProjectInfo> lstProjectInfo = fisProjectInfoService.listBySiteCodes(cdmsCodeList).stream().toList();
        Map<String, ProjectInfo> cdmsCodeToProjectInfoMap = lstProjectInfo.stream().collect(Collectors.toMap(ProjectInfo::getProjectCode, Function.identity(), (k1, k2) -> k1));
        // cdmsCode -> projectId
        List<String> projectIdList = lstProjectInfo.stream().map(ProjectInfo::getId).toList();

        List<RawRevenueSite> rawRevenueSiteList = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(cdmsCodeList,
                statDate);
        Map<String, RawRevenueSite> cdmsCodeToRawRevenueSiteMap = rawRevenueSiteList.stream()
                .collect(Collectors.toMap(RawRevenueSite::getSiteCode, Function.identity(), (k1, k2) -> k1));

        Map<String, Long> orgPersonCountMap = rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), startDate, endDate).stream()
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));


        Set<Integer> yearSet = getYearSet(startDate, endDate);

        List<SubjectBalance> lstSubjectBalance = CollectionUtils.isEmpty(yearSet) ?
                new ArrayList<>() :
                filterSubjectBalance(subjectBalanceService.listSalaryByProjectIdAndYear(new ArrayList<>(projectIdList), yearSet), startDate, endDate);

        List<NameValueVO> perCapitaOutputList = new ArrayList<>();
        List<NameValueVO> laborCostPerHundredOutputList = new ArrayList<>();

        for (String orgCode : qo.getOrganizationCodeList()) {
            // Organization organization = organizationHolder.findOrganizationByCode(orgCode);
            BaseBusinessUnit baseBusinessUnit = orgCodeToBaseBusinessUnitMap.get(orgCode);

            long onJobCount = Optional.ofNullable(orgPersonCountMap.get(orgCode)).orElse(0L);

            // 人均产值：地盘开工年月截止到所选年月，每月产值之和/每月在职人数之和
            BigDecimal cumulativeRevenue = Optional.ofNullable(cdmsCodeToRawRevenueSiteMap.get(baseBusinessUnit.getProjectCdmsCode())).map(RawRevenueSite::getAmountYear).orElse(BigDecimal.ZERO);
            BigDecimal perCapitaOutput = DemoUtils.divide(cumulativeRevenue, BigDecimal.valueOf(onJobCount));
            NameValueVO nameValueVO = new NameValueVO(baseBusinessUnit.getBuname(), perCapitaOutput);
            perCapitaOutputList.add(nameValueVO);

            // 百元产值人工成本：地盘开工年月截止到所选年月，累计薪酬总额/累计产值*100
            BigDecimal cumulativeSalary = calculateTotalSalary(lstSubjectBalance, Optional.ofNullable(cdmsCodeToProjectInfoMap.get(baseBusinessUnit.getProjectCdmsCode())).map(ProjectInfo::getId).orElse(null));
            BigDecimal laborCostPerHundredOutput = DemoUtils.divide(cumulativeSalary, cumulativeRevenue).multiply(BigDecimal.valueOf(100));
            laborCostPerHundredOutputList.add(new NameValueVO(baseBusinessUnit.getBuname(), laborCostPerHundredOutput));

        }

        perCapitaOutputList.sort(Comparator.comparing(NameValueVO::getValue, Comparator.nullsLast(Comparator.reverseOrder())));
        laborCostPerHundredOutputList.sort(Comparator.comparing(NameValueVO::getValue, Comparator.nullsLast(Comparator.reverseOrder())));

        return createEfficiencyRanking(perCapitaOutputList, laborCostPerHundredOutputList);
    }

    private Set<Integer> getYearSet(LocalDate startDate, LocalDate endDate) {
        Set<Integer> yearSet = new HashSet<>();
        if (startDate != null && endDate != null) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                yearSet.add(date.getYear());
            }
        }
        return yearSet;
    }

    private List<SubjectBalance> filterSubjectBalance(List<SubjectBalance> lstSubjectBalance, LocalDate startDate, LocalDate endDate) {
        return lstSubjectBalance.stream()
                .filter(subjectBalance -> {
                    LocalDate date = LocalDate.of(subjectBalance.getYear(), subjectBalance.getPeriod(), 1);
                    return !date.isBefore(startDate) && !date.isAfter(endDate);
                })
                .toList();
    }

    /* private long getOnJobCount(String orgCode, LocalDate[] dateRange) {
        if (dateRange == null) {
            return 0;
        }
        List<NameCountVO> orgPersonCountVOList = rosterDetailService.selectHKOrgOnJobCount(List.of(orgCode), dateRange[0], dateRange[1]);
        return orgPersonCountVOList.stream().findFirst().map(NameCountVO::getCount).orElse(0L);
    } */

    /* private BigDecimal calculateTotalRevenue(String orgCode, LocalDate statDate, LocalDate[] dateRange) {
        if (dateRange == null) {
            return BigDecimal.ZERO;
        }
        List<RawRevenue> lstRevenue = rawRevenueService.listByCompanyNamesAndYear(List.of(orgCode), statDate.getYear());
        BigDecimal currentTotalRevenue = BigDecimal.ZERO;

        for (LocalDate date = dateRange[0]; date.isBefore(dateRange[1]); date = date.plusMonths(1)) {
            int year = date.getYear();
            int month = date.getMonthValue();
            currentTotalRevenue = currentTotalRevenue.add(lstRevenue.stream()
                    .filter(rawRevenue -> rawRevenue.getYear() == year && rawRevenue.getMonth() == month)
                    .map(RawRevenue::getIncAmount)
                    .reduce(CommonUtils::add)
                    .orElse(BigDecimal.ZERO));
        }
        return currentTotalRevenue;
    } */

    private BigDecimal calculateTotalSalary(List<SubjectBalance> lstSubjectBalance, String projectId) {
        return lstSubjectBalance.stream()
                .filter(subjectBalance -> subjectBalance.getProjectId().equals(projectId))
                .map(SubjectBalance::getAmountEndHkd)
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
    }

    private BigDecimal currentTotalSalary(List<SubjectBalance> lstSubjectBalance, String projectId) {
        return lstSubjectBalance.stream()
                .filter(subjectBalance -> subjectBalance.getProjectId().equals(projectId))
                .map(SubjectBalance::getAmountYearHkd)
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
    }

    private EfficiencyRankingVO createEfficiencyRanking(List<NameValueVO> perCapitaOutputList, List<NameValueVO> laborCostPerHundredOutputList) {
        EfficiencyRankingVO efficiencyRankingVO = new EfficiencyRankingVO();
        efficiencyRankingVO.setPerCapitaOutputList(perCapitaOutputList);
        efficiencyRankingVO.setLaborCostPerHundredOutputList(laborCostPerHundredOutputList);
        return efficiencyRankingVO;
    }

    /**
     * 流失率=：仅计算管理人员。本年度主动离职人数/本年度平均人数
     * 主动离职定义：jobchange里离职原因为主动辞职、员工提出-劳动合同期满不续签两类
     *
     * @param qo
     * @return
     */
    public List<AnnualLossRateVO> getAnnualLossRate(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<Organization> lstOrganization = organizationService.listByCodes(qo.getOrganizationCodeList());
        // name -> siteCode
        Map<String, String> mapNameToSiteCode = lstOrganization.stream().collect(Collectors.toMap(Organization::getName, Organization::getSiteCode, (k1, k2) -> k1));

        Map<String, String> mapNameCode = lstOrganization.stream().collect(Collectors.toMap(Organization::getName, Organization::getCode));

        // 查询年度在职人数
        List<NameCountVO> orgPersonCountVOList = rosterDetailService.selectHKPersonCountByOrgCodeAndDateRange(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        // 年度平均人数：年度在职人数/12
        Map<String, Long> mapAvgOrgPersonCount = orgPersonCountVOList.stream().collect(Collectors.toMap(NameCountVO::getName, nameCountVO -> nameCountVO.getCount() / endDate.getMonthValue()));

        // 查询本年度离职人数
        List<NameCountVO> orgDismissCountVOList = rosterDetailService.selectHKDismissCountByOrgCodeAndDateRange(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        Map<String, Long> mapOrgDismissCount = orgDismissCountVOList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        // 查询本年度核心离职人数
        List<NameCountVO> coreDismissCountVOList = rosterDetailService.selectHKCoreDismissCountByOrgCodeAndDateRange(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        Map<String, Long> mapCoreDismissCount = coreDismissCountVOList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<AnnualLossRateVO> lstResult = new ArrayList<>();
        // List<Organization> lstOrganization = organizationService.listByCodes(qo.getOrganizationCodeList());

        long dismissCountTotal = orgDismissCountVOList.stream().map(NameCountVO::getCount).reduce(Long::sum).orElse(0L);
        long coreDismissCountTotal = coreDismissCountVOList.stream().map(NameCountVO::getCount).reduce(Long::sum).orElse(0L);
        long avgOnJobCountTotal = orgPersonCountVOList.stream().map(NameCountVO::getCount).reduce(Long::sum).map(count -> count / endDate.getMonthValue()).orElse(0L);
        for (String orgName : mapAvgOrgPersonCount.keySet()) {
            AnnualLossRateVO annualLossRateVO = new AnnualLossRateVO();
            annualLossRateVO.setName(formatOrgName(mapNameToSiteCode.get(orgName), orgName));
            annualLossRateVO.setCode(mapNameCode.get(orgName));
            long avgOnJobCount = mapAvgOrgPersonCount.getOrDefault(orgName, 0L);
            long dismissCount = mapOrgDismissCount.getOrDefault(orgName, 0L);
            long coreDismissCount = mapCoreDismissCount.getOrDefault(orgName, 0L);
            annualLossRateVO.setCoreLossRate(DemoUtils.divide(coreDismissCount, avgOnJobCount)); // 核心流失率= 核心离职人数/平均在职人数
            annualLossRateVO.setTotalLossRate(DemoUtils.divide(dismissCount, avgOnJobCount)); // 总体流失率= 本年度离职人数/上年底在职人数
            annualLossRateVO.setCoreLossCount(coreDismissCount); // 增加核心流失人数
            annualLossRateVO.setTotalLossCount(dismissCount);
            lstResult.add(annualLossRateVO);
        }

        // 按总体流失率降序
        lstResult.sort(Comparator.comparing(AnnualLossRateVO::getTotalLossRate, Comparator.nullsLast(Comparator.reverseOrder())));

        AnnualLossRateVO totalVO = new AnnualLossRateVO();
        totalVO.setName("总计");
        totalVO.setTotalLossRate(DemoUtils.divide(dismissCountTotal, avgOnJobCountTotal)); // 总体流失率= 本年度离职人数之和/上年底在职人数
        totalVO.setCoreLossRate(DemoUtils.divide(coreDismissCountTotal, avgOnJobCountTotal)); // 核心流失率= 核心离职人数之和/本年度平均人数
        totalVO.setCoreLossCount(coreDismissCountTotal);
        totalVO.setTotalLossCount(dismissCountTotal);
        lstResult.add(totalVO);

        return lstResult;
    }

    public ResultPage<RosterDetailCategoryVO> getAnnualLossList(HKLossQO qo,Integer pageNum,Integer pageSize){
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails = new ArrayList<>();
        PageHelper.startPage(pageNum,pageSize);
        switch (qo.getLossClass()){
            case "coreLoss":
                // 核心流失人员
                rosterDetails = rosterDetailService.selectHKCoreDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "totalLoss":
                // 总体流失人员
                rosterDetails = rosterDetailService.selectHKDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        }
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportAnnualLossList(HKLossQO qo,HttpServletResponse response) throws IOException {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails = new ArrayList<>();
        switch (qo.getLossClass()){
            case "coreLoss":
                // 核心流失人员
                rosterDetails = rosterDetailService.selectHKCoreDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "totalLoss":
                // 总体流失人员
                rosterDetails = rosterDetailService.selectHKDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        }
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    /**
     * Formats an organization name by prefixing it with the site code if not already present.
     * 
     * @param siteCode The site code to be prepended to the organization name
     * @param orgName The organization name to be formatted
     * @return The formatted organization name. If site code is blank, returns original orgName.
     *         Otherwise returns either the original orgName (if it already starts with siteCode)
     *         or siteCode + "-" + orgName
     */
    public static String formatOrgName(String siteCode, String orgName) {
        if (StringUtils.isBlank(siteCode)) {
            return orgName;
        }
        return orgName.startsWith(siteCode) ? orgName : siteCode + "-" + orgName;
    }

    public List<NameStringVO> getAnnualLossBreakdown(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        // 查询年度在职人数
        List<NameCountVO> orgPersonCountVOList = rosterDetailService.selectHKPersonCountByOrgCodeAndDateRange(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        // 年度平均人数：年度在职人数/12
        Map<String, Long> mapAvgOrgPersonCount = orgPersonCountVOList.stream().collect(Collectors.toMap(NameCountVO::getName, nameCountVO -> nameCountVO.getCount() / endDate.getMonthValue()));

        // 查询本年度离职人数
        List<NameCountVO> orgDismissCountVOList = rosterDetailService.selectHKDismissCountByOrgCodeAndDateRange(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        Map<String, Long> mapOrgDismissCount = orgDismissCountVOList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<AnnualLossRateVO> lstLossRate = new ArrayList<>();

        long dismissCountTotal = orgDismissCountVOList.stream().map(NameCountVO::getCount).reduce(Long::sum).orElse(0L);
        long avgOnJobCountTotal = orgPersonCountVOList.stream().map(NameCountVO::getCount).reduce(Long::sum).map(count -> count / endDate.getMonthValue()).orElse(0L);
        for (String orgName : mapAvgOrgPersonCount.keySet()) {
            AnnualLossRateVO annualLossRateVO = new AnnualLossRateVO();
            annualLossRateVO.setName(orgName);
            long avgOnJobCount = mapAvgOrgPersonCount.getOrDefault(orgName, 0L);
            long dismissCount = mapOrgDismissCount.getOrDefault(orgName, 0L);
            annualLossRateVO.setTotalLossRate(DemoUtils.divide(dismissCount, avgOnJobCount)); // 总体流失率= 本年度离职人数/上年底在职人数
            lstLossRate.add(annualLossRateVO);
        }

        List<NameStringVO> lstResult = new ArrayList<>();
        //處理不同維度的流失人數
        lstResult.addAll(rosterDetailService.selectHKDismissBreakdown(qo.getOrganizationCodeList(), startDate, endDate, "管理人员"));
        for(NameStringVO nameStringVO : lstResult) {
            nameStringVO.setValue(nameStringVO.getValue() + "人");
        }
        //處理總體流失率
        lstResult.add(new NameStringVO("总体流失率",DemoUtils.divide(dismissCountTotal, avgOnJobCountTotal).multiply(BigDecimal.valueOf(100)).toString() + "%",dismissCountTotal));

        return lstResult;
    }

    public ResultPage<RosterDetailCategoryVO> getAnnualLossBreakdownList(HKLossQO qo,Integer pageNum,Integer pageSize){
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails= new ArrayList<>();
        PageHelper.startPage(pageNum,pageSize);
        switch (qo.getLossClass()){
            case "totalLoss":
                // 总体流失人员
                rosterDetails = rosterDetailService.selectHKDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "coreLoss":
                // 核心流失人员
                rosterDetails = rosterDetailService.selectHKDismissBreakdownCoreList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "youthLoss":
                rosterDetails = rosterDetailService.selectHKDismissBreakdownYouthList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        }
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);
        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());
        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportAnnualLossBreakdownList(HKLossQO qo,HttpServletResponse response) throws IOException {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails= new ArrayList<>();
        switch (qo.getLossClass()){
            case "totalLoss":
                // 总体流失人员
                rosterDetails = rosterDetailService.selectHKDismissCountByOrgCodeAndDateList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "coreLoss":
                // 核心流失人员
                rosterDetails = rosterDetailService.selectHKDismissBreakdownCoreList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
                break;
            case "youthLoss":
                rosterDetails = rosterDetailService.selectHKDismissBreakdownYouthList(qo.getOrganizationCodeList(), startDate, endDate, "管理人员");
        }
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }

    }

    /**
     * Retrieves a list of organization codes for a specific sub-company department.
     *
     * @param departmentName The name of the sub-company department to search for 
     * @return List of organization codes associated with the specified department name
     */

    public List<String> getSubCompanyDeptsOrgCodeList(String departmentName) {
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Organization::getSubCompanyDept, departmentName)
                .eq(Organization::getIsDeleted, false);
        return organizationService.list(queryWrapper).stream().map(Organization::getCode).collect(Collectors.toList());
    }

    public List<YearMonthStaffCountInfoVO> listLast6MonthStaffCount(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        // 前六个月
        LocalDate startDate = statDate.minusMonths(5).withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        List<CodeNameCountVO> siteEmpTypeList = rosterDetailService.selectSiteEmpType(qo.getOrganizationCodeList(), startDate, endDate);

        List<YearMonthStaffCountInfoVO> lstResult = new ArrayList<>();
        for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
            // 组成 yyyy-MM的格式
            String yearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(date);

            YearMonthStaffCountInfoVO yearMonthStaffCountInfoVO = new YearMonthStaffCountInfoVO();
            yearMonthStaffCountInfoVO.setYearMonth(yearMonth);
            yearMonthStaffCountInfoVO.setValue0(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "内地".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue1(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "自有日薪".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue2(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "内派".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue3(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "地盘基层".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue4(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "核心港聘".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue5(StaffCountFullHelper.calcStaffCountByYearMonth(staffCountFullList, date));
            lstResult.add(yearMonthStaffCountInfoVO);
        }
        return lstResult;
    }

    public List<YearMonthStaffCountInfoVO> getSiteEmployeeDistributionFull(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);  // 当前日期
        String currentYearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(LocalDate.now());

        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        LocalDate[] dateRange = CollectionUtils.isEmpty(staffCountFullList) ? null : StaffCountHelper.getStartDateAndEndDateForList(staffCountFullList);
        LocalDate startStaffCountDate = dateRange != null ? dateRange[0] : null;
        LocalDate endStaffCountDate = dateRange != null ? dateRange[1] : null;

        List<CodeNameCountVO> siteEmpTypeList = rosterDetailService.selectSiteEmpType(qo.getOrganizationCodeList(), null, null);
        LocalDate startSiteEmpTypeDate = siteEmpTypeList.stream()
                .map(codeNameCountVO -> LocalDate.parse(codeNameCountVO.getCode() + "-01"))
                .min(LocalDate::compareTo)
                .orElse(null);

        /* LocalDate endSiteEmpTypeDate = siteEmpTypeList.stream()
                .map(codeNameCountVO -> LocalDate.parse(codeNameCountVO.getCode() + "-01"))
                .max(LocalDate::compareTo)
                .orElse(null); */

        // 确定 startDate
        LocalDate startDate = Stream.of(startStaffCountDate, startSiteEmpTypeDate)
                .filter(Objects::nonNull)
                .min(LocalDate::compareTo)
                .orElse(null);

        // 确定 endDate
        LocalDate endDate = Stream.of(statDate, endStaffCountDate)
                .filter(Objects::nonNull)
                .max(LocalDate::compareTo)
                .map(date -> date.plusYears(2)) // 对找到的最大日期加两年
                .orElse(null); // 如果找不到最大日期，返回 null


        List<YearMonthStaffCountInfoVO> lstResult = new ArrayList<>();
        for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
            // 获取当前月份的花名册headId
            String headId = rosterHeadService.getLatestRosterHead(date).getId();

            // 计算业务外判人数
            long hkOutsourceCount = rosterDetailService.selectHKOutsourceCount(qo.getOrganizationCodeList(), headId);
            long businessOutsourceCount = hkOutsourceCount;

            // 组成 yyyy-MM的格式
            String yearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(date);

            // 判断 yearMonth 是否大于当前年月，并决定使用当前年月的值
            String effectiveYearMonth = yearMonth.compareTo(currentYearMonth) > 0 ? currentYearMonth : yearMonth;

            YearMonthStaffCountInfoVO yearMonthStaffCountInfoVO = new YearMonthStaffCountInfoVO();
            yearMonthStaffCountInfoVO.setYearMonth(yearMonth);
            yearMonthStaffCountInfoVO.setValue0(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "内地".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            Long selfEmployedDaily = siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "自有日薪".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            yearMonthStaffCountInfoVO.setValue1(businessOutsourceCount + selfEmployedDaily); // 外判人数 + 自有日薪人数
            yearMonthStaffCountInfoVO.setValue2(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "内派".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue3(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "地盘基层".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue4(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "核心港聘".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue5(StaffCountFullHelper.calcStaffCountByYearMonth(staffCountFullList, date));
            yearMonthStaffCountInfoVO.setValue0StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.04"));
            long selfEmployedStaffCount = StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.05"); // 自有日薪
            long businessOutsourceStaffCount = StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.06"); // 业务外判
            yearMonthStaffCountInfoVO.setValue1StaffCount(selfEmployedStaffCount + businessOutsourceStaffCount); // 自有日薪 + 业务外判
            yearMonthStaffCountInfoVO.setValue2StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.01"));
            yearMonthStaffCountInfoVO.setValue3StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.03"));
            yearMonthStaffCountInfoVO.setValue4StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.02"));
            // 合计
            yearMonthStaffCountInfoVO.setValue6(yearMonthStaffCountInfoVO.getValue0() + yearMonthStaffCountInfoVO.getValue1() +
                    yearMonthStaffCountInfoVO.getValue2() + yearMonthStaffCountInfoVO.getValue3() + yearMonthStaffCountInfoVO.getValue4());
            yearMonthStaffCountInfoVO.setValue6StaffCount(yearMonthStaffCountInfoVO.getValue0StaffCount() + yearMonthStaffCountInfoVO.getValue1StaffCount() +
                    yearMonthStaffCountInfoVO.getValue2StaffCount() + yearMonthStaffCountInfoVO.getValue3StaffCount() + yearMonthStaffCountInfoVO.getValue4StaffCount());
            lstResult.add(yearMonthStaffCountInfoVO);
        }
        return lstResult;
    }



    public List<AppraisalResultVO> selectSiteAppraisalResult(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<AppraisalResultVO> lstResult = rosterDetailService.selectSiteAppraisalResult(qo.getOrganizationCodeList(), startDate, endDate);

        return lstResult;
    }

    public static long calcCurrentMonth(Organization organization, LocalDate statDate) {
        if (organization.getStartDate() != null && organization.getEndDate() != null) {
            if (statDate.isBefore(organization.getEndDate())) {
                return ChronoUnit.MONTHS.between(organization.getStartDate(), statDate.plusMonths(1));
            } else {
                return ChronoUnit.MONTHS.between(organization.getStartDate(), organization.getEndDate().plusMonths(1));
            }
        }
        return 0;
    }

    public static long calcTotalMonth(Organization organization) {
        if (organization.getStartDate() != null && organization.getEndDate() != null) {
            return ChronoUnit.MONTHS.between(organization.getStartDate(), organization.getEndDate().plusMonths(1));
        }
        return 0;
    }

    public static long calcCurrentContractMonth(Organization organization, LocalDate statDate) {
        if (organization.getContractStartDate() != null && organization.getContractEndDate() != null) {
            if (statDate.isBefore(organization.getContractEndDate())) {
                return ChronoUnit.MONTHS.between(organization.getContractStartDate(), statDate.plusMonths(1));
            } else {
                return ChronoUnit.MONTHS.between(organization.getContractStartDate(), organization.getContractEndDate().plusMonths(1));
            }
        }
        return 0;
    }

    public static long calcTotalContractMonth(Organization organization) {
        if (organization.getContractStartDate() != null && organization.getContractEndDate() != null) {
            return ChronoUnit.MONTHS.between(organization.getContractStartDate(), organization.getContractEndDate().plusMonths(1));
        }
        return 0;
    }

    /**
     * 全周期人均效能概览
     *
     * @param qo 组织代码列表
     * @return
     */
    public SiteOverviewVO getFullCycleSiteStaffOverview(HKDashQO qo) {
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");
        ServiceHelper.checkExist(qo.getStatDate(), "统计日期不能为空");

        Organization organization = organizationService.getByCode(qo.getOrganizationCodeList().get(0));
        Objects.requireNonNull(organization, "组织不存在");

        SiteOverviewVO siteOverviewVO = new SiteOverviewVO();
        siteOverviewVO.setEndDate(StaffCountFullHelper.determineEndDate(organization, qo.getStatDate()));
        siteOverviewVO.setCurrentMonth(calcCurrentMonth(organization, qo.getStatDate()));
        siteOverviewVO.setTotalMonth(calcTotalMonth(organization));
        siteOverviewVO.setCurrentContractMonth(calcCurrentContractMonth(organization, qo.getStatDate()));
        siteOverviewVO.setTotalContractMonth(calcTotalContractMonth(organization));

        siteOverviewVO.setCumulativeTimeRatio(calcCumulativeTimeRatio(organization, qo.getStatDate()));
        rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), organization.getStartDate(), qo.getStatDate()).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum)
                .ifPresent(siteOverviewVO::setCumulativeStaffCount);
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        long totalStaffCountPlan = StaffCountFullHelper.calcStaffCountPlan(staffCountFullList, organization.getStartDate(), qo.getStatDate());
        siteOverviewVO.setCumulativeStaffCountPlan(totalStaffCountPlan);
        siteOverviewVO.setCurrentStaffCountRatio(DemoUtils.divide(siteOverviewVO.getCumulativeStaffCount(), totalStaffCountPlan));
//        subjectBalanceService.listSalaryByProjectIdAndYearMonth(orgCodeToProjectIdList(qo.getOrganizationCodeList(), statDate), statDate.getYear(), statDate.getMonthValue())
//                .stream()
//                .map(SubjectBalance::getAmountEndHkd)
//                .filter(Objects::nonNull)
//                .reduce(CommonUtils::add)
//                .ifPresent(siteOverviewVO::setCumulativeSalaryAmount);



        long hKOrgOnJobCountForOutputValue = rosterDetailService.selectHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(), organization.getStartDate(), qo.getStatDate()).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum).orElse(0L);
        LocalDate orgStartDate = organization.getStartDate() == null ? qo.getStatDate().withDayOfYear(1) : organization.getStartDate();
        /*
        BigDecimal avgHKOrgOnJobCountForOutputValue = DemoUtils.divide(BigDecimal.valueOf(hKOrgOnJobCountForOutputValue),
                BigDecimal.valueOf((qo.getStatDate().getYear()*12 + qo.getStatDate().getMonthValue())
                                - (orgStartDate.getYear()*12 + orgStartDate.getMonthValue())), 4);
         */

        List<SalaryRecordVO> salaryRecordVOS = salaryRecordService.listFullYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), qo.getStatDate());
        BigDecimal cumulativeTotalSalary = SalaryRecordFacade.calculateSalary(salaryRecordVOS);
        siteOverviewVO.setCumulativeSalaryAmount(cumulativeTotalSalary);

        // 全周期薪酬预算
        BigDecimal salaryBudget = StaffCountFullHelper.getAllStaffASalaryCount(staffCountFullList, qo.getStatDate());

              /*  BigDecimal salaryBudget =  staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList()).stream()
                .filter(staffCountFull -> "薪酬预算".equals(staffCountFull.getSubjectType()))
                .map(StaffCountFullHelper::calcStaffCountFullValue)
                .reduce(CommonUtils::add).orElse(BigDecimal.ZERO);*/

        siteOverviewVO.setCumulativeSalaryBudget(salaryBudget);
        siteOverviewVO.setCurrentSalaryAmountRatio(DemoUtils.divide(siteOverviewVO.getCumulativeSalaryAmount(), salaryBudget));

        BigDecimal totalRevenue = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(organizationService.listSiteCodes(qo.getOrganizationCodeList()), qo.getStatDate())
                .stream()
                .map(RawRevenueSite::getAmountTotal) // 改为全周期产值了
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
        siteOverviewVO.setPerCapitaOutputValue(DemoUtils.divide(totalRevenue, hKOrgOnJobCountForOutputValue).multiply(BigDecimal.valueOf(12)).setScale(1, RoundingMode.HALF_UP));
        siteOverviewVO.setLaborCostPerHundredOutputValue(DemoUtils.divide(siteOverviewVO.getCumulativeSalaryAmount(), totalRevenue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        // 全周期编制人数
        BigDecimal allStaffCountPlan = StaffCountFullHelper.getAllStaffCountPlan(staffCountFullList);
        // 全周期目标产值
        BigDecimal fullTargetOutputValue = StaffCountFullHelper.getAllTargetOutputValue(staffCountFullList);
        //  地盘全周期目标产值汇总/全周期编制数数汇总全周期月份数
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        Integer monthCount = StaffCountFullHelper.getAllMonthCounts();
        if (allStaffCountPlan.compareTo(BigDecimal.ZERO) != 0){
            targetOutputValue = DemoUtils.divide(fullTargetOutputValue,allStaffCountPlan,4).multiply(BigDecimal.valueOf(monthCount));
        }
        siteOverviewVO.setTargetOutputValue(targetOutputValue);
        // 全周期薪酬预算（不判断时间)
        BigDecimal allSalaryBudget = StaffCountFullHelper.getAllStaffASalaryCount(staffCountFullList, HrrsConsts.STAFF_END_DATE);
        BigDecimal costTargetValue = BigDecimal.ZERO;
        if (fullTargetOutputValue.compareTo(BigDecimal.ZERO) != 0){
            costTargetValue = DemoUtils.divide(allSalaryBudget,fullTargetOutputValue,4).multiply(BigDecimal.valueOf(100));
        }
        siteOverviewVO.setCostTargetValue(costTargetValue);
        return siteOverviewVO;
    }

    /**
     * 获取年度人均效能概览
     *
     * @param qo 组织代码列表
     * @return
     */
    public SiteOverviewVO getYearlySiteStaffOverview(HKDashQO qo) {
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");
        ServiceHelper.checkExist(qo.getStatDate(), "统计日期不能为空");
        LocalDate statDate = getStatDate(qo);

        Organization organization = organizationService.getByCode(qo.getOrganizationCodeList().get(0));
        Objects.requireNonNull(organization, "组织不存在");

        SiteOverviewVO siteOverviewVO = new SiteOverviewVO();
        siteOverviewVO.setEndDate(organization.getEndDate());
        siteOverviewVO.setCurrentMonth(calcCurrentMonth(organization, qo.getStatDate()));
        siteOverviewVO.setTotalMonth(calcTotalMonth(organization));
        siteOverviewVO.setCurrentContractMonth(calcCurrentContractMonth(organization, qo.getStatDate()));
        siteOverviewVO.setTotalContractMonth(calcTotalContractMonth(organization));
        siteOverviewVO.setCumulativeTimeRatio(calcCumulativeTimeRatio(organization, statDate));
        if(siteOverviewVO.getCurrentMonth() > 1000) {
            siteOverviewVO.setCurrentMonth(0);
        }
        if(siteOverviewVO.getTotalMonth() > 1000) {
            siteOverviewVO.setTotalMonth(0);
            siteOverviewVO.setCumulativeTimeRatio(BigDecimal.ZERO);
        }
        if(siteOverviewVO.getCurrentContractMonth() > 1000) {
            siteOverviewVO.setCurrentContractMonth(0);
        }
        if(siteOverviewVO.getTotalContractMonth() > 1000) {
            siteOverviewVO.setTotalContractMonth(0);
        }
        rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), statDate.withDayOfYear(1), statDate).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum)
                .ifPresent(siteOverviewVO::setCumulativeStaffCount);
        // 查询出编制记录列表
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        long totalStaffCountPlan = StaffCountFullHelper.calcStaffCountPlan(staffCountFullList, statDate.withDayOfYear(1), statDate.withDayOfMonth(statDate.lengthOfMonth()));
        siteOverviewVO.setCumulativeStaffCountPlan(totalStaffCountPlan);
        siteOverviewVO.setCurrentStaffCountRatio(DemoUtils.divide(siteOverviewVO.getCumulativeStaffCount(), totalStaffCountPlan));

//        subjectBalanceService.listSalaryByProjectIdAndYearMonth(orgCodeToProjectIdList(qo.getOrganizationCodeList(), statDate), statDate.getYear(), statDate.getMonthValue())
//                .stream()
//                .map(SubjectBalance::getAmountYearHkd)
//                .filter(Objects::nonNull)
//                .reduce(CommonUtils::add)
//                .ifPresent(siteOverviewVO::setCumulativeSalaryAmount);

        List<SalaryRecordVO> salaryRecordVOS = salaryRecordService.listYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        BigDecimal cumulativeTotalSalary = SalaryRecordFacade.calculateSalary(salaryRecordVOS);
        siteOverviewVO.setCumulativeSalaryAmount(cumulativeTotalSalary);

        BigDecimal salaryBudget = StaffCountFullHelper.calcSalaryBudget(staffCountFullList, statDate.withDayOfYear(1), statDate.withDayOfMonth(statDate.lengthOfMonth()));
        siteOverviewVO.setCumulativeSalaryBudget(salaryBudget);
        siteOverviewVO.setCurrentSalaryAmountRatio(DemoUtils.divide(siteOverviewVO.getCumulativeSalaryAmount(), salaryBudget));

        long hKOrgOnJobCountForOutputValue = rosterDetailService.selectHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(), statDate.withDayOfYear(1), statDate).stream()
                .map(NameCountVO::getCount)
                .reduce(Long::sum).orElse(0L);
        BigDecimal avgHKOrgOnJobCountForOutputValue = DemoUtils.divide(BigDecimal.valueOf(hKOrgOnJobCountForOutputValue),
                BigDecimal.valueOf(statDate.getMonthValue()), 4);

        BigDecimal totalRevenue = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(organizationService.listSiteCodes(qo.getOrganizationCodeList()), statDate)
                .stream()
                .map(RawRevenueSite::getAmountYear) // 年度产值
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);

        siteOverviewVO.setPerCapitaOutputValue(DemoUtils.divide(totalRevenue, avgHKOrgOnJobCountForOutputValue).setScale(1, RoundingMode.HALF_UP));
        siteOverviewVO.setLaborCostPerHundredOutputValue(DemoUtils.divide(siteOverviewVO.getCumulativeSalaryAmount(), totalRevenue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));

        // 今年编制汇总
        BigDecimal totalYearStaffCount = StaffCountFullHelper.calcStaffCountYearByDate(staffCountFullList, statDate);
        // 今年目标产值汇总
        BigDecimal cumulativeValueYear = StaffCountFullHelper.calcTargetOutputValueYearByDate(staffCountFullList,statDate).multiply(BigDecimal.valueOf(10000));
        // 目标值 = 所选年月全年的目标产值汇总/全年（BZ.01~04）编制数汇总 * 12个月
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        if (totalYearStaffCount.compareTo(BigDecimal.ZERO) != 0){
            targetOutputValue = DemoUtils.divide(cumulativeValueYear,totalStaffCountPlan).multiply(BigDecimal.valueOf(12));
        }
        siteOverviewVO.setTargetOutputValue(targetOutputValue);
        // 今年薪酬预算汇总
        LocalDate startYear = statDate.withDayOfYear(1);
        LocalDate endYear = statDate.with(TemporalAdjusters.lastDayOfYear());
        BigDecimal totalYearBudget = StaffCountFullHelper.calcSalaryBudget(staffCountFullList, startYear, endYear);
        // 所选年月全年（XZYS.01~04）的薪酬预算数汇总/目标产值汇总*100
        BigDecimal costTargetValue = BigDecimal.ZERO;
        if (cumulativeValueYear.compareTo(BigDecimal.ZERO) != 0){
            costTargetValue = DemoUtils.divide(totalYearBudget,cumulativeValueYear).multiply(BigDecimal.valueOf(100));
        }
        siteOverviewVO.setCostTargetValue(costTargetValue);
        return siteOverviewVO;
    }

    public LocalDate getEndDate(LocalDate statDate) {
        /*if (isCurrentYearMonth(statDate)) {
            statDate = LocalDate.now();
        }*/
        return statDate.withDayOfMonth(statDate.lengthOfMonth());
    }

    /**
     * 计算累计时间比例：当前工期月/全周期月份数
     *
     * @return
     */
    private BigDecimal calcCumulativeTimeRatio(Organization organization, LocalDate date) {
        long totalMonths = calcTotalMonth(organization);
        long currentMonths = calcCurrentMonth(organization, date);
        if (currentMonths == -1) {
            return BigDecimal.ONE;
        }
        return DemoUtils.divide(currentMonths, totalMonths);
    }

    private long getAllStaffCount(List<String> orgCodeList, LocalDate statDate) {
        // 获取所有 StaffCountFull 对象
        List<StaffCountFull> lstStaffCountFull = staffCountFullService.listByOrgCodes(orgCodeList);

        // 获取字段名集合，仅包含以 "d" 开头的字段
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d") && isFieldBeforeStatDate(fieldName, statDate)) // 添加判断字段是否在 statDate 之前
                .collect(Collectors.toSet());

        // 过滤并计算总数
        BigDecimal totalCount = lstStaffCountFull.stream()
                // 筛选仅保留 subjectType 为 "编制人数" 的记录
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                // 遍历字段并计算总和
                .flatMap(staffCountFull -> fieldNames.stream()
                        .map(fieldName -> {
                            try {
                                return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException("Error reading field: " + fieldName, e);
                            }
                        }))
                // 累加 BigDecimal 值
                .reduce(BigDecimal.ZERO, CommonUtils::add);

        // 返回结果的 long 值
        return totalCount.longValue();
    }


    // 实现逻辑来判断 fieldName 是否在 statDate 之前
    private boolean isFieldBeforeStatDate(String fieldName, LocalDate statDate) {
        String pitchDate = DateTimeFormatter.ofPattern("yyyyMMdd").format(statDate);
        LocalDate endDate = LocalDate.parse(pitchDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 例如，假设字段名包含日期的部分，那么你可以解析并比较
        String dateString = fieldName.substring(1) + "01";
        LocalDate fieldDate = LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 比较日期: fieldName 日期是否在 statDate 之前或等于
        return !fieldDate.isAfter(endDate);
    }

    /**
     * 获取人力整体面板人数情况
     *
     * @param hkDashQO 查询参数
     * @return
     */
    public HROverallStatVO getHROverallStat(HKDashQO hkDashQO) {
        // 使用局部变量提高可读性和性能
        final LocalDate statDate = getStatDate(hkDashQO);
        final LocalDate startDate = statDate.withDayOfMonth(1);
        final LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        // 使用不可变的类别映射，减少重复计算
        final Map<String, Long> categoryCountMap = rosterDetailService
                .selectHKOverallCountByCategory(hkDashQO.getOrganizationCodeList(), startDate, endDate)
                .stream()
                .collect(Collectors.toMap(
                        NameCountVO::getName,
                        NameCountVO::getCount,
                        Long::sum  // 添加合并策略，处理可能的重复键
                ));

        // 使用方法提取，简化代码逻辑
        HROverallStatVO hrOverallStatVO = buildHROverallStatVO(categoryCountMap);

        long specialistCount = rosterDetailService.selectSpecialistCount(hkDashQO.getOrganizationCodeList(), startDate, endDate);
        hrOverallStatVO.setHksubSpecialistCount(specialistCount);
        hrOverallStatVO.setManagerCount(rosterDetailService.selectHKOverallManagerCount(hkDashQO.getOrganizationCodeList(), startDate, endDate));
        hrOverallStatVO.setTotalCount(rosterDetailService.selectHKOverallTotalCount(hkDashQO.getOrganizationCodeList(), startDate, endDate));
        return hrOverallStatVO;
    }

    public ResultPage<RosterDetailCategoryVO> getOverallDetail(HKDashQO qo, String personType, int pageNum, int pageSize){
        final LocalDate statDate = getStatDate(qo);
        final LocalDate startDate = statDate.withDayOfMonth(1);
        final LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        PageHelper.startPage(pageNum, pageSize);
        List<RosterDetail> rosterDetails = new ArrayList<>();
        switch (personType){
            case "total":
                // 总数
                rosterDetails = rosterDetailService.selectHKOverallTotalList(qo.getOrganizationCodeList(),startDate,endDate);
                break;
            case "manager":
                // 管理人员
                rosterDetails = rosterDetailService.selectHKOverallManagerList(qo.getOrganizationCodeList(),startDate,endDate);
                break;
            case "hkRecruit":
                // 港聘
                List<String> hkRecruitList = List.of("管理人员", "自有工人", "其他港聘");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(),startDate,endDate,hkRecruitList);
                break;
            case "intAssign":
                // 内派
                List<String>  intAssignList = List.of("内派");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(),startDate,endDate,intAssignList);
                break;
            case "mainland":
                // 内地
                List<String> mainlandList = List.of("内地");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(),startDate,endDate,mainlandList);
                break;
            case "overseas":
                // 海外
                List<String> overseasList = List.of("海外");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(),startDate,endDate,overseasList);
                break;
            case "selfWorker":
                // 自有工人
                rosterDetails = rosterDetailService.selectSelfWorkerList(qo.getOrganizationCodeList(),startDate,endDate);
                break;
            default:
                throw new IllegalArgumentException("Unsupported type: " + personType);

        }
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportOverallDetail(HKDashQO qo, String personType,HttpServletResponse response) throws IOException {
        final LocalDate statDate = getStatDate(qo);
        final LocalDate startDate = statDate.withDayOfMonth(1);
        final LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails = new ArrayList<>();
        switch (personType) {
            case "total":
                // 总数
                rosterDetails = rosterDetailService.selectHKOverallTotalList(qo.getOrganizationCodeList(), startDate, endDate);
                break;
            case "manager":
                // 管理人员
                rosterDetails = rosterDetailService.selectHKOverallManagerList(qo.getOrganizationCodeList(), startDate, endDate);
                break;
            case "hkRecruit":
                // 港聘
                List<String> hkRecruitList = List.of("管理人员", "自有工人", "其他港聘");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(), startDate, endDate, hkRecruitList);
                break;
            case "intAssign":
                // 内派
                List<String> intAssignList = List.of("内派");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(), startDate, endDate, intAssignList);
                break;
            case "mainland":
                // 内地
                List<String> mainlandList = List.of("内地");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(), startDate, endDate, mainlandList);
                break;
            case "overseas":
                // 海外
                List<String> overseasList = List.of("海外");
                rosterDetails = rosterDetailService.selectHKOverallListByCategory(qo.getOrganizationCodeList(), startDate, endDate, overseasList);
                break;
            case "selfWorker":
                // 自有工人
                rosterDetails = rosterDetailService.selectSelfWorkerList(qo.getOrganizationCodeList(), startDate, endDate);
                break;
            default:
                throw new IllegalArgumentException("Unsupported type: " + personType);
        }

                List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
                String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
                fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName);
                String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";
                try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
                     ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
                    FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    excelWriter.fill(categoryVOList, fillConfig, writeSheet);
                }
    }

    private HROverallStatVO buildHROverallStatVO(Map<String, Long> categoryCountMap) {
        HROverallStatVO vo = new HROverallStatVO();

        // 使用数组和循环替代重复代码，提高可维护性
        String[] hkCategories = {"管理人员", "自有工人", /*"含专才", */"其他港聘"};
        long[] hkCounts = new long[hkCategories.length];

        for (int i = 0; i < hkCategories.length; i++) {
            hkCounts[i] = categoryCountMap.getOrDefault(hkCategories[i], 0L);
        }

        // 设置港聘相关统计
        vo.setHksubManagerCount(hkCounts[0]);
        vo.setHksubInternalCount(hkCounts[1]);
        //vo.setHksubSpecialistCount(hkCounts[2]);
        vo.setHksubOtherCount(hkCounts[2]);

        // 计算港聘总数
        long hkTotal = Arrays.stream(hkCounts).sum();
        vo.setHkRecruitCount(hkTotal);

        // 设置其他类别统计
        String[] otherCategories = {"内派", "内地", "海外"};
        long[] otherCounts = new long[otherCategories.length];

        for (int i = 0; i < otherCategories.length; i++) {
            otherCounts[i] = categoryCountMap.getOrDefault(otherCategories[i], 0L);
        }

        vo.setIntAssignCount(otherCounts[0]);
        vo.setMainlandCount(otherCounts[1]);
        vo.setOverseasCount(otherCounts[2]);

        // 计算总数
        vo.setTotalCount(hkTotal + Arrays.stream(otherCounts).sum());

        return vo;
    }

    public List<YearCountVO> getHKYearStaffCountList(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        int endYear = statDate.getYear();
        int startYear = endYear - 9; // 往前推10年的起始年份

        List<YearCountVO> lstResult = new ArrayList<>();

        // 查询数据库中的实际数据
        List<Integer> yearList = new ArrayList<>();
        for (int year = startYear; year <= endYear; year++) {
            if (year > 2020) { // 2020年之后的数据从数据库查询
                yearList.add(year);
            }
        }

        Map<Integer, Long> yearCountMap = new HashMap<>();
        if (!yearList.isEmpty()) {
            List<YearCountVO> yearCountList = rosterDetailService.selectHKStaffCountByYear(qo.getOrganizationCodeList(),
                    yearList);
            yearCountMap = yearCountList.stream()
                    .collect(Collectors.toMap(YearCountVO::getYear, YearCountVO::getCount));
        }

        // 按年份顺序添加数据
        for (int year = startYear; year <= endYear; year++) {
            if (year <= 2020) {
                // 2015-2020年使用固定数据
                switch (year) {
                    case 2015 -> lstResult.add(YearCountVO.builder().build().setYear(2015).setCount(4300L));
                    case 2016 -> lstResult.add(YearCountVO.builder().build().setYear(2016).setCount(4400L));
                    case 2017 -> lstResult.add(YearCountVO.builder().build().setYear(2017).setCount(4000L));
                    case 2018 -> lstResult.add(YearCountVO.builder().build().setYear(2018).setCount(4600L));
                    case 2019 -> lstResult.add(YearCountVO.builder().build().setYear(2019).setCount(5300L));
                    case 2020 -> lstResult.add(YearCountVO.builder().build().setYear(2020).setCount(4800L));
                }
            } else {
                // 2021年及以后的数据从数据库获取
                lstResult
                        .add(YearCountVO.builder().build().setYear(year).setCount(yearCountMap.getOrDefault(year, 0L)));
            }
        }

        return lstResult;
    }

    public ResultPage<RosterDetailCategoryVO> getYearDetailList(HKYearQO qo, int pageNum, int pageSize){
        if (qo.getYear() < 2021){
            throw new ServiceException("2020年前无明细数据");

        }
        PageHelper.startPage(pageNum,pageSize);
        LocalDate today = LocalDate.now();
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKStaffListByYear(qo.getOrganizationCodeList(), qo.getYear(),qo.getYear().equals(today.getYear()));
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportYearDetail(HKYearQO qo,HttpServletResponse response) throws IOException {
        if (qo.getYear() < 2021){
            throw new ServiceException("2020年前无明细数据");

        }
        LocalDate today = LocalDate.now();
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKStaffListByYear(qo.getOrganizationCodeList(), qo.getYear(),qo.getYear().equals(today.getYear()));
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    /**
     * 查询人力整体看板-部门子公司人数统计情况
     *
     * @param qo
     * @return
     */
    public DeptStaffStatVO getDeptStaffStat(HKDashQO qo) {
        // 1. 使用局部常量优化日期计算
        final LocalDate statDate = getStatDate(qo);
        final LocalDate startDate = statDate.withDayOfMonth(1);
        final LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<NameCountVO> deptStaffCountList = rosterDetailService.selectHKDeptStaffCount(qo.getOrganizationCodeList(), startDate, endDate);

        // 3. 使用更高效的总数计算方法
        final long totalCount = calculateTotalCount(deptStaffCountList);

        // 4. 使用方法引用和函数式编程简化转换逻辑
        List<NameCountRateVO> lstNameCountRate = calculateNameCountRates(deptStaffCountList, totalCount);

        // 5. 使用构建器模式创建返回对象
        return DeptStaffStatVO.builder()
                .deptStaffList(lstNameCountRate)
                .officeStaffCount(rosterDetailService.selectHKOfficeStaffCount(qo.getOrganizationCodeList(), startDate, endDate))
                .build();
    }

    // 提取总数计算方法，提高代码可读性和可重用性
    private long calculateTotalCount(List<NameCountVO> countList) {
        return countList.stream()
                .mapToLong(NameCountVO::getCount)
                .sum();
    }

    // 提取比率计算方法，解耦业务逻辑
    private List<NameCountRateVO> calculateNameCountRates(List<NameCountVO> countList, long totalCount) {
        return countList.stream()
                .map(nameCountVO -> NameCountRateVO.builder()
                        .name(nameCountVO.getName())
                        .count(nameCountVO.getCount())
                        .rate(DemoUtils.divide(nameCountVO.getCount(), totalCount))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 查询人力整体看板-性别分布情况
     *
     * @param qo 查询参数
     * @return
     */
    public List<NameCountRateVO> distributeByGender(HKDashQO qo) {
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);
        return calculateNameCountRates(
                rosterDetailService.selectHKGenderCount(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1)
        );
    }

    /**
     * 查询人力整体看板-学历分布情况
     *
     * @param qo 查询参数
     * @return
     */
    public HREducationStatVO distributeByEducation(HKDashQO qo) {
        // 1. 抽取公共日期计算逻辑
        final List<LocalDate> dateRange = getStatDateRangeInMonth(qo);

        // 2. 获取教育统计数据
        List<NameCountVO> educationCountList = rosterDetailService
                .selectHKEducationCount(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1);

        // 3. 计算总人数
        final long totalCount = calculateTotalCount(educationCountList);

        // 4. 构建教育统计对象
        return buildHREducationStatVO(educationCountList, totalCount);
    }

    // 公共方法：计算统计日期范围
    private List<LocalDate> getStatDateRangeInMonth(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        return Arrays.asList(startDate, endDate);
    }

    // 公共方法：转换为带比率的统计列表
    private List<NameCountRateVO> calculateNameCountRates(List<NameCountVO> countList) {
        long totalCount = calculateTotalCount(countList);
        return countList.stream()
                .map(nameCountVO -> NameCountRateVO.builder()
                        .name(nameCountVO.getName())
                        .count(nameCountVO.getCount())
                        .rate(DemoUtils.divide(nameCountVO.getCount(), totalCount))
                        .build())
                .collect(Collectors.toList());
    }

    // 构建教育统计对象的专用方法
    private HREducationStatVO buildHREducationStatVO(List<NameCountVO> educationCountList, long totalCount) {
        // 使用函数式编程计算本科及以上人数
        long bachelorUpCount = educationCountList.stream()
                .filter(nameCountVO ->
                        "本科".equals(nameCountVO.getName()) ||
                                "硕士及以上".equals(nameCountVO.getName()))
                .mapToLong(NameCountVO::getCount)
                .sum();

        // 使用建造者模式创建对象
        return HREducationStatVO.builder()
                .educationList(calculateNameCountRates(educationCountList))
                .bachelorUpCount(bachelorUpCount)
                .bachelorUpRate(DemoUtils.divide(bachelorUpCount, totalCount))
                .build();
    }

    /**
     * 查询人力整体看板-年龄分布情况
     *
     * @param qo 查询参数
     * @return
     */
    public HRAgeStatVO getHKAgeStat(HKDashQO qo) {
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);

        List<NameCountVO> ageCountList = rosterDetailService.selectHKAgeStat(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1);

        return HRAgeStatVO.builder()
                .ageStatList(calculateNameCountRates(ageCountList))
                .avgAge(rosterDetailService.selectHKAvgAge(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1))
                .build();
    }

    /**
     * 查询人力整体看板-司龄分布情况
     *
     * @param qo 查询参数
     * @return
     */
    public HKCohlSeniorityStatVO getHKCohlSeniorityStat(HKDashQO qo) {
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);

        List<NameCountVO> seniorityCountList = rosterDetailService.selectHKCohlSeniority(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1);

        return HKCohlSeniorityStatVO.builder()
                .cohlSeniorityList(calculateNameCountRates(seniorityCountList))
                .avgCohlSeniority(rosterDetailService.selectHKAvgCohlSeniority(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), 1))
                .build();
    }

    public NationalityStatVO getNationalityStat(HKDashQO qo) {
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);

        List<NameCountVO> nationalityCountList = rosterDetailService.selectHKNationalityCount(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1));
        List<NameCountRateVO> nationalityRateList = calculateNameCountRates(nationalityCountList);

        List<NameCountVO> overseasCountList = rosterDetailService.selectHKOverseasCount(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1));
        List<NameCountRateVO> overseasRateList = calculateNameCountRates(overseasCountList);

        NationalityStatVO nationalityStatVO = new NationalityStatVO();
        nationalityStatVO.setNationalityList(nationalityRateList);
        nationalityStatVO.setOverseasList(overseasRateList);

        return nationalityStatVO;
    }

    public ResultPage<RosterDetailCategoryVO> getNationalityList(HKNationalityQO qo,Integer pageNum,Integer pageSize){
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKNationalityList(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), qo.getNationality());
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportNationalityList(HKNationalityQO qo,HttpServletResponse response) throws IOException {
        List<LocalDate> dateRange = getStatDateRangeInMonth(qo);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKNationalityList(qo.getOrganizationCodeList(), dateRange.get(0), dateRange.get(1), qo.getNationality());
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    /**
     * 中建香港板块下查询公司人员编制概况
     * <p>
     * 工程公司：按6大公司级架构展示：房屋、土木、基础（含机械公司）、机电、中建医疗、海宏（含中资讯），按组织code排序
     * 2025.02.20 新版-按照固定值排序：房屋、医疗、土木、机电、基础、海宏
     * 当月在岗人数：所选年月的在岗人数
     * 当月编制数：所选年月的编制总数
     * 当月编制使用率：当月在岗人数/当月编制数
     *
     * @param qo
     * @return
     */
    public List<PersonSituationVO> getSubsidiaryOverviewPersonSituation(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        YearMonth yearMonth = YearMonth.from(statDate);
        LocalDate endYear = statDate.with(TemporalAdjusters.lastDayOfYear());
        YearMonth endYearMonth = YearMonth.from(endYear);

        List<NameCountVO> subsidiaryCountList = rosterDetailService.selectHKPersonMonthCountBySubsidiary(HrrsConsts.HK_SUBSIDIARY_LIST, yearMonth, qo.getOrganizationCodeList());
        Map<String, Long> mapCount = subsidiaryCountList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<Organization> orgList = organizationService.listByCompanyNameAndSubCompanyName(List.of("中建香港"), HrrsConsts.HK_SUBSIDIARY_LIST);
        Map<String, List<String>> mapSubCompanyDeptToCode = orgList.stream()
                .collect(Collectors.groupingBy(Organization::getSubCompanyDept, Collectors.mapping(Organization::getCode, Collectors.toList())));

        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());


        Long totalOnJobCount = 0L;
        Long totalStaffCountPlan = 0L;
        // 按指定顺序处理
        List<String> orderedSubsidiaries = List.of("房屋公司", "中建医疗公司", "土木公司", "机电公司", "基础公司", "海宏公司");
        // 创建一个 Map 用于快速查找
        Map<String, PersonSituationVO> overviewMap = new HashMap<>();

        for (String subsidiary : mapCount.keySet()) {
            String pinyinSubsidiary = TranslateUtils.convertToPinyin(subsidiary);
            PersonSituationVO overviewVO = new PersonSituationVO();
            overviewVO.setCompanyName(subsidiary);

            long onJobCount = mapCount.getOrDefault(subsidiary, 0L);
            // 取StaffCountFull的 organizationCode 应该包含在 [mapSubCompanyDept 根据 subsidiary 取出的 siteCodeList] 中
            List<StaffCountFull> subsidiaryStaffCountList = lstStaffCount.stream().filter(staffCountFull ->
                    CollectionUtils.isNotEmpty(mapSubCompanyDeptToCode.get(subsidiary)) && mapSubCompanyDeptToCode.get(subsidiary).contains(staffCountFull.getOrganizationCode())).collect(Collectors.toList());

            if (TranslateUtils.convertToPinyin("基础公司").equals(pinyinSubsidiary)) {
                onJobCount += mapCount.getOrDefault("机械公司", 0L);
                List<String> orgCodes = mapSubCompanyDeptToCode.get("机械公司");
                subsidiaryStaffCountList.addAll(lstStaffCount.stream().filter(staffCountFull -> CollectionUtils.isNotEmpty(orgCodes) && orgCodes.contains(staffCountFull.getOrganizationCode())).toList());
            } else if ("海宏公司".equals(subsidiary)) {
                onJobCount += mapCount.getOrDefault("中建资讯科技公司", 0L);
                List<String> orgCodes = mapSubCompanyDeptToCode.get("中建资讯科技公司");
                subsidiaryStaffCountList.addAll(lstStaffCount.stream().filter(staffCountFull ->  CollectionUtils.isNotEmpty(orgCodes) && orgCodes.contains(staffCountFull.getOrganizationCode())).toList());
            }else if ("机械公司".equals(subsidiary) || "中建资讯科技公司".equals(subsidiary)) {
                // 跳过机械公司和中建资讯科技公司，因为它们的数据已经被合并到基础公司和海宏公司中
                continue;
            }
            overviewVO.setOnJobCountMonth(onJobCount);
            totalOnJobCount += onJobCount;
            // 新需求修改为统计所选日期年份12月的编制人数
            long staffCountPlan = StaffCountFullHelper.calcStaffCountOfMonth(subsidiaryStaffCountList, endYearMonth);
            overviewVO.setStaffCountMonth(staffCountPlan);
            totalStaffCountPlan += staffCountPlan;

            overviewVO.setUsageRateMonth(DemoUtils.divide(onJobCount, staffCountPlan));

            // 将每个公司情况存入 map
            overviewMap.put(subsidiary, overviewVO);
        }
        PersonSituationVO totalOverview = new PersonSituationVO();
        totalOverview.setCompanyName("总计");
        totalOverview.setOnJobCountMonth(totalOnJobCount);
        totalOverview.setStaffCountMonth(totalStaffCountPlan);
        totalOverview.setUsageRateMonth(DemoUtils.divide(totalOnJobCount, totalStaffCountPlan));

        // 按照 orderedSubsidiaries 的顺序构建最终的结果
        List<PersonSituationVO> lstOverview = new ArrayList<>();
        for (String subsidiary : orderedSubsidiaries) {
            if (overviewMap.containsKey(subsidiary)) {
                lstOverview.add(overviewMap.get(subsidiary));
            }
        }

        lstOverview.add(totalOverview); // 添加总计

        return lstOverview;
    }

    /**
     * 中建香港板块下查询公司人员编制概况
     * <p>
     * 工程公司：按6大公司级架构展示：房屋、土木、基础（含机械公司）、机电、中建医疗、海宏（含中资讯），按组织code排序
     * 排序修改成：房屋、医疗、土木、机电、基础、海宏
     * 月平均薪酬：本年度截止所选年月，累计薪酬总额/月份数
     * 人均产值：本年度截止到所选年月，工程公司每月产值之和/【年度平均人数】
     * 百元产值人工成本：本年度截止到所选年月，工程公司累计薪酬总额/累计产值*100
     *
     * @param qo
     * @return
     */
    public List<PersonEffectivenessSituationVO> getSubsidiaryOverviewPersonEffectivenessSituation(HKDashQO qo) {

        CalculateSalaryAndValue calculateSalaryAndValue = new CalculateSalaryAndValue(qo);
        return calculateSalaryAndValue.getOverviewList();
    }

    /**
     * 仅仅保留项目
     *
     * @param lstOrgs 组织机构信息
     * @return
     */
    private static List<Organization> keepOnlyProjects(List<Organization> lstOrgs) {
        if (CollectionUtils.isNotEmpty(lstOrgs)) {
            lstOrgs = lstOrgs.stream().filter(org -> Boolean.TRUE.equals(org.getIsProject())).collect(Collectors.toList());
        }
        return lstOrgs;
    }

    private String getCompayIdBySubsidiary(String subsidiary) {
        return HrrsConsts.DISPLAY_SUBSIDIARY_MAP.get(subsidiary);
    }

    public List<PersonSituationVO> getDepartmentOverviewPersonSituation(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        YearMonth yearMonth = YearMonth.from(statDate);
        LocalDate endYearDate = statDate.with(TemporalAdjusters.lastDayOfYear());
        YearMonth endYearMonth = YearMonth.from(endYearDate);

        List<NameCountVO> departmentCountList = rosterDetailService.selectHKPersonMonthCountBySubsidiary(HrrsConsts.HK_DEPARTMENT_LIST, yearMonth, qo.getOrganizationCodeList());
        Map<String, Long> mapCount = departmentCountList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<PersonSituationVO> lstOverview = new ArrayList<>();
        long onJobCountTotal = 0L;
        long staffCountPlanTotal = 0L;
        for (String department : mapCount.keySet()) {
            PersonSituationVO overviewVO = new PersonSituationVO();
            overviewVO.setCompanyName(department);

            long onJobCount = mapCount.getOrDefault(department, 0L);

            // 获取该部门下的所有组织机构代码
            List<String> orgCodeList = getSubCompanyDeptsOrgCodeList(department);
            orgCodeList = orgCodeList.stream().filter(code -> CollectionUtils.isEmpty(qo.getOrganizationCodeList()) || CollectionUtils.containsAny(qo.getOrganizationCodeList(), code)).collect(Collectors.toList());
            List<StaffCountFull> departmentStaffCountList = staffCountFullService.listByOrgCodes(orgCodeList);
            // 新需求修改为获取年未编制数
            long staffCountPlan = StaffCountFullHelper.calcStaffCountOfMonth(departmentStaffCountList, endYearMonth);
            staffCountPlanTotal += staffCountPlan;
            overviewVO.setStaffCountMonth(staffCountPlan);

            overviewVO.setOnJobCountMonth(onJobCount);
            onJobCountTotal += onJobCount;

            overviewVO.setUsageRateMonth(DemoUtils.divide(onJobCount, staffCountPlan));

            lstOverview.add(overviewVO);
        }
        PersonSituationVO totalOverview = new PersonSituationVO();
        totalOverview.setCompanyName("总计");
        totalOverview.setOnJobCountMonth(onJobCountTotal);
        totalOverview.setStaffCountMonth(staffCountPlanTotal);
        totalOverview.setUsageRateMonth(DemoUtils.divide(onJobCountTotal, staffCountPlanTotal));
        lstOverview.add(totalOverview);

        return lstOverview;
    }

    public List<PersonEffectivenessSituationVO> getDepartmentOverviewPersonEffectivenessSituation(HKDashQO hkDashQO) {
        LocalDate statDate = getStatDate(hkDashQO);
        LocalDate startDate = statDate.withDayOfYear(1);

        List<PersonEffectivenessSituationVO> lstOverview = new ArrayList<>();
        BigDecimal cumulativeSalaryAmountTotal = BigDecimal.ZERO;
        long managerCountForSalaryTotal = 0l;
        BigDecimal salaryAmountManagerTotal = BigDecimal.ZERO;
        BigDecimal budgetSalaryTotal = BigDecimal.ZERO;
        BigDecimal totalYearBudgetSalaryAmount = BigDecimal.ZERO;

        //取管理人員數
        List<Organization> orgList = organizationService.listByCodes(hkDashQO.getOrganizationCodeList());
        List<NameCountVO> subsidiaryManagerCountListForSalary = rosterDetailService.selectHKManagerCountGroupBySubsidiaryForSalary(HrrsConsts.HK_DEPARTMENT_LIST, startDate, statDate, orgList.stream().map(Organization::getCode).toList());
        Map<String, Long> mapSubsidiaryManagerCountForSalary = subsidiaryManagerCountListForSalary.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        for (String department : HrrsConsts.HK_DEPARTMENT_LIST) {
            PersonEffectivenessSituationVO overviewVO = new PersonEffectivenessSituationVO();
            overviewVO.setCompanyName(department);

            // 获取该部门下的所有组织机构代码
            List<String> orgCodeList = getSubCompanyDeptsOrgCodeList(department);
            if (CollectionUtils.isNotEmpty(hkDashQO.getOrganizationCodeList())) {
                orgCodeList = orgCodeList.stream().filter(hkDashQO.getOrganizationCodeList()::contains).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(orgCodeList)) {
                continue;
            }
            List<StaffCountFull> departmentStaffCountList = staffCountFullService.listByOrgCodes(orgCodeList);
            // 薪酬预算
            List<StaffCountFull> bugetSalaryList = departmentStaffCountList.stream().filter(item -> StringUtils.isNotBlank(item.getSubjectCode()) && !HrrsConsts.BUDGET_EXCLUDE_SALARY.contains(item.getSubjectCode())).collect(Collectors.toList());
            BigDecimal salaryBudget = DemoUtils.divide(StaffCountFullHelper.calcSalaryBudget(bugetSalaryList, statDate.withDayOfYear(1), statDate.with(TemporalAdjusters.lastDayOfMonth())),BigDecimal.valueOf(10000),4);
            // 全年薪酬预算
            BigDecimal totalYearSalaryBudget = StaffCountFullHelper.calcSalaryBudget(bugetSalaryList, statDate.withDayOfYear(1), statDate.with(TemporalAdjusters.lastDayOfYear()));
            overviewVO.setTotalYearSalaryBudget(totalYearSalaryBudget);
            if (totalYearSalaryBudget.compareTo(BigDecimal.ZERO) != 0){
                totalYearBudgetSalaryAmount = totalYearBudgetSalaryAmount.add(totalYearSalaryBudget);
            }
            long managerCountForSalary = mapSubsidiaryManagerCountForSalary.getOrDefault(department, 0L);

            // 薪酬
            //List<String> projectIdList = organizationFacade.listFisProjectIdByOrgCodes(orgCodeList);
            SalaryRecordService salaryRecordService = SpringContextHolder.getBean(SalaryRecordService.class);
            List<SalaryRecord> salaryRecordList = salaryRecordService.listSalaryRecordByOrgCodeAndYearMonth(orgCodeList, statDate.getYear(), statDate.getMonthValue());
            /*
            List<SubjectBalance> subjectBalanceList = subjectBalanceService.listSalaryByProjectIdAndYearMonth(projectIdList, statDate.getYear(), statDate.getMonthValue());
            BigDecimal cumulativeSalary = subjectBalanceList.stream().map(SubjectBalance::getAmountYearHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
            */
            BigDecimal cumulativeSalary = salaryRecordList.stream().map(SalaryRecord::getTotalSalary).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
            cumulativeSalary = CommonUtils.add(cumulativeSalary, calcSalaryByStaffCountFullCurrentMonth(departmentStaffCountList, statDate)); // 累计薪酬总额

            List<String> finalOrgCodeList = orgCodeList;
            List<SalaryRecord> subsidiarySalaryRecordListManager = salaryRecordList.stream().filter(salaryRecord -> CollectionUtils.isNotEmpty(finalOrgCodeList) && finalOrgCodeList.contains(salaryRecord.getOrganizationCode())
                            && isValidSalaryHKManager(salaryRecord.getEmployeeCategory(), salaryRecord.getStaffType()))
                    .filter(salaryRecord -> salaryRecord.getSalaryYear() == statDate.getYear() && salaryRecord.getSalaryMonth() == statDate.getMonthValue()).toList();
            BigDecimal cumulativeSalaryAmountManager = subsidiarySalaryRecordListManager.stream().map(SalaryRecord::getTotalSalary).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);

            BigDecimal avgPersonSalaryPerMonth = DemoUtils.divide(DemoUtils.divide(cumulativeSalary, statDate.getMonthValue()).setScale(4, RoundingMode.HALF_UP), BigDecimal.valueOf(10000), 4); // 月平均薪酬=累计薪酬总额/月份  万港元
            overviewVO.setAvgPersonSalaryPerMonth(avgPersonSalaryPerMonth);
            if (totalYearSalaryBudget.compareTo(BigDecimal.ZERO) != 0){
                overviewVO.setSalaryUsedRate(cumulativeSalary.divide(totalYearSalaryBudget,RoundingMode.HALF_UP));
            }
            if (salaryBudget.compareTo(BigDecimal.ZERO) != 0){
                budgetSalaryTotal.add(salaryBudget);
            }
            cumulativeSalaryAmountTotal = cumulativeSalaryAmountTotal.add(avgPersonSalaryPerMonth) ;
            overviewVO.setManagerAvgSalary(DemoUtils.divide(DemoUtils.divide(cumulativeSalaryAmountManager, managerCountForSalary).setScale(4, RoundingMode.HALF_UP), BigDecimal.valueOf(10000), 4));
            salaryAmountManagerTotal = salaryAmountManagerTotal.add(cumulativeSalaryAmountManager);
            managerCountForSalaryTotal += managerCountForSalary;

            lstOverview.add(overviewVO);
        }

        // 总计
        PersonEffectivenessSituationVO totalOverview = new PersonEffectivenessSituationVO();
        totalOverview.setCompanyName("总计");
        totalOverview.setAvgPersonSalaryPerMonth(cumulativeSalaryAmountTotal);
        totalOverview.setManagerAvgSalary(DemoUtils.divide(DemoUtils.divide(salaryAmountManagerTotal, managerCountForSalaryTotal).setScale(4, RoundingMode.HALF_UP), BigDecimal.valueOf(10000), 4));
        totalOverview.setTotalYearSalaryBudget(totalYearBudgetSalaryAmount);
        if (totalYearBudgetSalaryAmount.compareTo(BigDecimal.ZERO) != 0){
            totalOverview.setSalaryUsedRate(cumulativeSalaryAmountTotal.divide(totalYearBudgetSalaryAmount,RoundingMode.HALF_UP));
        }
        lstOverview.add(totalOverview);

        return lstOverview;
    }

    public List<RankingVO> getFullPeriodRanking(HKDashQO qo) {
        List<RankingVO> rankingVOList = new ArrayList<>();
        List<StaffVO> staffVOList = getFullPeriodStaff(qo);
        List<SalaryVO> salaryVOList = getFullPeriodSalary(qo);
        List<EfficiencyVO> efficiencyVOList = getFullPeriodEfficiency(qo);

        for(StaffVO staffVO : staffVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (staffVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(staffVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(staffVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        for(SalaryVO salaryVO : salaryVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (salaryVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(salaryVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(salaryVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        for(EfficiencyVO efficiencyVO : efficiencyVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (efficiencyVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(efficiencyVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(efficiencyVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        return rankingVOList;
    }

    public List<RankingVO> getCurrentYearRanking(HKDashQO qo) {
        List<RankingVO> rankingVOList = new ArrayList<>();
        List<StaffVO> staffVOList = getCurrentYearStaff(qo);
        List<SalaryVO> salaryVOList = getCurrentYearSalary(qo);
        List<EfficiencyVO> efficiencyVOList = getCurrentYearEfficiency(qo);

        for(StaffVO staffVO : staffVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (staffVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(staffVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(staffVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        for(SalaryVO salaryVO : salaryVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (salaryVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(salaryVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(salaryVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        for(EfficiencyVO efficiencyVO : efficiencyVOList) {
            boolean isExisting = false;
            for (RankingVO rankingVO : rankingVOList) {
                if (efficiencyVO.getName().equals(rankingVO.getName())) {
                    BeanUtils.copyProperties(efficiencyVO, rankingVO);
                    isExisting = true;
                }
            }
            if(!isExisting) {
                RankingVO rankingVO = new RankingVO();
                BeanUtils.copyProperties(efficiencyVO, rankingVO);
                rankingVOList.add(rankingVO);
            }
        }

        return rankingVOList;
    }

    public List<StaffVO> getFullPeriodStaff(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        // 员工编制信息
        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        // 组织机构信息
        List<Organization> lstOrgs = organizationService.listByCodes(qo.getOrganizationCodeList());
        lstOrgs = keepOnlyProjects(lstOrgs);

        // 这样做默认所有的编制都是填写的相同的期间日期
        LocalDate[] dateRange;
        StaffCountFull staffCountFull = lstStaffCount.stream()
                .findFirst()
                .orElse(null);
        if (staffCountFull == null) {
            dateRange = new LocalDate[]{null, statDate.withDayOfMonth(statDate.lengthOfMonth())};
        } else {
            dateRange = StaffCountHelper.getStartDateAndEndDate(staffCountFull);
            dateRange[0] = dateRange[0] == null ? statDate.withDayOfYear(1) : dateRange[0];
            dateRange[1] = statDate.withDayOfMonth(statDate.lengthOfMonth());
        }

        List<NameCountVO> lstPersonCountVO = rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), null, dateRange[1]);
        Map<String, Long> mapOrgPersonCount = lstPersonCountVO.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<StaffVO> lstWarning = new ArrayList<>();
        Long onJobTotal = 0L; // 总计累计在岗人数
        Long staffCountPlanTotal = 0L; // 总计编制人数

        for (Organization org : lstOrgs) {
            StaffVO staffVO = new StaffVO();

            staffVO.setName(formatOrgName(org.getSiteCode(), org.getName()));

            Long onJobCount = mapOrgPersonCount.getOrDefault(org.getCode(), 0L);
            staffVO.setOnJobCount(onJobCount);
            onJobTotal = onJobTotal + onJobCount;

            long staffCountPlan = getAllStaffCount(Arrays.asList(org.getCode()), statDate); // 累计编制人数:
            staffVO.setStaffCountPlan(staffCountPlan);
            staffCountPlanTotal += staffCountPlan;
            staffVO.setPlanUsageRate(DemoUtils.divide(onJobCount, staffCountPlan));

            //取地盤負責人
            if(StringUtils.isNotBlank(org.getSiteLeaderUsername())) {
                RosterDetail siteLeader = rosterDetailService.getByUsername(org.getSiteLeaderUsername(), statDate);
                if(siteLeader!= null) {
                    staffVO.setSiteLeaderName(siteLeader.getName());
                }
            }

            lstWarning.add(staffVO);
        }
        // 按累计在岗人数倒叙，如果是null则排在最后
        lstWarning.sort(Comparator.comparing(StaffVO::getOnJobCount, Comparator.nullsLast(Comparator.reverseOrder())));

        StaffVO staffTotal = new StaffVO();
        staffTotal.setName("总计");
        staffTotal.setOnJobCount(onJobTotal);
        staffTotal.setStaffCountPlan(staffCountPlanTotal);
        staffTotal.setPlanUsageRate(DemoUtils.divide(onJobTotal, staffCountPlanTotal));
        lstWarning.add(staffTotal);

        return lstWarning;
    }

    public List<StaffVO> getCurrentYearStaff(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo); // 所选日期
        LocalDate startDate = statDate.withDayOfYear(1); // 当年的第一天
//        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfYear()); // 所选日期当年的最后一天
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth()); // 所选日期当月的最后一天
        // 员工编制信息
        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        // 组织机构信息
        List<Organization> lstOrgs = organizationService.listByCodes(qo.getOrganizationCodeList());
        lstOrgs = keepOnlyProjects(lstOrgs);

        List<NameCountVO> lstPersonCountVO = rosterDetailService.selectHKOrgOnJobCount(qo.getOrganizationCodeList(), startDate, endDate);
        Map<String, Long> mapOrgPersonCount = lstPersonCountVO.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<StaffVO> lstWarning = new ArrayList<>();
        Long onJobTotal = 0L; // 总计累计在岗人数
        Long staffCountPlanTotal = 0L; // 总计编制人数

        for (Organization org : lstOrgs) {
            StaffVO staffVO = new StaffVO();
            // staffVO.setName(org.getName());
            staffVO.setName(formatOrgName(org.getSiteCode(), org.getName()));

            Long onJobCount = mapOrgPersonCount.getOrDefault(org.getCode(), 0L);
            staffVO.setOnJobCount(onJobCount);
            onJobTotal = onJobTotal + onJobCount;

            List<StaffCountFull> subsidiaryStaffCountList = lstStaffCount.stream()
                    .filter(staffCountFullPeriod -> org.getCode().equals(staffCountFullPeriod.getOrganizationCode())).collect(Collectors.toList());

            long staffCountPlan = StaffCountFullHelper.calcStaffCountOfYear(subsidiaryStaffCountList, statDate);
            staffVO.setStaffCountPlan(staffCountPlan);
            staffCountPlanTotal += staffCountPlan;
            staffVO.setPlanUsageRate(DemoUtils.divide(onJobCount, staffCountPlan));

            //取地盤負責人
            if(StringUtils.isNotBlank(org.getSiteLeaderUsername())) {
                RosterDetail siteLeader = rosterDetailService.getByUsername(org.getSiteLeaderUsername(), statDate);
                if(siteLeader!= null) {
                    staffVO.setSiteLeaderName(siteLeader.getName());
                }
            }

            lstWarning.add(staffVO);
        }
        // 按累计在岗人数倒叙，如果是null则排在最后
        lstWarning.sort(Comparator.comparing(StaffVO::getOnJobCount, Comparator.nullsLast(Comparator.reverseOrder())));

        StaffVO staffTotal = new StaffVO();
        staffTotal.setName("总计");
        staffTotal.setOnJobCount(onJobTotal);
        staffTotal.setStaffCountPlan(staffCountPlanTotal);
        staffTotal.setPlanUsageRate(DemoUtils.divide(onJobTotal, staffCountPlanTotal));
        lstWarning.add(staffTotal);

        return lstWarning;
    }

    public List<SalaryVO> getFullPeriodSalary(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        List<Organization> lstOrgs = organizationService.listByCodes(qo.getOrganizationCodeList());
        lstOrgs = keepOnlyProjects(lstOrgs);


        List<SalaryRecordVO> totalSalaryList = salaryRecordService.listFullYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        // totalSalaryList根据organizationCode进行分组Map<String, List<SalaryRecordVO>>
        Map<String, List<SalaryRecordVO>>  totalSalaryMap = totalSalaryList.stream().collect(Collectors.groupingBy(SalaryRecordVO::getOrganizationCode));


        List<SalaryVO> salaryList = new ArrayList<>();

        BigDecimal cumulativeSalaryTotal = BigDecimal.ZERO; // 累计薪酬总额 -- 总计
        BigDecimal budgetTotal = BigDecimal.ZERO;  // 薪酬预算 -- 总计

        for (Organization org : lstOrgs) {
            SalaryVO salaryVO = new SalaryVO();
            // salaryVO.setName(org.getName());
            salaryVO.setName(formatOrgName(org.getSiteCode(), org.getName()));

//            String projectId = mapCdmsCodeProjectId.get(mapOrgCodeOrg.get(org.getCode()).getSiteCode());
            // 全周期累计薪酬总额
            BigDecimal cumulativeTotalSalary = Optional.ofNullable(totalSalaryMap.get(org.getCode()))
                    .map(SalaryRecordFacade::calculateSalary)
                    .orElse(BigDecimal.ZERO); // 累计薪酬总额
            salaryVO.setCumulativeSalary(cumulativeTotalSalary);
            cumulativeSalaryTotal = cumulativeSalaryTotal.add(cumulativeTotalSalary);

            List<StaffCountFull> orgStaffCountList = lstStaffCount.stream().filter(staffCountFull ->
                    org.getCode().equals(staffCountFull.getOrganizationCode())
                            && "薪酬预算".equals(staffCountFull.getSubjectType())
                            && staffCountFull.getStartDate() != null
            ).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(orgStaffCountList)) {
                LocalDate startDate = orgStaffCountList.get(0).getStartDate();
                BigDecimal salaryBudget = StaffCountFullHelper.calcSalaryBudget(orgStaffCountList, startDate, statDate.with(TemporalAdjusters.lastDayOfMonth()));
                salaryVO.setBudgetSalary(salaryBudget);
                budgetTotal = budgetTotal.add(salaryBudget);
                salaryVO.setCumulativeSalaryUsageRate(DemoUtils.divide(salaryVO.getCumulativeSalary(), salaryBudget));
            }

            salaryList.add(salaryVO);
        }
        // 按累计薪酬使用率倒叙，如果是null则排在最后
        salaryList.sort(Comparator.comparing(SalaryVO::getCumulativeSalaryUsageRate, Comparator.nullsLast(Comparator.reverseOrder())));

        SalaryVO salaryTotal = new SalaryVO();
        salaryTotal.setName("总计");
        salaryTotal.setCumulativeSalary(cumulativeSalaryTotal);
        salaryTotal.setBudgetSalary(budgetTotal);
        salaryTotal.setCumulativeSalaryUsageRate(DemoUtils.divide(cumulativeSalaryTotal, budgetTotal)); // 累计薪酬总额/薪酬预算总额
        salaryList.add(salaryTotal);
        return salaryList;
    }

    public List<SalaryVO> getCurrentYearSalary(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        // 确定时间范围
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = getEndDate(statDate);

        List<StaffCountFull> lstStaffCount = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());

        List<Organization> lstOrgs = organizationService.listByCodes(qo.getOrganizationCodeList());
        lstOrgs = keepOnlyProjects(lstOrgs);

        List<SalaryRecordVO> salaryRecordVOList = salaryRecordService.listYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);

        List<SalaryVO> salaryList = new ArrayList<>();

        BigDecimal cumulativeSalaryTotal = BigDecimal.ZERO; // 累计薪酬总额 -- 总计
        BigDecimal budgetTotal = BigDecimal.ZERO;  // 薪酬预算 -- 总计

        for (Organization org : lstOrgs) {
            SalaryVO salaryVO = new SalaryVO();
            // salaryVO.setName(org.getName());
            salaryVO.setName(formatOrgName(org.getSiteCode(), org.getName()));



            if (CollectionUtils.isNotEmpty(salaryRecordVOList)) {
//                    BigDecimal cumulativeSalary = lstSubjectBalance.stream().map(SubjectBalance::getAmountYearHkd).reduce(CommonUtils::add).orElse(BigDecimal.ZERO);
                BigDecimal currentYearTotalSalary = SalaryRecordFacade.calculateSalaryByOrgCode(org.getCode(), salaryRecordVOList);
                salaryVO.setCumulativeSalary(currentYearTotalSalary);
                cumulativeSalaryTotal = cumulativeSalaryTotal.add(currentYearTotalSalary);
            }
//            }
            // 薪酬预算默认只统计XZYS.01 薪酬预算（内派）+ XZYS.02 薪酬预算（核心港聘）+ XZYS.03 薪酬预算（地盘基层）+ XZYS.04 薪酬预算（内派中台）
            List<StaffCountFull> staffCountFullList = lstStaffCount.stream().filter(staffCountFull ->
                    (org.getCode().equals(staffCountFull.getOrganizationCode())) && (StringUtils.isNotBlank(staffCountFull.getSubjectCode()) && !HrrsConsts.BUDGET_EXCLUDE_SALARY.contains(staffCountFull.getSubjectCode()))).collect(Collectors.toList());
            // 取subjectType = 薪酬预算
            BigDecimal salaryBudget = StaffCountFullHelper.calcSalaryBudget(staffCountFullList, startDate, endDate);
            salaryVO.setBudgetSalary(salaryBudget);
            budgetTotal = budgetTotal.add(salaryBudget);
            // 本年度截止到所选年月，所选地盘薪酬总金额之和/薪酬预算金额之和
            salaryVO.setCumulativeSalaryUsageRate(DemoUtils.divide(salaryVO.getCumulativeSalary(), salaryBudget));

            salaryList.add(salaryVO);
        }
        // 按累计薪酬使用率倒叙，如果是null则排在最后
        salaryList.sort(Comparator.comparing(SalaryVO::getCumulativeSalaryUsageRate, Comparator.nullsLast(Comparator.reverseOrder())));

        SalaryVO salaryTotal = new SalaryVO();
        salaryTotal.setName("总计");
        salaryTotal.setCumulativeSalary(cumulativeSalaryTotal);
        salaryTotal.setBudgetSalary(budgetTotal);
        salaryTotal.setCumulativeSalaryUsageRate(DemoUtils.divide(cumulativeSalaryTotal, budgetTotal)); // 累计薪酬总额/薪酬预算总额
        salaryList.add(salaryTotal);
        return salaryList;
    }

    public List<EfficiencyVO> getCurrentYearEfficiency(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);
        // 确定时间范围
        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = getEndDate(statDate);

        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");

        List<Organization> organizationList = organizationService.listByCodes(qo.getOrganizationCodeList());
        Map<String, Organization> orgCodeToOrganizationMap = organizationList.stream().collect(Collectors.toMap(Organization::getCode, Function.identity()));
        List<String> siteCodeList = organizationList.stream().map(Organization::getSiteCode).filter(StringUtils::isNotBlank).distinct().toList();

        List<RawRevenueSite> rawRevenueSiteList = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(siteCodeList, statDate);
        Map<String, RawRevenueSite> cdmsCodeToRawRevenueSiteMap = rawRevenueSiteList.stream().collect(Collectors.toMap(RawRevenueSite::getSiteCode, Function.identity(), (k1, k2) -> k1));

        Map<String, Long> orgPersonCountMap = rosterDetailService.selectHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(), startDate, endDate).stream()
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));


        List<SalaryRecordVO> salaryRecordVOS = salaryRecordService.listYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        // salaryRecordVOS根据organizationCode进行分组Map<String, List<SalaryRecordVO>>
        Map<String, List<SalaryRecordVO>>  totalSalaryMap = salaryRecordVOS.stream().collect(Collectors.groupingBy(SalaryRecordVO::getOrganizationCode));

        List<EfficiencyVO> efficiencyRankingVOList = new ArrayList<>();
        BigDecimal avgOnJobCountTotal = BigDecimal.ZERO; // 人数之和
        BigDecimal currentTotalRevenueTotal = BigDecimal.ZERO; // 本年度产值之和
        BigDecimal currentTotalSalaryTotal = BigDecimal.ZERO; // 本年度薪酬总额
        for (String orgCode : qo.getOrganizationCodeList()) {
            Organization organization = orgCodeToOrganizationMap.get(orgCode);
            if (organization == null || !Boolean.TRUE.equals(organization.getIsProject())) {
                continue;
            }
            EfficiencyVO efficiencyRankingVO = new EfficiencyVO();
            // BaseBusinessUnit baseBusinessUnit = orgCodeToBaseBusinessUnitMap.get(orgCode);
            // efficiencyRankingVO.setName(organization.getName());
            efficiencyRankingVO.setName(formatOrgName(organization.getSiteCode(), organization.getName()));

            long onJobCount = Optional.ofNullable(orgPersonCountMap.get(orgCode)).orElse(0L);
            BigDecimal avgOnJobCount = DemoUtils.divide(BigDecimal.valueOf(onJobCount),
                    BigDecimal.valueOf(statDate.getMonthValue()), 4);
            avgOnJobCountTotal = avgOnJobCountTotal.add(avgOnJobCount);

            // 人均产值：本年度截止到所选年月，每月产值之和/每月在职人数之和 * 12
            BigDecimal currentTotalRevenue = Optional.ofNullable(cdmsCodeToRawRevenueSiteMap.get(organization.getSiteCode())).map(RawRevenueSite::getAmountYear).orElse(BigDecimal.ZERO);
            currentTotalRevenueTotal = currentTotalRevenueTotal.add(currentTotalRevenue);
            BigDecimal perCapitalOutput = DemoUtils.divide(currentTotalRevenue, avgOnJobCount).setScale(1, RoundingMode.HALF_UP);
            efficiencyRankingVO.setPerCapitaOutput(perCapitalOutput);

            // 百元产值人工成本：本年度截止到所选年月，累计薪酬总额/累计产值*100
            BigDecimal currentTotalSalary = Optional.ofNullable(totalSalaryMap.get(orgCode))
                    .map(SalaryRecordFacade::calculateSalary)
                    .orElse(BigDecimal.ZERO); // 累计薪酬总额

            currentTotalSalaryTotal = currentTotalSalaryTotal.add(currentTotalSalary);
            BigDecimal laborCostPerHundredOutput = DemoUtils.divide(currentTotalSalary, currentTotalRevenue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            efficiencyRankingVO.setLaborCostPerHundredOutput(laborCostPerHundredOutput);

            efficiencyRankingVOList.add(efficiencyRankingVO);
        }

        efficiencyRankingVOList.sort(Comparator.comparing(EfficiencyVO::getPerCapitaOutput, Comparator.nullsLast(Comparator.reverseOrder())));

        EfficiencyVO efficiencyRankingVOTotal = new EfficiencyVO();
        efficiencyRankingVOTotal.setName("总计");
        efficiencyRankingVOTotal.setPerCapitaOutput(DemoUtils.divide(currentTotalRevenueTotal, avgOnJobCountTotal).setScale(1, RoundingMode.HALF_UP));
        efficiencyRankingVOTotal.setLaborCostPerHundredOutput(DemoUtils.divide(currentTotalSalaryTotal, currentTotalRevenueTotal, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        efficiencyRankingVOList.add(efficiencyRankingVOTotal);

        return efficiencyRankingVOList;
    }

    /**
     * 全周期人效
     *
     * @param qo 查询条件
     * @return
     */
    public List<EfficiencyVO> getFullPeriodEfficiency(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);  // 结束日期=截止日期，即选中的日期
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");


        List<Organization> organizationList = organizationService.listByCodes(qo.getOrganizationCodeList());
        Map<String, Organization> orgCodeToOrganizationMap = organizationList.stream().collect(Collectors.toMap(Organization::getCode, Function.identity()));
        List<String> siteCodeList = organizationList.stream().map(Organization::getSiteCode).filter(StringUtils::isNotBlank).distinct().toList();

        List<RawRevenueSite> rawRevenueSiteList = rawRevenueSiteService.listRawRevenueBySiteCodeAndDate(siteCodeList, statDate);
        Map<String, RawRevenueSite> cdmsCodeToRawRevenueSiteMap = rawRevenueSiteList.stream().collect(Collectors.toMap(RawRevenueSite::getSiteCode, Function.identity(), (k1, k2) -> k1));

        Map<String, Long> orgPersonCountMap = rosterDetailService.selectFullHKOrgOnJobCountForOutputValue(qo.getOrganizationCodeList(),  statDate).stream()
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));


        List<SalaryRecordVO> totalSalaryList = salaryRecordService.listFullYearTotalHKSalaryRecord(qo.getOrganizationCodeList(), statDate);
        // totalSalaryList根据organizationCode进行分组Map<String, List<SalaryRecordVO>>
        Map<String, List<SalaryRecordVO>>  totalSalaryMap = totalSalaryList.stream().collect(Collectors.groupingBy(SalaryRecordVO::getOrganizationCode));

        List<EfficiencyVO> efficiencyRankingVOList = new ArrayList<>();
        BigDecimal cumulativeRevenueTotal = BigDecimal.ZERO; // 地盘产值之和
        BigDecimal onJobCountTotal = BigDecimal.ZERO; // 人数之和
        BigDecimal cumulativeSalaryTotal = BigDecimal.ZERO; // 地盘薪酬总额
        for (String orgCode : qo.getOrganizationCodeList()) {
            EfficiencyVO efficiencyRankingVO = new EfficiencyVO();
            Organization organization = orgCodeToOrganizationMap.get(orgCode);
            if (organization == null || !Boolean.TRUE.equals(organization.getIsProject())) {
                continue;
            }
            // efficiencyRankingVO.setName(organization.getName());
            efficiencyRankingVO.setName(formatOrgName(organization.getSiteCode(), organization.getName()));

            long onJobCount = Optional.ofNullable(orgPersonCountMap.get(orgCode)).orElse(0L);
            onJobCountTotal = onJobCountTotal.add(BigDecimal.valueOf(onJobCount));
            LocalDate orgStartDate = organization.getStartDate() == null ? qo.getStatDate().withDayOfYear(1) : organization.getStartDate();

            // 人均产值：地盘开工年月截止到所选年月，每月产值之和/每月在职人数之和
            BigDecimal cumulativeRevenue = Optional.ofNullable(cdmsCodeToRawRevenueSiteMap.get(organization.getSiteCode())).map(RawRevenueSite::getAmountTotal).orElse(BigDecimal.ZERO);
            cumulativeRevenueTotal = cumulativeRevenueTotal.add(cumulativeRevenue);
            BigDecimal perCapitalOutput = DemoUtils.divide(cumulativeRevenue, onJobCount).multiply(BigDecimal.valueOf(12)).setScale(1, RoundingMode.HALF_UP);
            efficiencyRankingVO.setPerCapitaOutput(perCapitalOutput);


            // 百元产值人工成本：地盘开工年月截止到所选年月，累计薪酬总额/累计产值*100
            BigDecimal cumulativeTotalSalary = Optional.ofNullable(totalSalaryMap.get(orgCode))
                    .map(SalaryRecordFacade::calculateSalary)
                    .orElse(BigDecimal.ZERO); // 累计薪酬总额
            cumulativeSalaryTotal = cumulativeSalaryTotal.add(cumulativeTotalSalary); // 总计累计薪酬总额

            BigDecimal laborCostPerHundredOutput = DemoUtils.divide(cumulativeTotalSalary, cumulativeRevenue, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            efficiencyRankingVO.setLaborCostPerHundredOutput(laborCostPerHundredOutput);

            efficiencyRankingVOList.add(efficiencyRankingVO);
        }

        efficiencyRankingVOList.sort(Comparator.comparing(EfficiencyVO::getPerCapitaOutput, Comparator.nullsLast(Comparator.reverseOrder())));

        EfficiencyVO efficiencyRankingVOTotal = new EfficiencyVO();
        efficiencyRankingVOTotal.setName("总计");
        efficiencyRankingVOTotal.setPerCapitaOutput(DemoUtils.divide(cumulativeRevenueTotal, onJobCountTotal).multiply(BigDecimal.valueOf(12)).setScale(1, RoundingMode.HALF_UP));
        efficiencyRankingVOTotal.setLaborCostPerHundredOutput(DemoUtils.divide(cumulativeSalaryTotal, cumulativeRevenueTotal, 4).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        efficiencyRankingVOList.add(efficiencyRankingVOTotal);

        return efficiencyRankingVOList;
    }

    public ResultPage<RosterDetailCategoryVO> getElementRosterDetailCategory(HKDashQO qo, String personType, int pageNum, int pageSize) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ResultPage<>(Collections.emptyList(), RosterDetailCategoryVO.class);
        }
        Integer total = rosterDetailService.getHKRosterTypeListTotalCount(qo.getOrganizationCodeList(), headId, personType);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKRosterTypeList(qo.getOrganizationCodeList(), headId, personType, pageNum, pageSize);
        List<RosterDetailCategoryVO> categoryVOList = rosterDetails.stream().map(RosterDetailCategoryVO::fromModel).collect(Collectors.toList());
        return new ResultPage<>(categoryVOList, RosterDetailCategoryVO.class, total, pageNum, pageSize);
    }

    public void exportElementRosterDetailCategory(HKDashQO qo, String personType, HttpServletResponse response) throws IOException {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return;
        }
        // 去除人力资源部门
        qo.getOrganizationCodeList().removeAll(HrrsConsts.HR_ORGANIZATION_CODE);
        List<RosterDetail> rosterDetails = rosterDetailService.exportHKRosterTypeList(qo.getOrganizationCodeList(), headId, personType);
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            AtomicInteger seq = new AtomicInteger(1);
            rosterDetails.forEach(x -> x.setSeq(seq.getAndIncrement()));
            List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    public List<RateVO> getPersonCategory(HKDashQO qo, String projectName) {
        // 获取人员详情列表
        List<CountVO> countVOList = getHKRosterPersonCategoryList(qo, projectName);
        // 处理为空的情况
        if (countVOList == null || countVOList.isEmpty()) {
            return Collections.emptyList();
        }
        // 计算总人数
        long total = countVOList.stream().mapToLong(CountVO::getCount).sum();

        // 分组统计不同类别的人数
        Map<String, Long> mapCount = countVOList.stream()
                .collect(Collectors.groupingBy(vo -> personCategory(vo, projectName), Collectors.summingLong(CountVO::getCount)));

        // 定义类别与名称的对应关系
        List<String> categories = new ArrayList<>();
        if (HrrsConsts.PersonCategory.LEADER.equals(projectName)) {
            categories.addAll(Arrays.asList("工程公司领导", "写字楼", "地盘")); // 工程公司排除职能部门
        } else if (HrrsConsts.PersonCategory.HK_LEADER.equals(projectName)) {
            categories.addAll(Arrays.asList("公司领导", "职能部门", "子公司写字楼", "地盘"));
        }

        return categories.stream()
                .map(category -> createRateVO(category, mapCount.getOrDefault(category, 0L), total))
                .collect(Collectors.toList());
    }

    private List<CountVO> getHKRosterPersonCategoryList(HKDashQO qo, String projectName) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ArrayList<>();
        }
        if (qo instanceof HKDashOutsourceQO) {
            HKDashOutsourceQO outsourceQO = (HKDashOutsourceQO) qo;
            return rosterDetailService.selectHKOutsourceRosterPersonCategoryCountList(outsourceQO.getOrganizationCodeList(), outsourceQO.getOutsourceTypeList(), headId, projectName);
        } else {
            return rosterDetailService.selectHKRosterPersonCategoryCountList(qo.getOrganizationCodeList(), headId, projectName);
        }
    }

    private String personCategory(CountVO vo, String projectName) {
        String type = vo.getType();
        switch (type) {
            case "leader":
                if (HrrsConsts.PersonCategory.LEADER.equals(projectName)) {
                    return HrrsConsts.PersonCategory.ENGINEER_LEADER;
                } else if (HrrsConsts.PersonCategory.HK_LEADER.equals(projectName)) {
                    return HrrsConsts.PersonCategory.COMPANY_LEADER;
                }
                break;
            case "office":
            if (HrrsConsts.PersonCategory.LEADER.equals(projectName)) {
                return "写字楼";
            } else if (HrrsConsts.PersonCategory.HK_LEADER.equals(projectName)) {
                return "子公司写字楼";
            }
            break;
            case "dept":
                return "职能部门";
            default:
                return "地盘";
        }
        return "地盘"; // 默认返回 "地盘"
    }


    public ResultPage<RosterDetailCategoryVO> getPersonRosterDetailCategory(HKDashQO qo, String projectName, String personType, int pageNum, int pageSize) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ResultPage<>(Collections.emptyList(), RosterDetailCategoryVO.class);
        }
        String leaderName = "";
        String officeName = "";
        if (projectName.equals(HrrsConsts.PersonCategory.LEADER)) {
            leaderName = HrrsConsts.PersonCategory.ENGINEER_LEADER;
            officeName = "写字楼";
        } else if (projectName.equals(HrrsConsts.PersonCategory.HK_LEADER)) {
            leaderName = HrrsConsts.PersonCategory.COMPANY_LEADER;
            officeName = "子公司写字楼";
        }
        Integer total = rosterDetailService.selectHKRosterPersonCategoryPageListTotalCount(qo.getOrganizationCodeList(), headId, projectName, leaderName, officeName, personType);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKRosterPersonCategoryPageList(qo.getOrganizationCodeList(), headId, projectName, leaderName, officeName, personType, pageNum, pageSize);
        List<RosterDetailCategoryVO> categoryVOList = rosterDetails.stream().map(RosterDetailCategoryVO::fromModel).collect(Collectors.toList());
        return new ResultPage<>(categoryVOList, RosterDetailCategoryVO.class, total, pageNum, pageSize);
    }


    public void exportPersonRosterDetailCategory(HKDashQO qo, String projectName, String personType,HttpServletResponse response) throws IOException {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return;
        }
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        String leaderName = "";
        String officeName = "";
        if (projectName.equals(HrrsConsts.PersonCategory.LEADER)) {
            leaderName = HrrsConsts.PersonCategory.ENGINEER_LEADER;
            officeName = "写字楼";
        } else if (projectName.equals(HrrsConsts.PersonCategory.HK_LEADER)) {
            leaderName = HrrsConsts.PersonCategory.COMPANY_LEADER;
            officeName = "子公司写字楼";
        }
        // 去掉人力部门的数据
        qo.getOrganizationCodeList().removeAll(HrrsConsts.HR_ORGANIZATION_CODE);
        List<RosterDetail> rosterDetails = rosterDetailService.exportHKRosterPersonCategoryPageList(qo.getOrganizationCodeList(), headId, projectName, leaderName, officeName, personType);
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    public ResultPage<RosterDetailCategoryVO> getOutsourcePersonRosterDetailCategory(HKDashQO qo, String projectName, int pageNum, int pageSize) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ResultPage<>(Collections.emptyList(), RosterDetailCategoryVO.class);
        }
        Integer total = rosterDetailService.selectHKOutsourceRosterPersonCategoryPageListTotalCount(qo.getOrganizationCodeList(), headId);
        List<RosterDetail> rosterDetails = rosterDetailService.selectHKOutsourceRosterPersonCategoryPageList(qo.getOrganizationCodeList(), headId, pageNum, pageSize);
        List<RosterDetailCategoryVO> categoryVOList = rosterDetails.stream().map(RosterDetailCategoryVO::fromModel).collect(Collectors.toList());
        return new ResultPage<>(categoryVOList, RosterDetailCategoryVO.class, total, pageNum, pageSize);
    }
    public void exportOutsourcePersonRosterDetailCategory(HKDashQO qo,HttpServletResponse response) throws IOException {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return;
        }
        qo.getOrganizationCodeList().removeAll(HrrsConsts.HR_ORGANIZATION_CODE);
        List<RosterDetail> rosterDetails = rosterDetailService.exportHKOutsourceRosterPersonCategoryPageList(qo.getOrganizationCodeList(), headId);
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }

    }

    public ResultPage<RosterDetailCategoryVO> getRosterDetailDistributionByCategory(HKDashQO qo, String personType, int pageNum, int pageSize) {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return new ResultPage<>(Collections.emptyList(), RosterDetailCategoryVO.class);
        }

        Integer total = rosterDetailService.queryRosterDetailDistributionByCategoryTotalCount(qo.getOrganizationCodeList(), headId, personType, null);
        List<RosterDetail> rosterDetails = rosterDetailService.queryRosterDetailDistributionByCategory(qo.getOrganizationCodeList(), headId, personType, pageNum, pageSize);
        List<RosterDetailCategoryVO> categoryVOList = rosterDetails.stream().map(RosterDetailCategoryVO::fromModel).collect(Collectors.toList());
        return new ResultPage<>(categoryVOList, RosterDetailCategoryVO.class, total, pageNum, pageSize);
    }

    public void exportRosterDetailDistributionByCategory(HKDashQO qo, String personType,HttpServletResponse response) throws IOException {
        String headId = rosterHeadService.getLatestRosterHead(getStatDate(qo)).getId();
        if (StringUtils.isBlank(headId)) {
            return;
        }
        // 去除人力资源部门
        List<RosterDetail> rosterDetails = rosterDetailService.exportRosterDetailDistributionByCategory(qo.getOrganizationCodeList(), headId, personType);
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }

    }

    public List<SimpleOrgVO> listSubsidiaryOrgs() {
        List<Organization> organizations = organizationService.listAllHKSubCompanyOrgs();
        return organizations.stream().map(SimpleOrgVO::formOrganization).collect(Collectors.toList());
    }

    public List<StaffCountInfoVO> getHKEmployeeDistributionFull(HKDashQO qo) {
        LocalDate statDate = getStatDate(qo);  // 当前日期
        String currentYearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(LocalDate.now());

        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(qo.getOrganizationCodeList());
        LocalDate[] dateRange = CollectionUtils.isEmpty(staffCountFullList) ? null : StaffCountHelper.getStartDateAndEndDateForList(staffCountFullList);
        LocalDate startStaffCountDate = dateRange != null ? dateRange[0] : null;
        LocalDate endStaffCountDate = dateRange != null ? dateRange[1] : null;

        List<CodeNameCountVO> siteEmpTypeList = rosterDetailService.selectSiteEmpType(qo.getOrganizationCodeList(), null, null);
        List<CodeNameCountVO> empTypeList = rosterDetailService.selectEmpType(qo.getOrganizationCodeList(), null, null);

        Map<String, Map<String, List<String>>> finalGrouping = empTypeList.stream()
                .collect(Collectors.groupingBy(
                        CodeNameCountVO::getCode,
                        Collectors.collectingAndThen(
                                Collectors.groupingBy(
                                        CodeNameCountVO::getName,
                                        Collectors.mapping(CodeNameCountVO::getSiteCode, Collectors.toList())
                                ),
                                map -> map
                        )
                ));

        LocalDate startSiteEmpTypeDate = siteEmpTypeList.stream()
                .map(codeNameCountVO -> LocalDate.parse(codeNameCountVO.getCode() + "-01"))
                .min(LocalDate::compareTo)
                .orElse(null);

        /* LocalDate endSiteEmpTypeDate = siteEmpTypeList.stream()
                .map(codeNameCountVO -> LocalDate.parse(codeNameCountVO.getCode() + "-01"))
                .max(LocalDate::compareTo)
                .orElse(null); */

        // 确定 startDate
        LocalDate startDate = Stream.of(startStaffCountDate, startSiteEmpTypeDate)
                .filter(Objects::nonNull)
                .min(LocalDate::compareTo)
                .orElse(null);

        // 确定 endDate
        LocalDate endDate = Stream.of(statDate, endStaffCountDate)
                .filter(Objects::nonNull)
                .max(LocalDate::compareTo)
                .map(date -> date.plusYears(2)) // 对找到的最大日期加两年
                .orElse(null); // 如果找不到最大日期，返回 null

        if (startDate == null || endDate == null) {
            return Collections.emptyList();
        }


        List<StaffCountInfoVO> lstResult = new ArrayList<>();
        for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {

            // 获取当前月份的花名册headId
            String headId = rosterHeadService.getLatestRosterHead(date).getId();

            // 计算业务外判人数
            long hkOutsourceCount = rosterDetailService.selectHKOutsourceCount(qo.getOrganizationCodeList(), headId);
            long businessOutsourceCount = hkOutsourceCount;

            // 组成 yyyy-MM的格式
            String yearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(date);

            // 判断 yearMonth 是否大于当前年月，并决定使用当前年月的值
            String effectiveYearMonth = yearMonth.compareTo(currentYearMonth) > 0 ? currentYearMonth : yearMonth;

            StaffCountInfoVO staffCountInfoVO = new StaffCountInfoVO();
            staffCountInfoVO.setYearMonth(yearMonth);
            Long dept = empTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "职能部门".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            Long leader = empTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "公司领导".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            staffCountInfoVO.setValue1(dept + leader);
            staffCountInfoVO.setValue2(empTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "子公司写字楼".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));

            Long inPaiCount  = siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "内派".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            Long coreHongPengCount  = siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "核心港聘".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            staffCountInfoVO.setValue3(inPaiCount + coreHongPengCount);
            staffCountInfoVO.setValue4(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "地盘基层".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            staffCountInfoVO.setValue5(siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "内地".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            Long selfEmployedDaily = siteEmpTypeList.stream().filter(codeNameCountVO -> effectiveYearMonth.equals(codeNameCountVO.getCode()) && "自有日薪".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L);
            staffCountInfoVO.setValue6(selfEmployedDaily + businessOutsourceCount);
            //TODO: 加上是否地盤的判断
            Map<String, List<String>> listMap = finalGrouping.get(effectiveYearMonth);
            Long deptStaffCount = StaffCountFullHelper.calcStaffCountByDateAndOrgCode(
                    staffCountFullList, date, Optional.ofNullable(listMap).map(map -> map.get("职能部门")).orElse(Collections.emptyList())
            );
            Long leaderStaffCount = StaffCountFullHelper.calcStaffCountByDateAndOrgCode(
                    staffCountFullList, date, Optional.ofNullable(listMap).map(map -> map.get("公司领导")).orElse(Collections.emptyList())
            );
            Long subsidiaryStaffCount = StaffCountFullHelper.calcStaffCountByDateAndOrgCode(
                    staffCountFullList, date, Optional.ofNullable(listMap).map(map -> map.get("子公司写字楼")).orElse(Collections.emptyList())
            );
            staffCountInfoVO.setValue1StaffCount(deptStaffCount + leaderStaffCount);
            staffCountInfoVO.setValue2StaffCount(subsidiaryStaffCount);

            // 将staffCountFullList中 listMap的职能部门、公司领导、子公司写字楼的编制数 排除再计算
            List<String> excludeList = new ArrayList<>();
            excludeList.addAll(Optional.ofNullable(listMap).map(map -> map.get("职能部门")).orElse(Collections.emptyList()));
            excludeList.addAll(Optional.ofNullable(listMap).map(map -> map.get("公司领导")).orElse(Collections.emptyList()));
            excludeList.addAll(Optional.ofNullable(listMap).map(map -> map.get("子公司写字楼")).orElse(Collections.emptyList()));

            long inter = StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.01");// 内派
            long core = StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.02");// 核心港聘
            staffCountInfoVO.setValue3StaffCount(inter + core);
            staffCountInfoVO.setValue4StaffCount(StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.03")); // 地盘基层
            staffCountInfoVO.setValue5StaffCount(StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.04")); // 内地中台
            long selfEmployedStaffCount = StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.05"); // 自有日薪
            long businessOutsourceStaffCount = StaffCountFullHelper.calcStaffCountByDateAndSubjectCodeAndOrgCode(staffCountFullList, date, excludeList, "BZ.06"); // 业务外判
            staffCountInfoVO.setValue6StaffCount(selfEmployedStaffCount + businessOutsourceStaffCount);

            // 合计
            staffCountInfoVO.setValue7(staffCountInfoVO.getValue1() + staffCountInfoVO.getValue2() + staffCountInfoVO.getValue3()
                    + staffCountInfoVO.getValue4() + staffCountInfoVO.getValue5() + staffCountInfoVO.getValue6());
            staffCountInfoVO.setValue7StaffCount(staffCountInfoVO.getValue1StaffCount() + staffCountInfoVO.getValue2StaffCount() + staffCountInfoVO.getValue3StaffCount()
                    + staffCountInfoVO.getValue4StaffCount() + staffCountInfoVO.getValue5StaffCount() + staffCountInfoVO.getValue6StaffCount());
            lstResult.add(staffCountInfoVO);
        }
        return lstResult;
    }

    public boolean isValidSalaryHKManager(String category, String staffType) {
        return "内派".equals(category) && "管理人员".equals(staffType)
                || "港聘".equals(category) && "管理人员".equals(staffType)
                || "内地".equals(category) && "管理人员".equals(staffType)
                || "内地".equals(category) && "中智外包".equals(staffType);
    }
}
