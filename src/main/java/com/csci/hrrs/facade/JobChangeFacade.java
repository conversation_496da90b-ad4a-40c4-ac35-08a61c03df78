package com.csci.hrrs.facade;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.model.ResultPage;
import com.csci.common.util.DateUtils;
import com.csci.hrrs.dto.JobChangeDTO;
import com.csci.hrrs.model.JobChange;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.qo.JobChangeQO;
import com.csci.hrrs.service.JobChangeService;
import com.csci.hrrs.service.ServiceHelper;
import com.csci.hrrs.util.AuthDataUtils;
import com.csci.hrrs.util.DateRangeDecider;
import com.csci.hrrs.util.ExcelReader;
import com.csci.hrrs.util.holder.JobChangeHolder;
import com.csci.hrrs.util.holder.LatestRosterHolder;
import com.csci.hrrs.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class JobChangeFacade {

    @Resource
    private JobChangeHolder jobChangeHolder;

    @Resource
    private LatestRosterHolder latestRosterHolder;

    @Resource
    private JobChangeService jobChangeService;

    public ResultPage<JobChangeDTO> findByQO(JobChangeQO jobChangeQO) {
        ServiceHelper.checkExist(jobChangeQO.getDate(), "日期不能为空");
        if (StringUtils.equals("经理及高级经理", jobChangeQO.getJobLevelName())) {
            jobChangeQO.setJobLevelName("(高级)经理");
        }

        List<String> subGroupCodeList = AuthDataUtils.getAuthEmployeeSubgroupList();
        if (CollectionUtils.isEmpty(subGroupCodeList)) {
            return new ResultPage<>(new ArrayList<>(), JobChangeDTO.class);
        }
        List<String> usersOrgCodeList = AuthDataUtils.getAuthOrgCodeList();
        if (CollectionUtils.isEmpty(usersOrgCodeList)) {
            return new ResultPage<>(new ArrayList<>(), JobChangeDTO.class);
        }

        // 先查询出时间范围内的jobChange数据，根据date和type
        StringDateRangeVO dateRangeVO = DateRangeDecider.convertTypeToDateRange(jobChangeQO.getType(), jobChangeQO.getDate());
        LocalDate fromDate = DateUtils.toLocalDateTime(dateRangeVO.getFromDate()).toLocalDate();
        LocalDate toDate = DateUtils.toLocalDateTime(dateRangeVO.getToDate()).toLocalDate();
        List<JobChange> jobChangeList = jobChangeHolder.get().parallelStream().filter(x -> {
            // x.getChangeDate().compareTo(dateRangeVO.getFromDate()) >= 0 && x.getChangeDate().compareTo(dateRangeVO.getToDate()) <= 0
            LocalDate beginDate = Optional.of(DateUtils.toLocalDateTime(x.getBegindate())).map(LocalDateTime::toLocalDate).orElse(null);
            if (beginDate == null) {
                return false;
            }
            // 如果changeDate 在 fromDate 和 toDate 之间，返回true
            return !beginDate.isBefore(fromDate) && !beginDate.isAfter(toDate);
        }).collect(Collectors.toList());
        // 再根据花名册中的人员信息以查询条件过滤出符合条件的数据
        jobChangeList = jobChangeList.parallelStream().filter(x -> {
            RosterDetail rosterDetail = latestRosterHolder.getAllDataList().stream().filter(y -> y.getPernr().equals(x.getPernr())).findFirst().orElse(null);
            if (rosterDetail == null) {
                return false;
            }
            boolean isMatch = usersOrgCodeList.contains(rosterDetail.getOrganizationCode()) && subGroupCodeList.contains(rosterDetail.getEmployeeSubgroup());
            isMatch = isMatch && MemoryLoadRosterFacade.isValidByCondition(jobChangeQO, rosterDetail);
            if (StringUtils.isNotBlank(jobChangeQO.getEventName())) {
                isMatch = isMatch && jobChangeQO.getEventName().equals(x.getChangetyperefined());
            }
            if (StringUtils.isNotBlank(jobChangeQO.getJobLevelName())) {
                isMatch = isMatch && jobChangeQO.getJobLevelName().equals(x.getJobLevelRefined());
            }
            return isMatch;
        }).sorted(Comparator.comparing(JobChange::getBegindate).reversed()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(jobChangeList)) {
            return new ResultPage<>(new ArrayList<>(), JobChangeDTO.class);
        }

        /* 根据 jobChangeQO 的curPage 和 pageSize 计算分页数据 */
        ResultPage<JobChangeDTO> resultPage = new ResultPage<>(null);
        resultPage.setTotal(jobChangeList.size());
        resultPage.setPages((int) (resultPage.getTotal() % jobChangeQO.getPageSize() == 0 ? resultPage.getTotal() / jobChangeQO.getPageSize() : resultPage.getTotal() / jobChangeQO.getPageSize() + 1));
        resultPage.setPageSize(jobChangeQO.getPageSize());
        if (jobChangeQO.getCurPage() > resultPage.getPages()) {
            resultPage.setPageNum(resultPage.getPages());
        } else {
            resultPage.setPageNum(jobChangeQO.getCurPage());
        }
        resultPage.setList(jobChangeList.stream().skip((long) (resultPage.getPageNum() - 1) * resultPage.getPageSize()).limit(resultPage.getPageSize()).map(JobChangeDTO::fromJobChange).collect(Collectors.toList()));
        resultPage.setSize(resultPage.getList().size());

        return resultPage;
    }

    public ResultPage<JobChangeDTO> findByQONew(JobChangeQO jobChangeQO) {
        ServiceHelper.checkExist(jobChangeQO.getDate(), "日期不能为空");

        if ("经理及高级经理".equals(jobChangeQO.getJobLevelName())) {
            jobChangeQO.setJobLevelName("(高级)经理");
        }

        List<String> subGroupCodeList = AuthDataUtils.getAuthEmployeeSubgroupList();
        if (CollectionUtils.isEmpty(subGroupCodeList)) {
            return new ResultPage<>(Collections.emptyList(), JobChangeDTO.class);
        }

        List<String> usersOrgCodeList = AuthDataUtils.getAuthOrgCodeList();
        if (CollectionUtils.isEmpty(usersOrgCodeList)) {
            return new ResultPage<>(Collections.emptyList(), JobChangeDTO.class);
        }

        // 获取日期范围
        StringDateRangeVO dateRangeVO = DateRangeDecider.convertTypeToDateRange(jobChangeQO.getType(), jobChangeQO.getDate());
        LocalDate fromDate = DateUtils.toLocalDateTime(dateRangeVO.getFromDate()).toLocalDate();
        LocalDate toDate = DateUtils.toLocalDateTime(dateRangeVO.getToDate()).toLocalDate();

        // 构建 pernr 到 RosterDetail 的映射
        Map<String, RosterDetail> pernrToRosterDetailMap = latestRosterHolder.getAllDataList().stream()
                .collect(Collectors.toMap(RosterDetail::getPernr, Function.identity(), (existing, replacement) -> existing));

        // 过滤并收集符合条件的 JobChangeDTO 列表
        List<JobChangeDTO> jobChangeDTOList = new ArrayList<>();
        for (JobChange jobChange : jobChangeHolder.get()) {
            // 过滤掉 "资料变更" 的记录
            if (StringUtils.equalsIgnoreCase("资料变更", jobChange.getMntxt())) {
                continue;
            }
            // 过滤日期范围
            LocalDate beginDate = Optional.ofNullable(jobChange.getBegindate())
                    .map(DateUtils::toLocalDateTime)
                    .map(LocalDateTime::toLocalDate)
                    .orElse(null);
            if (beginDate == null || beginDate.isBefore(fromDate) || beginDate.isAfter(toDate)) {
                continue;
            }

            // 获取对应的 RosterDetail
            RosterDetail rosterDetail = pernrToRosterDetailMap.get(jobChange.getPernr());
            if (rosterDetail == null) {
                continue;
            }

            // 检查组织代码和员工子组
            if (!usersOrgCodeList.contains(rosterDetail.getOrganizationCode())
                    || !subGroupCodeList.contains(rosterDetail.getEmployeeSubgroup())) {
                continue;
            }

            // 应用额外的条件
            if (!MemoryLoadRosterFacade.isValidByCondition(jobChangeQO, rosterDetail)) {
                continue;
            }

            if (StringUtils.isNotBlank(jobChangeQO.getEventName())
                    && !jobChangeQO.getEventName().equals(jobChange.getChangetyperefined())) {
                continue;
            }

            if (StringUtils.isNotBlank(jobChangeQO.getJobLevelName())
                    && !jobChangeQO.getJobLevelName().equals(jobChange.getJobLevelRefined())) {
                continue;
            }

            jobChangeDTOList.add(JobChangeDTO.fromJobChange(jobChange));
        }

        if (jobChangeDTOList.isEmpty()) {
            return new ResultPage<>(Collections.emptyList(), JobChangeDTO.class);
        }

        // 排序并分页
        jobChangeDTOList.sort(Comparator.comparing(JobChangeDTO::getEventDate).reversed());

        // 分页处理
        ResultPage<JobChangeDTO> resultPage = new ResultPage<>(null);
        resultPage.setTotal(jobChangeDTOList.size());
        resultPage.setPages((int) (resultPage.getTotal() % jobChangeQO.getPageSize() == 0 ? resultPage.getTotal() / jobChangeQO.getPageSize() : resultPage.getTotal() / jobChangeQO.getPageSize() + 1));
        resultPage.setPageSize(jobChangeQO.getPageSize());
        if (jobChangeQO.getCurPage() > resultPage.getPages()) {
            resultPage.setPageNum(resultPage.getPages());
        } else {
            resultPage.setPageNum(jobChangeQO.getCurPage());
        }
        resultPage.setList(jobChangeDTOList.stream().skip((long) (resultPage.getPageNum() - 1) * resultPage.getPageSize()).limit(resultPage.getPageSize()).collect(Collectors.toList()));
        resultPage.setSize(resultPage.getList().size());

        return resultPage;
    }

    /**
     * 查询日期不连续的工作变动记录
     * <p>
     * 按pernr分组，如果同一个员工相近的两条变动记录，
     * 他们的前一段经历的enddate和后一段变动经历的begindate不是连续的，就将这两条记录保留
     *
     * @return 所有不连续的JobChange记录列表
     */
    public List<JobChange> findDiscontinuousJobChanges() {
        // 获取所有有效的JobChange记录
        List<JobChange> allJobChanges = jobChangeHolder.get();

        // 按员工工号分组
        Map<String, List<JobChange>> jobChangesByPernr = allJobChanges.stream()
                .filter(jobChange -> StringUtils.isNotBlank(jobChange.getPernr())
                        && StringUtils.isNotBlank(jobChange.getBegindate())
                        && StringUtils.isNotBlank(jobChange.getEnddate()))
                .collect(Collectors.groupingBy(JobChange::getPernr));

        // 存储所有不连续的记录
        List<JobChange> discontinuousChanges = new ArrayList<>();

        // 遍历每个员工的变动记录
        for (Map.Entry<String, List<JobChange>> entry : jobChangesByPernr.entrySet()) {
            List<JobChange> employeeChanges = entry.getValue();

            // 对记录按begindate排序
            employeeChanges.sort(Comparator.comparing(JobChange::getBegindate));

            // 检查相邻记录的日期是否连续
            for (int i = 0; i < employeeChanges.size() - 1; i++) {
                JobChange currentChange = employeeChanges.get(i);
                JobChange nextChange = employeeChanges.get(i + 1);

                // 判断当前记录的结束日期与下一条记录的开始日期是否连续
                // 日期格式通常为"yyyyMMdd"
                String currentEndDate = currentChange.getEnddate();
                String nextBeginDate = nextChange.getBegindate();

                // 转换为LocalDate进行比较
                LocalDate endDate = DateUtils.toLocalDateTime(currentEndDate).toLocalDate();
                LocalDate beginDate = DateUtils.toLocalDateTime(nextBeginDate).toLocalDate();

                // 如果endDate的后一天不等于beginDate，则这两条记录之间存在日期不连续
                if (endDate.plusDays(1).compareTo(beginDate) != 0) {
                    // 记录这两条不连续的记录
                    discontinuousChanges.add(currentChange);
                    discontinuousChanges.add(nextChange);
                }
            }
        }

        return discontinuousChanges;
    }

    /**
     * 查询职务信息不一致的变动记录
     * <p>
     * 比对变动记录中的职务(zw)、职务层级(zwcj)、职级(zzZj)字段与Excel中的数据是否一致
     *
     * @return 职务信息不一致的变动记录列表
     */
    public List<InconsistentJobChangeVO> findInconsistentPositionRecords() {
        try {
            // 获取所有职务变动信息
            List<JobChangeFieldVO> jobChangeFields = jobChangeService.findJobChangeFields();
            if (CollectionUtils.isEmpty(jobChangeFields)) {
                return Collections.emptyList();
            }

            // 从Excel文件中加载职务层级职级对应关系
            List<PositionLevelSeriesVO> positionLevelSeries = getPositionLevelSeriesFromExcel();
            if (CollectionUtils.isEmpty(positionLevelSeries)) {
                return Collections.emptyList();
            }

            // 构建职务名称到对应配置的映射
            Map<String, PositionLevelSeriesVO> positionMap = positionLevelSeries.stream()
                    .collect(Collectors.toMap(PositionLevelSeriesVO::getPositionName, Function.identity(),
                            (v1, v2) -> v1));

            // 存储不一致的记录
            List<InconsistentJobChangeVO> inconsistentRecords = new ArrayList<>();

            // 遍历所有变动记录，检查职务信息一致性
            for (JobChangeFieldVO field : jobChangeFields) {
                // 跳过职务为空的记录
                if (StringUtils.isBlank(field.getZw())) {
                    continue;
                }

                // 根据职务查找对应的配置
                PositionLevelSeriesVO config = positionMap.get(field.getZw());
                if (config == null) {
                    // 如果找不到对应配置，说明职务不一致
                    InconsistentJobChangeVO vo = createInconsistentRecord(field, false, false, false, "", "");
                    inconsistentRecords.add(vo);
                    continue;
                }

                // 检查职务层级是否一致
                boolean isLevelMatch = checkLevelMatch(field.getZwcj(), config.getLevelCode());

                // 检查职级是否一致
                boolean isRankMatch = checkRankMatch(field.getZzZj(), config.getRankRange());

                // 如果职务层级或职级不一致，添加到结果列表中
                if (!isLevelMatch || !isRankMatch) {
                    InconsistentJobChangeVO vo = createInconsistentRecord(
                            field, true, isLevelMatch, isRankMatch,
                            config.getLevelCode(), config.getRankRange());
                    inconsistentRecords.add(vo);
                }
            }

            return inconsistentRecords;
        } catch (Exception e) {
            log.error("查询职务信息不一致记录异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 创建不一致记录对象
     */
    private InconsistentJobChangeVO createInconsistentRecord(
            JobChangeFieldVO field, boolean isPositionMatch, boolean isLevelMatch,
            boolean isRankMatch, String excelLevelCode, String excelRankRange) {

        InconsistentJobChangeVO vo = new InconsistentJobChangeVO();
        vo.setPernr(field.getPernr());
        vo.setBegda(field.getBegda());
        vo.setEndda(field.getEndda());
        vo.setZw(field.getZw());
        vo.setZwcj(field.getZwcj());
        vo.setZzZj(field.getZzZj());
        vo.setIsPositionMatch(isPositionMatch);
        vo.setIsLevelMatch(isLevelMatch);
        vo.setIsRankMatch(isRankMatch);
        vo.setExcelLevelCode(excelLevelCode);
        vo.setExcelRankRange(excelRankRange);

        return vo;
    }

    /**
     * 检查职务层级是否匹配
     * 职务层级可能包含多个值，以斜杠分隔，如 D1/M11
     */
    private boolean checkLevelMatch(String jobChangeLevel, String excelLevel) {
        // 如果都为空，判定为匹配
        if (StringUtils.isBlank(jobChangeLevel) && StringUtils.isBlank(excelLevel)) {
            return true;
        }

        if (StringUtils.isBlank(jobChangeLevel) || StringUtils.isBlank(excelLevel)) {
            return false;
        }

        // 将变动记录中的职务层级按斜杠分割
        String[] levelCodes = jobChangeLevel.split("/");
        for (String levelCode : levelCodes) {
            if (StringUtils.equals(levelCode.trim(), excelLevel.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查职级是否匹配
     * Excel中的职级可能是一个范围，如 26/27/28/29/30/31/32
     * 变动记录中的职级格式为 G26，需要提取出数字部分进行比对
     */
    private boolean checkRankMatch(String jobChangeRank, String excelRanks) {
        // 如果都为空，判定为匹配
        if (StringUtils.isBlank(jobChangeRank) && StringUtils.isBlank(excelRanks)) {
            return true;
        }

        if (StringUtils.isBlank(jobChangeRank) || StringUtils.isBlank(excelRanks)) {
            return false;
        }

        // 从变动记录中的职级提取数字部分
        String rankNumber = extractRankNumber(jobChangeRank);
        if (StringUtils.isBlank(rankNumber)) {
            return false;
        }

        // 将Excel中的职级范围按斜杠分割
        String[] rankRange = excelRanks.split("/");
        for (String rank : rankRange) {
            if (StringUtils.equals(rankNumber.trim(), rank.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从职级字符串中提取数字部分，要处理有0在数字前的情况
     * 例如：
     * G26 -> 26
     * A01 -> 1
     * G08 -> 8
     * S15 -> 15
     *
     * @param rank 职级字符串
     * @return 提取出的数字部分
     */
    private String extractRankNumber(String rank) {
        if (StringUtils.isBlank(rank)) {
            return "";
        }

        // 使用正则表达式提取数字部分，处理G26和A01等情况
        Pattern pattern = Pattern.compile("[A-Za-z]*(\\d+)");
        Matcher matcher = pattern.matcher(rank);
        if (matcher.find()) {
            String numberPart = matcher.group(1);
            // 去除前导零
            return Integer.parseInt(numberPart) + "";
        }

        return "";
    }

    /**
     * 从资源目录获取职务层级系列Excel文件
     *
     * @return 职务层级系列列表
     */
    private List<PositionLevelSeriesVO> getPositionLevelSeriesFromExcel() {
        // 尝试从多个可能的位置读取文件
        List<String> possiblePaths = Arrays.asList(
                "position/position-level-series.xlsx",
                "classpath:position/position-level-series.xlsx",
                "/position/position-level-series.xlsx",
                "src/main/resources/position/position-level-series.xlsx");

        List<PositionLevelSeriesVO> result = Collections.emptyList();

        for (String path : possiblePaths) {
            try {
                List<PositionLevelSeriesVO> data = ExcelReader.readPositionLevelSeries(path);
                if (!CollectionUtils.isEmpty(data)) {
                    log.info("成功从路径 {} 读取职务层级系列数据", path);
                    result = data;
                    break;
                }
            } catch (Exception e) {
                log.warn("从路径 {} 读取职务层级系列数据失败: {}", path, e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            log.error("无法读取职务层级系列数据，请检查Excel文件是否存在及格式是否正确");
        } else {
            log.info("成功读取职务层级系列数据，共{}条记录", result.size());
        }

        return result;
    }

    public void loadSearchBaseData(JobChangeSearchVO jobChangeSearchVO) {
        // 参数校验
        if (jobChangeSearchVO == null) {
            log.error("jobChangeSearchVO参数不能为空");
            return;
        }

        if (jobChangeService == null) {
            log.error("jobChangeService未正确注入");
            jobChangeSearchVO.setDeptName(Collections.emptyList());
            jobChangeSearchVO.setSubDepartName(Collections.emptyList());
            jobChangeSearchVO.setDeptNameB(Collections.emptyList());
            jobChangeSearchVO.setSubDepartNameB(Collections.emptyList());
            return;
        }

        try {
            // deptName
            LambdaQueryWrapper<JobChange> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(JobChange::getDepartment);
            queryWrapper.groupBy(JobChange::getDepartment);
            List<String> deptNames = jobChangeService.list(queryWrapper).stream()
                    .filter(Objects::nonNull) // 过滤掉null对象
                    .map(JobChange::getDepartment)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
            jobChangeSearchVO.setDeptName(deptNames);

            // subDepartName
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(JobChange::getProject);
            queryWrapper.groupBy(JobChange::getProject);
            List<String> subDepartNames = jobChangeService.list(queryWrapper).stream()
                    .filter(Objects::nonNull) // 过滤掉null对象
                    .map(JobChange::getProject)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
            jobChangeSearchVO.setSubDepartName(subDepartNames);

            // deptNameB
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(JobChange::getDepartmentB);
            queryWrapper.groupBy(JobChange::getDepartmentB);
            List<String> deptNameBs = jobChangeService.list(queryWrapper).stream()
                    .filter(Objects::nonNull) // 过滤掉null对象
                    .map(JobChange::getDepartmentB)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
            jobChangeSearchVO.setDeptNameB(deptNameBs);

            // subDepartNameB
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(JobChange::getProjectB);
            queryWrapper.groupBy(JobChange::getProjectB);
            List<String> subDepartNameBs = jobChangeService.list(queryWrapper).stream()
                    .filter(Objects::nonNull) // 过滤掉null对象
                    .map(JobChange::getProjectB)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
            jobChangeSearchVO.setSubDepartNameB(subDepartNameBs);
        } catch (Exception e) {
            log.error("加载搜索基础数据失败", e);
            // 设置空列表作为默认值
            jobChangeSearchVO.setDeptName(Collections.emptyList());
            jobChangeSearchVO.setSubDepartName(Collections.emptyList());
            jobChangeSearchVO.setDeptNameB(Collections.emptyList());
            jobChangeSearchVO.setSubDepartNameB(Collections.emptyList());
        }
    }
}
