package com.csci.hrrs.facade;

import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.service.TagService;
import com.csci.hrrs.util.TalentTagUtils;
import com.csci.hrrs.util.holder.LatestRosterHolder;
import com.csci.hrrs.vo.TagNodeVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.csci.hrrs.util.TalentTagUtils.addChild;
import static com.csci.hrrs.util.TalentTagUtils.initTagNode;

@Component
@LogMethod
public class TagFacade {

    @Resource
    private TagService tagService;

    @Resource
    private LatestRosterHolder latestRosterHolder;

    public TagNodeVO getTalentRootTag() {

        TagNodeVO rootTag = getRootCondition();

        addChild(rootTag, getBasicCondition(rootTag.getId()));
        addChild(rootTag, tagService.getTagTree());
        // todo 添加能力大类
        // todo 添加经验大类
        // todo 添加绩效大类

        return rootTag;

    }

    public List<TagNodeVO> listByParentId(String parentId) {
        // In the absence of structured data, manually construct data by hard coding
        /*if ("root".equals(parentId)) {
            List<TagNodeVO> tagNodeVOS = new ArrayList<>();
            tagNodeVOS.add(initTagNode("basicCondition", "basicCondition", "基本情况", parentId, "dir"));
            tagNodeVOS.add(tagService.getRootTag());
            return tagNodeVOS;
        } else {
            return tagService.listByParentId(parentId);
        }*/
        switch (parentId) {
            case "root" -> {
                List<TagNodeVO> tagNodeVOS = new ArrayList<>();
                tagNodeVOS.add(initTagNode("basicCondition", "basicCondition", "基本情况", parentId, "dir"));
                TagNodeVO expRootTag = tagService.getRootTag();
                expRootTag.setParentId(parentId);
                expRootTag.setType("dir");
                tagNodeVOS.add(expRootTag);
                return tagNodeVOS;
            }
            case "basicCondition" -> {
                return listBaseCondition();
            }
            case "age" -> {
                return TalentTagUtils.getDefaultAgeTagScore("age").getChildren();
            }
            case "maritalStatus" -> {
                return TalentTagUtils.getDefaultMarriageTagScore("maritalStatus", latestRosterHolder.get()).getChildren();
            }
            case "gender" -> {
                return TalentTagUtils.getDefaultGenderScore("gender").getChildren();
            }
            case "height" -> {
                return TalentTagUtils.getDefaultHeightTag("height").getChildren();
            }
            case "ethnicity" -> {
                return TalentTagUtils.getBaseEthnicityTag("ethnicity", latestRosterHolder.get()).getChildren();
            }
            case "politicalStatus" -> {
                return TalentTagUtils.getPoliticalStatusTag("politicalStatus", latestRosterHolder.get()).getChildren();
            }
            case "hometown" -> {
                return TalentTagUtils.getHometownTag("hometown", latestRosterHolder.get()).getChildren();
            }
            case "birthplace" -> {
                return TalentTagUtils.getBirthplaceTag("birthplace", latestRosterHolder.get()).getChildren();
            }
            case "parentsLocation" -> {
                return TalentTagUtils.getParentsLocationTag("parentsLocation", latestRosterHolder.get()).getChildren();
            }
            case "spouseLocation" -> {
                return TalentTagUtils.getSpouseLocationTag("spouseLocation", latestRosterHolder.get()).getChildren();
            }
            case "education" -> {
                return TalentTagUtils.getEducationTag("education", latestRosterHolder.get()).getChildren();
            }
            case "schoolAll" -> {
                return TalentTagUtils.getSchoolTag("schoolAll", latestRosterHolder.get()).getChildren();
            }
            case "sourceTag" -> {
                return TalentTagUtils.getTalentSourceTag("sourceTag", latestRosterHolder.get()).getChildren();
            }
            /*case "jobTitle" -> {
                return TalentTagUtils.getJobTitleTag("jobTitle", latestRosterHolder.get()).getChildren();
            }*/
            case "nationality" -> {
                return TalentTagUtils.getNationalityTag("nationality", latestRosterHolder.get()).getChildren();
            }
            case "cohlSeniority" -> {
                return TalentTagUtils.getSeniorityTag("cohlSeniority", latestRosterHolder.get()).getChildren();
            }
            case "workSeniority" -> {
                return TalentTagUtils.getWorkSeniorityTag("workSeniority", latestRosterHolder.get()).getChildren();
            }
            case "employeeCategory" -> {
                return TalentTagUtils.getEmployeeCategoryTag("employeeCategory", latestRosterHolder.get()).getChildren();
            }
            case "hireTypeQuery" -> {
                return TalentTagUtils.getHireTypeTag("hireTypeQuery", latestRosterHolder.get()).getChildren();
            }
            case "positionType" -> {
                return TalentTagUtils.getPositionTypeTag("positionType", latestRosterHolder.get()).getChildren();
            }
            case "positionLevelCode" -> {
                return TalentTagUtils.getPositionLevelTag("positionLevelCode", latestRosterHolder.get()).getChildren();
            }
            case "jobGrade" -> {
                return TalentTagUtils.getJobGradeTag("jobGrade", latestRosterHolder.get()).getChildren();
            }
            case "yearsInCurrentJobLevel" -> {
                return TalentTagUtils.getYearsInCurrentJobLevelTag("yearsInCurrentJobLevel").getChildren();
            }
            case "yearsInCurrentRank" -> {
                return TalentTagUtils.getYearsInCurrentRankTag("yearsInCurrentRank").getChildren();
            }
            case "yearsInCurrentPosition" -> {
                return TalentTagUtils.getYearsInCurrentPositionTag("yearsInCurrentPosition").getChildren();
            }
            case "major" -> {
                return TalentTagUtils.getMajorTag("major", latestRosterHolder.get()).getChildren();
            }
            case "speciality" -> {
                return TalentTagUtils.getSpecialityTag("speciality", latestRosterHolder.get()).getChildren();
            }
            case "interestsHobbies" -> {
                return TalentTagUtils.getInterestsHobbiesTag("interestsHobbies", latestRosterHolder.get()).getChildren();
            }

            default -> {
                return tagService.listByParentId(parentId);
            }
        }
    }

    private TagNodeVO getRootCondition() {
        TagNodeVO root = new TagNodeVO();
        root.setParentId(null);
        root.setId("root");
        root.setCode("root");
        root.setName("root");
        // 设置为了dir，前端就会设置其为无法记分
        root.setType("dir");
        root.setChildren(new ArrayList<>());
        return root;
    }

    private List<TagNodeVO> listBaseCondition() {
        List<TagNodeVO> tagNodeVOS = new ArrayList<>();
        tagNodeVOS.add(initTagNode("age", "age", "年龄", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("maritalStatus", "maritalStatus", "婚姻", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("gender", "gender", "性别", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("height", "height", "身高", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("ethnicity", "ethnicity", "民族", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("politicalStatus", "politicalStatus", "政治面貌", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("hometown", "hometown", "籍贯", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("birthplace", "birthplace", "出生地", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("parentsLocation", "parentsLocation", "父母居住地", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("spouseLocation", "spouseLocation", "配偶居住地", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("education", "education", "学历", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("graduateSchool", "graduateSchool", "毕业学校", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("talentSource", "talentSource", "来源", "basicCondition", "tag"));
        // tagNodeVOS.add(initTagNode("jobTitle", "jobTitle", "职称信息", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("nationality", "nationality", "国籍", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("cohlSeniority", "cohlSeniority", "中海司龄", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("workSeniority", "workSeniority", "工龄", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("employeeCategory", "employeeCategory", "员工类别", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("hireTypeQuery", "hireTypeQuery", "聘用类型", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("positionType", "positionType", "职务类型", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("positionLevelCode", "positionLevelCode", "职务层级", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("jobGrade", "jobGrade", "职级", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("yearsInCurrentJobLevel", "yearsInCurrentJobLevel", "层级年资", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("yearsInCurrentRank", "yearsInCurrentRank", "职级年资", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("yearsInCurrentPosition", "yearsInCurrentPosition", "岗位年资", "basicCondition", "range"));
        tagNodeVOS.add(initTagNode("major", "major", "学习专业", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("speciality", "speciality", "特长", "basicCondition", "tag"));
        tagNodeVOS.add(initTagNode("interestsHobbies", "interestsHobbies", "兴趣爱好", "basicCondition", "tag"));

        return tagNodeVOS;
    }

    /**
     * 初始化基础条件
     *
     * @param parentId
     * @return
     */
    private TagNodeVO getBasicCondition(String parentId) {
        TagNodeVO basicCondition = new TagNodeVO();
        basicCondition.setChildren(new ArrayList<>());
        basicCondition.setId("basicCondition");
        basicCondition.setParentId(parentId);
        basicCondition.setCode("basicCondition");
        basicCondition.setName("基本情况");
        basicCondition.setType("dir");

        // 年龄
        addChild(basicCondition, TalentTagUtils.getDefaultAgeTagScore(basicCondition.getId()));
        // 婚姻
        addChild(basicCondition, TalentTagUtils.getDefaultMarriageTagScore(basicCondition.getId(), latestRosterHolder.get()));
        // 性别
        addChild(basicCondition, TalentTagUtils.getDefaultGenderScore(basicCondition.getId()));
        // 身高
        addChild(basicCondition, TalentTagUtils.getDefaultHeightTag(basicCondition.getId()));
        // 民族
        addChild(basicCondition, TalentTagUtils.getBaseEthnicityTag(basicCondition.getId(), latestRosterHolder.get()));
        // 政治面貌
        addChild(basicCondition, TalentTagUtils.getPoliticalStatusTag(basicCondition.getId(), latestRosterHolder.get()));
        // 籍贯
        addChild(basicCondition, TalentTagUtils.getHometownTag(basicCondition.getId(), latestRosterHolder.get()));
        // 出生地
        addChild(basicCondition, TalentTagUtils.getBirthplaceTag(basicCondition.getId(), latestRosterHolder.get()));
        // 父母居住地
        addChild(basicCondition, TalentTagUtils.getParentsLocationTag(basicCondition.getId(), latestRosterHolder.get()));
        // 配偶居住地
        addChild(basicCondition, TalentTagUtils.getSpouseLocationTag(basicCondition.getId(), latestRosterHolder.get()));
        // 学历
        addChild(basicCondition, TalentTagUtils.getEducationTag(basicCondition.getId(), latestRosterHolder.get()));
        // 毕业学校
        addChild(basicCondition, TalentTagUtils.getGraduateSchoolTag(basicCondition.getId()));
        // 来源
        addChild(basicCondition, TalentTagUtils.getTalentSourceTag(basicCondition.getId(), latestRosterHolder.get()));
        // 职称信息
        addChild(basicCondition, TalentTagUtils.getJobTitleTag(basicCondition.getId(), latestRosterHolder.get()));
        // 国籍
        addChild(basicCondition, TalentTagUtils.getNationalityTag(basicCondition.getId(), latestRosterHolder.get()));
        // 中海司龄:cohlSeniority
        addChild(basicCondition, TalentTagUtils.getSeniorityTag(basicCondition.getId(), latestRosterHolder.get()));
        // 工龄:workSeniority
        addChild(basicCondition, TalentTagUtils.getWorkSeniorityTag(basicCondition.getId(), latestRosterHolder.get()));
        // 员工类别:employeeCategory
        addChild(basicCondition, TalentTagUtils.getEmployeeCategoryTag(basicCondition.getId(), latestRosterHolder.get()));
        // 聘用类型:hireType
        addChild(basicCondition, TalentTagUtils.getHireTypeTag(basicCondition.getId(), latestRosterHolder.get()));
        // 职务类型：职位类型
        addChild(basicCondition, TalentTagUtils.getPositionTypeTag(basicCondition.getId(), latestRosterHolder.get()));
        // 职务层级: positionLevel
        addChild(basicCondition, TalentTagUtils.getPositionLevelTag(basicCondition.getId(), latestRosterHolder.get()));
        // 职级: jobGrade
        addChild(basicCondition, TalentTagUtils.getJobGradeTag(basicCondition.getId(), latestRosterHolder.get()));
        // 层级年资(任现职务层级时间)：yearsInCurrentJobLevel
        addChild(basicCondition, TalentTagUtils.getYearsInCurrentJobLevelTag(basicCondition.getId()));
        // 职级年资(任现职级时间): yearsInCurrentRank
        addChild(basicCondition, TalentTagUtils.getYearsInCurrentRankTag(basicCondition.getId()));
        // 岗位年资(任现岗位时间): yearsInCurrentPosition
        addChild(basicCondition, TalentTagUtils.getYearsInCurrentPositionTag(basicCondition.getId()));
        // 学习专业
        addChild(basicCondition, TalentTagUtils.getMajorTag(basicCondition.getId(), latestRosterHolder.get()));
        // 特长
        addChild(basicCondition, TalentTagUtils.getSpecialityTag(basicCondition.getId(), latestRosterHolder.get()));
        // 兴趣爱好: interestsHobbies
        addChild(basicCondition, TalentTagUtils.getInterestsHobbiesTag(basicCondition.getId(), latestRosterHolder.get()));

        return basicCondition;
    }

}
