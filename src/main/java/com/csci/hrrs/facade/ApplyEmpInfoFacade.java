package com.csci.hrrs.facade;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.DateUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.apply.model.*;
import com.csci.hrrs.apply.service.*;
import com.csci.hrrs.apply.vo.*;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.feishu.FeishuCardMessage;
import com.csci.hrrs.feishu.FeishuService;
import com.csci.hrrs.model.*;
import com.csci.hrrs.service.*;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.vo.EducationExperienceVO;
import com.csci.hrrs.vo.RosterDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.csci.hrrs.util.RedisLockUtil;

@Component
@LogMethod
@Slf4j
public class ApplyEmpInfoFacade {

    @Value("${feishu.eids.appId}")
    private String appCodeEIDS;

    @Value("${feishu.eids.appSecret}")
    private String appSecretEIDS;

    @Value("${spring.profiles.active:}")
    private String profile;

    @Resource
    private ApplyEmpInfoService applyEmpInfoService;

    @Resource
    private FactRosterService factRosterService;

    @Resource
    private FeishuService feishuService;

    @Resource
    private RosterDetailService rosterDetailService;
    @Autowired
    private ApplyEmpEducationService applyEmpEducationService;
    @Autowired
    private ApplyEmpProTitleService applyEmpProTitleService;
    @Autowired
    private UserService userService;
    @Autowired
    private ApplyEmpOtherQualificationsService applyEmpOtherQualificationsService;
    @Autowired
    private ApplyEmpRewardPunishmentService applyEmpRewardPunishmentService;
    @Autowired
    private ApplyEmpFamilyMemberService applyEmpFamilyMemberService;
    @Autowired
    private ApplyEmpKinship3311Service applyEmpKinship3311Service;
    @Autowired
    private ApplyEmpAbroadService applyEmpAbroadService;
    @Autowired
    private ApplyEmpRelativeEnterpriseService applyEmpRelativeEnterpriseService;
    @Autowired
    private ApplyEmpSocialPtjobService applyEmpSocialPtjobService;

    @Resource
    private TitleQualificationService titleQualificationService;

    @Resource
    private ApplyEmpProQualificationService applyEmpProQualificationService;

    @Resource
    private RewardPunishService rewardPunishService;
    @Autowired
    private FamilyMemberService familyMemberService;

    @Resource
    private ApplyEmpSignatureService applyEmpSignatureService;

    @Resource
    private SendMessageFacade sendMessageFacade;

    @Autowired
    private MessageSendLogService messageSendLogService;

    private static ApplyEmpInfoVO convertMain(RosterDetailVO rosterDetailVO) {
        ApplyEmpInfoVO applyEmpInfoVO = new ApplyEmpInfoVO();
        // applyEmpInfoVO.setId();
        applyEmpInfoVO.setStaffNo(rosterDetailVO.getPernr());
        applyEmpInfoVO.setChineseName(rosterDetailVO.getNameSimplified());
        applyEmpInfoVO.setEnglishName(rosterDetailVO.getEngNameOrigin());
        applyEmpInfoVO.setTraditionalName(rosterDetailVO.getName());
        applyEmpInfoVO.setPlatform(rosterDetailVO.getPlatform());
        applyEmpInfoVO.setSubsidiary(rosterDetailVO.getSubsidiary());
        applyEmpInfoVO.setSubProject(rosterDetailVO.getSubProject());
        applyEmpInfoVO.setPosition(rosterDetailVO.getPositionLevel());
        applyEmpInfoVO.setPositionLevel(rosterDetailVO.getPositionLevelCode());

        applyEmpInfoVO.setStartCurrentPosDate(rosterDetailVO.getDurationCurrentPos());
        applyEmpInfoVO.setDurationCurrentPos(rosterDetailVO.getYearsInCurrentPosition());
        applyEmpInfoVO.setStartCurrentJobDate(rosterDetailVO.getDurationCurrentLevel());
        applyEmpInfoVO.setDurationCurrentJob(rosterDetailVO.getYearsInCurrentJobLevel());
        applyEmpInfoVO.setStartWorkDate(rosterDetailVO.getStartWorkTime());
        applyEmpInfoVO.setJoinCohlDate(rosterDetailVO.getJoinCohlTime());
        applyEmpInfoVO.setFirstArriveHkDate(rosterDetailVO.getStartOverseasTime());
        applyEmpInfoVO.setLatestArriveHkDate(rosterDetailVO.getLatestArriveHkDate());
        applyEmpInfoVO.setEthnicity(rosterDetailVO.getEthnicity());
        applyEmpInfoVO.setBirthdate(rosterDetailVO.getBirthdate());
        applyEmpInfoVO.setHkPhoneNumber(rosterDetailVO.getOverseasPhone());
        applyEmpInfoVO.setMainlandPhoneNumber(rosterDetailVO.getDomesticPhone());
        applyEmpInfoVO.setEmail(rosterDetailVO.getEmail());
        // applyEmpInfoVO.setPartyMember();
        applyEmpInfoVO.setBirthPlace(rosterDetailVO.getBirthPlace());
        applyEmpInfoVO.setHometown(rosterDetailVO.getHometown());
        applyEmpInfoVO.setBloodType(rosterDetailVO.getBloodType());
        applyEmpInfoVO.setMainlandIdCard(rosterDetailVO.getMainlandIdCard());
        applyEmpInfoVO.setForeignIdCard(rosterDetailVO.getForeignIdCard());
        applyEmpInfoVO.setHkMoPassport(rosterDetailVO.getHkMoPassport());
        applyEmpInfoVO.setEndorExpirationDate(
                Optional.ofNullable(DateUtils.toLocalDateTime(rosterDetailVO.getHkMoEndExpiry()))
                        .map(LocalDateTime::toLocalDate).orElse(null));
        applyEmpInfoVO.setCertExpirationDate(rosterDetailVO.getHkMoCertExpiryDate());
        applyEmpInfoVO.setPersonalEmail(rosterDetailVO.getPersonalEmail());
        applyEmpInfoVO.setMainlandAddress(rosterDetailVO.getDomesticAddress());
        applyEmpInfoVO.setHkAddress(rosterDetailVO.getOverseasAddress());
        applyEmpInfoVO.setLocationOfResidence(rosterDetailVO.getResLoc());
        applyEmpInfoVO.setLocationOfArchive(rosterDetailVO.getArchiveCompany());
        // applyEmpInfoVO.setSocialSecurityLocation();
        applyEmpInfoVO.setSourceOrgName(rosterDetailVO.getSource());
        applyEmpInfoVO.setMaritalStatus(rosterDetailVO.getMaritalStatus());
        applyEmpInfoVO.setEmploymentMode(rosterDetailVO.getEmploymentMode());
        applyEmpInfoVO.setAge(Optional.ofNullable(rosterDetailVO.getAge()).map(BigDecimal::new).orElse(null));
        applyEmpInfoVO.setWorkSeniority(rosterDetailVO.getWorkSeniority());
        applyEmpInfoVO.setCohlSeniority(rosterDetailVO.getCohlSeniority());
        applyEmpInfoVO.setHobbies(rosterDetailVO.getInterestsHobbies());
        applyEmpInfoVO.setWorkPlace(rosterDetailVO.getWorkPlace());
        applyEmpInfoVO.setHeight(rosterDetailVO.getHeight());

        applyEmpInfoVO.setBankAccount(rosterDetailVO.getBankAccount());
        applyEmpInfoVO.setPartyRegularDate(rosterDetailVO.getPartyRegularDate());
        applyEmpInfoVO.setJoinPartyTime(rosterDetailVO.getPartyJoiningDate());

        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        Organization org = organizationService.getByCode(rosterDetailVO.getOrganizationCode());
        applyEmpInfoVO.setWorkLocationType(Boolean.TRUE.equals(org.getIsProject()) ? "地盘" : "写字楼");

        return applyEmpInfoVO;
    }

    private static void checkStatusForUpdate(ApplyEmpInfo applyEmpInfo) {
        if (StringUtils.equalsIgnoreCase(HrrsConsts.ApplyEmpInfoStatus.submitted, applyEmpInfo.getStatus())) {
            throw new ServiceException("员工信息已提交，不可修改");
        }
    }

    public ApplyEmpInfoVO convertFromRosterDetailVO(RosterDetailVO rosterDetailVO) {
        ApplyEmpInfoVO applyEmpInfoVO = convertMain(rosterDetailVO);
        applyEmpInfoVO.setApplyEmpEducationList(convertEducationList(rosterDetailVO.getEducationExperiences()));
        applyEmpInfoVO.setApplyEmpProTitleList(convertProTitle(rosterDetailVO));
        applyEmpInfoVO.setApplyEmpOtherQualificationsList(getOtherQualByStaffNo(rosterDetailVO.getPernr()));
        applyEmpInfoVO.setApplyEmpRewardPunishmentList(getRewardPunishmentByStaffNo(rosterDetailVO.getPernr()));
        applyEmpInfoVO.setApplyEmpFamilyMemberList(getFamilyMemberByStaffNo(rosterDetailVO));
        applyEmpInfoVO.setApplyEmpKinship3311List(getEmpKinship3311ByStaffNo(rosterDetailVO));
        applyEmpInfoVO.setApplyEmpProQualificationList(convertProQualification(rosterDetailVO));
        // applyEmpInfoVO.setApplyEmpAbroadList();
        // applyEmpInfoVO.setApplyEmpRelativeEnterpriseList();
        // applyEmpInfoVO.setApplyEmpSocialPtjobList();
        return applyEmpInfoVO;
    }

    private List<ApplyEmpProQualificationVO> convertProQualification(RosterDetailVO rosterDetailVO) {
        List<TitleQualification> lstTitleQualification = titleQualificationService
                .listByPernrAndType(rosterDetailVO.getPernr(), TitleQualificationService.TQType.PROF_QUAL);
        List<ApplyEmpProQualificationVO> qualificationVOList = new ArrayList<>();
        for (TitleQualification titleQualification : lstTitleQualification) {
            ApplyEmpProQualificationVO vo = ApplyEmpProQualificationVO.fromTitleQualification(titleQualification);
            qualificationVOList.add(vo);
        }
        return qualificationVOList;
    }

    /**
     * 员工家庭成员信息
     * not in (N'咨询人', N'其他')
     *
     * @param rosterDetailVO
     * @return
     */
    private List<ApplyEmpFamilyMemberVO> getFamilyMemberByStaffNo(RosterDetailVO rosterDetailVO) {
        LambdaQueryWrapper<FamilyMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FamilyMember::getPernr, rosterDetailVO.getPernr()).notIn(FamilyMember::getAppellation, "咨询人",
                "其他");
        queryWrapper.orderByDesc(FamilyMember::getBirthdate);
        List<FamilyMember> familyMemberList = familyMemberService.list(queryWrapper);
        List<ApplyEmpFamilyMemberVO> applyEmpFamilyMemberVOList = new ArrayList<>();
        int sort = 1;
        for (FamilyMember familyMember : familyMemberList) {
            ApplyEmpFamilyMemberVO applyEmpFamilyMemberVO = new ApplyEmpFamilyMemberVO();
            // applyEmpFamilyMemberVO.setId();
            applyEmpFamilyMemberVO.setSort(sort++);
            applyEmpFamilyMemberVO.setName(familyMember.getName());
            applyEmpFamilyMemberVO.setRelationship(familyMember.getAppellation());
            Optional.ofNullable(familyMember.getBirthdate()).map(DateUtils::toLocalDateTime)
                    .map(LocalDateTime::toLocalDate)
                    .ifPresent(applyEmpFamilyMemberVO::setBirthdate);
            // applyEmpFamilyMemberVO.setWorkplace();
            applyEmpFamilyMemberVO.setPositionName(familyMember.getJob());
            applyEmpFamilyMemberVO.setLocation(familyMember.getLivingPlace());
            applyEmpFamilyMemberVO.setIsEmergency("01".equals(familyMember.getIsEmergContacts()));
            applyEmpFamilyMemberVO.setPhoneNumber(familyMember.getPhone());
            applyEmpFamilyMemberVO.setPolitic(familyMember.getPolitic());
            applyEmpFamilyMemberVO.setPoliticCode(familyMember.getPoliticCode());
            applyEmpFamilyMemberVO.setGender(familyMember.getSex());
            // applyEmpFamilyMemberVO.setEducationLevel();
            applyEmpFamilyMemberVO.setRelationEffectiveDate(convertRelationEffectiveDate(rosterDetailVO, familyMember));
            applyEmpFamilyMemberVO.setAppellationCode(familyMember.getFamsaCode());
            applyEmpFamilyMemberVO.setAppellationName(familyMember.getFamsaText());

            applyEmpFamilyMemberVOList.add(applyEmpFamilyMemberVO);
        }
        return applyEmpFamilyMemberVOList;
    }

    private List<ApplyEmpRewardPunishmentVO> getRewardPunishmentByStaffNo(String staffNo) {
        LambdaQueryWrapper<RewardPunish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RewardPunish::getPernr, staffNo);
        queryWrapper.orderByDesc(RewardPunish::getRewardPunishDate);
        List<RewardPunish> rewardPunishList = rewardPunishService.list(queryWrapper);
        List<ApplyEmpRewardPunishmentVO> applyEmpRewardPunishmentVOList = new ArrayList<>();
        int sort = 1;
        for (RewardPunish rewardPunish : rewardPunishList) {
            ApplyEmpRewardPunishmentVO applyEmpRewardPunishmentVO = new ApplyEmpRewardPunishmentVO();
            // applyEmpRewardPunishmentVO.setId();
            applyEmpRewardPunishmentVO.setSort(sort++);
            applyEmpRewardPunishmentVO.setName(rewardPunish.getRewardPunishName());
            applyEmpRewardPunishmentVO.setUnitName(rewardPunish.getIssueUnit());
            Optional.ofNullable(rewardPunish.getRewardPunishDate()).map(DateUtils::toLocalDateTime)
                    .map(LocalDateTime::toLocalDate)
                    .ifPresent(applyEmpRewardPunishmentVO::setOccurrenceDate);
            applyEmpRewardPunishmentVO.setType(rewardPunish.getVencourtype());
            applyEmpRewardPunishmentVO.setLevel(rewardPunish.getRewardPunishLevel());
            applyEmpRewardPunishmentVO.setLevelCode(rewardPunish.getRewardPunishLevelCode());
            applyEmpRewardPunishmentVOList.add(applyEmpRewardPunishmentVO);
        }
        return applyEmpRewardPunishmentVOList;
    }

    /**
     * 根据员工编号获取员工的其他资质信息
     * 数据来源：factRoster
     *
     * @param staffNo 员工编号
     * @return
     */
    private List<ApplyEmpOtherQualificationsVO> getOtherQualByStaffNo(String staffNo) {
        LambdaQueryWrapper<TitleQualification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TitleQualification::getPernr, staffNo).eq(TitleQualification::getType,
                TitleQualificationService.TQType.PROF_QUAL);
        queryWrapper.orderByDesc(TitleQualification::getEvaluationDate);
        List<TitleQualification> titleQualificationList = titleQualificationService.list(queryWrapper);

        List<ApplyEmpOtherQualificationsVO> applyEmpOtherQualificationsVOList = new ArrayList<>();
        int sort = 1;
        for (TitleQualification titleQualification : titleQualificationList) {
            ApplyEmpOtherQualificationsVO applyEmpOtherQualificationsVO = new ApplyEmpOtherQualificationsVO();
            // applyEmpOtherQualificationsVO.setId();
            applyEmpOtherQualificationsVO.setQualificationName(titleQualification.getTitleName());
            // applyEmpOtherQualificationsVO.setSpecialty();
            applyEmpOtherQualificationsVO.setSort(sort++);
            applyEmpOtherQualificationsVOList.add(applyEmpOtherQualificationsVO);
        }
        return applyEmpOtherQualificationsVOList;
    }

    private List<ApplyEmpProTitleVO> convertProTitle(RosterDetailVO rosterDetailVO) {

        LambdaQueryWrapper<TitleQualification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TitleQualification::getPernr, rosterDetailVO.getPernr()).eq(TitleQualification::getType,
                TitleQualificationService.TQType.TITLE_INFO);
        queryWrapper.orderByDesc(TitleQualification::getEvaluationDate);

        List<TitleQualification> titleQualificationList = titleQualificationService.list(queryWrapper);

        List<ApplyEmpProTitleVO> applyEmpProTitleVOList = new ArrayList<>();
        int sort = 1;
        for (TitleQualification titleQualification : titleQualificationList) {
            ApplyEmpProTitleVO applyEmpProTitleVO = ApplyEmpProTitleVO.fromTitleQualification(titleQualification);
            // applyEmpProTitleVO.setId();
            applyEmpProTitleVO.setSort(sort++);
            applyEmpProTitleVOList.add(applyEmpProTitleVO);
        }
        return applyEmpProTitleVOList;
    }

    private List<ApplyEmpEducationVO> convertEducationList(List<EducationExperienceVO> educationExperienceVOList) {
        if (CollectionUtils.isEmpty(educationExperienceVOList)) {
            return new ArrayList<>();
        }
        // 转换之后，按顺序设置sort
        AtomicInteger index = new AtomicInteger(1);
        return educationExperienceVOList.stream().map(ApplyEmpEducationVO::fromEducationExperienceVO)
                .peek(vo -> vo.setSort(index.getAndIncrement())).collect(Collectors.toList());
    }

    private List<ApplyEmpKinship3311VO> getEmpKinship3311ByStaffNo(RosterDetailVO rosterDetailVO) {
        LambdaQueryWrapper<FamilyMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FamilyMember::getPernr, rosterDetailVO.getPernr()).eq(FamilyMember::getAppellation, "其他");
        queryWrapper.orderByDesc(FamilyMember::getBirthdate);
        List<FamilyMember> kinship3311List = familyMemberService.list(queryWrapper);
        List<ApplyEmpKinship3311VO> applyEmpKinship3311VOList = new ArrayList<>();
        int sort = 1;
        for (FamilyMember familyMember : kinship3311List) {
            ApplyEmpKinship3311VO applyEmpKinship3311VO = new ApplyEmpKinship3311VO();
            // applyEmpKinship3311VO.setId();
            applyEmpKinship3311VO.setSort(sort++);
            applyEmpKinship3311VO.setName(familyMember.getName());
            applyEmpKinship3311VO.setRelationship(familyMember.getNotes());
            applyEmpKinship3311VO.setTitle(familyMember.getNotes());
            applyEmpKinship3311VO.setOrganizationName(familyMember.getWorkUnit());
            applyEmpKinship3311VO.setPositionName(familyMember.getJob());
            // applyEmpKinship3311VO.setJoin3311Date();
            applyEmpKinship3311VO.setImproper(false);
            // applyEmpKinship3311VO.setImproperDesc();
            applyEmpKinship3311VO.setRelationEffectiveDate(convertRelationEffectiveDate(rosterDetailVO, familyMember));

            applyEmpKinship3311VOList.add(applyEmpKinship3311VO);
        }
        return applyEmpKinship3311VOList;
    }

    /**
     * 获取当前用户的员工申报信息
     *
     * @return
     */
    public ApplyEmpInfoVO getCurrentUserEmpInfo() {
        String username = ContextUtils.getCurrentUser().getUsername();
        return getApplyEmpInfo(username);
    }

    public ApplyEmpInfoVO getApplyEmpInfo(String username) {
        ApplyEmpInfo applyEmpInfo = applyEmpInfoService.getByUsername(username);
        ApplyEmpInfoVO applyEmpInfoVO;

        if (applyEmpInfo == null) {
            applyEmpInfoVO = initApplyEmpInfo(username);
            createApplyEmpInfo(applyEmpInfoVO);
        } else {
            applyEmpInfoVO = ApplyEmpInfoVO.fromApplyEmpInfo(applyEmpInfo);

        }

        loadRelatedInfo(applyEmpInfoVO);
        return applyEmpInfoVO;
    }

    public ApplyEmpInfoVO getApplyEmpInfoByStaffNo(String staffNo) {
        ApplyEmpInfo applyEmpInfo = applyEmpInfoService.getByStaffNo(staffNo);
        ApplyEmpInfoVO applyEmpInfoVO;

        if (applyEmpInfo == null) {
            return null;
        } else {
            applyEmpInfoVO = ApplyEmpInfoVO.fromApplyEmpInfo(applyEmpInfo);
        }

        loadRelatedInfo(applyEmpInfoVO);
        return applyEmpInfoVO;
    }

    /**
     * 从花名册重新加载员工信息
     *
     * @return
     */
    public ApplyEmpInfoVO loadLatestRosterData() {
        return loadLatestRosterData(ContextUtils.getCurrentUser().getUsername());
    }

    private LocalDate convertRelationEffectiveDate(RosterDetailVO rosterDetailVO, FamilyMember familyMember) {
        // 目前查到的所有称谓如下：
        //
        // 兄弟姐妹
        //
        //
        //
        //
        // 其他
        //
        // 咨询人
        //
        //
        List<String> getSelfBirthDate = Arrays.asList("父母", "母亲", "父亲", "哥哥", "姐姐");
        List<String> getTheirBirthDate = Arrays.asList("子女", "弟弟", "妹妹");
        String getMarriageDate = "配偶";
        if (getSelfBirthDate.contains(familyMember.getAppellation())) {
            return rosterDetailVO.getBirthdate();
        } else if (getTheirBirthDate.contains(familyMember.getAppellation())) {
            return Optional.ofNullable(familyMember.getBirthdate()).map(DateUtils::toLocalDateTime)
                    .map(LocalDateTime::toLocalDate).orElse(null);
        } else if (getMarriageDate.equals(familyMember.getAppellation())) {
            // return rosterDetailVO.getma
        } else {
        }
        return null;
    }

    private void deleteExistRecord(String id) {
        ApplyEmpInfo record = new ApplyEmpInfo();
        record.setIsDeleted(true);
        LambdaUpdateWrapper<ApplyEmpInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(ApplyEmpInfo::getIsDeleted, false)
                .eq(ApplyEmpInfo::getId, id)
                .in(ApplyEmpInfo::getStatus, HrrsConsts.ApplyEmpInfoStatus.allowUpdateStatus);
        if (!applyEmpInfoService.update(record, updateWrapper)) {
            throw new ServiceException("更新员工信息失败, 请刷新后重试");
        }
    }

    public ApplyEmpInfoVO loadLatestRosterData(String username) {
        ApplyEmpInfo existApplyEmpInfo = applyEmpInfoService.getByUsername(username);
        if (existApplyEmpInfo != null) {
            checkStatusForUpdate(existApplyEmpInfo);
            deleteExistRecord(existApplyEmpInfo.getId());
        }
        ApplyEmpInfoVO applyEmpInfoVO = initApplyEmpInfo(username);
        createApplyEmpInfo(applyEmpInfoVO);
        return applyEmpInfoVO;
    }

    public void syncRosterData(String username) {
        // 获取已存在的申报记录
        ApplyEmpInfo existApplyEmpInfo = applyEmpInfoService.getByUsername(username);
        if (existApplyEmpInfo != null) {
            // 验证是否允许更新
            checkStatusForUpdate(existApplyEmpInfo);

            // 获取最新花名册数据并更新
            RosterDetailVO rosterDetailVO = rosterDetailService.getRosterDetailByPernr(existApplyEmpInfo.getStaffNo(),
                    HrrsConsts.Language.traditionalChinese);
            ApplyEmpInfoVO applyEmpInfoVO = convertFromRosterDetailVO(rosterDetailVO);
            applyEmpInfoVO.setId(existApplyEmpInfo.getId());
            applyEmpInfoService.updateById(applyEmpInfoVO.toApplyEmpInfo());
        } else {
            // 创建新的申报记录
            ApplyEmpInfoVO applyEmpInfoVO = initApplyEmpInfo(username);
            createApplyEmpInfo(applyEmpInfoVO);
        }
    }

    public void checkForUpdate(ApplyEmpInfo applyEmpInfo) {
        ServiceHelper.checkExist(applyEmpInfo, "员工信息不存在");
        checkStatusForUpdate(applyEmpInfo);
    }

    /**
     * 加载相关信息: 学历，职称，其他资质，奖惩，家庭成员，亲属入职3311，境外移居，亲属企业，社会兼职
     *
     * @param applyEmpInfoVO 员工信息
     */
    public void loadRelatedInfo(ApplyEmpInfoVO applyEmpInfoVO) {
        applyEmpInfoVO.setSignature(applyEmpSignatureService.getSignatureByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpEducationList(applyEmpEducationService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpProTitleList(applyEmpProTitleService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpOtherQualificationsList(
                applyEmpOtherQualificationsService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO
                .setApplyEmpRewardPunishmentList(applyEmpRewardPunishmentService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpFamilyMemberList(applyEmpFamilyMemberService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpKinship3311List(applyEmpKinship3311Service.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpAbroadList(applyEmpAbroadService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpRelativeEnterpriseList(
                applyEmpRelativeEnterpriseService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO.setApplyEmpSocialPtjobList(applyEmpSocialPtjobService.listByEmpId(applyEmpInfoVO.getId()));
        applyEmpInfoVO
                .setApplyEmpProQualificationList(applyEmpProQualificationService.listByEmpId(applyEmpInfoVO.getId()));

        applyEmpInfoVO.setSignature(applyEmpSignatureService.getSignatureByEmpId(applyEmpInfoVO.getId()));
    }

    private void createApplyEmpInfo(ApplyEmpInfoVO applyEmpInfoVO) {
        ApplyEmpInfo applyEmpInfo = applyEmpInfoVO.toApplyEmpInfo();
        applyEmpInfoService.save(applyEmpInfo);
        applyEmpInfoVO.setId(applyEmpInfo.getId());

        createDetails(applyEmpInfoVO);
    }

    private void createDetails(ApplyEmpInfoVO applyEmpInfoVO) {
        // 学历信息
        createEducation(applyEmpInfoVO);

        // 员工职称信息
        createProTitle(applyEmpInfoVO);

        // 其他资质信息
        createOtherQualifications(applyEmpInfoVO);

        // 员工奖惩信息
        createRewardPunishment(applyEmpInfoVO);

        // 员工家庭成员信息
        createFamilyMember(applyEmpInfoVO);

        // 员工亲属入职3311信息
        createEmpKinship3311(applyEmpInfoVO);

        // 境外移居等情况（备注：指本人、配偶、子女）
        createAbroad(applyEmpInfoVO);

        // 员工亲属企业信息
        createRelativeEnterprise(applyEmpInfoVO);

        // 员工社会兼职信息
        createSocialPtjob(applyEmpInfoVO);

        createProQualification(applyEmpInfoVO);
    }

    private void createSocialPtjob(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpSocialPtjobList())) {
            List<ApplyEmpSocialPtjob> applyEmpSocialPtjobList = applyEmpInfoVO.getApplyEmpSocialPtjobList().stream()
                    .map(socialPtjobVO -> {
                        ApplyEmpSocialPtjob applyEmpSocialPtjob = socialPtjobVO.toEntity();
                        applyEmpSocialPtjob.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpSocialPtjob;
                    }).toList();
            applyEmpSocialPtjobService.saveBatch(applyEmpSocialPtjobList);
        }
    }

    private void createRelativeEnterprise(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpRelativeEnterpriseList())) {
            List<ApplyEmpRelativeEnterprise> applyEmpRelativeEnterpriseList = applyEmpInfoVO
                    .getApplyEmpRelativeEnterpriseList().stream().map(relativeEnterpriseVO -> {
                        ApplyEmpRelativeEnterprise applyEmpRelativeEnterprise = relativeEnterpriseVO.toEntity();
                        applyEmpRelativeEnterprise.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpRelativeEnterprise;
                    }).toList();
            applyEmpRelativeEnterpriseService.saveBatch(applyEmpRelativeEnterpriseList);
        }
    }

    private void createAbroad(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpAbroadList())) {
            List<ApplyEmpAbroad> applyEmpAbroadList = applyEmpInfoVO.getApplyEmpAbroadList().stream().map(abroadVO -> {
                ApplyEmpAbroad applyEmpAbroad = abroadVO.toEntity();
                applyEmpAbroad.setEmpId(applyEmpInfoVO.getId());
                return applyEmpAbroad;
            }).toList();
            applyEmpAbroadService.saveBatch(applyEmpAbroadList);
        }
    }

    private void createEmpKinship3311(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpKinship3311List())) {
            List<ApplyEmpKinship3311> applyEmpKinship3311List = applyEmpInfoVO.getApplyEmpKinship3311List().stream()
                    .map(kinship3311VO -> {
                        ApplyEmpKinship3311 applyEmpKinship3311 = kinship3311VO.toEntity();
                        applyEmpKinship3311.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpKinship3311;
                    }).toList();
            applyEmpKinship3311Service.saveBatch(applyEmpKinship3311List);
        }
    }

    private void createFamilyMember(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpFamilyMemberList())) {
            List<ApplyEmpFamilyMember> applyEmpFamilyMemberList = applyEmpInfoVO.getApplyEmpFamilyMemberList().stream()
                    .map(familyMemberVO -> {
                        ApplyEmpFamilyMember applyEmpFamilyMember = familyMemberVO.toEntity();
                        applyEmpFamilyMember.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpFamilyMember;
                    }).toList();
            applyEmpFamilyMemberService.saveBatch(applyEmpFamilyMemberList);
        }
    }

    private void createRewardPunishment(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpRewardPunishmentList())) {
            List<ApplyEmpRewardPunishment> applyEmpRewardPunishmentList = applyEmpInfoVO
                    .getApplyEmpRewardPunishmentList().stream().map(rewardPunishmentVO -> {
                        ApplyEmpRewardPunishment applyEmpRewardPunishment = rewardPunishmentVO.toEntity();
                        applyEmpRewardPunishment.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpRewardPunishment;
                    }).toList();
            applyEmpRewardPunishmentService.saveBatch(applyEmpRewardPunishmentList);
        }
    }

    private void createOtherQualifications(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpOtherQualificationsList())) {
            List<ApplyEmpOtherQualifications> applyEmpOtherQualificationsList = applyEmpInfoVO
                    .getApplyEmpOtherQualificationsList().stream().map(otherQualificationsVO -> {
                        ApplyEmpOtherQualifications applyEmpOtherQualifications = otherQualificationsVO.toEntity();
                        applyEmpOtherQualifications.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpOtherQualifications;
                    }).toList();
            applyEmpOtherQualificationsService.saveBatch(applyEmpOtherQualificationsList);
        }
    }

    private void createProTitle(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpProTitleList())) {
            List<ApplyEmpProTitle> applyEmpProTitleList = applyEmpInfoVO.getApplyEmpProTitleList().stream()
                    .map(proTitleVO -> {
                        ApplyEmpProTitle applyEmpProTitle = proTitleVO.toEntity();
                        applyEmpProTitle.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpProTitle;
                    }).toList();
            applyEmpProTitleService.saveBatch(applyEmpProTitleList);
        }
    }

    public ApplyEmpInfoVO initApplyEmpInfo(String username) {
        ApplyEmpInfoVO applyEmpInfoVO;
        // FactRoster factRoster = getFactRosterByUsername(username);
        RosterDetail rosterDetail = rosterDetailService.getByUsername(username, LocalDate.now());
        // 如果有员工信息，则初始化为员工信息
        if (rosterDetail != null) {
            RosterDetailVO rosterDetailVO = rosterDetailService.getRosterDetailByPernr(rosterDetail.getPernr(),
                    HrrsConsts.Language.traditionalChinese);
            applyEmpInfoVO = convertFromRosterDetailVO(rosterDetailVO);
            loadFromFactRoster(applyEmpInfoVO, factRosterService.getFactRosterByPernr(rosterDetail.getPernr()));
        } else {
            // 如果没有员工信息，则初始化为用户信息
            User user = userService.getUserByUsername(username);
            applyEmpInfoVO = new ApplyEmpInfoVO();
            applyEmpInfoVO.setStaffNo(user.getStaffNo());
            applyEmpInfoVO.setChineseName(user.getName());
        }
        applyEmpInfoVO.setUsername(username);
        return applyEmpInfoVO;
    }

    /**
     * 从花名册（中间库）加载员工信息
     *
     * @param applyEmpInfoVO
     * @param factRoster
     */
    private void loadFromFactRoster(ApplyEmpInfoVO applyEmpInfoVO, FactRoster factRoster) {
        applyEmpInfoVO.setEthnicityCode(factRoster.getEthnicityCode());
        applyEmpInfoVO.setBloodTypeCode(factRoster.getBloodTypeCode());
        applyEmpInfoVO.setMaritalStatusCode(factRoster.getMaritalStatusCode());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateApplyEmpInfo(ApplyEmpInfoVO applyEmpInfoVO) {
        String currentUsername = ContextUtils.getCurrentUser().getUsername();
        String lockKey = "updateEmpInfo-" + currentUsername;
        String lockValue = UUID.randomUUID().toString();
        boolean locked = false;
        try {
            // 加锁，防止重复提交，设置5分钟过期
            locked = RedisLockUtil.lock(lockKey, lockValue, 5 * 60 * 1000L);
            if (!locked) {
                throw new ServiceException("请勿重复提交");
            }

            // 查询原有信息并校验
            ApplyEmpInfo existApplyEmpInfo = applyEmpInfoService.getById(applyEmpInfoVO.getId());
            if (existApplyEmpInfo == null) {
                throw new ServiceException("员工信息不存在，无法更新");
            }

            // 校验是否为本人操作
            if (!currentUsername.equalsIgnoreCase(existApplyEmpInfo.getUsername())) {
                // 此处应记录安全审计日志
                log.warn("安全警告：用户 {} 尝试修改用户 {} 的信息。", currentUsername, existApplyEmpInfo.getUsername());
                throw new ServiceException("无权修改他人信息");
            }

            // 核心字段防篡改校验
            if (!Objects.equals(existApplyEmpInfo.getStaffNo(), applyEmpInfoVO.getStaffNo())
                    || !Objects.equals(existApplyEmpInfo.getChineseName(), applyEmpInfoVO.getChineseName())) {
                log.warn("安全警告：用户 {} 尝试修改核心字段 staffNo 或 chineseName。", currentUsername);
                throw new ServiceException("禁止修改员工编号或姓名");
            }

            checkForUpdate(existApplyEmpInfo);

            // 提交次数不通过前端传递，需要根据提交类型自行计算
            applyEmpInfoVO.setMainInfoSubmitCount(null);
            applyEmpInfoVO.setOtherInfoSubmitCount(null);

            // 校验提交类型
            String submitType = applyEmpInfoVO.getSubmitType();
            if (StringUtils.isNotBlank(submitType)) {
                if ("main".equals(submitType)) {
                    // 提交主要信息时，需要检查签名是否上传
                    ServiceHelper.checkExist(applyEmpInfoVO.getSignature(), "请上传签名");
                    saveOrUpdateSignature(applyEmpInfoVO);
                    // 并发下建议用乐观锁或数据库自增，这里防御null
                    Integer mainCount = existApplyEmpInfo.getMainInfoSubmitCount();
                    applyEmpInfoVO.setMainInfoSubmitCount(mainCount == null ? 1 : mainCount + 1);
                } else if ("other".equals(submitType)) {
                    Integer otherCount = existApplyEmpInfo.getOtherInfoSubmitCount();
                    applyEmpInfoVO.setOtherInfoSubmitCount(otherCount == null ? 1 : otherCount + 1);
                } else {
                    throw new ServiceException("提交类型非法");
                }
            }

            // 更新主表
            applyEmpInfoService.updateEmpInfo(applyEmpInfoVO);

            // 子表更新，异常时记录日志并抛出，保证事务回滚
            try {
                if (applyEmpInfoVO.getApplyEmpEducationList() != null)
                    updateEducation(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpProTitleList() != null)
                    updateProTitle(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpOtherQualificationsList() != null)
                    updateOtherQualifications(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpRewardPunishmentList() != null)
                    updateRewardPunishment(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpFamilyMemberList() != null)
                    updateFamilyMember(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpKinship3311List() != null)
                    updateEmpKinship3311(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpAbroadList() != null)
                    updateAbroad(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpRelativeEnterpriseList() != null)
                    updateRelativeEnterprise(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpSocialPtjobList() != null)
                    updateSocialPtjob(applyEmpInfoVO);
                if (applyEmpInfoVO.getApplyEmpProQualificationList() != null)
                    updateProQualification(applyEmpInfoVO);
            } catch (Exception e) {
                log.error("更新员工子表信息异常", e);
                throw new ServiceException("更新员工子表信息失败", e); // 保证事务回滚
            }
        } finally {
            if (locked) {
                RedisLockUtil.unlock(lockKey, lockValue);
            }
        }
    }

    private void saveOrUpdateSignature(ApplyEmpInfoVO applyEmpInfoVO) {
        String existSignature = applyEmpSignatureService.getSignatureByEmpId(applyEmpInfoVO.getId());
        if (StringUtils.equalsIgnoreCase(existSignature, applyEmpInfoVO.getSignature())) {
            log.info("签名未发生变化，不需要更新");
            return;
        }
        applyEmpSignatureService.updateEmpSignature(applyEmpInfoVO.getId(), applyEmpInfoVO.getSignature());
    }

    private void updateProQualification(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpProQualification> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpProQualification::getEmpId, applyEmpInfoVO.getId());
        applyEmpProQualificationService.remove(updateWrapper);
        createProQualification(applyEmpInfoVO);
    }

    private void createProQualification(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpProQualificationList())) {
            List<ApplyEmpProQualification> applyEmpProQualificationList = applyEmpInfoVO
                    .getApplyEmpProQualificationList().stream().map(proQualificationVO -> {
                        ApplyEmpProQualification applyEmpProQualification = proQualificationVO.toEntity();
                        applyEmpProQualification.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpProQualification;
                    }).toList();
            applyEmpProQualificationService.saveBatch(applyEmpProQualificationList);
        }
    }

    private void updateSocialPtjob(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpSocialPtjob> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpSocialPtjob::getEmpId, applyEmpInfoVO.getId());
        applyEmpSocialPtjobService.remove(updateWrapper);
        createSocialPtjob(applyEmpInfoVO);
    }

    private void updateRelativeEnterprise(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpRelativeEnterprise> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpRelativeEnterprise::getEmpId, applyEmpInfoVO.getId());
        applyEmpRelativeEnterpriseService.remove(updateWrapper);
        createRelativeEnterprise(applyEmpInfoVO);
    }

    private void updateAbroad(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpAbroad> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpAbroad::getEmpId, applyEmpInfoVO.getId());
        applyEmpAbroadService.remove(updateWrapper);
        createAbroad(applyEmpInfoVO);
    }

    private void updateEmpKinship3311(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpKinship3311> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpKinship3311::getEmpId, applyEmpInfoVO.getId());
        applyEmpKinship3311Service.remove(updateWrapper);
        createEmpKinship3311(applyEmpInfoVO);
    }

    private void updateFamilyMember(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpFamilyMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpFamilyMember::getEmpId, applyEmpInfoVO.getId());
        applyEmpFamilyMemberService.remove(updateWrapper);
        createFamilyMember(applyEmpInfoVO);
    }

    private void updateRewardPunishment(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpRewardPunishment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpRewardPunishment::getEmpId, applyEmpInfoVO.getId());
        applyEmpRewardPunishmentService.remove(updateWrapper);
        createRewardPunishment(applyEmpInfoVO);
    }

    private void updateOtherQualifications(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpOtherQualifications> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpOtherQualifications::getEmpId, applyEmpInfoVO.getId());
        applyEmpOtherQualificationsService.remove(updateWrapper);
        createOtherQualifications(applyEmpInfoVO);
    }

    private void updateProTitle(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaUpdateWrapper<ApplyEmpProTitle> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApplyEmpProTitle::getEmpId, applyEmpInfoVO.getId());
        applyEmpProTitleService.remove(updateWrapper);
        createProTitle(applyEmpInfoVO);
    }

    private void updateEducation(ApplyEmpInfoVO applyEmpInfoVO) {
        LambdaQueryWrapper<ApplyEmpEducation> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(ApplyEmpEducation::getEmpId, applyEmpInfoVO.getId());
        applyEmpEducationService.remove(updateWrapper);
        createEducation(applyEmpInfoVO);
    }

    private void createEducation(ApplyEmpInfoVO applyEmpInfoVO) {
        if (CollectionUtils.isNotEmpty(applyEmpInfoVO.getApplyEmpEducationList())) {
            List<ApplyEmpEducation> applyEmpEducationList = applyEmpInfoVO.getApplyEmpEducationList().stream()
                    .map(educationVO -> {
                        ApplyEmpEducation applyEmpEducation = educationVO.toEntity();
                        applyEmpEducation.setEmpId(applyEmpInfoVO.getId());
                        return applyEmpEducation;
                    }).toList();
            applyEmpEducationService.saveBatch(applyEmpEducationList);
        }
    }

    public void sendApplyToUser(String username, LocalDateTime deadline) {
        // 获取员工信息，如不存在则初始化
        ApplyEmpInfo applyEmpInfo = applyEmpInfoService.getByUsername(username);
        String relatedId = applyEmpInfo != null ? applyEmpInfo.getId() : null;
        if (applyEmpInfo == null) {
            ApplyEmpInfoVO applyEmpInfoVO = getApplyEmpInfo(username);
            relatedId = applyEmpInfoVO != null ? applyEmpInfoVO.getId() : null;
        }

        // 提取常量，便于维护
        final String APP_ID = "cli_a88d880b283d500c"; // 建议后续用配置注入
        final String REDIRECT_URI = "https://api.biz.3311csci.com/zhtappsso/api/AuthLogin/eids";

        // 生成飞书授权链接
        String state = String.format("%08d", new java.util.Random().nextInt(100000000)); // 8位随机数字防止CSRF
        String feishuAuthUrl = String.format(
                "https://open.feishu.cn/open-apis/authen/v1/index?app_id=%s&redirect_uri=%s&state=%s",
                APP_ID,
                URLEncoder.encode(REDIRECT_URI, StandardCharsets.UTF_8),
                state);

        // 构建飞书卡片消息
        FeishuCardMessage.CardBuilder builder = feishuService.getCardBuilder();
        String title = "重要通知：员工个人信息申报";
        String[] weekDays = {"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
        String weekDayStr = "（" + weekDays[deadline.getDayOfWeek().getValue() - 1] + "）";
        String dateStr = deadline.toLocalDate().toString();
        String timeStr = deadline.toLocalTime().toString();
        builder.withHeader(title, "purple")
                .withConfig(true, true, false)
                .addHtmlDiv("亲爱的领导、同事：<br>为确保员工人事信息完整、准确，现开展\"2025年度员工个人基础信息申报及确认工作\"，详见邮件通知。<br>本次采集及确认的员工个人信息申报内容将直接关联后续人事档案更新、员工监督与管理、人才盘点及培训与发展等重要工作，与个人权益紧密相关，请务必认真对待，按时完成信息更新。")
                .addHtmlDiv("请按照申报系统页面内详细提示操作，其中产生于内地的信息(如地址、学历、职称等)应使用简体规范汉字录入，保证与相关证明材料上的记载一致。<br>如有疑问欢迎与所在单位人力资源部联系。")
                .addHtmlDiv("👉 [点击此处进入申报系统](" + feishuAuthUrl + ")")
                .addHtmlDiv("<div style=\"font-size:18px;\">请您务必于截止日期 <font color='red'>" + dateStr
                        + weekDayStr + timeStr + "前</font> 前完成信息填报，感谢您的理解与配合！</div>");
        builder.withCardLink(feishuAuthUrl);

        // 统一转换email，避免重复调用
        String email = convertUsernameToEmail(username);
        Map<String, Boolean> sendResultMap = feishuService.batchSendCustomCardToEmail(List.of(email), builder);

        // 发送成功后，记录消息发送日志
        if (Boolean.TRUE.equals(sendResultMap.get(email))) {
            MessageSendLog log = new MessageSendLog();
            log.setRelatedId(relatedId);
            log.setReceiverUsername(username);
            log.setMessageContent(title + "，截止日期：" + deadline.toString() + weekDayStr);
            log.setCreationTime(java.time.LocalDateTime.now());
            log.setCreateUsername(
                    ContextUtils.getCurrentUser() != null ? ContextUtils.getCurrentUser().getUsername() : "system");
            messageSendLogService.save(log);
        }
    }

    // 创建一个方法，将指定的 username 转换成 email：aaa.cohl.com
    private String convertUsernameToEmail(String username) {
        if (StringUtils.isBlank(username)) {
            return "";
        }
        if (username.contains("@")) {
            return username;
        }
        return username + "@cohl.com";
    }
}
