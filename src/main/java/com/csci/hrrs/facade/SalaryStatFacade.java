package com.csci.hrrs.facade;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.SalaryCategoryEnum;
import com.csci.hrrs.constant.SalaryStatConstant;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.PayGroup;
import com.csci.hrrs.model.SalaryStat;
import com.csci.hrrs.model.SalaryStatHead;
import com.csci.hrrs.qo.SalaryStatQO;
import com.csci.hrrs.service.*;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.holder.OrganizationHolder;
import com.csci.hrrs.vo.EditSalaryStatVO;
import com.csci.hrrs.vo.FillSalaryStatVO;
import com.csci.hrrs.vo.PayGroupVO;
import com.csci.hrrs.vo.SalaryStatVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.hrrs.constant.SalaryCategoryEnum.*;

@Component
@LogMethod
@Slf4j
public class SalaryStatFacade {

    public static final String defaultPayGroupId = "main";

    @Resource
    private SalaryStatHeadService salaryStatHeadService;

    @Resource
    private SalaryStatService salaryStatService;

    @Resource
    private SaveSalaryHeadWithDetailFacade saveSalaryHeadWithDetailFacade;

    @Resource
    private SalaryStatAuthorityCheckFacade salaryStatAuthorityCheckFacade;

    @Resource
    private PayGroupUserService payGroupUserService;

    @Resource
    private PayGroupService payGroupService;

    @Resource
    private PayGroupOrgService payGroupOrgService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private OrganizationHolder organizationHolder;

    /**
     * 生成本地薪酬统计头，不会保存到数据库
     *
     * @param organizationCode 组织机构编码
     * @param year
     * @return
     */
    static SalaryStatHead generateLocalHead(String organizationCode, Integer year) {
        SalaryStatHead head = new SalaryStatHead();
        head.setId(CommonUtils.randomUuid());
        head.setYear(year);
        head.setOrganizationCode(organizationCode);
        return head;
    }

    /**
     * 计算行总计
     *
     * @param salaryStat 薪酬统计
     * @return
     */
    private static BigDecimal calcRowTotal(SalaryStat salaryStat) {
        BigDecimal rowTotal;
        rowTotal = CommonUtils.add(salaryStat.getSalaryTotal(), salaryStat.getBonusTotal());
        // rowTotal = CommonUtils.add(rowTotal, salaryStat.getBonusBudget());
        return rowTotal;
    }

    private static void calculateSumByMonth(SalaryStat summary, List<SalaryStat> sumList) {
        summary.setJan(sumList.stream().map(SalaryStat::getJan).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setFeb(sumList.stream().map(SalaryStat::getFeb).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setMar(sumList.stream().map(SalaryStat::getMar).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setApr(sumList.stream().map(SalaryStat::getApr).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setMay(sumList.stream().map(SalaryStat::getMay).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setJun(sumList.stream().map(SalaryStat::getJun).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setJul(sumList.stream().map(SalaryStat::getJul).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setAug(sumList.stream().map(SalaryStat::getAug).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setSep(sumList.stream().map(SalaryStat::getSep).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setOct(sumList.stream().map(SalaryStat::getOct).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setNov(sumList.stream().map(SalaryStat::getNov).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setDec(sumList.stream().map(SalaryStat::getDec).reduce(BigDecimal.ZERO, BigDecimal::add));

        if (StringUtils.equalsIgnoreCase(summary.getType2(), SalaryStatConstant.MONTH_SALARY)) {
            summary.setMarketingBonus(sumList.stream().map(SalaryStat::getMarketingBonus).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setContractCashed(sumList.stream().map(SalaryStat::getContractCashed).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setBonusTotal(calcBonusTotal(summary));
            summary.setTotal(calcRowTotal(summary));
            summary.setOtherBonus(sumList.stream().map(SalaryStat::getOtherBonus)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setPerformanceBonus(
                    sumList.stream().map(SalaryStat::getPerformanceBonus)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    /**
     * 计算薪酬，奖金
     *
     * @param salaryStat
     */
    private static void calcAndSetRowTotal(SalaryStat salaryStat) {
        // 如果是人数
        if (StringUtils.equalsIgnoreCase(salaryStat.getType2(), SalaryStatConstant.MONTH_PERSON_COUNT)) {
            salaryStat.setSalaryTotal(calculateSumByMonthValue(salaryStat));
        } else if (StringUtils.equalsIgnoreCase(salaryStat.getType2(), SalaryStatConstant.ACCUMULATIVE_PERSON_COUNT)) {
            salaryStat.setSalaryTotal(salaryStat.getDec());
        } else if (StringUtils.equalsIgnoreCase(salaryStat.getType2(), SalaryStatConstant.MONTH_SALARY)) {
            salaryStat.setSalaryTotal(calculateSumByMonthValue(salaryStat));
            salaryStat.setBonusTotal(CommonUtils.add(salaryStat.getMarketingBonus(), salaryStat.getContractCashed()));
            salaryStat.setTotal(calcRowTotal(salaryStat));
        } else if (StringUtils.equalsIgnoreCase(salaryStat.getType2(), SalaryStatConstant.ACCUMULATIVE_SALARY)) {
            salaryStat.setSalaryTotal(salaryStat.getDec());
            salaryStat.setBonusTotal(CommonUtils.add(salaryStat.getMarketingBonus(), salaryStat.getContractCashed()));
            salaryStat.setTotal(calcRowTotal(salaryStat));
        }
    }

    /**
     * 计算1-12月数字之和
     *
     * @param salaryStat
     * @return
     */
    private static BigDecimal calculateSumByMonthValue(SalaryStat salaryStat) {
        BigDecimal sum = BigDecimal.ZERO;
        for (int i = 0; i < 12; i++) {
            sum = CommonUtils.add(sum, getMonthValue(salaryStat, i));
        }
        return sum;
    }

    private static BigDecimal getMonthValue(SalaryStat salaryStat, int monthIndex) {
        return switch (monthIndex) {
            case 0 -> salaryStat.getJan();
            case 1 -> salaryStat.getFeb();
            case 2 -> salaryStat.getMar();
            case 3 -> salaryStat.getApr();
            case 4 -> salaryStat.getMay();
            case 5 -> salaryStat.getJun();
            case 6 -> salaryStat.getJul();
            case 7 -> salaryStat.getAug();
            case 8 -> salaryStat.getSep();
            case 9 -> salaryStat.getOct();
            case 10 -> salaryStat.getNov();
            case 11 -> salaryStat.getDec();
            default -> throw new IllegalArgumentException("Invalid month index: " + monthIndex);
        };
    }

    /**
     * 计算奖金总计
     *
     * @param salaryStat 薪酬统计
     * @return
     */
    private static BigDecimal calcBonusTotal(SalaryStat salaryStat) {
        BigDecimal bonusTotal;
        bonusTotal = CommonUtils.add(salaryStat.getMarketingBonus(), salaryStat.getContractCashed());
        return bonusTotal;
    }

    private static void sumSubList(List<SalaryStat> mainSalaryList, List<SalaryStat> subSalaryList) {
        mainSalaryList.forEach(s -> {
            List<SalaryStat> subList = subSalaryList.stream().filter(p -> p.getCategoryCode().equals(s.getCategoryCode()) && p.getType1().equals(s.getType1()) && p.getType2().equals(s.getType2())).toList();
            s.setJan(subList.stream().map(SalaryStat::getJan).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setFeb(subList.stream().map(SalaryStat::getFeb).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setMar(subList.stream().map(SalaryStat::getMar).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setApr(subList.stream().map(SalaryStat::getApr).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setMay(subList.stream().map(SalaryStat::getMay).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setJun(subList.stream().map(SalaryStat::getJun).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setJul(subList.stream().map(SalaryStat::getJul).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setAug(subList.stream().map(SalaryStat::getAug).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setSep(subList.stream().map(SalaryStat::getSep).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setOct(subList.stream().map(SalaryStat::getOct).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setNov(subList.stream().map(SalaryStat::getNov).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setDec(subList.stream().map(SalaryStat::getDec).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setSalaryTotal(subList.stream().map(SalaryStat::getSalaryTotal).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setBonusBudget(subList.stream().map(SalaryStat::getBonusBudget).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setMarketingBonus(subList.stream().map(SalaryStat::getMarketingBonus).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setContractCashed(subList.stream().map(SalaryStat::getContractCashed).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setBonusTotal(subList.stream().map(SalaryStat::getBonusTotal).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setTotal(subList.stream().map(SalaryStat::getTotal).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setOtherBonus(subList.stream().map(SalaryStat::getOtherBonus).reduce(CommonUtils::add).orElse(BigDecimal.ZERO));
            s.setPerformanceBonus(subList.stream().map(SalaryStat::getPerformanceBonus).reduce(CommonUtils::add)
                    .orElse(BigDecimal.ZERO));
        });
    }

    /**
     * 查询填报页面数据
     *
     * @param salaryStatQO 查询条件
     * @return
     */
    public List<SalaryStatVO> listSalaryStatFill(SalaryStatQO salaryStatQO) {
        ServiceHelper.checkExist(salaryStatQO.getOrganizationCode(), "组织编码不能为空");
        ServiceHelper.checkExist(salaryStatQO.getPayGroupId(), "薪酬小组不能为空");

        salaryStatAuthorityCheckFacade.checkAuthority(salaryStatQO.getOrganizationCode(), salaryStatQO.getPayGroupId());

        String payGroupId = salaryStatQO.getPayGroupId();

        SalaryStatHead head = findOrCreateSalaryStat(salaryStatQO);

        // 查询payGroupId是否有对应的薪酬填报记录
        tryInitSalaryStatWithPayGroup(salaryStatQO, head, payGroupId);

        List<SalaryStatVO> lstResult = new ArrayList<>();
        lstResult.addAll(salaryStatService.listFillBudget(head.getId(), payGroupId).stream().map(SalaryStatVO::fromModel).toList());
        lstResult.addAll(salaryStatService.listFillRecordsForCurrentUser(head.getId(), payGroupId).stream().map(SalaryStatVO::fromModel).toList());
        return lstResult;
    }

    private void tryInitSalaryStatWithPayGroup(SalaryStatQO salaryStatQO, SalaryStatHead head, String payGroupId) {
        LambdaQueryWrapper<SalaryStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SalaryStat::getHeadId, head.getId())
                .eq(SalaryStat::getIsDeleted, false)
                .eq(SalaryStat::getPayGroupId, payGroupId);
        if (!salaryStatService.exists(queryWrapper)) {
            String lock = "saveSalaryStat:" + salaryStatQO.getOrganizationCode() + ":" + salaryStatQO.getYear() + ":" + payGroupId;
            // 如果没有，则初始化薪酬统计数据
            if (RedisLockUtil.lock(lock)) {
                try {
                    SalaryStatGenerator generator = new SalaryStatGenerator(salaryStatQO.getOrganizationCode(), salaryStatQO.getYear());
                    List<SalaryStat> salaryStatList = generator.getSalaryStatList();
                    salaryStatList.forEach(s -> {
                        s.setPayGroupId(payGroupId);
                        s.setHeadId(head.getId());
                    });
                    salaryStatService.saveBatch(salaryStatList);
                } finally {
                    RedisLockUtil.unlock(lock);
                }
            } else {
                throw new ServiceException("该单位的数据正在保存中，请刷新后再试");
            }

        }
    }

    /**
     * 获取或初始化薪资统计头表数据, 如果进行初始化，则明细数据也会被初始化
     *
     * @param salaryStatQO 薪资统计查询对象,包含年份和组织编码
     * @return 薪资统计头表对象
     */
    private SalaryStatHead findOrCreateSalaryStat(SalaryStatQO salaryStatQO) {
        // 根据年份和组织编码查询薪资统计头表
        SalaryStatHead head = salaryStatHeadService.findByYearAndOrg(salaryStatQO.getYear(),
                salaryStatQO.getOrganizationCode());
        // 如果头表不存在,则初始化头表和明细数据
        if (head == null) {
            head = createAndSaveSalaryStatHeadAndDetails(salaryStatQO);
        }
        return head;
    }

    /**
     * 创建并保存薪资统计头表和明细数据
     *
     * @param salaryStatQO 查询对象
     * @return 新创建的薪资统计头表对象
     */
    private SalaryStatHead createAndSaveSalaryStatHeadAndDetails(SalaryStatQO salaryStatQO) {
        // 创建薪资统计数据生成器
        SalaryStatGenerator generator = new SalaryStatGenerator(salaryStatQO.getOrganizationCode(),
                salaryStatQO.getYear());
        // 获取头表数据
        SalaryStatHead head = generator.getHead();
        // 初始化薪资统计明细列表
        List<SalaryStat> lstSalaryStat = new ArrayList<>();
        // 添加薪资实际统计数据
        lstSalaryStat.addAll(generator.getSalaryStatList());
        // 添加薪资预算数据
        lstSalaryStat.addAll(generator.getSalaryBudgetList());
        // 保存头表和明细数据
        saveSalaryHeadWithDetailFacade.saveSalaryStat(head, lstSalaryStat);
        log.info("已创建并保存薪资统计头表和明细数据，组织编码：{}，年份：{}", salaryStatQO.getOrganizationCode(), salaryStatQO.getYear());
        return head;
    }

    @Transactional
    public void saveSalaryStat(FillSalaryStatVO fillSalaryStatVO) {
        ServiceHelper.checkExist(fillSalaryStatVO.getSalaryStatVOList(), "薪酬统计记录不能为空");
        SalaryStatHead head = Optional.ofNullable(
                        salaryStatHeadService.findByYearAndOrg(fillSalaryStatVO.getYear(), fillSalaryStatVO.getOrganizationCode()))
                .orElseThrow(() -> new ServiceException("薪酬统计头表不存在"));

        List<SalaryStat> lstSalary = salaryStatService.listForQueryPageByHeadId(head.getId(), null, fillSalaryStatVO.getPayGroupId());
        setUpdateSalaryStatsField(fillSalaryStatVO, lstSalary);

        // 将数据分成两个部分，一个是预算数据，一个是非预算数据
        List<SalaryStat> budgetList = lstSalary.stream().filter(s -> Objects.equals(s.getIsBudget(), Boolean.TRUE)).toList();
        List<SalaryStat> notBudgetList = lstSalary.stream().filter(s -> !Objects.equals(s.getIsBudget(), Boolean.TRUE)).toList();

        handleBudgetTotal(budgetList);

        calculateCategories(notBudgetList);

        List<SalaryStat> updateList = new ArrayList<>(budgetList);
        updateList.addAll(notBudgetList);

        saveSalaryStats(head.getId(), updateList);

    }

    /**
     * 计算指定组织机构在该年度的所有薪酬统计数据汇总
     * 会将所有薪酬小组的数据汇总的薪酬小组为空的记录中
     *
     * @param organizationCode 组织编码
     * @param year             年份
     */
    public void calculateOrgAll(String organizationCode, int year) {
        SalaryStatHead head = salaryStatHeadService.findByYearAndOrg(year, organizationCode);
        if (head == null) {
            log.warn("薪酬统计头表不存在，无法计算");
            return;
        }

        // 查询主数据（主数据等于薪酬小组数据汇总）
        List<SalaryStat> lstSalary = salaryStatService.listForQueryPageByHeadId(head.getId(), null, defaultPayGroupId);
        // 查询薪酬小组数据
        LambdaQueryWrapper<SalaryStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SalaryStat::getIsDeleted, false).eq(SalaryStat::getOrganizationCode, organizationCode).eq(SalaryStat::getYear, year)
                .ne(SalaryStat::getPayGroupId, defaultPayGroupId);
        List<SalaryStat> lstPayGroupSalary = salaryStatService.list(queryWrapper);

        // 将薪酬小组数据汇总到主数据中
        sumSubList(lstSalary, lstPayGroupSalary);

        calculateCategories(lstSalary);
        saveSalaryStats(head.getId(), lstSalary);
    }


    /**
     * 汇总计算指定组织机构及其下属机构的薪酬统计数据
     *
     * @param organizationCode 组织机构编码
     * @param year             统计年份
     */
    public void aggregateSalaryStat(String organizationCode, Integer year) {
        // 构建查询条件并获取或创建薪酬统计头表
        SalaryStatQO salaryStatQO = new SalaryStatQO();
        salaryStatQO.setOrganizationCode(organizationCode);
        salaryStatQO.setYear(year);
        SalaryStatHead head = findOrCreateSalaryStat(salaryStatQO);

        // 获取当前组织机构的主要薪酬统计数据
        List<SalaryStat> lstMainSalary = salaryStatService.listForQueryPageByHeadId(head.getId(), null, defaultPayGroupId);

        if (organizationService.isRootCompanyByCode(organizationCode)) {
            // 1. 根节点: 汇总下级单位的薪酬总表
            List<Organization> lstSubOrg = organizationHolder.findSubOrgsByCode(organizationCode);
            List<SalaryStat> lstSubSalary = new ArrayList<>();
            for (Organization subOrg : lstSubOrg) {
                List<SalaryStat> lstSubOrgSalary = salaryStatService.listMainSalaryStat(subOrg.getCode(), year);
                if (CollectionUtils.isNotEmpty(lstSubOrgSalary)) {
                    lstSubSalary.addAll(lstSubOrgSalary);
                }
            }
            if (CollectionUtils.isNotEmpty(lstSubSalary)) {
                // 第一次汇总计算：将下级单位数据汇总到当前单位
                sumSubList(lstMainSalary, lstSubSalary);
            }
        } else if (organizationService.isPlatformByCode(organizationCode)) {
            // 2. 平台公司: 汇总该组织所有薪酬小组填报的数据
            LambdaQueryWrapper<SalaryStat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SalaryStat::getHeadId, head.getId())
                    .ne(SalaryStat::getPayGroupId, defaultPayGroupId)
                    .eq(SalaryStat::getIsDeleted, false);
            List<SalaryStat> lstPayGroupSalary = salaryStatService.list(queryWrapper);

            if (CollectionUtils.isNotEmpty(lstPayGroupSalary)) {
                // 第一次汇总计算：将薪酬小组数据汇总到主数据
                sumSubList(lstMainSalary, lstPayGroupSalary);
            }
        } else {
            throw new ServiceException("暂时不支持该组织机构类型的汇总计算");
        }

        // 只计算基础分类，避免重复计算汇总分类
        calculateBasicCategories(lstMainSalary);

        // 添加计算结果日志记录
        logCalculationResults(organizationCode, year, lstMainSalary);

        saveSalaryStats(head.getId(), lstMainSalary);
    }

    /**
     * 记录计算结果详情，用于核对计算是否正确
     *
     * @param organizationCode 组织编码
     * @param year             年份
     * @param lstMainSalary    主要薪酬统计数据
     */
    private void logCalculationResults(String organizationCode, Integer year, List<SalaryStat> lstMainSalary) {
        log.info("================= 薪酬统计计算结果详情 =================");
        log.info("组织编码: {}, 年份: {}", organizationCode, year);

        // 1. 记录基础数据
        log.info("----------------- 基础数据 -----------------");
        logCategoryData(lstMainSalary, "内派", INNER_ASSIGNED.getCode());
        logCategoryData(lstMainSalary, "港/澳聘月薪", HK_MONTHLY.getCode());
        logCategoryData(lstMainSalary, "港/澳聘日薪", HK_DAILY.getCode());
        logCategoryData(lstMainSalary, "内地", MAINLAND.getCode());
        logCategoryData(lstMainSalary, "海外", OVERSEAS.getCode());
        logCategoryData(lstMainSalary, "外包管理人员", OUTSOURCED_MANAGERS.getCode());
        logCategoryData(lstMainSalary, "外包操作人员", OUTSOURCED_OPERATORS.getCode());

        log.info("----------------- 汇总计算过程 -----------------");

        // 2. 记录自有合计计算过程
        logOwnedTotalCalculation(lstMainSalary);

        // 3. 记录外包总计计算过程
        logOutsourcedTotalCalculation(lstMainSalary);

        // 4. 记录全口径计算过程
        logGrandTotalCalculation(lstMainSalary);

        // 5. 记录总计（全部人员）计算过程
        logTotalAllCalculation(lstMainSalary);

        log.info("================= 薪酬统计计算结果详情结束 =================");
    }

    /**
     * 记录单个类别的数据
     */
    private void logCategoryData(List<SalaryStat> lstMainSalary, String categoryName, String categoryCode) {
        // 当月人数
        BigDecimal personCount = getCategoryValue(lstMainSalary, categoryCode, SalaryStatConstant.MONTH_PERSON_COUNT,
                "salaryTotal");
        // 当月薪酬
        BigDecimal salaryAmount = getCategoryValue(lstMainSalary, categoryCode, SalaryStatConstant.MONTH_SALARY,
                "total");

        log.info("{} - 当月人数: {}, 当月薪酬: {}", categoryName, personCount, salaryAmount);
    }

    /**
     * 记录自有合计计算过程：自有合计 = 内派 + 港/澳聘月薪 + 港/澳聘日薪 + 内地 + 海外
     */
    private void logOwnedTotalCalculation(List<SalaryStat> lstMainSalary) {
        BigDecimal innerPersonCount = getCategoryValue(lstMainSalary, INNER_ASSIGNED.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal hkMonthlyPersonCount = getCategoryValue(lstMainSalary, HK_MONTHLY.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal hkDailyPersonCount = getCategoryValue(lstMainSalary, HK_DAILY.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal mainlandPersonCount = getCategoryValue(lstMainSalary, MAINLAND.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal overseasPersonCount = getCategoryValue(lstMainSalary, OVERSEAS.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal ownedTotalPersonCount = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");

        BigDecimal innerSalary = getCategoryValue(lstMainSalary, INNER_ASSIGNED.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal hkMonthlySalary = getCategoryValue(lstMainSalary, HK_MONTHLY.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal hkDailySalary = getCategoryValue(lstMainSalary, HK_DAILY.getCode(), SalaryStatConstant.MONTH_SALARY,
                "total");
        BigDecimal mainlandSalary = getCategoryValue(lstMainSalary, MAINLAND.getCode(), SalaryStatConstant.MONTH_SALARY,
                "total");
        BigDecimal overseasSalary = getCategoryValue(lstMainSalary, OVERSEAS.getCode(), SalaryStatConstant.MONTH_SALARY,
                "total");
        BigDecimal ownedTotalSalary = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");

        log.info("自有合计人数计算: {} + {} + {} + {} + {} = {} (实际结果: {})",
                innerPersonCount, hkMonthlyPersonCount, hkDailyPersonCount, mainlandPersonCount, overseasPersonCount,
                innerPersonCount.add(hkMonthlyPersonCount).add(hkDailyPersonCount).add(mainlandPersonCount)
                        .add(overseasPersonCount),
                ownedTotalPersonCount);

        log.info("自有合计薪酬计算: {} + {} + {} + {} + {} = {} (实际结果: {})",
                innerSalary, hkMonthlySalary, hkDailySalary, mainlandSalary, overseasSalary,
                innerSalary.add(hkMonthlySalary).add(hkDailySalary).add(mainlandSalary).add(overseasSalary),
                ownedTotalSalary);
    }

    /**
     * 记录外包总计计算过程：外包总计 = 外包管理人员 + 外包操作人员
     */
    private void logOutsourcedTotalCalculation(List<SalaryStat> lstMainSalary) {
        BigDecimal managersPersonCount = getCategoryValue(lstMainSalary, OUTSOURCED_MANAGERS.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal operatorsPersonCount = getCategoryValue(lstMainSalary, OUTSOURCED_OPERATORS.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal outsourcedTotalPersonCount = getCategoryValue(lstMainSalary, OUTSOURCED_TOTAL.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");

        BigDecimal managersSalary = getCategoryValue(lstMainSalary, OUTSOURCED_MANAGERS.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal operatorsSalary = getCategoryValue(lstMainSalary, OUTSOURCED_OPERATORS.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal outsourcedTotalSalary = getCategoryValue(lstMainSalary, OUTSOURCED_TOTAL.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");

        log.info("外包总计人数计算: {} + {} = {} (实际结果: {})",
                managersPersonCount, operatorsPersonCount, managersPersonCount.add(operatorsPersonCount),
                outsourcedTotalPersonCount);

        log.info("外包总计薪酬计算: {} + {} = {} (实际结果: {})",
                managersSalary, operatorsSalary, managersSalary.add(operatorsSalary), outsourcedTotalSalary);
    }

    /**
     * 记录全口径计算过程：全口径 = 自有合计 + 外包管理人员
     */
    private void logGrandTotalCalculation(List<SalaryStat> lstMainSalary) {
        BigDecimal ownedTotalPersonCount = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal managersPersonCount = getCategoryValue(lstMainSalary, OUTSOURCED_MANAGERS.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal grandTotalPersonCount = getCategoryValue(lstMainSalary, TOTAL.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");

        BigDecimal ownedTotalSalary = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal managersSalary = getCategoryValue(lstMainSalary, OUTSOURCED_MANAGERS.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal grandTotalSalary = getCategoryValue(lstMainSalary, TOTAL.getCode(), SalaryStatConstant.MONTH_SALARY,
                "total");

        log.info("全口径人数计算: {} + {} = {} (实际结果: {})",
                ownedTotalPersonCount, managersPersonCount, ownedTotalPersonCount.add(managersPersonCount),
                grandTotalPersonCount);

        log.info("全口径薪酬计算: {} + {} = {} (实际结果: {})",
                ownedTotalSalary, managersSalary, ownedTotalSalary.add(managersSalary), grandTotalSalary);
    }

    /**
     * 记录总计（全部人员）计算过程：总计（全部人员） = 自有合计 + 外包总计
     */
    private void logTotalAllCalculation(List<SalaryStat> lstMainSalary) {
        BigDecimal ownedTotalPersonCount = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal outsourcedTotalPersonCount = getCategoryValue(lstMainSalary, OUTSOURCED_TOTAL.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");
        BigDecimal totalAllPersonCount = getCategoryValue(lstMainSalary, TOTAL_ALL.getCode(),
                SalaryStatConstant.MONTH_PERSON_COUNT, "salaryTotal");

        BigDecimal ownedTotalSalary = getCategoryValue(lstMainSalary, TOTAL_OWNED.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal outsourcedTotalSalary = getCategoryValue(lstMainSalary, OUTSOURCED_TOTAL.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");
        BigDecimal totalAllSalary = getCategoryValue(lstMainSalary, TOTAL_ALL.getCode(),
                SalaryStatConstant.MONTH_SALARY, "total");

        log.info("总计（全部人员）人数计算: {} + {} = {} (实际结果: {})",
                ownedTotalPersonCount, outsourcedTotalPersonCount,
                ownedTotalPersonCount.add(outsourcedTotalPersonCount), totalAllPersonCount);

        log.info("总计（全部人员）薪酬计算: {} + {} = {} (实际结果: {})",
                ownedTotalSalary, outsourcedTotalSalary, ownedTotalSalary.add(outsourcedTotalSalary), totalAllSalary);
    }

    /**
     * 获取指定类别和类型的数值
     */
    private BigDecimal getCategoryValue(List<SalaryStat> lstMainSalary, String categoryCode, String type2,
                                        String fieldName) {
        return lstMainSalary.stream()
                .filter(s -> categoryCode.equals(s.getCategoryCode()) && type2.equals(s.getType2()))
                .findFirst()
                .map(s -> {
                    try {
                        switch (fieldName) {
                            case "salaryTotal":
                                return Optional.ofNullable(s.getSalaryTotal()).orElse(BigDecimal.ZERO);
                            case "total":
                                return Optional.ofNullable(s.getTotal()).orElse(BigDecimal.ZERO);
                            default:
                                return BigDecimal.ZERO;
                        }
                    } catch (Exception e) {
                        return BigDecimal.ZERO;
                    }
                })
                .orElse(BigDecimal.ZERO);
    }

    private void setUpdateSalaryStatsField(FillSalaryStatVO fillSalaryStatVO, List<SalaryStat> lstSalary) {
        fillSalaryStatVO.getSalaryStatVOList().forEach(salaryStatVO -> {
            SalaryStat salaryStat = lstSalary.stream()
                    .filter(s -> s.getId().equals(salaryStatVO.getId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException("根据id查询薪酬统计记录不存在: " + salaryStatVO.getType1() + "-" + salaryStatVO.getType2()));
            BeanUtils.copyProperties(salaryStatVO, salaryStat);
        });
    }

    private void calculateCategories(List<SalaryStat> lstSalary) {

        calculateAndSummarizeCategory(lstSalary, OUTSOURCED_MANAGERS.getCode());
        calculateAndSummarizeCategory(lstSalary, INNER_ASSIGNED.getCode());
        calculateAndSummarizeCategory(lstSalary, HK_MONTHLY.getCode());
        calculateAndSummarizeCategory(lstSalary, HK_DAILY.getCode());
        calculateAndSummarizeCategory(lstSalary, MAINLAND.getCode());
        calculateAndSummarizeCategory(lstSalary, OVERSEAS.getCode());
        calculateAndSummarizeCategory(lstSalary, OUTSOURCED_OPERATORS.getCode());

        calculateTotalForOwned(lstSalary);
        calculateTotalForOutsourced(lstSalary);
        calculateTotalForAll(lstSalary);
        calculateForTotal(lstSalary);
    }

    /**
     * 计算基础分类，不包括汇总分类的重复计算
     * 用于 aggregateSalaryStat 方法，避免重复计算已经汇总过的数据
     *
     * @param lstSalary 薪酬统计列表
     */
    private void calculateBasicCategories(List<SalaryStat> lstSalary) {
        // 只计算基础分类的内部汇总，不重复计算已经汇总过的分类
        calculateAndSummarizeCategory(lstSalary, OUTSOURCED_MANAGERS.getCode());
        calculateAndSummarizeCategory(lstSalary, INNER_ASSIGNED.getCode());
        calculateAndSummarizeCategory(lstSalary, HK_MONTHLY.getCode());
        calculateAndSummarizeCategory(lstSalary, HK_DAILY.getCode());
        calculateAndSummarizeCategory(lstSalary, MAINLAND.getCode());
        calculateAndSummarizeCategory(lstSalary, OVERSEAS.getCode());
        calculateAndSummarizeCategory(lstSalary, OUTSOURCED_OPERATORS.getCode());

        // 注意：不调用下面这些方法，因为汇总已经在 sumSubList 中完成
        // 如果调用这些方法，会导致数据被重复累加
        // calculateTotalForOwned(lstSalary);
        // calculateTotalForOutsourced(lstSalary);
        // calculateTotalForAll(lstSalary);
        // calculateForTotal(lstSalary);
    }

    private void calculateAndSummarizeCategory(List<SalaryStat> lstSalary, String categoryCode) {
        List<SalaryStat> categoryList = lstSalary.stream().filter(s -> categoryCode.equals(s.getCategoryCode())).toList();
        calculateTotal(categoryList);
    }

    /**
     * 计算自有合计
     *
     * @param lstSalary
     */
    private void calculateTotalForOwned(List<SalaryStat> lstSalary) {
        calculateAndSumGroups(lstSalary, TOTAL_OWNED,
                List.of(INNER_ASSIGNED, HK_MONTHLY, HK_DAILY, MAINLAND, OVERSEAS));
    }

    private SalaryStat convertByRate(SalaryStat salaryStat, BigDecimal rate) {
        SalaryStat result = new SalaryStat();
        BeanUtils.copyProperties(salaryStat, result, "id", "headId", "isDeleted", "createTime", "updateTime",
                "version");

        result.setJan(salaryStat.getJan().multiply(rate));
        result.setFeb(salaryStat.getFeb().multiply(rate));
        result.setMar(salaryStat.getMar().multiply(rate));
        result.setApr(salaryStat.getApr().multiply(rate));
        result.setMay(salaryStat.getMay().multiply(rate));
        result.setJun(salaryStat.getJun().multiply(rate));
        result.setJul(salaryStat.getJul().multiply(rate));
        result.setAug(salaryStat.getAug().multiply(rate));
        result.setSep(salaryStat.getSep().multiply(rate));
        result.setOct(salaryStat.getOct().multiply(rate));
        result.setNov(salaryStat.getNov().multiply(rate));
        result.setDec(salaryStat.getDec().multiply(rate));

        result.setSalaryTotal(Optional.ofNullable(salaryStat.getSalaryTotal()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setBonusBudget(Optional.ofNullable(salaryStat.getBonusBudget()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setMarketingBonus(
                Optional.ofNullable(salaryStat.getMarketingBonus()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setContractCashed(
                Optional.ofNullable(salaryStat.getContractCashed()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setBonusTotal(Optional.ofNullable(salaryStat.getBonusTotal()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setOtherBonus(Optional.ofNullable(salaryStat.getOtherBonus()).orElse(BigDecimal.ZERO).multiply(rate));
        result.setPerformanceBonus(
                Optional.ofNullable(salaryStat.getPerformanceBonus()).orElse(BigDecimal.ZERO).multiply(rate));

        // 重新计算合计
        result.setBonusTotal(calcBonusTotal(result));
        result.setTotal(CommonUtils.add(
                CommonUtils.add(result.getSalaryTotal(), result.getBonusTotal()),
                result.getPerformanceBonus()));
        return result;
    }

    /**
     * 处理预算数据，所有的预算数据计算逻辑都在此处处理
     *
     * @param salaryStatList
     */
    private void handleBudgetTotal(List<SalaryStat> salaryStatList) {
        List<SalaryStat> budgetTotal = salaryStatList.stream().filter(s -> TOTAL.getCode().equals(s.getCategoryCode()) && Objects.equals(s.getIsBudget(), Boolean.TRUE)).toList();
        if (CollectionUtils.isNotEmpty(budgetTotal)) {
            calculateTotal(budgetTotal);
        }
    }

    /**
     * 计算外包合计
     *
     * @param lstSalary
     */
    private void calculateTotalForOutsourced(List<SalaryStat> lstSalary) {
        calculateAndSumGroups(lstSalary, OUTSOURCED_TOTAL, List.of(OUTSOURCED_MANAGERS, OUTSOURCED_OPERATORS));
    }

    /**
     * 逐项计算
     * result = a - b
     *
     * @param result
     * @param a
     * @param b
     */
    private void calculateSubtract(SalaryStat result, SalaryStat a, SalaryStat b) {
        result.setJan(a.getJan().subtract(b.getJan()));
        result.setFeb(a.getFeb().subtract(b.getFeb()));
        result.setMar(a.getMar().subtract(b.getMar()));
        result.setApr(a.getApr().subtract(b.getApr()));
        result.setMay(a.getMay().subtract(b.getMay()));
        result.setJun(a.getJun().subtract(b.getJun()));
        result.setJul(a.getJul().subtract(b.getJul()));
        result.setAug(a.getAug().subtract(b.getAug()));
        result.setSep(a.getSep().subtract(b.getSep()));
        result.setOct(a.getOct().subtract(b.getOct()));
        result.setNov(a.getNov().subtract(b.getNov()));
        result.setDec(a.getDec().subtract(b.getDec()));
    }


    /**
     * 计算总计(全部)
     * 计算公式: 总计(全部) = 自有合计 + 外包合计
     *
     * @param lstSalary 薪酬统计列表
     */
    private void calculateTotalForAll(List<SalaryStat> lstSalary) {
        calculateAndSumGroups(lstSalary, TOTAL_ALL, List.of(TOTAL_OWNED, OUTSOURCED_TOTAL));
    }


    /**
     * 计算全口径薪酬统计数据
     * 计算公式: 全口径 = 自有合计 + 外包管理人员
     *
     * @param salaryStatList 薪酬统计列表
     */
    private void calculateForTotal(List<SalaryStat> salaryStatList) {
        calculateAndSumGroups(salaryStatList, TOTAL, List.of(TOTAL_OWNED, OUTSOURCED_MANAGERS));
    }

    /**
     * 使用这个方法时，传入的参数必须要是按照列表顺序才可以
     *
     * @param salaryStatList
     */
    private void calculateTotal(List<SalaryStat> salaryStatList) {
        if (CollectionUtils.isEmpty(salaryStatList)) {
            return;
        }

        Optional<SalaryStat> personCountOpt = salaryStatList.stream()
                .filter(s -> SalaryStatConstant.MONTH_PERSON_COUNT.equals(s.getType2())).findFirst();
        Optional<SalaryStat> personAvgOpt = salaryStatList.stream()
                .filter(s -> SalaryStatConstant.ACCUMULATIVE_PERSON_COUNT.equals(s.getType2())).findFirst();

        if (personCountOpt.isPresent() && personAvgOpt.isPresent()) {
            calculateAveragePersonCount(personCountOpt.get(), personAvgOpt.get());
        }

        Optional<SalaryStat> monthSalaryOpt = salaryStatList.stream()
                .filter(s -> SalaryStatConstant.MONTH_SALARY.equals(s.getType2())).findFirst();
        Optional<SalaryStat> accumulativeSalaryOpt = salaryStatList.stream()
                .filter(s -> SalaryStatConstant.ACCUMULATIVE_SALARY.equals(s.getType2())).findFirst();

        if (monthSalaryOpt.isPresent() && accumulativeSalaryOpt.isPresent()) {
            calculateAccumulativeSalary(monthSalaryOpt.get(), accumulativeSalaryOpt.get());
        }
    }

    private void sumBySubList(List<SalaryStat> totalList, List<List<SalaryStat>> subLists) {
        // Find the '当月人数' row in the total list
        totalList.stream()
                .filter(s -> SalaryStatConstant.MONTH_PERSON_COUNT.equals(s.getType2()))
                .findFirst()
                .ifPresent(monthPersonCountRow -> {
                    // Collect all '当月人数' rows from sublists
                    List<SalaryStat> subPersonCounts = subLists.stream()
                            .flatMap(List::stream)
                            .filter(s -> SalaryStatConstant.MONTH_PERSON_COUNT.equals(s.getType2()))
                            .toList();
                    if (CollectionUtils.isNotEmpty(subPersonCounts)) {
                        calculateSumByMonth(monthPersonCountRow, subPersonCounts);
                    }
                });

        // Find the '当月薪酬' row in the total list
        totalList.stream()
                .filter(s -> SalaryStatConstant.MONTH_SALARY.equals(s.getType2()))
                .findFirst()
                .ifPresent(monthSalaryRow -> {
                    // Collect and process all '当月薪酬' rows from sublists
                    List<SalaryStat> subSalaries = subLists.stream()
                            .map(sublist -> {
                                // For each sublist, find the salary and currency info
                                Optional<SalaryStat> salaryOpt = sublist.stream()
                                        .filter(s -> SalaryStatConstant.MONTH_SALARY.equals(s.getType2()))
                                        .findFirst();
                                Optional<SalaryStat> currencyInfoOpt = sublist.stream()
                                        .filter(s -> SalaryStatConstant.ACCUMULATIVE_SALARY.equals(s.getType2()))
                                        .findFirst();

                                if (salaryOpt.isPresent() && currencyInfoOpt.isPresent()) {
                                    SalaryStat salaryStat = salaryOpt.get();
                                    if (StringUtils.equalsIgnoreCase(salaryStat.getCurrency(), "人民币")) {
                                        return salaryStat;
                                    } else {
                                        BigDecimal rate = BigDecimal.ONE;
                                        try {
                                            rate = new BigDecimal(currencyInfoOpt.get().getCurrency());
                                        } catch (Exception e) {
                                            log.error("转换汇率出错", e);
                                        }
                                        return convertByRate(salaryStat, rate);
                                    }
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .toList();

                    if (CollectionUtils.isNotEmpty(subSalaries)) {
                        calculateSumByMonth(monthSalaryRow, subSalaries);
                    }

                    // 先对所有子项和总计项进行计算，确保获取到最终的Total值
                    calculateTotal(totalList);
                    subSalaries.forEach(s -> calculateAccumulativeSalary(s, new SalaryStat())); // 计算Total

                    // 记录日志
                    String calculationDetails = subSalaries.stream()
                            .map(s -> String.format("%s(%.2f)", s.getType1(), s.getTotal()))
                            .collect(Collectors.joining(" + "));
                    log.info("计算 {}: {} = {}", monthSalaryRow.getType1(), calculationDetails,
                            monthSalaryRow.getTotal());
                });

        calculateTotal(totalList);
    }

    private void calculateAndSumGroups(List<SalaryStat> allStats, SalaryCategoryEnum totalGroup,
                                       List<SalaryCategoryEnum> subGroups) {
        List<SalaryStat> totalList = allStats.stream()
                .filter(s -> totalGroup.getCode().equals(s.getCategoryCode()))
                .toList();

        if (CollectionUtils.isEmpty(totalList)) {
            return;
        }

        List<List<SalaryStat>> subLists = subGroups.stream()
                .map(subGroup -> allStats.stream()
                        .filter(s -> subGroup.getCode().equals(s.getCategoryCode()))
                        .toList())
                .filter(CollectionUtils::isNotEmpty)
                .toList();

        if (CollectionUtils.isNotEmpty(subLists)) {
            sumBySubList(totalList, subLists);
        }
    }

    /**
     * 查询薪酬统计列表
     * 会根据查询条件查询出头表记录，然后根据头表id查询明细记录
     * 根据用户角色来返回查询结果，如果当前用户拥有
     *
     * @param salaryStatQO 查询条件
     * @return
     */
    public EditSalaryStatVO getSalaryStat(SalaryStatQO salaryStatQO) {
        // 检查年份和组织编码是否存在
        ServiceHelper.checkExist(salaryStatQO.getYear(), "年份不能为空");
        ServiceHelper.checkExist(salaryStatQO.getOrganizationCode(), "组织编码不能为空");

        // 查找对应的薪资统计头信息
        SalaryStatHead head = findOrCreateSalaryStat(salaryStatQO);

        // 上年薪酬统计头表记录
        SalaryStatQO lastYearQO = new SalaryStatQO(salaryStatQO.getYear() - 1, salaryStatQO.getOrganizationCode(), null);
        SalaryStatHead lastYearHead = findOrCreateSalaryStat(lastYearQO);

        // 准备返回结果
        EditSalaryStatVO result = new EditSalaryStatVO();
        result.setYear(head.getYear());
        result.setOrganizationCode(head.getOrganizationCode());

        List<SalaryStat> lstSalaryStat = salaryStatService.listForQueryPageByHeadId(head.getId(), null, defaultPayGroupId);
        result.setSalaryStatVOList(lstSalaryStat.stream().filter(s -> !s.getIsBudget()).map(SalaryStatVO::fromModel).toList());
        result.setSalaryBudgetList(lstSalaryStat.stream().filter(SalaryStat::getIsBudget).map(SalaryStatVO::fromModel).toList());

        List<SalaryStatVO> lastYearSalaryList = salaryStatService.listForQueryPageByHeadId(lastYearHead.getId(), null, defaultPayGroupId).stream().map(SalaryStatVO::fromModel).toList();
        result.setLastYearSalaryList(lastYearSalaryList.stream().filter(s -> !s.getIsBudget()).toList());
        return result;
    }

    /**
     * 计算 当月人数 和 累计平均人数 行的汇总数
     * 累计平均人数行是根据当月人数行计算出来的
     *
     * @param ssPersonCount 当月人数
     * @param ssPersonAvg   累计平均人数
     */
    private void calculateAveragePersonCount(SalaryStat ssPersonCount, SalaryStat ssPersonAvg) {
        BigDecimal sum = BigDecimal.ZERO;
        for (int i = 0; i < 12; i++) {
            // 计算1-12月人数累计数
            sum = CommonUtils.add(sum, getMonthValue(ssPersonCount, i));
            // 计算平设置累计平均人数
            setMonthValue(ssPersonAvg, i, sum.divide(BigDecimal.valueOf(i + 1), 2, RoundingMode.HALF_UP));
        }
        // 设置当月人数的累计数：这个人数似乎没有任何意义，取消设置
        ssPersonCount.setSalaryTotal(sum);
        // 奖金数等字段也没有计算的必要
        Optional.ofNullable(ssPersonCount.getMarketingBonus())
                .ifPresent(bonus -> ssPersonCount.setBonusTotal(calcBonusTotal(ssPersonCount)));
        // 2024-08-13 新需求，总数等于工资性人数小计
        ssPersonCount.setTotal(sum);

    }

    /**
     * 计算薪酬发放行和 本年度累计 行的汇总数
     *
     * @param salaryVO             当月发放金额
     * @param accumulativeSalaryVO 本年度累计
     */
    private void calculateAccumulativeSalary(SalaryStat salaryVO, SalaryStat accumulativeSalaryVO) {
        BigDecimal sum = BigDecimal.ZERO;
        for (int i = 0; i < 12; i++) {
            // 累计发放金额
            sum = sum.add(getMonthValue(salaryVO, i));
            // 设置累计行每月的累计值
            setMonthValue(accumulativeSalaryVO, i, sum);
        }
        // 当月发放金额行，需要进行汇总值计算
        salaryVO.setSalaryTotal(sum);
        // 设置奖金总计
        BigDecimal bonusTotal = CommonUtils.add(salaryVO.getMarketingBonus(), salaryVO.getContractCashed());
        salaryVO.setBonusTotal(bonusTotal);
        // 总额 = 薪酬合计 + 奖金合计 + 绩效奖金(performanceBonus) + 其他奖金(otherBonus)
        salaryVO.setTotal(
                CommonUtils.add(
                        CommonUtils.add(salaryVO.getSalaryTotal(), salaryVO.getBonusTotal()),
                        Optional.ofNullable(salaryVO.getPerformanceBonus()).orElse(BigDecimal.ZERO)));

        // 设置 本年度累计 行汇总数
        accumulativeSalaryVO.setSalaryTotal(accumulativeSalaryVO.getDec());
        accumulativeSalaryVO.setMarketingBonus(salaryVO.getMarketingBonus());
        accumulativeSalaryVO.setContractCashed(salaryVO.getContractCashed());
        accumulativeSalaryVO.setBonusTotal(bonusTotal);
        accumulativeSalaryVO.setTotal(
                CommonUtils.add(
                        CommonUtils.add(accumulativeSalaryVO.getSalaryTotal(),
                                accumulativeSalaryVO.getBonusTotal()),
                        Optional.ofNullable(accumulativeSalaryVO.getPerformanceBonus()).orElse(BigDecimal.ZERO)));
    }

    private void setMonthValue(SalaryStat salaryStat, int monthIndex, BigDecimal value) {
        switch (monthIndex) {
            case 0 -> salaryStat.setJan(value);
            case 1 -> salaryStat.setFeb(value);
            case 2 -> salaryStat.setMar(value);
            case 3 -> salaryStat.setApr(value);
            case 4 -> salaryStat.setMay(value);
            case 5 -> salaryStat.setJun(value);
            case 6 -> salaryStat.setJul(value);
            case 7 -> salaryStat.setAug(value);
            case 8 -> salaryStat.setSep(value);
            case 9 -> salaryStat.setOct(value);
            case 10 -> salaryStat.setNov(value);
            case 11 -> salaryStat.setDec(value);
            default -> throw new IllegalArgumentException("Invalid month index: " + monthIndex);
        }
    }


    private void saveSalaryStats(String headId, List<SalaryStat> salaryStatList) {
        if (CollectionUtils.isEmpty(salaryStatList)) {
            return;
        }
        salaryStatList.forEach(salaryStat -> {
            salaryStat.setHeadId(headId);
        });

        salaryStatService.updateBatchById(salaryStatList);
    }

    private SalaryStatHead createNewSalaryStatHead(String organizationCode, Integer year) {
        SalaryStatHead head = generateLocalHead(organizationCode, year);
        salaryStatHeadService.save(head);
        return head;
    }

    /**
     * 查询当前用户在薪酬填报模块拥有填报权限的组织机构编码列表
     *
     * @return 当前用户拥有填报权限的组织编码列表（薪酬）
     */
    public List<String> listCurrentUsersOrgCodes() {
        // 先查询用户拥有的薪酬小组
        List<String> payGroupIds = payGroupUserService.listGroupIdByUserId(ContextUtils.getCurrentUser().getId());
        // 通过小组查询对应的组织机构
        return payGroupOrgService.listOrgCodeByPayGroupIdList(payGroupIds);
    }

    public List<PayGroupVO> listCurrentUserPayGroups(String organizationCode) {

        List<PayGroup> payGroupList = payGroupService.selectPayGroupByOrgCodeAndUserId(organizationCode, ContextUtils.getCurrentUser().getId());
        if (CollectionUtils.isEmpty(payGroupList)) {
            return new ArrayList<>();
        }
        return payGroupList.stream().map(PayGroupVO::fromPayGroup).toList();
    }

    /**
     * 重新计算指定组织和年份下所有薪酬小组的薪酬统计数据
     *
     * @param organizationCode 组织机构编码
     * @param year             年份
     */
    @Transactional
    public void recalculateSalaryStatForAllPayGroups(String organizationCode, Integer year) {
        // 1. 获取薪酬统计头表
        SalaryStatHead head = salaryStatHeadService.findByYearAndOrg(year, organizationCode);
        if (head == null) {
            log.warn("未找到薪酬统计头表，组织: {}, 年份: {}. 跳过重新计算.", organizationCode, year);
            return;
        }

        // 2. 获取该头表下所有的薪酬统计记录
        LambdaQueryWrapper<SalaryStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SalaryStat::getHeadId, head.getId()).eq(SalaryStat::getIsDeleted, false);
        List<SalaryStat> allStats = salaryStatService.list(queryWrapper);

        if (CollectionUtils.isEmpty(allStats)) {
            log.warn("未找到薪酬统计记录，头表ID: {}. 跳过重新计算.", head.getId());
            return;
        }

        // 3. 提取所有不重复的薪酬小组ID, 并排除默认的汇总ID
        List<String> payGroupIds = allStats.stream()
                .map(SalaryStat::getPayGroupId)
                .filter(StringUtils::isNotEmpty)
                .filter(id -> !Objects.equals(id, defaultPayGroupId))
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(payGroupIds)) {
            log.warn("在头表ID: {} 下未找到任何需要计算的薪酬小组.", head.getId());
            return;
        }

        // 4. 从数据库中筛选出未被删除的薪酬小组
        LambdaQueryWrapper<PayGroup> payGroupQuery = new LambdaQueryWrapper<>();
        payGroupQuery.in(PayGroup::getId, payGroupIds).eq(PayGroup::getIsDeleted, false);
        List<String> activePayGroupIds = payGroupService.list(payGroupQuery).stream()
                .map(PayGroup::getId)
                .toList();

        if (CollectionUtils.isEmpty(activePayGroupIds)) {
            log.warn("未找到任何有效的薪酬小组，头表ID: {}. 跳过重新计算.", head.getId());
            return;
        }

        // 5. 遍历每个有效的薪酬小组，重新计算其数据
        for (String payGroupId : activePayGroupIds) {
            List<SalaryStat> statsForPayGroup = allStats.stream()
                    .filter(s -> payGroupId.equals(s.getPayGroupId()))
                    .toList();

            // 将数据分为预算和非预算两部分
            List<SalaryStat> budgetList = statsForPayGroup.stream()
                    .filter(s -> Objects.equals(s.getIsBudget(), Boolean.TRUE))
                    .toList();
            List<SalaryStat> notBudgetList = statsForPayGroup.stream()
                    .filter(s -> !Objects.equals(s.getIsBudget(), Boolean.TRUE))
                    .toList();

            // 分别处理预算和非预算数据的计算
            if (CollectionUtils.isNotEmpty(budgetList)) {
                handleBudgetTotal(budgetList);
            }
            if (CollectionUtils.isNotEmpty(notBudgetList)) {
                calculateCategories(notBudgetList);
            }
        }

        // 6. 批量更新所有修改后的数据
        if (CollectionUtils.isNotEmpty(allStats)) {
            saveSalaryStats(head.getId(), allStats);
            log.info("成功为组织: {}, 年份: {} 重新计算并更新了所有薪酬小组的薪酬统计数据", organizationCode, year);
        }
    }

}
