package com.csci.hrrs.facade;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.model.BaseStatInfoDetail;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.service.*;
import com.csci.hrrs.vo.BaseStatInfoHeadVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.csci.hrrs.service.ServiceHelper.checkExist;

/**
 * 基础统计信息汇总
 */
@Component
@LogMethod
public class BaseStatInfoAggregateFacade {

    @Resource
    private BaseStatInfoHeadService baseStatInfoHeadService;

    @Resource
    private BaseStatInfoDetailService baseStatInfoDetailService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private BaseHeadSubmitStatusService baseHeadSubmitStatusService;

    @Resource
    private WorkflowControlService workflowControlService;

    /**
     * 汇总人力基础统计信息
     *
     * @param headId 头id
     * @param lastUpdateVersion 最后更新版本
     * @param withUnSubmitted 是否包含未提交的数据
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String aggregate(String headId, Integer lastUpdateVersion, boolean withUnSubmitted) {
        // 验证参数
        checkExist(headId, "headId不能为空");
        checkExist(lastUpdateVersion, "lastUpdateVersion不能为空");

        workflowControlService.throwIfHasWorkflowControl(headId, null);

        // 第一步，删除原有数据
        BaseStatInfoHeadVO existedVO = Optional.ofNullable(baseStatInfoHeadService.selectById(headId)).orElseThrow(() -> new ServiceException("未找到"));

        baseStatInfoHeadService.deleteById(headId, lastUpdateVersion);

        // 第二步，重新统计数据
        BaseStatInfoHeadVO newVO = baseStatInfoHeadService.getOrInit(existedVO.getOrganizationCode(), existedVO.getStatDate().getYear(), existedVO.getStatDate().getMonthValue());
        Organization organization = organizationService.getByCode(existedVO.getOrganizationCode());
        List<String> lstSubOrgHeadIds;
        if (withUnSubmitted) {
            lstSubOrgHeadIds = baseStatInfoHeadService.listSubOrgHeadIds(organization.getCode(), newVO.getStatDate());
        } else {
            lstSubOrgHeadIds = baseHeadSubmitStatusService.selectAllSubmittedHeadIdsOfSubOrgs(organization.getId(), existedVO.getStatDate(), BaseHeadSubmitStatusService.TableKey.base_stat_info);
        }

        if (CollectionUtils.isEmpty(lstSubOrgHeadIds)) {
            return newVO.getId();
        }
        List<BaseStatInfoDetail> lstDetails = baseStatInfoDetailService.selectByHeadIds(lstSubOrgHeadIds);
        // Map<String, List<BaseStatInfoDetail>> map = lstDetails.stream().collect(Collectors.groupingBy(BaseStatInfoDetail::getPlatformCode));

        // List<BaseStatInfoDetailVO> lstDetailVO = BaseStatInfoDetailService.listStatInfoDetail(newVO.getId());

        List<BaseStatInfoDetail> lstDetail = new ArrayList<>();

        int seq = 1;
        for (BaseStatInfoDetail existedDetail : lstDetails) {
            BaseStatInfoDetail detail = new BaseStatInfoDetail();
            BeanUtils.copyProperties(existedDetail, detail);
            detail.setHeadId(newVO.getId());
            detail.setSeq(seq++);
            detail.setId(null);
            detail.setCreateUserId(null);
            detail.setCreateUsername(null);
            detail.setCreationTime(null);

            lstDetail.add(detail);

        }

        // 拿出所有公司小计的行
        if (StringUtils.equals(organization.getCode(), "00100001")) {
            List<BaseStatInfoDetail> lstSubTotal = lstDetail.stream().filter(detail -> detail.getSubOrgName().equals("公司小计")).toList();
            BaseStatInfoDetail total = summarizeAll(lstSubTotal);
            total.setHeadId(newVO.getId());
            total.setSeq(seq);
            lstDetail.add(total);
        }

        baseStatInfoDetailService.saveBatch(lstDetail);

        return newVO.getId();
    }

    private BaseStatInfoDetail summarizeAll(List<BaseStatInfoDetail> detailList) {
        BaseStatInfoDetail result = new BaseStatInfoDetail();
        result.setSubOrgName("总计");
        // 所有的字段都要汇总
        result.setEoyHeadCount(detailList.stream().mapToInt(BaseStatInfoDetail::getEoyHeadCount).sum());
        result.setEomHeadCount(detailList.stream().mapToInt(BaseStatInfoDetail::getEomHeadCount).sum());
        result.setCurrentHeadCount(detailList.stream().mapToInt(BaseStatInfoDetail::getCurrentHeadCount).sum());
        result.setNetIncreaseFlm(detailList.stream().mapToInt(BaseStatInfoDetail::getNetIncreaseFlm).sum());
        result.setIncreaseRateFlm(detailList.stream().map(BaseStatInfoDetail::getIncreaseRateFlm).reduce(BigDecimal.ZERO, CommonUtils::add));
        result.setNetIncreaseFly(detailList.stream().mapToInt(BaseStatInfoDetail::getNetIncreaseFly).sum());
        result.setIncreaseRateFly(detailList.stream().map(BaseStatInfoDetail::getIncreaseRateFly).reduce(BigDecimal.ZERO, CommonUtils::add));
        result.setMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgmtCount).sum());
        result.setMldMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMldMgmtCount).sum());
        result.setHkAssignedMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getHkAssignedMgmtCount).sum());
        result.setHkLocalMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getHkLocalMgmtCount).sum());
        result.setMcAssignedMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMcAssignedMgmtCount).sum());
        result.setMcLocalMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMcLocalMgmtCount).sum());
        result.setOvsAssignedMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOvsAssignedMgmtCount).sum());
        result.setOvsLocalMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOvsLocalMgmtCount).sum());
        result.setNonMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getNonMgmtCount).sum());
        result.setMldNonMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMldNonMgmtCount).sum());
        result.setHkNonMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getHkNonMgmtCount).sum());
        result.setMcNonMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMcNonMgmtCount).sum());
        result.setOvsNonMgmtCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOvsNonMgmtCount).sum());
        result.setCompContrCount(detailList.stream().mapToInt(BaseStatInfoDetail::getCompContrCount).sum());
        result.setMonWageEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMonWageEmpCount).sum());
        result.setDailyWageEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getDailyWageEmpCount).sum());
        result.setOtherCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOtherCount).sum());
        result.setAssisGenMgrAndAbvCount(detailList.stream().mapToInt(BaseStatInfoDetail::getAssisGenMgrAndAbvCount).sum());
        result.setSeniorMgrCount(detailList.stream().mapToInt(BaseStatInfoDetail::getSeniorMgrCount).sum());
        result.setManagerCount(detailList.stream().mapToInt(BaseStatInfoDetail::getManagerCount).sum());
        result.setDptMgrCount(detailList.stream().mapToInt(BaseStatInfoDetail::getDptMgrCount).sum());
        result.setAssisMgrCount(detailList.stream().mapToInt(BaseStatInfoDetail::getAssisMgrCount).sum());
        result.setOtherPosCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOtherPosCount).sum());
        result.setMldEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMldEmpCount).sum());
        result.setHkEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getHkEmpCount).sum());
        result.setMacauEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMacauEmpCount).sum());
        result.setOvsEmpCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOvsEmpCount).sum());
        result.setIntTransCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getIntTransCount()) ? x.getIntTransCount() : 0).sum());
        // result.setCpsRecruCount(detailList.stream().mapToInt(BaseStatInfoDetail::getCpsRecruCount).sum());
        result.setCpsRecruCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getCpsRecruCount()) ? x.getCpsRecruCount() : 0).sum());
        // result.setExtRecruCount(detailList.stream().mapToInt(BaseStatInfoDetail::getExtRecruCount).sum());
        result.setExtRecruCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getExtRecruCount()) ? x.getExtRecruCount() : 0).sum());
        // result.setIntTransOutCount(detailList.stream().mapToInt(BaseStatInfoDetail::getIntTransOutCount).sum());
        result.setIntTransOutCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getIntTransOutCount()) ? x.getIntTransOutCount() : 0).sum());
        // result.setVolunResignCount(detailList.stream().mapToInt(BaseStatInfoDetail::getVolunResignCount).sum());
        result.setVolunResignCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getVolunResignCount()) ? x.getVolunResignCount() : 0).sum());
        // result.setRetireCount(detailList.stream().mapToInt(BaseStatInfoDetail::getRetireCount).sum());
        result.setRetireCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getRetireCount()) ? x.getRetireCount() : 0).sum());
        // result.setOptEliCount(detailList.stream().mapToInt(BaseStatInfoDetail::getOptEliCount).sum());
        result.setOptEliCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getOptEliCount()) ? x.getOptEliCount() : 0).sum());
        result.setYtdAttritionRate(detailList.stream().map(BaseStatInfoDetail::getYtdAttritionRate).reduce(BigDecimal.ZERO, CommonUtils::add));
        // result.setMgrIntTransCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrIntTransCount).sum());
        result.setMgrIntTransCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrIntTransCount()) ? x.getMgrIntTransCount() : 0).sum());
        // result.setMgrCpsRecruCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrCpsRecruCount).sum());
        result.setMgrCpsRecruCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrCpsRecruCount()) ? x.getMgrCpsRecruCount() : 0).sum());
        // result.setMgrExtRecruCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrExtRecruCount).sum());
        result.setMgrExtRecruCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrExtRecruCount()) ? x.getMgrExtRecruCount() : 0).sum());
        // result.setMgrIntTransOutCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrIntTransOutCount).sum());
        result.setMgrIntTransOutCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrIntTransOutCount()) ? x.getMgrIntTransOutCount() : 0).sum());
        // result.setMgrVolunResignCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrVolunResignCount).sum());
        result.setMgrVolunResignCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrVolunResignCount()) ? x.getMgrVolunResignCount() : 0).sum());
        // result.setMgrRetireCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrRetireCount).sum());
        result.setMgrRetireCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrRetireCount()) ? x.getMgrRetireCount() : 0).sum());
        // result.setMgrOptEliCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrOptEliCount).sum());
        result.setMgrOptEliCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrOptEliCount()) ? x.getMgrOptEliCount() : 0).sum());
        result.setMgrYtdAttritionRate(detailList.stream().map(BaseStatInfoDetail::getMgrYtdAttritionRate).reduce(BigDecimal.ZERO, CommonUtils::add));
        // result.setEoyOtsdCount(detailList.stream().mapToInt(BaseStatInfoDetail::getEoyOtsdCount).sum());
        result.setEoyOtsdCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getEoyOtsdCount()) ? x.getEoyOtsdCount() : 0).sum());
        // result.setEomOtsdCount(detailList.stream().mapToInt(BaseStatInfoDetail::getEomOtsdCount).sum());
        result.setEomOtsdCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getEomOtsdCount()) ? x.getEomOtsdCount() : 0).sum());
        // result.setCurrOtsdCount(detailList.stream().mapToInt(BaseStatInfoDetail::getCurrOtsdCount).sum());
        result.setCurrOtsdCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getCurrOtsdCount()) ? x.getCurrOtsdCount() : 0).sum());
        result.setOtsdIncTlm(detailList.stream().map(BaseStatInfoDetail::getOtsdIncTlm).reduce(BigDecimal.ZERO, CommonUtils::add));
        result.setOtsdIncTly(detailList.stream().map(BaseStatInfoDetail::getOtsdIncTly).reduce(BigDecimal.ZERO, CommonUtils::add));
        // result.setMissOrDead(detailList.stream().mapToInt(BaseStatInfoDetail::getMissOrDead).sum());
        result.setMissOrDead(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMissOrDead()) ? x.getMissOrDead() : 0).sum());
        // result.setMgrMissOrDead(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrMissOrDead).sum());
        result.setMgrMissOrDead(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrMissOrDead()) ? x.getMgrMissOrDead() : 0).sum());
        // result.setMgrLastMonthCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrLastMonthCount).sum());
        result.setMgrLastMonthCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrLastMonthCount()) ? x.getMgrLastMonthCount() : 0).sum());
        // result.setMgrLastYearCount(detailList.stream().mapToInt(BaseStatInfoDetail::getMgrLastYearCount).sum());
        result.setMgrLastYearCount(detailList.stream().mapToInt(x -> Objects.nonNull(x.getMgrLastYearCount()) ? x.getMgrLastYearCount() : 0).sum());

        return result;
    }

}
