package com.csci.hrrs.facade.fis;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.common.util.HttpSimulator;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.model.fis.RawRevenueSite;
import com.csci.hrrs.service.RawRevenueSiteService;
import com.csci.hrrs.vo.output.efficiency.ManualOutputVO;
import com.csci.hrrs.vo.output.fis.RequestOutputVO;
import com.csci.hrrs.vo.output.fis.ResponseOutputVO;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName: OutputFACADE
 * @Auther: <EMAIL>
 * @Date: 2025/6/17 上午9:50
 * @Description:
 */
@Component
@LogMethod
public class OutputFacade {
    @Value("${fis.output.value.url}")
    private String outputValueUrl;
    @Value("${fis.auth.key}")
    private String authKey;
    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private RawRevenueSiteService rawRevenueSiteService;

    /**
     * 财务-工程收入-每月发生数
     *
     * @param projectIds
     * @param startDate
     * @param endDate
     */
    public List<ResponseOutputVO> fisOutValue(String projectIds, String startDate, String endDate) {
        RequestOutputVO params = new RequestOutputVO();
        params.setProjectIds(projectIds);
        params.setStartDate(startDate);
        params.setEndDate(endDate);

        Map<String, String> headers = new HashMap<>(1);
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.put("x-auth-token", authKey);

        String response = HttpSimulator.sendPostRequest(outputValueUrl, gson.toJson(params), headers);
        JsonObject jsonResponse = gson.fromJson(response, JsonObject.class);
        if (jsonResponse.get("code").getAsInt() == 0) {
            JsonArray jsonArray = jsonResponse.getAsJsonArray("list");
            Type listType = new TypeToken<List<ResponseOutputVO>>() {
            }.getType();
            return gson.fromJson(jsonArray, listType);
        }
        return null;
    }

    /**
     * 手工导入-工程公司汇总数产值
     *
     * @param siteCodes
     * @param startDate
     * @param endDate
     * @return
     */
    public List<ManualOutputVO> manualOutValue(List<String> siteCodes, String startDate, String endDate) {
        List<RawRevenueSite> rawRevenueSites = rawRevenueSiteService.listFullRawRevenueBySiteCodeAndDate(siteCodes, startDate, endDate);
        // 2. 分组汇总逻辑
        Map<String, ManualOutputVO> resultMap = rawRevenueSites.stream()
                .filter(Objects::nonNull) // 过滤空值
                .collect(Collectors.groupingBy(
                        // 分组键：siteCode + yearMonth
                        entity -> entity.getSiteCode() + "_" +
                                String.format("%04d-%02d", entity.getYear(), entity.getMonth()),

                        // 下游收集器：合并同组数据
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 创建VO对象
                                    RawRevenueSite first = list.get(0);
                                    ManualOutputVO vo = new ManualOutputVO();
                                    vo.setSiteCode(first.getSiteCode());
                                    vo.setSiteName(first.getSiteName());
                                    vo.setYearMonth(String.format("%04d-%02d", first.getYear(), first.getMonth()));

                                    // 累加amountYear
                                    BigDecimal sum = list.stream()
                                            .map(RawRevenueSite::getAmountYear)
                                            .filter(Objects::nonNull)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    vo.setManualAmount(sum);

                                    return vo;
                                }
                        )
                ));

        // 3. 返回结果列表
        return new ArrayList<>(resultMap.values());
    }
}
