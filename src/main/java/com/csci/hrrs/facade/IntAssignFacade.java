package com.csci.hrrs.facade;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.hk.HKDashFacade;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.qo.HKDashQO;
import com.csci.hrrs.qo.IntAssignMajorQO;
import com.csci.hrrs.service.IntAssignService;
import com.csci.hrrs.service.StaffCountFullService;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.vo.*;
import com.csci.hrrs.vo.subsidiary.RosterDetailCategoryVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@LogMethod
public class IntAssignFacade {

    private final List<String> sortAgeList = Arrays.asList("30岁以下", "30-39岁", "40-49岁", "50-59岁");
    private final List<String> sortListCohlSeniority = Arrays.asList("1年以下", "1-3年", "3-10年", "10-20年", "20年以上");
    private final List<String> sortListPositionLevel = Arrays.asList("总经理及以上", "副总/助总", "高级经理/经理", "副经理/助理经理",
            "业务主任");
    private final List<String> sortListPositionLevelBusi = Arrays.asList("总经理及以上", "副总/助总", "高级/地盘经理/经理/副经理(主持)",
            "高级/业务经理", "副经理/助理经理", "地盘工程师");
    private final List<String> sortListSubsidiary = Arrays.asList("房屋公司", "土木公司", "中建医疗公司", "基础公司", "机电公司");
    @Resource
    private IntAssignService intAssignService;
    @Resource
    private StaffCountFullService staffCountFullService;

    /**
     * 获取内派员工面板概况
     *
     * @return com.csci.hrrs.vo.IntAssignOverviewVO
     */
    @Deprecated
    public IntAssignOverviewVO getIntAssignOverviewOld(HKDashQO qo) {
        // Old implementation kept for reference
        throw new UnsupportedOperationException("Please use getIntAssignOverview instead");
    }

    /**
     * 获取内派员工面板概况
     * 优化版本: 合并多次数据库查询为一次，使用Map存储中间结果减少重复计算
     *
     * @param qo 查询参数
     * @return IntAssignOverviewVO 内派员工概况
     */
    public IntAssignOverviewVO getIntAssignOverview(HKDashQO qo) {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate firstDayOfMonth = statDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = statDate.withDayOfMonth(statDate.lengthOfMonth());

        IntAssignOverviewVO intAssignOverviewVO = new IntAssignOverviewVO();

        // 合并所有统计查询
        List<NameCountVO> lstIntAssign = intAssignService.selectIntAssignCount(qo.getOrganizationCodeList(),
                firstDayOfMonth, lastDayOfMonth);
        List<NameCountVO> lstHzz = intAssignService.selectHzzCount(qo.getOrganizationCodeList(),
                firstDayOfMonth,
                lastDayOfMonth);
        List<NameCountVO> lstTechOrWorker = intAssignService.selectTechOrWorkerCount(
                qo.getOrganizationCodeList(),
                firstDayOfMonth, lastDayOfMonth);
        List<NameCountVO> lstDeptBusi = intAssignService.selectDeptBusiCount(qo.getOrganizationCodeList(),
                firstDayOfMonth, lastDayOfMonth);
        List<NameCountVO> lstEngCompanyDeptOrSite = intAssignService
                .selectEngCompanyDeptOrSiteCount(qo.getOrganizationCodeList(), firstDayOfMonth,
                        lastDayOfMonth);

        // 计算所有统计数据
        Map<String, Long> combinedMap = new HashMap<>();
        combineCounts(lstIntAssign, combinedMap);
        combineCounts(lstHzz, combinedMap);
        combineCounts(lstTechOrWorker, combinedMap);
        combineCounts(lstDeptBusi, combinedMap);
        combineCounts(lstEngCompanyDeptOrSite, combinedMap);

        long totalCount = combinedMap.values().stream().reduce(0L, Long::sum);
        long intAssignCount = combinedMap.getOrDefault("intAssign", 0L);

        // 设置数据到视图对象
        intAssignOverviewVO.setIntAssignCount(intAssignCount);
        intAssignOverviewVO.setIntAssignRate(DemoUtils.divide(intAssignCount, totalCount));
        intAssignOverviewVO.setHzzCount(combinedMap.getOrDefault("hzz", 0L));
        intAssignOverviewVO.setHzzRate(DemoUtils.divide(combinedMap.getOrDefault("hzz", 0L), intAssignCount));
        intAssignOverviewVO.setManagerCount(
                intAssignService.selectManagerCount(qo.getOrganizationCodeList(), firstDayOfMonth,
                        lastDayOfMonth));
        intAssignOverviewVO
                .setMechanicTechCount(combinedMap.getOrDefault("tech", 0L)
                        + combinedMap.getOrDefault("worker", 0L));
        intAssignOverviewVO.setWorkerCount(0L);
        intAssignOverviewVO.setManagerDeptCount(combinedMap.getOrDefault("dept", 0L));
        intAssignOverviewVO.setManagerDeptRate(
                DemoUtils.divide(intAssignOverviewVO.getManagerDeptCount(),
                        intAssignOverviewVO.getManagerCount()));
        intAssignOverviewVO.setManagerBusiCount(combinedMap.getOrDefault("busi", 0L));
        intAssignOverviewVO.setOfficeCount(combinedMap.getOrDefault("office", 0L));
        intAssignOverviewVO.setSiteCount(combinedMap.getOrDefault("site", 0L));
        intAssignOverviewVO
                .setEngCompanyCount(intAssignOverviewVO.getOfficeCount()
                        + intAssignOverviewVO.getSiteCount());
        intAssignOverviewVO.setZzxHaiHongCount(
                intAssignService.selectZZXHaihongCount(qo.getOrganizationCodeList(), firstDayOfMonth,
                        lastDayOfMonth));
        intAssignOverviewVO.setJiXieCount(
                intAssignService.selectJiXieCount(qo.getOrganizationCodeList(), firstDayOfMonth,
                        lastDayOfMonth));

        return intAssignOverviewVO;
    }

    private void combineCounts(List<NameCountVO> list, Map<String, Long> map) {
        for (NameCountVO nameCountVO : list) {
            map.merge(nameCountVO.getName(), nameCountVO.getCount(), Long::sum);
        }
    }

    public List<NameCountRateVO> distributeBySchoolType(HKDashQO qo) {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());

        List<NameCountVO> lstNameCount = intAssignService.selectDistrBySchoolType(qo.getOrganizationCodeList(),
                startDate, endDate);
        // long totalCount = lstNameCount.stream().map(NameCountVO::getCount).reduce(0L,
        // Long::sum);
        List<NameCountRateVO> lstResult = HKRecruitFacade.convertToNameCountRateVOS(lstNameCount);
        // 根据count由大到小排序
        lstResult.sort(Comparator.comparing(NameCountRateVO::getCount).reversed());
        return lstResult;
    }

    public AgeDistrVO distributeByAge(HKDashQO qo) {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());

        List<NameCountVO> lstNameCount = intAssignService.selectDistrByAge(qo.getOrganizationCodeList(),
                startDate,
                endDate);

        // 计算总数
        long totalCount = lstNameCount.stream().mapToLong(NameCountVO::getCount).sum();

        // 转换并计算比例，避免多次遍历
        List<NameCountRateVO> lstNameCountRate = lstNameCount.stream()
                .map(nameCountVO -> {
                    NameCountRateVO nameCountRateVO = new NameCountRateVO();
                    nameCountRateVO.setName(nameCountVO.getName());
                    nameCountRateVO.setCount(nameCountVO.getCount());
                    nameCountRateVO.setRate(DemoUtils.divide(nameCountVO.getCount(), totalCount));
                    return nameCountRateVO;
                })
                .sorted(Comparator.comparingInt(o -> sortAgeList.indexOf(o.getName())))
                .collect(Collectors.toList());

        AgeDistrVO ageDistrVO = new AgeDistrVO();
        ageDistrVO.setAvgAge(Optional.ofNullable(intAssignService.selectAvgAge(null, startDate, endDate))
                .orElse(BigDecimal.ZERO).setScale(1, RoundingMode.HALF_UP));
        ageDistrVO.setAgeDistrList(lstNameCountRate);
        return ageDistrVO;
    }

    public CohlSeniorityDistrVO distributeByCohlSeniority(HKDashQO qo) {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());

        List<NameCountVO> lstNameCount = intAssignService.selectDistrByCohlSeniority(
                qo.getOrganizationCodeList(),
                startDate, endDate);

        // 计算总数
        long totalCount = lstNameCount.stream().mapToLong(NameCountVO::getCount).sum();

        // 转换并计算比例，避免多次遍历
        List<NameCountRateVO> lstNameCountRate = lstNameCount.stream()
                .map(nameCountVO -> {
                    NameCountRateVO nameCountRateVO = new NameCountRateVO();
                    nameCountRateVO.setName(nameCountVO.getName());
                    nameCountRateVO.setCount(nameCountVO.getCount());
                    nameCountRateVO.setRate(DemoUtils.divide(nameCountVO.getCount(), totalCount));
                    return nameCountRateVO;
                })
                .sorted(Comparator.comparingInt(o -> sortListCohlSeniority.indexOf(o.getName())))
                .collect(Collectors.toList());

        CohlSeniorityDistrVO cohlSeniorityDistrVO = new CohlSeniorityDistrVO();
        cohlSeniorityDistrVO.setAvgCohlSeniority(
                Optional.ofNullable(intAssignService.selectAvgCohlSeniority(null, startDate, endDate))
                        .orElse(BigDecimal.ZERO).setScale(1, RoundingMode.HALF_UP));
        cohlSeniorityDistrVO.setCohlSeniorityDistrList(lstNameCountRate);
        return cohlSeniorityDistrVO;
    }

    public List<NameCountRateVO> distributeByPositionLevelDept(HKDashQO qo) {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());

        List<NameCountVO> lstNameCount = intAssignService.selectDistrByPositionInDept(
                qo.getOrganizationCodeList(),
                startDate, endDate);

        // 计算总数
        long totalCount = lstNameCount.stream().mapToLong(NameCountVO::getCount).sum();

        // 转换并计算比例，避免多次遍历
        return lstNameCount.stream()
                .map(nameCountVO -> {
                    NameCountRateVO nameCountRateVO = new NameCountRateVO();
                    nameCountRateVO.setName(nameCountVO.getName());
                    nameCountRateVO.setCount(nameCountVO.getCount());
                    nameCountRateVO.setRate(DemoUtils.divide(nameCountVO.getCount(), totalCount));
                    return nameCountRateVO;
                })
                .sorted(Comparator.comparingInt(o -> sortListPositionLevel.indexOf(o.getName())))
                .collect(Collectors.toList());
    }
    public ResultPage<RosterDetailCategoryVO> distributeByPositionLevelDeptList(IntAssignMajorQO qo,Integer pageNum,Integer pageSize){
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = intAssignService.selectDistrByPositionInDeptList(qo.getOrganizationCodeList(), startDate, endDate, qo.getMajor());
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public ResultPage<RosterDetailCategoryVO> getAssistantGeneralManagerList(HKDashQO qo,Integer pageNum,Integer pageSize){
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = intAssignService.getAssistantGeneralManagerList(qo.getOrganizationCodeList(), startDate, endDate);
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());

    }

    public void exportAssistantGeneralManagerList(HKDashQO qo,HttpServletResponse response) throws IOException {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());
        List<RosterDetail> rosterDetails = intAssignService.getAssistantGeneralManagerList(qo.getOrganizationCodeList(), startDate, endDate);
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    public void exportDistributeByPositionLevelDeptList(IntAssignMajorQO qo, HttpServletResponse response) throws IOException {
        LocalDate date = HKDashFacade.getStatDate(qo);
        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());
        List<RosterDetail> rosterDetails = intAssignService.selectDistrByPositionInDeptList(qo.getOrganizationCodeList(), startDate, endDate, qo.getMajor());
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    public List<NameCountRateVO> distributeByPositionLevelBusi(HKDashQO qo) {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<NameCountVO> lstNameCount = intAssignService.selectDistrByPositionInBusi(
                qo.getOrganizationCodeList(),
                startDate, endDate);

        // 计算总数
        long totalCount = lstNameCount.stream().mapToLong(NameCountVO::getCount).sum();

        // 转换并计算比例，避免多次遍历
        return lstNameCount.stream()
                .map(nameCountVO -> {
                    NameCountRateVO nameCountRateVO = new NameCountRateVO();
                    nameCountRateVO.setName(nameCountVO.getName());
                    nameCountRateVO.setCount(nameCountVO.getCount());
                    nameCountRateVO.setRate(DemoUtils.divide(nameCountVO.getCount(), totalCount));
                    return nameCountRateVO;
                })
                .sorted(Comparator.comparingInt(o -> sortListPositionLevelBusi.indexOf(o.getName())))
                .collect(Collectors.toList());
    }

    public ResultPage<RosterDetailCategoryVO> distributeByPositionLevelBusiList(IntAssignMajorQO qo,Integer pageNum,Integer pageSize){
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        PageHelper.startPage(pageNum,pageSize);
        List<RosterDetail> rosterDetails = intAssignService.selectDistrByPositionInBusiList(qo.getOrganizationCodeList(), startDate, endDate, qo.getMajor());
        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());
        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public void exportDistributeByPositionLevelBusiList(IntAssignMajorQO qo,HttpServletResponse response) throws IOException {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());
        List<RosterDetail> rosterDetails = intAssignService.selectDistrByPositionInBusiList(qo.getOrganizationCodeList(), startDate, endDate, qo.getMajor());
        List<RosterDetailExportVO> categoryVOList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());
        String fileName = MessageFormat.format("人员花名册({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String templatePath = "template/hkdash/hk_dash_roster_template.xlsx";


        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath);
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(categoryVOList, fillConfig, writeSheet);
        }
    }

    public SiteInChargeOverviewVO getSiteInChargeOverview(HKDashQO qo) {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate startDate = statDate.withDayOfMonth(1);
        LocalDate endDate = statDate.withDayOfMonth(statDate.lengthOfMonth());

        SiteInChargeOverviewVO siteInChargeOverviewVO = new SiteInChargeOverviewVO();
        // siteInChargeOverviewVO.setSiteInChargeCount(staffCountFullService.selectManagerCount(qo.getOrganizationCodeList()));

        List<NameCountVO> lstOnGoingSiteInfo = staffCountFullService
                .selectOnGoingProjectCountBySubsidiary(qo.getOrganizationCodeList(), startDate);
        Map<String, Long> mapSubsidiaryOnGoingSiteInfo = lstOnGoingSiteInfo.stream().collect(
                Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<NameCountVO> lstManagerCountBySubsidiary = staffCountFullService
                .selectManagerCountBySubsidiary(qo.getOrganizationCodeList(), startDate);
        Map<String, Long> mapSubsidiaryManagerCount = lstManagerCountBySubsidiary.stream().collect(
                Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));

        List<CodeNameCountVO> lstSubsidiaryManagerDistrByPosition = staffCountFullService
                .selectSubsidiaryManagerDistrByPosition(qo.getOrganizationCodeList(), startDate,
                        endDate);
        Map<String, Map<String, Long>> mapSubsidiaryManagerDistrByPosition = lstSubsidiaryManagerDistrByPosition
                .stream()
                .collect(Collectors.groupingBy(CodeNameCountVO::getCode,
                        Collectors.toMap(CodeNameCountVO::getName, CodeNameCountVO::getCount)));

        siteInChargeOverviewVO.setSubsidiaryStatList(sortListSubsidiary.stream()
                .map(subsidiary -> {
                    SiteInChargeOverviewVO.SubsidiaryStatVO subsidiaryStatVO = new SiteInChargeOverviewVO.SubsidiaryStatVO();
                    subsidiaryStatVO.setSubsidiaryName(subsidiary);
                    subsidiaryStatVO.setOngoingProjectsCount(
                            mapSubsidiaryOnGoingSiteInfo.getOrDefault(subsidiary, 0L));
                    subsidiaryStatVO.setTotalManagers(
                            mapSubsidiaryManagerCount.getOrDefault(subsidiary, 0L));
                    subsidiaryStatVO.setZhuzongCount(mapSubsidiaryManagerDistrByPosition
                            .getOrDefault(subsidiary, new HashMap<>())
                            .getOrDefault("助总及以上", 0L));
                    subsidiaryStatVO.setSeniorManagersCount(mapSubsidiaryManagerDistrByPosition
                            .getOrDefault(subsidiary, new HashMap<>())
                            .getOrDefault("高级地盘经理", 0L));
                    subsidiaryStatVO.setSiteManagersCount(mapSubsidiaryManagerDistrByPosition
                            .getOrDefault(subsidiary, new HashMap<>())
                            .getOrDefault("地盘经理", 0L));
                    subsidiaryStatVO.setDeputyManagersCount(mapSubsidiaryManagerDistrByPosition
                            .getOrDefault(subsidiary, new HashMap<>())
                            .getOrDefault("副经理主持", 0L));
                    return subsidiaryStatVO;
                })
                .collect(Collectors.toList()));

        // 计算地盘负责人总数
        siteInChargeOverviewVO.setSiteInChargeCount(siteInChargeOverviewVO.getSubsidiaryStatList().stream()
                .map(SiteInChargeOverviewVO.SubsidiaryStatVO::getTotalManagers)
                .reduce(0L, Long::sum));
        siteInChargeOverviewVO
                .setOnGoingProjectsCount(lstOnGoingSiteInfo.stream().map(NameCountVO::getCount)
                        .reduce(0L, Long::sum));

        return siteInChargeOverviewVO;
    }

    public ResultPage<RosterDetailCategoryVO> getIntAssignDetailPage(HKDashQO qo, String personType, int pageNum, int pageSize) {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate firstDayOfMonth = statDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = statDate.withDayOfMonth(statDate.lengthOfMonth());

        PageHelper.startPage(pageNum, pageSize);

        List<RosterDetail> rosterDetails = new ArrayList<>();

        switch (personType) {
            case "intAssign": // 内派人员总数详情
                rosterDetails = intAssignService.selectIntAssignDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "hzz": // 海之子人数详情
                rosterDetails = intAssignService.selectHzzDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "manager": // 管理人员详情
                rosterDetails = intAssignService.selectManagerDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "mechanicTech":
                rosterDetails = intAssignService.selectTechOrWorkerDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, null);
                break;
            case "managerDept":
                rosterDetails = intAssignService.selectDeptBusiDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, "dept");
                break;
            case "managerBusi":
                rosterDetails = intAssignService.selectDeptBusiDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, "busi");
                break;
            case "office":
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, personType);
                break;
            case "site":
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, personType);
                break;
            case "engCompany": // 工程公司=写字楼+地盘
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, null);
                break;
            case "zzxHaiHong":
                rosterDetails = intAssignService.selectZZXHaihongDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "jiXie":
                rosterDetails = intAssignService.selectJiXieDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            default:
                throw new IllegalArgumentException("Unsupported type: " + personType);
        }

        // 获取分页信息 - 使用long类型接收total
        PageInfo<RosterDetail> pageInfo = new PageInfo<>(rosterDetails);

        // 转换为VO对象
        List<RosterDetailCategoryVO> detailList = rosterDetails.stream()
                .map(RosterDetailCategoryVO::fromModel)
                .collect(Collectors.toList());

        return new ResultPage<>(detailList, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    public List<RosterDetailExportVO> getExportIntAssignDetail(HKDashQO qo, String personType) {
        LocalDate statDate = HKDashFacade.getStatDate(qo);
        LocalDate firstDayOfMonth = statDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = statDate.withDayOfMonth(statDate.lengthOfMonth());

        List<RosterDetail> rosterDetails = new ArrayList<>();
        // 内派人员导出不包含人力资源部门
        qo.getOrganizationCodeList().removeAll(HrrsConsts.HR_ORGANIZATION_CODE);
        switch (personType) {
            case "intAssign": // 内派人员总数详情
                rosterDetails = intAssignService.selectIntAssignDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "hzz": // 海之子人数详情
                rosterDetails = intAssignService.selectHzzDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "manager": // 管理人员详情
                rosterDetails = intAssignService.selectManagerDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "mechanicTech":
                rosterDetails = intAssignService.selectTechOrWorkerDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, null);
                break;
            case "managerDept":
                rosterDetails = intAssignService.selectDeptBusiDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, "dept");
                break;
            case "managerBusi":
                rosterDetails = intAssignService.selectDeptBusiDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, "busi");
                break;
            case "office":
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, personType);
                break;
            case "site":
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, personType);
                break;
            case "engCompany": // 工程公司=写字楼+地盘
                rosterDetails = intAssignService.selectEngCompanyDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth, null);
                break;
            case "zzxHaiHong":
                rosterDetails = intAssignService.selectZZXHaihongDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            case "jiXie":
                rosterDetails = intAssignService.selectJiXieDetail(qo.getOrganizationCodeList(),
                        firstDayOfMonth, lastDayOfMonth);
                break;
            default:
                throw new IllegalArgumentException("Unsupported type: " + personType);
        }


        // 转换为VO对象
        List<RosterDetailExportVO> detailList = rosterDetails.stream().map(RosterDetailExportVO::fromRosterDetail).collect(Collectors.toList());

        return detailList;
    }
}
