package com.csci.hrrs.facade;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultBean;
import com.csci.common.util.CommonUtils;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.configuration.parallel.ParallelExecutor;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.constant.StaffCountConstant;
import com.csci.hrrs.model.PushOaRecord;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.model.StaffCountRequest;
import com.csci.hrrs.service.*;
import com.csci.hrrs.util.StaffCountFullHelper;
import com.csci.hrrs.util.oa.OAFlowStarter;
import com.csci.hrrs.vo.CodeMsgVO;
import com.csci.hrrs.vo.NameCountVO;
import com.csci.hrrs.vo.StaffCountRequestVO;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
@LogMethod
public class StaffCountRequestFacade {

    private final static Gson GSON = CustomGsonBuilder.createGson();

    @Resource
    private RosterDetailService rosterDetailService;

    @Resource
    private StaffCountRequestService staffCountRequestService;
    @Resource
    private OAFlowStarter oaFlowStarter;
    @Autowired
    private PushOaRecordService pushOaRecordService;
    @Autowired
    private StaffCountFullService staffCountFullService;

    private static StaffCountRequest buildStaffCountRequest(StaffCountRequestVO staffCountRequestVO, int status) {
        validateStaffCountRequest(staffCountRequestVO);
        StaffCountRequest staffCountRequest = staffCountRequestVO.toEntity();
        staffCountRequest.setId(CommonUtils.randomUuid());
        staffCountRequest.setStatus(status);
        return staffCountRequest;
    }

    public static void validateStaffCountRequest(StaffCountRequestVO staffCountRequestVO) {
        ServiceHelper.checkExist(staffCountRequestVO.getOrgCode(), "组织机构编码不能为空");
        ServiceHelper.checkExist(staffCountRequestVO.getRecruitmentType(), "招聘类型不能为空");
        ServiceHelper.checkExist(staffCountRequestVO.getRecruitmentQuantity(), "招聘数量不能为空");
    }

    public ResultBean<String> checkCreateStaffCountRequest(StaffCountRequestVO staffCountRequestVO) {
        validateStaffCountRequest(staffCountRequestVO);
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate startOfYear = today.withDayOfYear(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        /*提交校验1：总计当月在职人数之和A与总计当月编制总和B（当前年月，不是所选年月）
        A<B：流程正常发起
        B<=A<=B*1.1，提示“当月在岗人数已超本月编制人数！”，流程正常发起
        A>B*1.1：提示“当月在岗人数已超本月编制人数的10%，需调整全周期编制计划！”，不允许发起流程
        提交校验2：总计本年度累计在职人数之和C与总计本年度累计编制总和D
        C<D：流程正常发起
        D<=C<=D*1.05，提示“年度累计用工总量已超计划，请注意！”，流程正常发起
        C>D*1.05：提示“年度累计用工总量超出计划的5%，需调整全周期编制计划！”，不允许发起流程
        提交校验3：
        当月在职人数>=全周期编制中
        度编制最大值，且当前年月<=月度编制最大值所在年月-3时。提示“全周期用工峰值提前3个月，需调整全周期编制计划！”，不允许发起流程*/
        List<String> orgCodes = StaffCountFullHelper.getChildrenOrgCodes(staffCountRequestVO.getOrgCode());  // 加上子部门
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(orgCodes);
        CodeMsgVO codeMsgVO = new CodeMsgVO();
        // 当月在岗人数
        List<NameCountVO> lstMonthNameCount = rosterDetailService.selectCountByOrgCodeGroupByType(orgCodes, startOfMonth, endOfMonth);
        // 累计在职人数 C
        List<NameCountVO> lstYearNameCount = rosterDetailService.selectCountByOrgCodeGroupByType(orgCodes,startOfYear,endOfMonth);
        if (StaffCountConstant.STAFF_RECRUITMENT_TYPE.contains(staffCountRequestVO.getRecruitmentType())){
            // 管理人员
            ResultBean<String> checkStaffCountResult = checkStaffCount(staffCountFullList, today, startOfYear, endOfMonth, lstMonthNameCount, lstYearNameCount);
            if (Objects.nonNull(checkStaffCountResult)){
                return checkStaffCountResult;
            }
        } else if (StaffCountConstant.WORKER_TYPE.contains(staffCountRequestVO.getRecruitmentType())){
            // 工人队伍
            ResultBean<String> checkWorkerResult = checkWorkerCount(staffCountFullList, today, startOfYear, endOfMonth, lstMonthNameCount, lstYearNameCount);
            if (Objects.nonNull(checkWorkerResult)){
                return checkWorkerResult;
            }
        }
        return new ResultBean<>(0, "流程正常发起");

    }

    private ResultBean<String> checkStaffCount(List<StaffCountFull> staffCountFullList,LocalDate today,LocalDate startOfYear,LocalDate endOfMonth,List<NameCountVO> lstMonthNameCount,List<NameCountVO> lstYearNameCount){
        long staffCountPlan = StaffCountFullHelper.calcStaffCountByYearMonth(staffCountFullList, today);
        long staffCountPlanYear = StaffCountFullHelper.calcStaffCountPlan(staffCountFullList, startOfYear, endOfMonth);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        Map.Entry<LocalDate, BigDecimal> maxStaffCountDateAndValue = StaffCountFullHelper.getMaxStaffCountDateAndValue(staffCountFullList);
        LocalDate maxStaffCountDate = Optional.ofNullable(maxStaffCountDateAndValue).map(Map.Entry::getKey).orElse(LocalDate.now());
        BigDecimal maxStaffCountValue = Optional.ofNullable(maxStaffCountDateAndValue).map(Map.Entry::getValue).orElse(BigDecimal.ZERO);
        // 当月在岗人数
        long thisMonthCount = 0;
        if (!CollectionUtils.isEmpty(lstMonthNameCount)){
            thisMonthCount = lstMonthNameCount.stream().filter(item->HrrsConsts.HR_STAFF_CODE.contains(item.getName())).mapToLong(NameCountVO::getCount).sum();
        }
        // 累计在职人数
        long thisYearCount = 0;
        if (!CollectionUtils.isEmpty(lstYearNameCount)){
            thisYearCount = lstYearNameCount.stream().filter(item->HrrsConsts.HR_STAFF_CODE.contains(item.getName())).mapToLong(NameCountVO::getCount).sum();
        }
        // 严重的排前面，也就是code越大越严重
        if (thisMonthCount >= maxStaffCountValue.longValue() && !today.isAfter(maxStaffCountDate.minusMonths(3))) {
            return new ResultBean<>(2, "全周期管理人员用工峰值时间（"+today.format(formatter)+"），较计划峰值时间（"+maxStaffCountDate.format(formatter)+"）提前3个月，需调整全周期编制计划！");
        }
        if (thisYearCount > staffCountPlanYear * 1.05) {
            return new ResultBean<>(2, "年度 管理人员 用工总量超出计划的5%，需调整全周期编制计划！");
        }
        if (thisMonthCount > staffCountPlan * 1.1) {
            return new ResultBean<>(2, "当月 管理人员 在岗人数超出计划的10%，需调整全周期编制计划！");
        }

        if (thisYearCount >= staffCountPlanYear && thisYearCount <= staffCountPlanYear * 1.05) {
            return new ResultBean<>(1, "年度累计用工总量已超计划，请注意！");
        }

        if (thisMonthCount >= staffCountPlan && thisMonthCount <= staffCountPlan * 1.1) {
            return new ResultBean<>(1, "当月在岗人数已超本月编制人数！");
        }
        return null;
    }

    private ResultBean<String> checkWorkerCount(List<StaffCountFull> staffCountFullList,LocalDate today,LocalDate startOfYear,LocalDate endOfMonth,List<NameCountVO> lstMonthNameCount,List<NameCountVO> lstYearNameCount){
        // 当月工人队伍人数
        long workerPlan = StaffCountFullHelper.calcWorkerCountByYearMonth(staffCountFullList, today);
        // 累计工人队伍人数
        long workerCountPlanYear = StaffCountFullHelper.calcWorkerCountPlan(staffCountFullList, startOfYear, endOfMonth);
        // 当月在岗人数
        long thisMonthCount = 0;
        if (!CollectionUtils.isEmpty(lstMonthNameCount)){
            thisMonthCount = lstMonthNameCount.stream().filter(item->!HrrsConsts.HR_STAFF_CODE.contains(item.getName())).mapToLong(NameCountVO::getCount).sum();
        }
        // 累计在职人数
        long thisYearCount = 0;
        if (!CollectionUtils.isEmpty(lstYearNameCount)){
            thisYearCount = lstYearNameCount.stream().filter(item->!HrrsConsts.HR_STAFF_CODE.contains(item.getName())).mapToLong(NameCountVO::getCount).sum();
        }
        // 工人队伍最大人数和最大人数月份
        Map.Entry<LocalDate, BigDecimal> maxWorkerCountDateAndValue = StaffCountFullHelper.getMaxWorkerCountDateAndValue(staffCountFullList);
        LocalDate maxWorkerDate = Optional.ofNullable(maxWorkerCountDateAndValue).map(Map.Entry::getKey).orElse(LocalDate.now());
        BigDecimal maxWorkerCountValue = Optional.ofNullable(maxWorkerCountDateAndValue).map(Map.Entry::getValue).orElse(BigDecimal.ZERO);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 严重的排前面，也就是code越大越严重
        if (thisMonthCount >= maxWorkerCountValue.longValue() && !today.isAfter(maxWorkerDate.minusMonths(3))) {
            return new ResultBean<>(2, "全周期工人队伍用工峰值时间（"+today.format(formatter)+"），较计划峰值时间（"+maxWorkerDate.format(formatter)+"）提前3个月，需调整全周期编制计划！");
        }
        if (thisYearCount > workerCountPlanYear * 1.05) {
            return new ResultBean<>(2, "年度 工人队伍 用工总量超出计划的5%，需调整全周期编制计划！");
        }
        if (thisMonthCount > workerPlan * 1.1) {
            return new ResultBean<>(2, "当月 工人队伍 在岗人数超出计划的10%，需调整全周期编制计划！");
        }

        if (thisYearCount >= workerCountPlanYear && thisYearCount <= workerCountPlanYear * 1.05) {
            return new ResultBean<>(1, "年度累计用工总量已超计划，请注意！");
        }

        if (thisMonthCount >= workerPlan && thisMonthCount <= workerPlan * 1.1) {
            return new ResultBean<>(1, "当月在岗人数已超本月工人队伍人数！");
        }
        return null;
    }


    /**
     * 创建用工人数申请，自动提交到OA
     *
     * @param staffCountRequestVO 用工人数申请
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String createStaffCountRequest(StaffCountRequestVO staffCountRequestVO) {
        StaffCountRequest staffCountRequest = buildStaffCountRequest(staffCountRequestVO, HrrsConsts.StaffCountRequestStatus.SUBMITTED);
        PushOaRecord pushOaRecord = buildPushOaRecord(staffCountRequest.getId());
        try {
            // 如果流程发起失败，会抛出异常，并回滚事务
            staffCountRequestService.save(staffCountRequest);
            String response = oaFlowStarter.startFlow(staffCountRequest, pushOaRecord);
            handleResponse(response, staffCountRequest, pushOaRecord);
        } catch (Exception e) {
            if (StringUtils.isBlank(pushOaRecord.getResponse())) {
                pushOaRecord.setResponse(e.getMessage());
            } else {
                pushOaRecord.setResponse(pushOaRecord.getResponse() + "; " + e.getMessage());
            }
            throw new ServiceException("提交流程到OA失败: " + e.getMessage());
        } finally {
            // 异步保存推送记录；不影响主流程；不跟随事务
            ParallelExecutor.submit(() -> pushOaRecordService.save(pushOaRecord));
        }

        return staffCountRequest.getId();
    }

    private PushOaRecord buildPushOaRecord(String businessId) {
        PushOaRecord pushOaRecord = new PushOaRecord();
        pushOaRecord.setBusinessId(businessId);
        pushOaRecord.setStatus(HrrsConsts.StaffCountRequestStatus.DRAFT);
        return pushOaRecord;
    }

    /**
     * response:
     * caseId
     * subject
     * formRecordId
     *
     * {
     *     "status": 0,
     *     "code": "BOOT_0000",
     *     "data": {
     *         "pageInfo": null,
     *         "content": {
     *             "caseId": "4532707031079845776",
     *             "subject": "中建香港-人力资源需求申请表(陶利 2024-12-10 18:41)",
     *             "formRecordId": "-8688451763109643523"
     *         }
     *     },
     *     "message": "SUCCESS"
     * }
     *
     * @param response
     */
    private void handleResponse(String response, StaffCountRequest staffCountRequest, PushOaRecord pushOaRecord) {
        // 处理响应
        JsonObject jsonResp = GSON.fromJson(response, JsonObject.class);

        // 检查状态码
        checkResponseStatus(jsonResp);

        // 更新状态
        setStatusToSubmitted(staffCountRequest, pushOaRecord);

        // 提取并更新内容
        updatePushOaRecordFromResponse(jsonResp, pushOaRecord);
    }

    // 检查响应状态
    private void checkResponseStatus(JsonObject jsonResp) {
        int status = jsonResp.get("status").getAsInt();
        if (status != 0) {
            throw new ServiceException("提交流程到OA失败: " + jsonResp.get("message").getAsString());
        }
    }

    // 更新请求状态
    private void setStatusToSubmitted(StaffCountRequest staffCountRequest, PushOaRecord pushOaRecord) {
        staffCountRequest.setStatus(HrrsConsts.StaffCountRequestStatus.SUBMITTED);
        pushOaRecord.setStatus(HrrsConsts.StaffCountRequestStatus.SUBMITTED);
    }

    // 从响应中提取并更新 PushOaRecord
    private void updatePushOaRecordFromResponse(JsonObject jsonResp, PushOaRecord pushOaRecord) {
        JsonObject data = jsonResp.getAsJsonObject("data");
        if (data != null) {
            JsonObject content = data.getAsJsonObject("content");
            if (content != null) {
                String caseId = getJsonField(content, "caseId");
                String subject = getJsonField(content, "subject");
                String formRecordId = getJsonField(content, "formRecordId");
                pushOaRecord.setCaseId(caseId);
                pushOaRecord.setFormRecordId(formRecordId);
                pushOaRecord.setSubject(subject);
            }
        }
    }

    // 从 JSON 对象中获取字段值
    private String getJsonField(JsonObject jsonObject, String fieldName) {
        return jsonObject.has(fieldName) ? jsonObject.get(fieldName).getAsString() : "";
    }


    @Transactional(rollbackFor = Exception.class)
    public void terminateOaFlow(String caseId) {
        PushOaRecord pushOaRecord = pushOaRecordService.getByCaseId(caseId);
        if (pushOaRecord != null) {
            pushOaRecord.setStatus(HrrsConsts.StaffCountRequestStatus.TERMINATED);
            pushOaRecord.setIsDeleted(true);
            pushOaRecordService.updateById(pushOaRecord);

            StaffCountRequest staffCountRequest = staffCountRequestService.getById(pushOaRecord.getBusinessId());
            staffCountRequest.setStatus(HrrsConsts.StaffCountRequestStatus.TERMINATED);
            staffCountRequestService.updateById(staffCountRequest);
        }
    }

    public void finishOaFlow(String caseId) {
        PushOaRecord pushOaRecord = pushOaRecordService.getByCaseId(caseId);
        if (pushOaRecord != null) {
            pushOaRecord.setStatus(HrrsConsts.StaffCountRequestStatus.APPROVED);
            pushOaRecordService.updateById(pushOaRecord);

            StaffCountRequest staffCountRequest = staffCountRequestService.getById(pushOaRecord.getBusinessId());
            staffCountRequest.setStatus(HrrsConsts.StaffCountRequestStatus.APPROVED);
            staffCountRequestService.updateById(staffCountRequest);
        }
    }

}
