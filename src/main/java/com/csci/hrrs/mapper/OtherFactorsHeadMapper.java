package com.csci.hrrs.mapper;

import com.csci.hrrs.model.OtherFactorsHead;
import com.csci.hrrs.model.OtherFactorsHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OtherFactorsHeadMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	long countByExample(OtherFactorsHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int deleteByExample(OtherFactorsHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int insert(OtherFactorsHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int insertSelective(OtherFactorsHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	List<OtherFactorsHead> selectByExample(OtherFactorsHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	OtherFactorsHead selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") OtherFactorsHead row, @Param("example") OtherFactorsHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int updateByExample(@Param("row") OtherFactorsHead row, @Param("example") OtherFactorsHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int updateByPrimaryKeySelective(OtherFactorsHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	int updateByPrimaryKey(OtherFactorsHead row);
}