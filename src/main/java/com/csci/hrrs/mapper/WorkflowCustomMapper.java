package com.csci.hrrs.mapper;

import com.csci.hrrs.qo.WorkflowControlQO;
import com.csci.hrrs.vo.OrganizationExportData;
import com.csci.hrrs.vo.WorkflowControlVO;
import com.csci.hrrs.vo.WorkflowNodeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkflowCustomMapper {

    List<WorkflowNodeVO> selectWorkflowNodeByWorkflowId(@Param("workflowId") String workflowId);

    /**
     * 查询社会绩效一
     *
     * @param qo
     * @return
     */
    List<WorkflowControlVO> selectWorkflowControlOfSocialPerfOne(WorkflowControlQO qo);

    /**
     * 人力基本情况统计表流程数据查询
     *
     * @param qo
     * @return
     */
    List<WorkflowControlVO> selectWorkflowControlOfBaseStatInfo(WorkflowControlQO qo);

    List<WorkflowControlVO> selectWorkflowControlOfSocialPerfTwo(WorkflowControlQO qo);

    /**
     * 查询环境绩效
     *
     * @param qo 查询条件
     * @return
     */
    List<WorkflowControlVO> selectWorkflowControlOfAmbient(WorkflowControlQO qo);

    List<WorkflowControlVO> selectUnSubmitWorkflowOfAmbient(WorkflowControlQO qo);

    List<OrganizationExportData> selectUnSubmitOrganizationOfAmbient(WorkflowControlQO qo);

    List<OrganizationExportData> selectUnSubmitOrganizationOfSocialPerfOne(WorkflowControlQO qo);

    List<OrganizationExportData> selectUnSubmitOrganizationOfSocialPerfTwo(WorkflowControlQO qo);

}
