package com.csci.hrrs.mapper;

import com.csci.hrrs.model.ExternalExperience;
import com.csci.hrrs.model.ExternalExperienceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ExternalExperienceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    long countByExample(ExternalExperienceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    int deleteByExample(ExternalExperienceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    int insert(ExternalExperience record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    int insertSelective(ExternalExperience record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    List<ExternalExperience> selectByExample(ExternalExperienceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ExternalExperience record, @Param("example") ExternalExperienceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_EXTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ExternalExperience record, @Param("example") ExternalExperienceExample example);
}