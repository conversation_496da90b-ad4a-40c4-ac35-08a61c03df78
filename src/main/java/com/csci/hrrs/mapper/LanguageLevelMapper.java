package com.csci.hrrs.mapper;

import com.csci.hrrs.model.LanguageLevel;
import com.csci.hrrs.model.LanguageLevelExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LanguageLevelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    long countByExample(LanguageLevelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    int deleteByExample(LanguageLevelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    int insert(LanguageLevel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    int insertSelective(LanguageLevel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    List<LanguageLevel> selectByExample(LanguageLevelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LanguageLevel record, @Param("example") LanguageLevelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_language_level
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LanguageLevel record, @Param("example") LanguageLevelExample example);
}