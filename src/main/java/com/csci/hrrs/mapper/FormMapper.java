package com.csci.hrrs.mapper;

import com.csci.hrrs.model.Form;
import com.csci.hrrs.model.FormExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FormMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    long countByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int deleteByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int insert(Form row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int insertSelective(Form row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    List<Form> selectByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    Form selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") Form row, @Param("example") FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") Form row, @Param("example") FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(Form row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Form row);
}