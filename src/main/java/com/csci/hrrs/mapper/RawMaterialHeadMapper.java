package com.csci.hrrs.mapper;

import com.csci.hrrs.model.RawMaterialHead;
import com.csci.hrrs.model.RawMaterialHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RawMaterialHeadMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	long countByExample(RawMaterialHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int deleteByExample(RawMaterialHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int insert(RawMaterialHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int insertSelective(RawMaterialHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	List<RawMaterialHead> selectByExample(RawMaterialHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	RawMaterialHead selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") RawMaterialHead row, @Param("example") RawMaterialHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int updateByExample(@Param("row") RawMaterialHead row, @Param("example") RawMaterialHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int updateByPrimaryKeySelective(RawMaterialHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_raw_material_head
	 * @mbg.generated  Mon Jun 27 14:32:20 HKT 2022
	 */
	int updateByPrimaryKey(RawMaterialHead row);
}