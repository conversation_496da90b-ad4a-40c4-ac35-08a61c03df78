package com.csci.hrrs.mapper;

import com.csci.hrrs.model.OtherFactorsDetail5;
import com.csci.hrrs.model.OtherFactorsDetail5Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OtherFactorsDetail5Mapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	long countByExample(OtherFactorsDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int deleteByExample(OtherFactorsDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int insert(OtherFactorsDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int insertSelective(OtherFactorsDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	List<OtherFactorsDetail5> selectByExample(OtherFactorsDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	OtherFactorsDetail5 selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") OtherFactorsDetail5 row,
			@Param("example") OtherFactorsDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int updateByExample(@Param("row") OtherFactorsDetail5 row, @Param("example") OtherFactorsDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int updateByPrimaryKeySelective(OtherFactorsDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_detail_5
	 * @mbg.generated  Tue Jun 28 10:52:29 HKT 2022
	 */
	int updateByPrimaryKey(OtherFactorsDetail5 row);
}