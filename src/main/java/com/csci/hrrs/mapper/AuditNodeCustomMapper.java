package com.csci.hrrs.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import com.csci.hrrs.provider.*;
import com.csci.hrrs.vo.*;

@Mapper
public interface AuditNodeCustomMapper {
	
    @SelectProvider(type=AuditNodeSqlProvider.class,method="listAuditNodeDetailSql")
    public List<AuditNodeDetailVO> listAuditNodeDetail(@Param("userId") String userId);

}
