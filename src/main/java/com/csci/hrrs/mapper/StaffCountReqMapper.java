package com.csci.hrrs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.hrrs.model.StaffCountReq;
import com.csci.hrrs.vo.DateCountVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface StaffCountReqMapper extends BaseMapper<StaffCountReq> {

    /**
     * 查询用户权限范围内的人员编制信息，按日期、专业进行分组
     *
     * @param userId      用户ID
     * @param orgCodeList
     * @param fromDate
     * @param toDate
     * @return
     */
    List<DateCountVO> selectStaffCountByDate(@Param("userId") String userId, @Param("orgCodeList") List<String> orgCodeList,
                                             @Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate);


    /**
     * 查询指定年月的人员编制数量
     *
     * @param userId      用户ID
     * @param orgCodeList
     * @param year
     * @param month
     * @return
     */
    Long selectStaffCountByMonthAndOrg(@Param("userId") String userId, @Param("orgCodeList") List<String> orgCodeList,
                                       @Param("year") Integer year, @Param("month") Integer month);
}
