package com.csci.hrrs.mapper;

import com.csci.hrrs.model.DynamicFormByItemHead;
import com.csci.hrrs.model.DynamicFormByItemHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DynamicFormByItemHeadMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	long countByExample(DynamicFormByItemHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int deleteByExample(DynamicFormByItemHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int insert(DynamicFormByItemHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int insertSelective(DynamicFormByItemHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	List<DynamicFormByItemHead> selectByExample(DynamicFormByItemHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	DynamicFormByItemHead selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") DynamicFormByItemHead row,
			@Param("example") DynamicFormByItemHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int updateByExample(@Param("row") DynamicFormByItemHead row,
			@Param("example") DynamicFormByItemHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int updateByPrimaryKeySelective(DynamicFormByItemHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_head
	 * @mbg.generated  Tue May 17 11:25:39 HKT 2022
	 */
	int updateByPrimaryKey(DynamicFormByItemHead row);
}