package com.csci.hrrs.mapper;

import com.csci.hrrs.model.WorkflowNode;
import com.csci.hrrs.model.WorkflowNodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowNodeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    long countByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int deleteByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int insert(WorkflowNode row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int insertSelective(WorkflowNode row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    List<WorkflowNode> selectByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    WorkflowNode selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") WorkflowNode row, @Param("example") WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") WorkflowNode row, @Param("example") WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkflowNode row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkflowNode row);
}