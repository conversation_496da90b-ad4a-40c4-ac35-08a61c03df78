package com.csci.hrrs.mapper;

import com.csci.hrrs.model.ResourcesEnergyDetail5;
import com.csci.hrrs.model.ResourcesEnergyDetail5Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ResourcesEnergyDetail5Mapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	long countByExample(ResourcesEnergyDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int deleteByExample(ResourcesEnergyDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int insert(ResourcesEnergyDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int insertSelective(ResourcesEnergyDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	List<ResourcesEnergyDetail5> selectByExample(ResourcesEnergyDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	ResourcesEnergyDetail5 selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") ResourcesEnergyDetail5 row,
			@Param("example") ResourcesEnergyDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int updateByExample(@Param("row") ResourcesEnergyDetail5 row,
			@Param("example") ResourcesEnergyDetail5Example example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int updateByPrimaryKeySelective(ResourcesEnergyDetail5 row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_resources_energy_detail_5
	 * @mbg.generated  Tue Jun 28 10:54:07 HKT 2022
	 */
	int updateByPrimaryKey(ResourcesEnergyDetail5 row);
}