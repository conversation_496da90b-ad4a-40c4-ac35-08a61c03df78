package com.csci.hrrs.mapper;

import com.csci.hrrs.model.FamilyBusRelation;
import com.csci.hrrs.model.FamilyBusRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FamilyBusRelationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    long countByExample(FamilyBusRelationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int deleteByExample(FamilyBusRelationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int insert(FamilyBusRelation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int insertSelective(FamilyBusRelation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    List<FamilyBusRelation> selectByExample(FamilyBusRelationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    FamilyBusRelation selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") FamilyBusRelation row, @Param("example") FamilyBusRelationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") FamilyBusRelation row, @Param("example") FamilyBusRelationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(FamilyBusRelation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_pi_family_bus_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(FamilyBusRelation row);
}