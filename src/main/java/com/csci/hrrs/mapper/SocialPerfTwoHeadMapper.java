package com.csci.hrrs.mapper;

import com.csci.hrrs.model.SocialPerfTwoHead;
import com.csci.hrrs.model.SocialPerfTwoHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerfTwoHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    long countByExample(SocialPerfTwoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int deleteByExample(SocialPerfTwoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int insert(SocialPerfTwoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int insertSelective(SocialPerfTwoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    List<SocialPerfTwoHead> selectByExample(SocialPerfTwoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    SocialPerfTwoHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") SocialPerfTwoHead record, @Param("example") SocialPerfTwoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") SocialPerfTwoHead record, @Param("example") SocialPerfTwoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SocialPerfTwoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_head
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SocialPerfTwoHead record);
}