package com.csci.hrrs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "水資源明细行数据")
public class WaterResourcesDetailVO {

    @Schema(description = "显示名称")
    private String displayName;

    @Schema(description = "地表水")
    private String col1;

    @Schema(description = "地下水")
    private String col2;

    @Schema(description = "海水")
    private String col3;

    @Schema(description = "第三方的水(向市政供水設施、公共或私入設施購買的水)")
    private String col4;

    @Schema(description = "項目所在地區是否存在用水壓力")
    private String col5;

    @Schema(description = "中水/廢水回用")
    private String col6;

    @Schema(description = "地表水(經污水處理設施處理後)直接排放到自然水體(大海、河流或湖泊等)")
    private String col7;

    @Schema(description = "地下水(經污水處理設施處理後)直接排放到地下水")
    private String col8;

    @Schema(description = "海水(經污水處理設施處理後)直接排放到大海")
    private String col9;

    @Schema(description = "公用雨水渠(連接市政管網後排放)")
    private String col10;

    @Schema(description = "公用污水渠(連接市政管網後排放)")
    private String col11;

    @Schema(description = "排水是否為淡水(溶解性固體總量(TDS)≦1000mg/L)")
    private String col12;
}
