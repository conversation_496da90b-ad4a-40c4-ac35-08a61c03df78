package com.csci.hrrs.vo.subsidiary;

import com.alibaba.excel.annotation.ExcelProperty;
import com.csci.hrrs.apply.model.ApplyEmpInfo;
import com.csci.hrrs.apply.service.ApplyEmpInfoService;
import com.csci.hrrs.model.FactRoster;
import com.csci.hrrs.model.JobChange;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.service.FactRosterService;
import com.csci.hrrs.service.JobChangeService;
import com.csci.hrrs.util.SpringContextHolder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/5/12 10:03
 **/
@Data
public class RosterDetailCategoryExportVO {
    @Schema(description = "序号")
    @ExcelProperty("序号")
    private Integer seq;

    @Schema(description = "员工编号")
    @ExcelProperty("员工编号")
    private String pernr;

    @Schema(description = "姓名")
    @ExcelProperty("姓名")
    private String name;

    /**
     * 表:fact_roster
    */
    @Schema(description = "部门")
    @ExcelProperty("部门")
    private String subsidiary;

    /**
     * 表:fact_roster
    */
    @Schema(description = "地盘")
    @ExcelProperty("地盘")
    private String subProject;

    @Schema(description = "聘用类型")
    @ExcelProperty("聘用类型")
    private String hireType;

    @Schema(description = "所属岗位")
    @ExcelProperty("所属岗位")
    private String jobTypeNd;

    /**
     * 表:fact_roster
    */
    @Schema(description = "职务层级")
    @ExcelProperty("职务层级")
    private String positionLevel;

    /**
     * 表:fact_roster
     */
    @Schema(description = "职务层级代码")
    @ExcelProperty("职务层级代码")
    private String positionLevelCode;

    /**
     * 表:fact_roster
     */
    @Schema(description = "职位")
    @ExcelProperty("职位")
    private String primaryPosition;

    @Schema(description = "入职日期")
    @ExcelProperty("入职日期")
    private LocalDate join3311Time;

    /**
     * 表:fact_roster
    */
    @Schema(description = "转正日期")
    @ExcelProperty("转正日期")
    private String zzSjsj;

    @Schema(description = "参加工作时间")
    @ExcelProperty("参加工作时间")
    private LocalDate startWorkTime;

    @Schema(description = "性别")
    @ExcelProperty("性别")
    private String gender;

    /**
     * 表:fact_roster
    */
    @Schema(description = "手机")
    @ExcelProperty("手机")
    private String mobile;

    @Schema(description = "邮箱")
    @ExcelProperty("邮箱")
    private String email;

    /**
     * 表:fact_roster
     */
    @Schema(description = "身份证号码")
    @ExcelProperty("身份证号码")
    private String mainlandIdCard;

    @Schema(description = "出生日期")
    @ExcelProperty("出生日期")
    private LocalDate birthdate;

    @Schema(description = "年龄")
    @ExcelProperty("年龄")
    private Integer age;

    @Schema(description = "民族")
    @ExcelProperty("民族")
    private String ethnicity;

    /**
     * 表:fact_roster
     */
    @Schema(description = "籍贯")
    @ExcelProperty("籍贯")
    private String hometown;

    /**
     * 表:fact_roster
     */
    @Schema(description = "婚姻状况")
    @ExcelProperty("婚姻状况")
    private String maritalStatus;

    /**
     * 表:fact_roster
     */
    @Schema(description = "政治面貌")
    @ExcelProperty("政治面貌")
    private String politicalStatus;

    /**
     * 表:fact_roster
     */
    @Schema(description = "学历")
    @ExcelProperty("学历")
    private String education;

    @Schema(description = "学历学校及专业")
    @ExcelProperty("学历学校及专业")
    private String major;

    /**
     * 表:fact_roster
     */
    @Schema(description = "职称")
    @ExcelProperty("职称")
    private String jobTitle;

    /**
     * 表:fact_roster
     */
    @Schema(description = "其它资源")
    @ExcelProperty("其它资源")
    private String credentials;

    /**
     * 表:fact_roster
     */
    @Schema(description = "离职日期")
    @ExcelProperty("离职日期")
    private LocalDate dimissionDate;

    /**
     * 表:t_job_change
    */
    @Schema(description = "离职原因")
    @ExcelProperty("离职原因")
    private String lzyy;



    public static RosterDetailCategoryExportVO fromModel(RosterDetail rosterDetail,boolean onJob) {
        RosterDetailCategoryExportVO rosterDetailCategoryVO = new RosterDetailCategoryExportVO();

        BeanUtils.copyProperties(rosterDetail, rosterDetailCategoryVO);
        if (Objects.nonNull(rosterDetail.getGender())){
            if (rosterDetail.getGender().equals(1)){
                rosterDetailCategoryVO.setGender("男");
            } else if (rosterDetail.getGender().equals(0)){
                rosterDetailCategoryVO.setGender("女");
            } else {
                rosterDetailCategoryVO.setGender("其他");
            }
        }

        if (!onJob){
            // 填充离职原因
            JobChangeService jobChangeService = SpringContextHolder.getBean(JobChangeService.class);
            JobChange jobChange = jobChangeService.getJobChangeByPernr(rosterDetail.getPernr(),"离职");
            if (Objects.nonNull(jobChange)){
                rosterDetailCategoryVO.setLzyy(jobChange.getLzyy());
            }
        }
        return rosterDetailCategoryVO;
    }
}
