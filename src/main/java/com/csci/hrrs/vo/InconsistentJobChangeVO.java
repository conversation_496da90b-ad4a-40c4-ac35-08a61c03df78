package com.csci.hrrs.vo;

import lombok.Data;

/**
 * 职务信息不一致的变动记录VO
 * 用于返回职务、层级和职级信息不一致的人员变动记录
 */
@Data
public class InconsistentJobChangeVO {
    /**
     * 员工编号
     */
    private String pernr;

    /**
     * 开始日期
     */
    private String begda;

    /**
     * 结束日期
     */
    private String endda;

    /**
     * 职务
     */
    private String zw;

    /**
     * 职务层级
     */
    private String zwcj;

    /**
     * 职级
     */
    private String zzZj;

    /**
     * 是否职务一致
     */
    private Boolean isPositionMatch;

    /**
     * 是否层级一致
     */
    private Boolean isLevelMatch;

    /**
     * 是否职级一致
     */
    private Boolean isRankMatch;

    /**
     * Excel中对应的层级代码
     */
    private String excelLevelCode;

    /**
     * Excel中对应的职级范围
     */
    private String excelRankRange;
}