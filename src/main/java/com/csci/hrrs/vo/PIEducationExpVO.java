package com.csci.hrrs.vo;

import com.csci.hrrs.model.EducationExp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;

@Data
@Schema(name = "EducationExpVO", description = "教育经历")
public class PIEducationExpVO {

    private LocalDate startDate;

    private LocalDate endDate;

    private String schoolName;

    private String major;

    private String educationLevel;

    private String degree;

    private Boolean isHighest;

    private String studyMode;

    /**
     * 转换为model
     *
     * @return 人员信息
     */
    public EducationExp toModel() {
        EducationExp model = new EducationExp();
        BeanUtils.copyProperties(this, model);
        return model;
    }
}
