package com.csci.hrrs.vo;

import com.csci.common.util.DateUtils;
import com.csci.hrrs.dto.ExternalParttimeExpDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Objects;

/**
 * CREATE TABLE t_external_parttime_job (
 *   id NVARCHAR(50) PRIMARY KEY,
 *   start_date DATE,  --开始时间
 *   end_date DATE, --截止时间
 *   organization_name NVARCHAR(255), --机构名称
 *   parttime_position NVARCHAR(255), --兼职职务
 *   job_description NVARCHAR(500), --兼职工作内容
 *   industry NVARCHAR(255), --所属行业
 *   is_paid BIT --是否受薪
 * );
 */
@Data
@Schema(name = "ExternalParttimeJobVO", description = "外部兼职视图对象")
public class ExternalParttimeJobVO {

    private String pernr;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "截止时间")
    private LocalDate endDate;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "兼职职务")
    private String parttimePosition;

    @Schema(description = "兼职工作内容")
    private String jobDescription;

    @Schema(description = "所属行业")
    private String industry;

    @Schema(description = "兼职关系")
    private String relationship;

    @Schema(description = "是否受薪")
    private Boolean isPaid;

    @Schema(description = "是否受薪文本")
    private String paidText;

    @Schema(description = "是否与公司利益存在冲突")
    private Boolean isConflictWithCompanyInterest;

    @Schema(description = "是否与公司利益存在冲突文本")
    private String conflictWithCompanyInterestText;

    /**
     * 从DTO转换为VO
     *
     * @param dto 外部兼职经历DTO
     * @return
     */
    public static ExternalParttimeJobVO fromDTO(ExternalParttimeExpDTO dto) {
        ExternalParttimeJobVO vo = new ExternalParttimeJobVO();
        vo.setPernr(dto.getPernr());
        vo.setStartDate(DateUtils.toLocalDateTime(dto.getBegda()).toLocalDate());
        vo.setEndDate(DateUtils.toLocalDateTime(dto.getEndda()).toLocalDate());
        vo.setIndustry(dto.getZzJghy());
        vo.setOrganizationName(dto.getZzJgmc01());
        vo.setParttimePosition(dto.getZzJzzw());
        vo.setJobDescription(dto.getZzJzgznr());
        vo.setRelationship(dto.getZzJggx());
        vo.setIsPaid(StringUtils.equalsIgnoreCase(dto.getZzSfyx(), "01"));
        vo.setIsConflictWithCompanyInterest(StringUtils.equalsIgnoreCase(dto.getZzSflyct(), "01"));
        return vo;
    }

    public String getPaidText() {
        if (Objects.equals(isPaid, Boolean.TRUE)) {
            return "是";
        } else {
            return "否";
        }
    }

    public String getConflictWithCompanyInterestText() {
        if (Objects.equals(isConflictWithCompanyInterest, Boolean.TRUE)) {
            return "是";
        } else {
            return "否";
        }
    }

}
