package com.csci.hrrs.constant;

import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/5/20 12:33
 **/
@Getter
public enum FeedbackStatusEnum {

    DRAFT(0,"草稿"),

    SUBMITTED(1,"提交"),

    TEMPORARY(2,"暂存"),

    FINISH(3,"完成");


    private Integer code;

    private String desc;

    FeedbackStatusEnum(Integer code,String desc){
        this.code = code;
        this.desc = desc;
    }

}
