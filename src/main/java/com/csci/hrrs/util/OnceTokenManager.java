package com.csci.hrrs.util;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.util.context.ContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用于生产和验证一次性token
 *
 * <AUTHOR>
 */
@Component
public class OnceTokenManager {

    private static final long ONCE_TOKEN_EXPIRE_TIME = 5;

    @Resource
    private RedisCache redisCache;

    /**
     * 生成一次性token，该token只能使用一次
     *
     * @return
     */
    public String generateOnceToken() {
        return generateOnceToken(HrrsConsts.PROJECT_PREFIX);
    }

    /**
     * 生成一次性token，该token只能使用一次
     *
     * @param prefix token前缀
     * @return
     */
    public String generateOnceToken(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            prefix = HrrsConsts.PROJECT_PREFIX;
        }
        if (!StringUtils.endsWith(prefix, "-") && !StringUtils.endsWith(prefix, "_")) {
            prefix = prefix + "-";
        }
        String key = prefix + "ot-" + CommonUtils.randomUuid();
        Object authToken = Optional.ofNullable(ContextUtils.getContextValue(HrrsConsts.AUTH_TOKEN_KEY)).orElse("apiToken");
        redisCache.set(key, authToken, ONCE_TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        return key;
    }

    /**
     * 校验onceToken是否有效
     *
     * @param key
     * @return
     */
    public boolean checkOnceToken(String key) {
        try {
            return StringUtils.isNotBlank(redisCache.getString(key));
        } finally {
            // redisCache.delete(key);
            // 将删除代码注释掉，让token在有效时间内可以多次使用
        }
    }

    /**
     * 获取OnceToken作为key存储的值
     *
     * @param key
     * @return
     */
    public String getAuthToken(String key) {
        return redisCache.getString(key);
    }
}
