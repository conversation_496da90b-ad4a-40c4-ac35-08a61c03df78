package com.csci.hrrs.util.holder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.model.PageableVO;
import com.csci.common.model.ResultPage;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.service.RosterDetailService;
import com.csci.hrrs.service.RosterHeadService;
import com.csci.hrrs.util.RedisCache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @deprecated 减少使用本地缓存，直接查询数据库
 */
@Component
@Slf4j
@Deprecated
public class LatestRosterHolder extends AbstractDataHolder<RosterDetail, List<RosterDetail>> {

    @Resource
    private RosterHeadService rosterHeadService;

    @Resource
    private RosterDetailService rosterDetailService;

    @Getter
    private List<RosterDetail> formerDataList;

    @Getter
    private List<RosterDetail> allDataList;

    @Getter
    private List<RosterDetail> hkDataList;

    @Resource
    private RedisCache redisCache;

    @Override
    protected void loadEntity() {
        String headId = rosterHeadService.getLatestHeadId();

        // 获取在职员工记录
        loadOnJobRoster(headId);

        // 获取离职员工记录
        loaderFormerRoster();

        log.info("加载最新花名册数据完成，共{}条数据", allDataList.size());

    }

    private void loaderFormerRoster() {
        // 使用分页查询防止大数据量导致内存溢出
        int pageSize = 1000;
        int currentPage = 1;
        formerDataList = new ArrayList<>();

        while (true) {
            PageableVO pageableVO = new PageableVO();
            pageableVO.setPageSize(pageSize);
            pageableVO.setCurPage(currentPage);

            LambdaQueryWrapper<RosterDetail> formerQueryWrapper = new LambdaQueryWrapper<>();
            formerQueryWrapper.eq(RosterDetail::getIsDeleted, false)
                    .eq(RosterDetail::getHeadId, HrrsConsts.formerEmployeeHeadId);

            ResultPage<RosterDetail> resultPage = rosterDetailService.listByPageWithQuery(formerQueryWrapper,
                    pageableVO);

            if (resultPage == null || CollectionUtils.isEmpty(resultPage.getList())) {
                break;
            }

            formerDataList.addAll(resultPage.getList());
            log.info("已加载{}条离职花名册数据", formerDataList.size());

            // 判断是否最后一页
            if (currentPage >= resultPage.getPages()) {
                break;
            }

            currentPage++;
        }

        // 合并数据时也使用流式处理避免大列表操作
        allDataList = Stream.concat(get().stream(), formerDataList.stream())
                .collect(Collectors.toList());
    }

    private void loadOnJobRoster(String headId) {
        LambdaQueryWrapper<RosterDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RosterDetail::getHeadId, headId).eq(RosterDetail::getIsDeleted, Boolean.FALSE);

        // 使用分页查询防止大数据量导致内存溢出
        int pageSize = 1000;
        int currentPage = 1;
        List<RosterDetail> dataList = new ArrayList<>();

        while (true) {
            PageableVO pageableVO = new PageableVO();
            pageableVO.setPageSize(pageSize);
            pageableVO.setCurPage(currentPage);

            // 修复：将查询条件传入listByPage方法
            ResultPage<RosterDetail> resultPage = rosterDetailService.listByPageWithQuery(queryWrapper, pageableVO);

            // 添加异常处理防止中断
            try {
                if (resultPage == null || CollectionUtils.isEmpty(resultPage.getList())) {
                    break;
                }

                dataList.addAll(resultPage.getList());
                log.info("已加载{}条花名册数据", dataList.size());

                // 判断是否最后一页
                if (currentPage >= resultPage.getPages()) {
                    break;
                }

                currentPage++;
            } catch (Exception e) {
                log.error("分页查询花名册数据异常，当前页: {}", currentPage, e);
                break;
            }
        }

        setDataList(dataList);
    }

    // setDataList(rosterDetailService.listByHeadId(headId));
}
