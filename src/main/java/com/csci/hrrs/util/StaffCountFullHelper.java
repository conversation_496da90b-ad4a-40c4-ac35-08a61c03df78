package com.csci.hrrs.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.util.CommonUtils;
import com.csci.common.util.DateUtils;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.hk.HKDashFacade;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.service.OrganizationService;
import com.csci.hrrs.service.RosterDetailService;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class StaffCountFullHelper {

    public static long calcStaffCountByYearMonth(List<StaffCountFull> staffCountFullList, LocalDate date) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);

        return staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .map(staffCountFull -> {
                    try {
                        // 检查字段是否存在
                        Field field = StaffCountFull.class.getDeclaredField(fieldName);
                        field.setAccessible(true); // 设置可访问性
                        BigDecimal value = (BigDecimal) field.get(staffCountFull);
                        // 如果value不存在，返回0
                        return value == null ? BigDecimal.ZERO : value;
                    } catch (NoSuchFieldException e) {
                        // 如果字段不存在，记录信息
                        return BigDecimal.ZERO;
                    } catch (IllegalAccessException e) {
                        // 记录非法访问异常
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(CommonUtils::add)
                .map(BigDecimal::longValue)
                .orElse(0L);
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/6/13 10:15
     * @Description 计算当月工人队伍人数(外包加自有日薪)
     * @Param : staffCountFullList
     * @Param : date
     * @Return: long
    */
    public static long calcWorkerCountByYearMonth(List<StaffCountFull> staffCountFullList, LocalDate date) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);

        return staffCountFullList.stream()
                .filter(staffCountFull -> "BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode()) || "BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .map(staffCountFull -> {
                    try {
                        // 检查字段是否存在
                        Field field = StaffCountFull.class.getDeclaredField(fieldName);
                        field.setAccessible(true); // 设置可访问性
                        BigDecimal value = (BigDecimal) field.get(staffCountFull);
                        // 如果value不存在，返回0
                        return value == null ? BigDecimal.ZERO : value;
                    } catch (NoSuchFieldException e) {
                        // 如果字段不存在，记录信息
                        return BigDecimal.ZERO;
                    } catch (IllegalAccessException e) {
                        // 记录非法访问异常
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(CommonUtils::add)
                .map(BigDecimal::longValue)
                .orElse(0L);
    }

    /**
     * 获取全周期计划的员工编制信息
     *
     * @param lstStaffCount
     * @param statDate
     * @return
     */
    public static long calcAllStaffCount(List<StaffCountFull> lstStaffCount, LocalDate statDate) {
        // 获取字段名集合，仅包含以 "d" 开头的字段
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d") && isFieldBeforeStatDate(fieldName, statDate)) // 添加判断字段是否在 statDate 之前
                .collect(Collectors.toSet());

        // 过滤并计算总数
        BigDecimal totalCount = lstStaffCount.stream()
                // 筛选仅保留 subjectType 为 "编制人数" 的记录
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                // 遍历字段并计算总和
                .flatMap(staffCountFull -> fieldNames.stream()
                        .map(fieldName -> {
                            try {
                                return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException("Error reading field: " + fieldName, e);
                            }
                        }))
                // 累加 BigDecimal 值
                .reduce(BigDecimal.ZERO, CommonUtils::add);

        // 返回结果的 long 值
        return totalCount.longValue();
    }


    // 实现逻辑来判断 fieldName 是否在 statDate 之前
    private static boolean isFieldBeforeStatDate(String fieldName, LocalDate statDate) {
        String pitchDate = DateTimeFormatter.ofPattern("yyyyMMdd").format(statDate);
        LocalDate endDate = LocalDate.parse(pitchDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 例如，假设字段名包含日期的部分，那么你可以解析并比较
        String dateString = fieldName.substring(1) + "01";
        LocalDate fieldDate = LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 比较日期: fieldName 日期是否在 statDate 之前或等于
        return !fieldDate.isAfter(endDate);
    }


    public static BigDecimal calcStaffCountYearByDateAndCode(List<StaffCountFull> staffCountFullList, LocalDate date, String subjectCode) {
        BigDecimal total = BigDecimal.ZERO;
        List<StaffCountFull> validStaffCountFullList = staffCountFullList.stream()
                .filter(staffCountFull -> StringUtils.equals(subjectCode, staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : validStaffCountFullList) {
            for (LocalDate d = date.withDayOfYear(1); d.isBefore(date.with(TemporalAdjusters.lastDayOfMonth())); d = d.plusMonths(1)) {
                total = CommonUtils.add(total, HKDashFacade.getStaffCountFieldByDateAndCode(staffCountFull, d));
            }
        }
        return total;
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/5/23 14:30
     * @Description 获取一年的编制汇总
     * @Param : staffCountFullList
     * @Param : date
     * @Return: java.math.BigDecimal
    */
    public static BigDecimal calcStaffCountYearByDate(List<StaffCountFull> staffCountFullList,LocalDate date){
        BigDecimal total = BigDecimal.ZERO;
        List<StaffCountFull> validStaffCountFullList = staffCountFullList.stream()
                .filter(staffCountFull -> HrrsConsts.HR_STAFF_CODE.contains(staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : validStaffCountFullList) {
            for (LocalDate d = date.withDayOfYear(1); d.isBefore(date.with(TemporalAdjusters.lastDayOfYear())); d = d.plusMonths(1)) {
                total = CommonUtils.add(total, HKDashFacade.getStaffCountFieldByDateAndCode(staffCountFull, d));
            }
        }
        return total;
    }
    public static BigDecimal calcTargetOutputValueYearByDate(List<StaffCountFull> staffCountFullList,LocalDate date){
        BigDecimal total = BigDecimal.ZERO;
        List<StaffCountFull> validStaffCountFullList = staffCountFullList.stream()
                .filter(staffCountFull -> HrrsConsts.TARGET_OUTPUT_VALUE_CODE.equals(staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : validStaffCountFullList) {
            for (LocalDate d = date.withDayOfYear(1); d.isBefore(date.with(TemporalAdjusters.lastDayOfYear())); d = d.plusMonths(1)) {
                total = CommonUtils.add(total, HKDashFacade.getStaffCountFieldByDateAndCode(staffCountFull, d));
            }
        }
        return total;
    }

    public static long calcStaffCountByDateAndCode(List<StaffCountFull> staffCountFullList, LocalDate date, String subjectCode) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);

        return staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()) && subjectCode.equals(staffCountFull.getSubjectCode()))
                .map(staffCountFull -> {
                    try {
                        // 检查字段是否存在
                        Field field = StaffCountFull.class.getDeclaredField(fieldName);
                        field.setAccessible(true); // 设置可访问性
                        BigDecimal value = (BigDecimal) field.get(staffCountFull);
                        // 如果value不存在，返回0
                        return value == null ? BigDecimal.ZERO : value;
                    } catch (IllegalAccessException | NoSuchFieldException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(CommonUtils::add)
                .map(BigDecimal::longValue)
                .orElse(0L);
    }

    public static long calcStaffCountByDateAndSubjectCodeAndOrgCode(List<StaffCountFull> staffCountFullList, LocalDate date, List<String> orgCodes, String subjectCode) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);

        return staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()) && subjectCode.equals(staffCountFull.getSubjectCode()))
                .filter(staffCountFull -> !orgCodes.contains(staffCountFull.getOrganizationCode())) // 排除特定orgCodes数据
                .map(staffCountFull -> {
                    try {
                        // 检查字段是否存在
                        Field field = StaffCountFull.class.getDeclaredField(fieldName);
                        field.setAccessible(true); // 设置可访问性
                        BigDecimal value = (BigDecimal) field.get(staffCountFull);
                        // 如果value不存在，返回0
                        return value == null ? BigDecimal.ZERO : value;
                    } catch (IllegalAccessException | NoSuchFieldException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(CommonUtils::add)
                .map(BigDecimal::longValue)
                .orElse(0L);
    }

    public static BigDecimal calcStaffCountMonthByDateAndCode(List<StaffCountFull> staffCountFullList, LocalDate date, String subjectCode) {
        BigDecimal total = BigDecimal.ZERO;
        List<StaffCountFull> validStaffCountFullList = staffCountFullList.stream()
                .filter(staffCountFull -> StringUtils.equals(subjectCode, staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : validStaffCountFullList) {
            total = CommonUtils.add(total, HKDashFacade.getStaffCountFieldByDateAndCode(staffCountFull, date));
        }
        return total;
    }

    public static long calcStaffCountOfYear(List<StaffCountFull> staffCountFullList, LocalDate statDate) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType())).toList();

        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
                try {
                    Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                    total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
                } catch (Exception ignore) {
                }
            }
        }
        return total.longValue();
    }

    public static long calcYearStaffCountOfSubjectCode(List<StaffCountFull> staffCountFullList, LocalDate statDate, List<String> subjectCodes) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> subjectCodes.contains(staffCountFull.getSubjectCode()))
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType())).toList();

        LocalDate startDate = statDate.withDayOfYear(1);
        LocalDate endDate = statDate.with(TemporalAdjusters.lastDayOfMonth());

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
                try {
                    Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                    total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
                } catch (Exception ignore) {
                }
            }
        }
        return total.longValue();
    }

    public static BigDecimal calcYearSalaryOfSubjectCode(List<StaffCountFull> staffCountFullList, LocalDate startDate,LocalDate endDate, List<String> subjectCodes) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> subjectCodes.contains(staffCountFull.getSubjectCode()))
                .filter(staffCountFull -> "编制预算".equals(staffCountFull.getSubjectType())).toList();

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
                try {
                    Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                    total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
                } catch (Exception ignore) {
                }
            }
        }
        return total;
    }


    /**
     * 计算指定编制记录所有属性的值之和
     *
     * @param staffCountFull 编制记录
     * @return
     */
    public static BigDecimal calcStaffCountFullValue(StaffCountFull staffCountFull) {
        return Stream.of(FieldUtils.getAllFields(staffCountFull.getClass()))
                .filter(field -> field.getName().startsWith("d"))
                .map(field -> {
                    try {
                        return (BigDecimal) FieldUtils.readField(staffCountFull, field.getName(), true);
                    } catch (IllegalAccessException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(CommonUtils::add)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 计算指定日期范围内编制人数
     *
     * @param staffCountFullList 编制人数
     * @param startDate
     * @param endDate
     * @return
     */
    public static long calcStaffCountPlan(List<StaffCountFull> staffCountFullList, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(staffCountFullList)) {
            return 0;
        }
        if (startDate == null || endDate == null) {
            return 0;
        }
        long total = 0;
        // 取subjectType=编制人数
        List<StaffCountFull> lstStaffCount = staffCountFullList.stream()
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .toList();
        for (StaffCountFull staffCountFull : lstStaffCount) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                total += getValueByDate(staffCountFull, date).longValue();
            }
        }
        return total;
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/6/13 10:12
     * @Description 计算指定日期范围内工人队伍人数(外包加自有日薪)
     * @Param : staffCountFullList
     * @Param : startDate
     * @Param : endDate
     * @Return: long
    */
    public static long calcWorkerCountPlan(List<StaffCountFull> staffCountFullList, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(staffCountFullList)) {
            return 0;
        }
        if (startDate == null || endDate == null) {
            return 0;
        }
        long total = 0;
        // 取subjectType=编制人数
        List<StaffCountFull> lstStaffCount = staffCountFullList.stream()
                .filter(staffCountFull -> "BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode()) || "BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : lstStaffCount) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                total += getValueByDate(staffCountFull, date).longValue();
            }
        }
        return total;
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/5/23 17:00
     * @Description 获取全周期的编制人数
     * @Param : staffCountFullList
     * @Return: java.math.BigDecimal
    */
    public static BigDecimal getAllStaffCountPlan(List<StaffCountFull> staffCountFullList){
        // 获取字段名集合，仅包含以 "d" 开头的字段
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d")) // 添加判断字段是否在 statDate 之前
                .collect(Collectors.toSet());

        // 过滤并计算总数
        return staffCountFullList.stream()
                // 筛选仅保留 subjectType 为 "编制人数" 的记录
                // .filter(staffCountFull -> "薪酬预算".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> HrrsConsts.HR_STAFF_CODE.contains(staffCountFull.getSubjectCode()))
                // 遍历字段并计算总和
                .flatMap(staffCountFull -> fieldNames.stream()
                        .map(fieldName -> {
                            try {
                                return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException("Error reading field: " + fieldName, e);
                            }
                        }))
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    public static BigDecimal getValueByDate(StaffCountFull staffCountFull, LocalDate date) {
        String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
        try {
            BigDecimal count = (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
            return count == null ? BigDecimal.ZERO : count;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 对编制人数每个月的值求和，返回最大值的日期和值
     *
     * @param staffCountFullList
     * @return
     */
    public static Map.Entry<LocalDate, BigDecimal> getMaxStaffCountDateAndValue(List<StaffCountFull> staffCountFullList) {
        Map<LocalDate, BigDecimal> mapAllDateValue = new HashMap<>();
        Field[] fields = FieldUtils.getAllFields(StaffCountFull.class);
        List<String> fieldNames = Stream.of(fields)
                .filter(field -> field.getName().startsWith("d"))
                .map(Field::getName)
                .toList();
        staffCountFullList.stream()
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .forEach(staffCountFull -> {
                    fieldNames.forEach(fieldName -> {
                        // 排除掉2023年12月，这个月是初始化的期初数据
                        if ("d202312".equals(fieldName)) {
                            return;
                        }
                        try {
                            BigDecimal value = (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            LocalDate date = DateUtils.toLocalDateTime(fieldName.substring(1)).toLocalDate();
                            mapAllDateValue.put(date, CommonUtils.add(mapAllDateValue.getOrDefault(date, BigDecimal.ZERO), value));
                        } catch (Exception e) {
                            log.error("get value error", e);
                        }
                    });
                });
        return mapAllDateValue.entrySet().stream().max(Map.Entry.comparingByValue()).orElse(null);
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/6/13 10:18
     * @Description 对工人队伍人数每个月的值求和，返回最大值的日期和值
     * @Param : staffCountFullList
     * @Return: java.util.Map.Entry<java.time.LocalDate,java.math.BigDecimal>
    */
    public static Map.Entry<LocalDate, BigDecimal> getMaxWorkerCountDateAndValue(List<StaffCountFull> staffCountFullList) {
        Map<LocalDate, BigDecimal> mapAllDateValue = new HashMap<>();
        Field[] fields = FieldUtils.getAllFields(StaffCountFull.class);
        List<String> fieldNames = Stream.of(fields)
                .filter(field -> field.getName().startsWith("d"))
                .map(Field::getName)
                .toList();
        staffCountFullList.stream()
                .filter(staffCountFull -> "BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode()) || "BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode()))
                .forEach(staffCountFull -> {
                    fieldNames.forEach(fieldName -> {
                        // 排除掉2023年12月，这个月是初始化的期初数据
                        if ("d202312".equals(fieldName)) {
                            return;
                        }
                        try {
                            BigDecimal value = (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            LocalDate date = DateUtils.toLocalDateTime(fieldName.substring(1)).toLocalDate();
                            mapAllDateValue.put(date, CommonUtils.add(mapAllDateValue.getOrDefault(date, BigDecimal.ZERO), value));
                        } catch (Exception e) {
                            log.error("get value error", e);
                        }
                    });
                });
        return mapAllDateValue.entrySet().stream().max(Map.Entry.comparingByValue()).orElse(null);
    }

    /**
     * 计算指定日期范围内的最大编制人数及其对应的日期
     *
     * @param staffCountFullList 编制人数列表
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @return 最大值及对应日期的映射
     */
    public static Map.Entry<LocalDate, BigDecimal> getMaxStaffCountByYear(List<StaffCountFull> staffCountFullList, LocalDate startDate, LocalDate endDate) {
        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .toList();

        Map<LocalDate, BigDecimal> mapAllDateValue = new HashMap<>();
        for (StaffCountFull staffCountFull : forStaffList) {
            for (LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusMonths(1)) {
                String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
                try {
                    BigDecimal value = (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                    if (value == null) {
                        value = BigDecimal.ZERO; // 如果值为null则设为0
                    }
                    mapAllDateValue.put(date, CommonUtils.add(mapAllDateValue.getOrDefault(date, BigDecimal.ZERO), value));
                } catch (Exception e) {
                    log.error("get value error for date: {}", date, e);
                }
            }
        }

        return mapAllDateValue.entrySet().stream().max(Map.Entry.comparingByValue()).orElse(null);
    }


    public static BigDecimal calcSalaryBudget(List<StaffCountFull> staffCountFullList, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(staffCountFullList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal total = BigDecimal.ZERO;
        // 取subjectType=薪酬预算
        List<StaffCountFull> lstStaffCount = staffCountFullList.stream()
                // .filter(staffCountFull -> "薪酬预算".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> HrrsConsts.BUDGET_SALARY.contains(staffCountFull.getSubjectCode()))
                .toList();
        for (StaffCountFull staffCountFull : lstStaffCount) {
            for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
                total = total.add(getValueByDate(staffCountFull, date));
            }
        }
        return total;
    }

    /**
     * 全周期薪酬预算
     *
     * @param lstStaffCountFull
     * @param statDate
     * @return
     */
    public static BigDecimal getAllStaffASalaryCount(List<StaffCountFull> lstStaffCountFull, LocalDate statDate) {

        // 获取字段名集合，仅包含以 "d" 开头的字段
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d") && isFieldBeforeStatDate(fieldName, statDate)) // 添加判断字段是否在 statDate 之前
                .collect(Collectors.toSet());

        // 过滤并计算总数
        return lstStaffCountFull.stream()
                // 筛选仅保留 subjectType 为 "编制人数" 的记录
                // .filter(staffCountFull -> "薪酬预算".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> HrrsConsts.BUDGET_SALARY.contains(staffCountFull.getSubjectCode()))
                // 遍历字段并计算总和
                .flatMap(staffCountFull -> fieldNames.stream()
                        .map(fieldName -> {
                            try {
                                return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException("Error reading field: " + fieldName, e);
                            }
                        }))
                // 累加 BigDecimal 值
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    /**
     * @author: chenchengwu
     * @Date: 2025/5/28 17:03
     * @Description 计算全周期目标产值
     * @Param : staffCountFullList
     * @Return: java.math.BigDecimal
    */
    public static BigDecimal getAllTargetOutputValue(List<StaffCountFull> staffCountFullList){
        // 获取字段名集合，仅包含以 "d" 开头的字段
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d"))
                .collect(Collectors.toSet());
        return staffCountFullList.stream()
                .filter(staffCountFull -> HrrsConsts.TARGET_OUTPUT_VALUE_CODE.equals(staffCountFull.getSubjectCode()))
                // 遍历字段并计算总和
                .flatMap(staffCountFull -> fieldNames.stream()
                        .map(fieldName -> {
                            try {
                                return (BigDecimal) FieldUtils.readField(staffCountFull, fieldName, true);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException("Error reading field: " + fieldName, e);
                            }
                        }))
                // 累加 BigDecimal 值
                .reduce(BigDecimal.ZERO, CommonUtils::add);
    }

    public static Integer getAllMonthCounts(){
        Integer monthCount = Integer.valueOf(0);
        Set<String> fieldNames = Stream.of(FieldUtils.getAllFields(StaffCountFull.class))
                .map(Field::getName)
                .filter(fieldName -> fieldName.startsWith("d") ) // 添加判断字段是否在 statDate 之前
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(fieldNames)){
            monthCount = fieldNames.size();
        }
        return monthCount;
    }

    /**
     * 计算指定日期的编制人数
     *
     * @param staffCountFullList
     * @param yearMonth
     * @return
     */
    public static long calcStaffCountOfMonth(List<StaffCountFull> staffCountFullList, YearMonth yearMonth) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .toList();

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(yearMonth);
            try {
                Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
            } catch (Exception ignore) {
            }
        }
        return total.longValue();
    }

    /**
     * 计算指定日期的内派编制人数
     *
     * @param staffCountFullList
     * @param yearMonth
     * @return
     */
    public static long calculateStaffCountOfSubjectCodeMonth(List<StaffCountFull> staffCountFullList, YearMonth yearMonth, List<String> subjectCodes) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> subjectCodes.contains(staffCountFull.getSubjectCode()))
                .toList();

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(yearMonth);
            try {
                Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
            } catch (Exception ignore) {
            }
        }
        return total.longValue();
    }

    /**
     * 计算指定日期的内派编制人数
     *
     * @param staffCountFullList
     * @param yearMonth
     * @return
     */
    public static BigDecimal calculateSalaryOfSubjectCodeMonth(List<StaffCountFull> staffCountFullList, YearMonth yearMonth, List<String> subjectCodes) {

        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> "薪酬预算".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> subjectCodes.contains(staffCountFull.getSubjectCode()))
                .toList();

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(yearMonth);
            try {
                Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
            } catch (Exception ignore) {
            }
        }
        return total;
    }

    public static long calcSixMonthMaxWorkerDemand(List<StaffCountFull> staffCountFullList, LocalDate date) {
        long max = 0;
        for (int i = 0; i < 6; i++) {
            LocalDate d = date.plusMonths(i);
            long count = calcStaffCountOfMonth(staffCountFullList, YearMonth.from(d));
            if (count > max) {
                max = count;
            }
        }
        return max;
    }

    /**
     * 取得组织机构的开始日期，如果开始日期为空则取花名册最早拥有人员记录的日期
     *
     * @param organization 组织机构
     * @return
     */
    public static LocalDate determineStartDate(Organization organization) {
        LocalDate startDate = organization.getStartDate();
        if (startDate == null) {
            RosterDetailService rosterDetailService = SpringContextHolder.getBean(RosterDetailService.class);
            startDate = rosterDetailService.selectMinStatDate(organization.getCode());
        }

        return startDate;
    }

    public static LocalDate determineEndDate(Organization organization, LocalDate statDate) {
        LocalDate endDate = organization.getEndDate();
        if (endDate == null) {
            endDate = statDate;
        }
        return endDate;
    }

    public static Long calcStaffCountByDateAndOrgCode(List<StaffCountFull> staffCountFullList, LocalDate date, List<String> orgCodes) {
        List<StaffCountFull> forStaffList = staffCountFullList.stream()
                .filter(staffCountFull -> "编制人数".equals(staffCountFull.getSubjectType()))
                .filter(staffCountFull -> orgCodes.contains(staffCountFull.getOrganizationCode()))
                .filter(staffCountFull -> !"BZ.06".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除外包
                .filter(staffCountFull -> !"BZ.05".equalsIgnoreCase(staffCountFull.getSubjectCode())) // 排除自有日薪
                .toList();

        BigDecimal total = BigDecimal.ZERO;
        for (StaffCountFull staffCountFull : forStaffList) {
            String fieldName = "d" + DateTimeFormatter.ofPattern("yyyyMM").format(date);
            try {
                Field field = FieldUtils.getField(StaffCountFull.class, fieldName, true);
                total = CommonUtils.add(total, (BigDecimal) field.get(staffCountFull));
            } catch (Exception ignore) {
            }
        }
        return total.longValue();
    }

    public static List<String> getChildrenOrgCodes(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return Collections.emptyList();
        }

        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        Organization organization = organizationService.getByCode(orgCode);
        if (organization == null) {
            return Collections.emptyList();
        }

        // 缓存香港子公司列表（建议提升为类变量）
        List<String> hkSubCompanyCodes = organizationService.listAllHKSubCompany()
                .stream()
                .map(Organization::getCode)
                .toList();

        // 如果不是香港子公司下的项目，直接返回原orgCode
        if (!hkSubCompanyCodes.contains(organization.getParentCode())) {
            return Collections.singletonList(orgCode);
        }

        // 查询子单位（优化查询条件）
        List<Organization> childrenOrgs = organizationService.list(
                new LambdaQueryWrapper<Organization>()
                        .select(Organization::getCode)
                        .eq(Organization::getIsDeleted, false)
                        .likeRight(Organization::getNo, organization.getNo())
        );

        return childrenOrgs.stream()
                .map(Organization::getCode)
                .collect(Collectors.toList());
    }
}
