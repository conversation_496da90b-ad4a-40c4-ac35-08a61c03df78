package com.csci.hrrs.talent.facade;

import com.csci.common.model.PageableVO;
import com.csci.common.model.ResultPage;
import com.csci.hrrs.facade.FormerEmployeeFacade;
import com.csci.hrrs.model.*;
import com.csci.hrrs.service.*;
import com.csci.hrrs.talent.entity.TalentReviewParticipant;
import com.csci.hrrs.talent.entity.TalentReviewPlan;
import com.csci.hrrs.talent.entity.ViewTalentReviewParticipant;
import com.csci.hrrs.talent.service.TalentReviewAssignmentService;
import com.csci.hrrs.talent.service.TalentReviewParticipantService;
import com.csci.hrrs.talent.service.TalentReviewPlanService;
import com.csci.hrrs.talent.service.ViewTalentReviewParticipantService;
import com.csci.hrrs.talent.task.TalentReviewWorkflowTask;
import com.csci.hrrs.talent.vo.TalentReviewParticipantVO;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.util.excel.ColumnDefVO;
import com.csci.hrrs.util.excel.GeneralExporter;
import com.csci.hrrs.vo.FormVO;
import com.csci.hrrs.vo.RosterDetailVO;
import com.csci.hrrs.vo.WorkflowControlVO;
import com.csci.hrrs.vo.WorkflowNodeVO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import com.csci.hrrs.login.util.TokenHandler;
import com.csci.hrrs.util.context.ContextUtils;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人才盘点Facade类
 * 用于协调各个Service之间的交互
 */
@Component
@Slf4j
public class TalentReviewFacade {

    @Resource
    private TalentReviewPlanService talentReviewPlanService;

    @Resource
    private TalentInventoryRosterService talentInventoryRosterService;

    @Resource
    private TalentReviewParticipantService talentReviewParticipantService;

    @Resource
    private TalentReviewWorkflowTask talentReviewWorkflowTask;

    @Resource
    private ViewTalentReviewParticipantService viewTalentReviewParticipantService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private TokenHandler tokenHandler;

    @Resource
    private TalentReviewAssignmentService talentReviewAssignmentService;

    /**
     * 创建盘点计划并自动生成参与人员记录，同时异步为每个参与者发起工作流程
     *
     * @param plan 盘点计划信息
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createPlanWithParticipants(TalentReviewPlan plan) {
        // 如果id为空，则生成UUID，否则使用参数中的值
        if (plan.getId() == null || plan.getId().isEmpty()) {
            plan.setId(UUID.randomUUID().toString());
        }
        // 设置创建时间和最后更新时间
        LocalDateTime now = LocalDateTime.now();
        plan.setCreationTime(now);
        plan.setLastUpdateTime(now);

        // 保存计划
        boolean result = talentReviewPlanService.save(plan);

        if (result) {
            // 获取所有员工记录
            List<TalentInventoryRoster> rosterList = talentInventoryRosterService.list();

            // 创建参与人员记录
            List<TalentReviewParticipant> participants = new ArrayList<>();
            for (TalentInventoryRoster roster : rosterList) {
                TalentReviewParticipant participant = new TalentReviewParticipant();
                participant.setPlanId(plan.getId());
                participant.setStaffNo(roster.getPernr());
                participant.setIsCoreTalent(false);
                participant.setPromotionSalary(false);
                participant.setStatus("pending");
                participants.add(participant);
            }

            // 批量保存参与人员记录
            boolean saveResult = talentReviewParticipantService.batchAddParticipants(participants);

            if (saveResult) {
                // 获取所有参与者的ID
                List<String> participantIds = participants.stream()
                        .map(TalentReviewParticipant::getId)
                        .collect(java.util.stream.Collectors.toList());

                // 异步发起工作流程
                talentReviewWorkflowTask.processWorkflowInit(participantIds);
            }

            return saveResult;
        }

        return result;
    }

    public void exportWorkflowAuditToFile() {
        try (GeneralExporter exporter = new GeneralExporter();
                FileOutputStream fileOutputStream = new FileOutputStream(
                        "C:\\Users\\<USER>\\Desktop\\output-file\\TalentReviewParticipants.xlsx")) {
            exporter.setTitleText("人才盘点审批表");
            exporter.setSheetName("审批表");
            exporter.setStatDate(LocalDate.now());
            exporter.exportToExcel(listTalentWorkflowAuditInfo(), getHeaderList(), fileOutputStream);
        } catch (Exception e) {
            log.error("导出花名册失败", e);
        }
    }

    public List<ColumnDefVO> getHeaderList() {
        List<ColumnDefVO> columnDefList = new ArrayList<>();
        columnDefList.add(new ColumnDefVO("组织机构名称", "orgName"));
        columnDefList.add(new ColumnDefVO("组织机构编码", "orgCode"));
        columnDefList.add(new ColumnDefVO("审批人1姓名", "audit1name"));
        columnDefList.add(new ColumnDefVO("审批人1用户名", "audit1username"));
        columnDefList.add(new ColumnDefVO("审批人2姓名", "audit2name"));
        columnDefList.add(new ColumnDefVO("审批人2用户名", "audit2username"));
        columnDefList.add(new ColumnDefVO("审批人3姓名", "audit3name"));
        columnDefList.add(new ColumnDefVO("审批人3用户名", "audit3username"));
        columnDefList.add(new ColumnDefVO("大团队负责人用户名", "teamLeaderUsername"));
        return columnDefList;
    }

    public List<Map<String, Object>> listTalentWorkflowAuditInfo() {
        List<Map<String, Object>> list = new ArrayList<>();
        // list.add(Map.of("orgCode", "100000", "orgName", "总部"));
        // list.add(Map.of("orgCode", "100001", "orgName", "北京分公司"));
        // list.add(Map.of("orgCode", "100002", "orgName", "上海分公司"));

        FormService formService = SpringContextHolder.getBean(FormService.class);
        FormVO form = formService.findByCode("talent-review-plan");

        WorkflowNodeService workflowNodeService = SpringContextHolder.getBean(WorkflowNodeService.class);
        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        WorkflowService workflowService = SpringContextHolder.getBean(WorkflowService.class);
        List<Workflow> lstWorkflow = workflowService.listByFormId(form.getId());

        Map<String, Object> data = new HashMap<>();
        for (Workflow workflow : lstWorkflow) {
            if (workflow != null) {
                Organization organization = organizationService.getByCode(workflow.getOrganizationCode());
                data.put("orgName", organization.getName());
                data.put("orgCode", organization.getCode());
                List<WorkflowNodeVO> lstNodeVO = ServiceHelper.listSortedWorkflowNodes(workflow.getId());
                for (WorkflowNodeVO workflowNodeVO : lstNodeVO) {
                    List<User> lstUser = workflowNodeService.listNodeUser(workflowNodeVO.getId());
                    if (CollectionUtils.isNotEmpty(lstUser)) {
                        User user = lstUser.get(0);
                        if (StringUtils.equalsIgnoreCase("审批人1", workflowNodeVO.getName())) {
                            data.put("audit1name", user.getName());
                            data.put("audit1username", user.getUsername());
                        } else if ("审批人2".equalsIgnoreCase(workflowNodeVO.getName())) {
                            data.put("audit2name", user.getName());
                            data.put("audit2username", user.getUsername());
                        } else if ("审批人3".equalsIgnoreCase(workflowNodeVO.getName())) {
                            data.put("audit3name", user.getName());
                            data.put("audit3username", user.getUsername());
                        } else if ("大团队负责人".equalsIgnoreCase(workflowNodeVO.getName())) {
                            data.put("teamLeaderUsername", user.getUsername());
                        }
                    }
                }
                list.add(data);
            }
        }

        return list;
    }

    /**
     * 根据盘点计划ID分页查询参与人员列表，并附带其工作流状态信息。
     * 支持三级权限控制：
     * 1. 人才盘点管理员：查看所有参与人员
     * 2. 有组织机构分配权限的用户：查看分配的组织机构参与人员 + 有审批权限的参与人员
     * 3. 普通用户：仅查看有审批权限的参与人员
     *
     * @param planId     盘点计划ID
     * @param pageableVO 分页参数
     * @return 包含参与人员详细信息和工作流状态的分页结果
     */
    public ResultPage<TalentReviewParticipantVO> listParticipantsByPlanByPage(String planId, PageableVO pageableVO) {
        ResultPage<ViewTalentReviewParticipant> resultPage;
        // 1. 如果当前用户是人才盘点管理员，则允许查询方案的所有参与人员信息
        if (tokenHandler.isTalentManager()) {
            resultPage = viewTalentReviewParticipantService.listByPage(planId, pageableVO);
        } else {
            // 2. 检查用户是否有基于组织机构的分配权限
            String currentUsername = ContextUtils.getCurrentUser().getUsername();
            String currentUserId = ContextUtils.getCurrentUser().getId();
            List<String> assignedOrgCodes = talentReviewAssignmentService.getAssignedOrganizationCodes(currentUsername);

            if (assignedOrgCodes != null && !assignedOrgCodes.isEmpty()) {
                // 3. 用户有组织机构权限分配，同时查询分配的组织机构和有审批权限的参与人员
                resultPage = viewTalentReviewParticipantService.listByPageOrgCodesAndUserId(planId, pageableVO,
                        assignedOrgCodes, currentUserId);
            } else {
                // 4. 用户没有组织机构权限分配，使用原有的审批权限逻辑
                resultPage = viewTalentReviewParticipantService.listByPage(planId, pageableVO, currentUserId);
            }
        }
        return convertToParticipantVOPage(resultPage);
    }

    /**
     * 根据盘点计划ID和组织机构编码列表分页查询参与人员列表
     * 注意：此方法仅查询指定组织机构的参与人员，不包含审批权限
     * 已被 listByPageOrgCodesAndUserId 方法替代，保留用于特殊需求
     *
     * @param planId     盘点计划ID
     * @param pageableVO 分页参数
     * @param orgCodes   组织机构编码列表
     * @return 分页结果
     */
    @Deprecated
    private ResultPage<ViewTalentReviewParticipant> listParticipantsByPlanAndOrgCodes(String planId,
            PageableVO pageableVO, List<String> orgCodes) {
        // 参数验证
        if (StringUtils.isBlank(planId)) {
            throw new IllegalArgumentException("计划ID不能为空");
        }
        if (pageableVO == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        if (CollectionUtils.isEmpty(orgCodes)) {
            return new ResultPage<>(Collections.emptyList(), 0, pageableVO.getCurPage(), pageableVO.getPageSize());
        }

        // 使用优化后的批量查询方法，解决分页和性能问题
        return viewTalentReviewParticipantService.listByPageAndOrgCodes(planId, pageableVO, orgCodes);
    }

    private ResultPage<TalentReviewParticipantVO> convertToParticipantVOPage(
            ResultPage<ViewTalentReviewParticipant> resultPage) {
        // 如果查询结果列表为空，直接构造并返回空的分页VO结果，避免后续无效操作
        if (CollectionUtils.isEmpty(resultPage.getList())) {
            return new ResultPage<>(Collections.emptyList(), resultPage.getTotal(), resultPage.getPageNum(),
                    resultPage.getPageSize());
        }

        // 批量提取业务ID并查询对应的流程控制信息
        List<String> businessIdList = resultPage.getList().stream()
                .map(TalentReviewParticipant::getId)
                .collect(Collectors.toList());

        List<WorkflowControlVO> lstWorkflowControl = workflowControlService
                .listWorkflowControlByBusinessIds(businessIdList);

        // 将流程控制信息列表转换为以businessId为键的Map，便于快速查找
        Map<String, WorkflowControlVO> mapWorkflowControl = lstWorkflowControl.stream()
                .collect(Collectors.toMap(WorkflowControlVO::getBusinessId, workflowControl -> workflowControl));

        // 组装最终的VO列表，将参与人信息与流程信息合并
        List<TalentReviewParticipantVO> voList = resultPage.getList().stream().map(participant -> {
            TalentReviewParticipantVO participantVO = new TalentReviewParticipantVO();
            // 从视图对象复制基础属性到VO
            BeanUtils.copyProperties(participant, participantVO);
            // 从Map中获取并设置对应的流程控制信息
            participantVO.setWorkflowControl(mapWorkflowControl.get(participant.getId()));
            return participantVO;
        }).collect(Collectors.toList());

        // 构造并返回最终的分页结果对象
        return new ResultPage<>(voList, resultPage.getTotal(), resultPage.getPageNum(), resultPage.getPageSize());
    }
}