package com.csci.hrrs.talent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 盘点评分记录实体类
 */
@Data
@TableName("talent_review_score")
public class TalentReviewScore {
    
    /**
     * 主键ID
     */
    @TableId
    private String id;
    
    /**
     * 盘点计划ID
     */
    private String planId;
    
    /**
     * 参与人员ID
     */
    private String participantId;
    
    /**
     * 盘点属性ID
     */
    private String attributeId;
    
    /**
     * 盘点属性名称(冗余字段)
     */
    private String attributeName;
    
    /**
     * 盘点属性编码(冗余字段)
     */
    private String attributeCode;
    
    /**
     * 评分值
     */
    private String scoreValue;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 评分人ID
     */
    private String createUserId;
    
    /**
     * 创建时间
     */
    private LocalDateTime creationTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
} 