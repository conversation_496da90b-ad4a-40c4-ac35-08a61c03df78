package com.csci.hrrs.talent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 盘点计划参与人员实体类
 */
@Data
@TableName("talent_review_participant")
public class TalentReviewParticipant {
    
    /**
     * 主键ID
     */
    @TableId
    private String id;
    
    /**
     * 盘点计划ID
     */
    private String planId;
    
    /**
     * 员工编号(关联v_roster_hk_base视图)
     */
    private String staffNo;
    
    /**
     * 是否核心人才(1:是,0:否)
     */
    private Boolean isCoreTalent;
    
    /**
     * 是否提职调薪(1:是,0:否)
     */
    private Boolean promotionSalary;
    
    /**
     * 状态(pending/in_progress/approved/rejected)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime creationTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /*表单内容*/
    private String form;
} 