package com.csci.hrrs.talent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.hrrs.talent.entity.ViewTalentReviewParticipant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ViewTalentReviewParticipantMapper extends BaseMapper<ViewTalentReviewParticipant> {

    List<ViewTalentReviewParticipant> selectByUserIdAndPlanId(@Param("userId") String userId, @Param("planId") String planId);

    List<ViewTalentReviewParticipant> selectByOrgCodesAndUserIdAndPlanId(@Param("orgCodes") List<String> orgCodes,
            @Param("userId") String userId,
            @Param("planId") String planId);

}
