package com.csci.hrrs.talent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.hrrs.talent.entity.TalentReviewParticipant;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 盘点计划参与人员Mapper接口
 */
@Mapper
public interface TalentReviewParticipantMapper extends BaseMapper<TalentReviewParticipant> {

    /**
     * 根据计划ID查询参与人员列表
     * 
     * @param planId 计划ID
     * @return 参与人员列表
     */
    List<TalentReviewParticipant> selectByPlanId(String planId);
} 