package com.csci.hrrs.talent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.talent.entity.TalentReviewAttribute;
import com.csci.hrrs.talent.entity.TalentReviewPlan;
import com.csci.hrrs.talent.mapper.TalentReviewAttributeMapper;
import com.csci.hrrs.talent.service.TalentReviewAttributeService;
import com.csci.hrrs.talent.service.TalentReviewPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 盘点属性Service实现类
 */
@Service
public class TalentReviewAttributeServiceImpl extends ServiceImpl<TalentReviewAttributeMapper, TalentReviewAttribute>
        implements TalentReviewAttributeService {

    @Autowired
    private TalentReviewPlanService talentReviewPlanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAttribute(TalentReviewAttribute attribute) {
        // 生成UUID
        attribute.setId(UUID.randomUUID().toString());
        // 设置创建时间和最后更新时间
        LocalDateTime now = LocalDateTime.now();
        attribute.setCreationTime(now);
        attribute.setLastUpdateTime(now);
        return save(attribute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddAttributes(List<TalentReviewAttribute> attributes) {
        // 设置默认值
        LocalDateTime now = LocalDateTime.now();
        attributes.forEach(attribute -> {
            attribute.setId(UUID.randomUUID().toString());
            attribute.setCreationTime(now);
            attribute.setLastUpdateTime(now);
        });
        return saveBatch(attributes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAttribute(TalentReviewAttribute attribute) {
        // 更新最后更新时间
        attribute.setLastUpdateTime(LocalDateTime.now());
        return updateById(attribute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttribute(String id) {
        return removeById(id);
    }

    @Override
    public TalentReviewAttribute getAttributeDetail(String id) {
        return getById(id);
    }

    @Override
    public List<TalentReviewAttribute> getAttributesBySchemeId(String schemeId) {
        LambdaQueryWrapper<TalentReviewAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TalentReviewAttribute::getSchemeId, schemeId)
               .orderByAsc(TalentReviewAttribute::getSortOrder);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(String id, Integer sortOrder) {
        TalentReviewAttribute attribute = new TalentReviewAttribute();
        attribute.setId(id);
        attribute.setSortOrder(sortOrder);
        attribute.setLastUpdateTime(LocalDateTime.now());
        return updateById(attribute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateAttributes(String schemeId, List<TalentReviewAttribute> attributes) {
        // 检查方案是否被盘点计划使用
        List<TalentReviewPlan> plans = talentReviewPlanService.getPlansBySchemeId(schemeId);
        if (!plans.isEmpty()) {
            throw new RuntimeException("该方案已被盘点计划使用，无法修改属性");
        }

        // 删除原有属性
        LambdaQueryWrapper<TalentReviewAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TalentReviewAttribute::getSchemeId, schemeId);
        remove(wrapper);

        // 设置新属性的默认值
        LocalDateTime now = LocalDateTime.now();
        attributes.forEach(attribute -> {
            attribute.setId(UUID.randomUUID().toString());
            attribute.setSchemeId(schemeId);
            attribute.setCreationTime(now);
            attribute.setLastUpdateTime(now);
        });

        // 批量保存新属性
        return saveBatch(attributes);
    }
} 