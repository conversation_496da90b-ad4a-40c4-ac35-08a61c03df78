package com.csci.hrrs.talent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.csci.hrrs.talent.entity.TalentReviewApproval;

import java.util.List;

/**
 * 审批记录Service接口
 */
public interface TalentReviewApprovalService extends IService<TalentReviewApproval> {
    
    /**
     * 添加审批记录
     *
     * @param approval 审批记录信息
     * @return 添加结果
     */
    boolean addApproval(TalentReviewApproval approval);
    
    /**
     * 更新审批记录
     *
     * @param approval 审批记录信息
     * @return 更新结果
     */
    boolean updateApproval(TalentReviewApproval approval);
    
    /**
     * 删除审批记录
     *
     * @param id 审批记录ID
     * @return 删除结果
     */
    boolean deleteApproval(String id);
    
    /**
     * 获取审批记录详情
     *
     * @param id 审批记录ID
     * @return 审批记录详情
     */
    TalentReviewApproval getApprovalDetail(String id);
    
    /**
     * 获取参与人员的所有审批记录
     *
     * @param participantId 参与人员ID
     * @return 审批记录列表
     */
    List<TalentReviewApproval> getApprovalsByParticipantId(String participantId);
    
    /**
     * 获取计划下的所有审批记录
     *
     * @param planId 计划ID
     * @return 审批记录列表
     */
    List<TalentReviewApproval> getApprovalsByPlanId(String planId);
    
    /**
     * 获取特定审批人的审批记录
     *
     * @param planId 计划ID
     * @param participantId 参与人员ID
     * @param approverNo 审批人编号
     * @return 审批记录
     */
    TalentReviewApproval getApprovalByApprover(String planId, String participantId, String approverNo);
    
    /**
     * 更新审批状态
     *
     * @param id 审批记录ID
     * @param status 审批状态
     * @return 更新结果
     */
    boolean updateApprovalStatus(String id, String status);
    
    /**
     * 提交审批
     *
     * @param id 审批记录ID
     * @param comment 审批意见
     * @return 提交结果
     */
    boolean submitApproval(String id, String comment);
} 