package com.csci.hrrs.talent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.talent.entity.TalentReviewApproval;
import com.csci.hrrs.talent.mapper.TalentReviewApprovalMapper;
import com.csci.hrrs.talent.service.TalentReviewApprovalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 审批记录Service实现类
 */
@Service
public class TalentReviewApprovalServiceImpl extends ServiceImpl<TalentReviewApprovalMapper, TalentReviewApproval>
        implements TalentReviewApprovalService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addApproval(TalentReviewApproval approval) {
        // 生成ID
        approval.setId(UUID.randomUUID().toString());
        // 设置创建时间
        approval.setCreationTime(LocalDateTime.now());
        // 设置最后更新时间
        approval.setLastUpdateTime(LocalDateTime.now());
        // 设置默认状态
        approval.setApprovalStatus("pending");
        return save(approval);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateApproval(TalentReviewApproval approval) {
        // 设置最后更新时间
        approval.setLastUpdateTime(LocalDateTime.now());
        return updateById(approval);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteApproval(String id) {
        return removeById(id);
    }

    @Override
    public TalentReviewApproval getApprovalDetail(String id) {
        return getById(id);
    }

    @Override
    public List<TalentReviewApproval> getApprovalsByParticipantId(String participantId) {
        LambdaQueryWrapper<TalentReviewApproval> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TalentReviewApproval::getParticipantId, participantId);
        return list(wrapper);
    }

    @Override
    public List<TalentReviewApproval> getApprovalsByPlanId(String planId) {
        LambdaQueryWrapper<TalentReviewApproval> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TalentReviewApproval::getPlanId, planId);
        return list(wrapper);
    }

    @Override
    public TalentReviewApproval getApprovalByApprover(String planId, String participantId, String approverNo) {
        LambdaQueryWrapper<TalentReviewApproval> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TalentReviewApproval::getPlanId, planId)
                .eq(TalentReviewApproval::getParticipantId, participantId)
                .eq(TalentReviewApproval::getApproverNo, approverNo);
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateApprovalStatus(String id, String status) {
        TalentReviewApproval approval = new TalentReviewApproval();
        approval.setId(id);
        approval.setApprovalStatus(status);
        approval.setLastUpdateTime(LocalDateTime.now());
        return updateById(approval);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitApproval(String id, String comment) {
        TalentReviewApproval approval = new TalentReviewApproval();
        approval.setId(id);
        approval.setApprovalComment(comment);
        approval.setLastUpdateTime(LocalDateTime.now());
        return updateById(approval);
    }
} 