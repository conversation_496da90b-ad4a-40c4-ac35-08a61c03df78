package com.csci.hrrs.talent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.csci.hrrs.talent.entity.TalentReviewScheme;

/**
 * 盘点方案Service接口
 */
public interface TalentReviewSchemeService extends IService<TalentReviewScheme> {
    
    /**
     * 创建盘点方案
     *
     * @param scheme 盘点方案信息
     * @return 创建结果
     */
    boolean createScheme(TalentReviewScheme scheme);
    
    /**
     * 更新盘点方案
     *
     * @param scheme 盘点方案信息
     * @return 更新结果
     */
    boolean updateScheme(TalentReviewScheme scheme);
    
    /**
     * 删除盘点方案
     *
     * @param id 方案ID
     * @return 删除结果
     */
    boolean deleteScheme(String id);
    
    /**
     * 获取盘点方案详情
     *
     * @param id 方案ID
     * @return 方案详情
     */
    TalentReviewScheme getSchemeDetail(String id);
    
    /**
     * 设置方案为模板
     *
     * @param id 方案ID
     * @return 设置结果
     */
    boolean setAsTemplate(String id);
    
    /**
     * 启用/禁用方案
     *
     * @param id 方案ID
     * @param status 状态
     * @return 设置结果
     */
    boolean updateStatus(String id, Boolean status);
} 