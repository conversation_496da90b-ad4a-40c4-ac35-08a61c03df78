package com.csci.hrrs.configuration.parallel;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 用于处理日志的线程
 */
public class LogParallelExecutor {

    private static final ThreadPool THREAD_POOL = new ThreadPool(120, TimeUnit.SECONDS, "Log-threadProol");

    /**
     * 提交一个异步任务
     *
     * @param callable
     * @return
     */
    public static <T> Future<T> submit(Callable<T> callable) {
        return THREAD_POOL.submit(callable);
    }

    /**
     * 提交一个异步任务
     *
     * @param runnable
     * @return
     */
    public static Future<?> submit(Runnable runnable) {
        return THREAD_POOL.submit(runnable);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param callables
     */
    public static void submitAndWait(Callable<?>... callables) {
        THREAD_POOL.submitAndWait(callables);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param tasks
     */
    public static void submitAndWait(Runnable... tasks) {
        THREAD_POOL.submitAndWait(tasks);
    }

}
