package com.csci.hrrs.visa.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class VisaStaffExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public VisaStaffExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVisaNoIsNull() {
            addCriterion("visa_no is null");
            return (Criteria) this;
        }

        public Criteria andVisaNoIsNotNull() {
            addCriterion("visa_no is not null");
            return (Criteria) this;
        }

        public Criteria andVisaNoEqualTo(String value) {
            addCriterion("visa_no =", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoNotEqualTo(String value) {
            addCriterion("visa_no <>", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoGreaterThan(String value) {
            addCriterion("visa_no >", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoGreaterThanOrEqualTo(String value) {
            addCriterion("visa_no >=", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoLessThan(String value) {
            addCriterion("visa_no <", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoLessThanOrEqualTo(String value) {
            addCriterion("visa_no <=", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoLike(String value) {
            addCriterion("visa_no like", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoNotLike(String value) {
            addCriterion("visa_no not like", value, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoIn(List<String> values) {
            addCriterion("visa_no in", values, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoNotIn(List<String> values) {
            addCriterion("visa_no not in", values, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoBetween(String value1, String value2) {
            addCriterion("visa_no between", value1, value2, "visaNo");
            return (Criteria) this;
        }

        public Criteria andVisaNoNotBetween(String value1, String value2) {
            addCriterion("visa_no not between", value1, value2, "visaNo");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNull() {
            addCriterion("company is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNotNull() {
            addCriterion("company is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEqualTo(String value) {
            addCriterion("company =", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotEqualTo(String value) {
            addCriterion("company <>", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThan(String value) {
            addCriterion("company >", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("company >=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThan(String value) {
            addCriterion("company <", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThanOrEqualTo(String value) {
            addCriterion("company <=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLike(String value) {
            addCriterion("company like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotLike(String value) {
            addCriterion("company not like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyIn(List<String> values) {
            addCriterion("company in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotIn(List<String> values) {
            addCriterion("company not in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyBetween(String value1, String value2) {
            addCriterion("company between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotBetween(String value1, String value2) {
            addCriterion("company not between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNull() {
            addCriterion("operate_time is null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNotNull() {
            addCriterion("operate_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeEqualTo(LocalDateTime value) {
            addCriterion("operate_time =", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("operate_time <>", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThan(LocalDateTime value) {
            addCriterion("operate_time >", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("operate_time >=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThan(LocalDateTime value) {
            addCriterion("operate_time <", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("operate_time <=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIn(List<LocalDateTime> values) {
            addCriterion("operate_time in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("operate_time not in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("operate_time between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("operate_time not between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andNameEngIsNull() {
            addCriterion("name_eng is null");
            return (Criteria) this;
        }

        public Criteria andNameEngIsNotNull() {
            addCriterion("name_eng is not null");
            return (Criteria) this;
        }

        public Criteria andNameEngEqualTo(String value) {
            addCriterion("name_eng =", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngNotEqualTo(String value) {
            addCriterion("name_eng <>", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngGreaterThan(String value) {
            addCriterion("name_eng >", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngGreaterThanOrEqualTo(String value) {
            addCriterion("name_eng >=", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngLessThan(String value) {
            addCriterion("name_eng <", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngLessThanOrEqualTo(String value) {
            addCriterion("name_eng <=", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngLike(String value) {
            addCriterion("name_eng like", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngNotLike(String value) {
            addCriterion("name_eng not like", value, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngIn(List<String> values) {
            addCriterion("name_eng in", values, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngNotIn(List<String> values) {
            addCriterion("name_eng not in", values, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngBetween(String value1, String value2) {
            addCriterion("name_eng between", value1, value2, "nameEng");
            return (Criteria) this;
        }

        public Criteria andNameEngNotBetween(String value1, String value2) {
            addCriterion("name_eng not between", value1, value2, "nameEng");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryIsNull() {
            addCriterion("staff_category is null");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryIsNotNull() {
            addCriterion("staff_category is not null");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryEqualTo(String value) {
            addCriterion("staff_category =", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryNotEqualTo(String value) {
            addCriterion("staff_category <>", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryGreaterThan(String value) {
            addCriterion("staff_category >", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("staff_category >=", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryLessThan(String value) {
            addCriterion("staff_category <", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryLessThanOrEqualTo(String value) {
            addCriterion("staff_category <=", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryLike(String value) {
            addCriterion("staff_category like", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryNotLike(String value) {
            addCriterion("staff_category not like", value, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryIn(List<String> values) {
            addCriterion("staff_category in", values, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryNotIn(List<String> values) {
            addCriterion("staff_category not in", values, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryBetween(String value1, String value2) {
            addCriterion("staff_category between", value1, value2, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andStaffCategoryNotBetween(String value1, String value2) {
            addCriterion("staff_category not between", value1, value2, "staffCategory");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyIsNull() {
            addCriterion("mainland_contracted_company is null");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyIsNotNull() {
            addCriterion("mainland_contracted_company is not null");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyEqualTo(String value) {
            addCriterion("mainland_contracted_company =", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyNotEqualTo(String value) {
            addCriterion("mainland_contracted_company <>", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyGreaterThan(String value) {
            addCriterion("mainland_contracted_company >", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("mainland_contracted_company >=", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyLessThan(String value) {
            addCriterion("mainland_contracted_company <", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyLessThanOrEqualTo(String value) {
            addCriterion("mainland_contracted_company <=", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyLike(String value) {
            addCriterion("mainland_contracted_company like", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyNotLike(String value) {
            addCriterion("mainland_contracted_company not like", value, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyIn(List<String> values) {
            addCriterion("mainland_contracted_company in", values, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyNotIn(List<String> values) {
            addCriterion("mainland_contracted_company not in", values, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyBetween(String value1, String value2) {
            addCriterion("mainland_contracted_company between", value1, value2, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andMainlandContractedCompanyNotBetween(String value1, String value2) {
            addCriterion("mainland_contracted_company not between", value1, value2, "mainlandContractedCompany");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNull() {
            addCriterion("birthdate is null");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNotNull() {
            addCriterion("birthdate is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdateEqualTo(LocalDateTime value) {
            addCriterion("birthdate =", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotEqualTo(LocalDateTime value) {
            addCriterion("birthdate <>", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThan(LocalDateTime value) {
            addCriterion("birthdate >", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("birthdate >=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThan(LocalDateTime value) {
            addCriterion("birthdate <", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("birthdate <=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateIn(List<LocalDateTime> values) {
            addCriterion("birthdate in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotIn(List<LocalDateTime> values) {
            addCriterion("birthdate not in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("birthdate between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("birthdate not between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("gender is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("gender is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(String value) {
            addCriterion("gender =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(String value) {
            addCriterion("gender <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(String value) {
            addCriterion("gender >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(String value) {
            addCriterion("gender >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(String value) {
            addCriterion("gender <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(String value) {
            addCriterion("gender <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLike(String value) {
            addCriterion("gender like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotLike(String value) {
            addCriterion("gender not like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<String> values) {
            addCriterion("gender in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<String> values) {
            addCriterion("gender not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(String value1, String value2) {
            addCriterion("gender between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(String value1, String value2) {
            addCriterion("gender not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameIsNull() {
            addCriterion("business_city_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameIsNotNull() {
            addCriterion("business_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameEqualTo(String value) {
            addCriterion("business_city_name =", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameNotEqualTo(String value) {
            addCriterion("business_city_name <>", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameGreaterThan(String value) {
            addCriterion("business_city_name >", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_city_name >=", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameLessThan(String value) {
            addCriterion("business_city_name <", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameLessThanOrEqualTo(String value) {
            addCriterion("business_city_name <=", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameLike(String value) {
            addCriterion("business_city_name like", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameNotLike(String value) {
            addCriterion("business_city_name not like", value, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameIn(List<String> values) {
            addCriterion("business_city_name in", values, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameNotIn(List<String> values) {
            addCriterion("business_city_name not in", values, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameBetween(String value1, String value2) {
            addCriterion("business_city_name between", value1, value2, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityNameNotBetween(String value1, String value2) {
            addCriterion("business_city_name not between", value1, value2, "businessCityName");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeIsNull() {
            addCriterion("business_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeIsNotNull() {
            addCriterion("business_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeEqualTo(String value) {
            addCriterion("business_city_code =", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeNotEqualTo(String value) {
            addCriterion("business_city_code <>", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeGreaterThan(String value) {
            addCriterion("business_city_code >", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_city_code >=", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeLessThan(String value) {
            addCriterion("business_city_code <", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeLessThanOrEqualTo(String value) {
            addCriterion("business_city_code <=", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeLike(String value) {
            addCriterion("business_city_code like", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeNotLike(String value) {
            addCriterion("business_city_code not like", value, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeIn(List<String> values) {
            addCriterion("business_city_code in", values, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeNotIn(List<String> values) {
            addCriterion("business_city_code not in", values, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeBetween(String value1, String value2) {
            addCriterion("business_city_code between", value1, value2, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCityCodeNotBetween(String value1, String value2) {
            addCriterion("business_city_code not between", value1, value2, "businessCityCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoIsNull() {
            addCriterion("business_organization_no is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoIsNotNull() {
            addCriterion("business_organization_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoEqualTo(String value) {
            addCriterion("business_organization_no =", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoNotEqualTo(String value) {
            addCriterion("business_organization_no <>", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoGreaterThan(String value) {
            addCriterion("business_organization_no >", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoGreaterThanOrEqualTo(String value) {
            addCriterion("business_organization_no >=", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoLessThan(String value) {
            addCriterion("business_organization_no <", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoLessThanOrEqualTo(String value) {
            addCriterion("business_organization_no <=", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoLike(String value) {
            addCriterion("business_organization_no like", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoNotLike(String value) {
            addCriterion("business_organization_no not like", value, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoIn(List<String> values) {
            addCriterion("business_organization_no in", values, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoNotIn(List<String> values) {
            addCriterion("business_organization_no not in", values, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoBetween(String value1, String value2) {
            addCriterion("business_organization_no between", value1, value2, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationNoNotBetween(String value1, String value2) {
            addCriterion("business_organization_no not between", value1, value2, "businessOrganizationNo");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeIsNull() {
            addCriterion("business_organization_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeIsNotNull() {
            addCriterion("business_organization_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeEqualTo(String value) {
            addCriterion("business_organization_code =", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeNotEqualTo(String value) {
            addCriterion("business_organization_code <>", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeGreaterThan(String value) {
            addCriterion("business_organization_code >", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_organization_code >=", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeLessThan(String value) {
            addCriterion("business_organization_code <", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeLessThanOrEqualTo(String value) {
            addCriterion("business_organization_code <=", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeLike(String value) {
            addCriterion("business_organization_code like", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeNotLike(String value) {
            addCriterion("business_organization_code not like", value, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeIn(List<String> values) {
            addCriterion("business_organization_code in", values, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeNotIn(List<String> values) {
            addCriterion("business_organization_code not in", values, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeBetween(String value1, String value2) {
            addCriterion("business_organization_code between", value1, value2, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationCodeNotBetween(String value1, String value2) {
            addCriterion("business_organization_code not between", value1, value2, "businessOrganizationCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Byte value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Byte value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Byte value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Byte value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Byte value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Byte> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Byte> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Byte value1, Byte value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Byte value1, Byte value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdIsNull() {
            addCriterion("business_organization_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdIsNotNull() {
            addCriterion("business_organization_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdEqualTo(String value) {
            addCriterion("business_organization_id =", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdNotEqualTo(String value) {
            addCriterion("business_organization_id <>", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdGreaterThan(String value) {
            addCriterion("business_organization_id >", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdGreaterThanOrEqualTo(String value) {
            addCriterion("business_organization_id >=", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdLessThan(String value) {
            addCriterion("business_organization_id <", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdLessThanOrEqualTo(String value) {
            addCriterion("business_organization_id <=", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdLike(String value) {
            addCriterion("business_organization_id like", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdNotLike(String value) {
            addCriterion("business_organization_id not like", value, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdIn(List<String> values) {
            addCriterion("business_organization_id in", values, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdNotIn(List<String> values) {
            addCriterion("business_organization_id not in", values, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdBetween(String value1, String value2) {
            addCriterion("business_organization_id between", value1, value2, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrganizationIdNotBetween(String value1, String value2) {
            addCriterion("business_organization_id not between", value1, value2, "businessOrganizationId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_visa_staff
     *
     * @mbg.generated do_not_delete_during_merge Mon Jun 16 20:38:18 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_visa_staff
     *
     * @mbg.generated Mon Jun 16 20:38:18 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}