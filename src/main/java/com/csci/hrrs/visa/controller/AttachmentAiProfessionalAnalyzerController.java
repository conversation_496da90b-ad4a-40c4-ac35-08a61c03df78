package com.csci.hrrs.visa.controller;


import com.alibaba.fastjson.JSONObject;
import com.csci.hrrs.model.ResultBean;
import com.csci.hrrs.visa.hiagent.analyzer.impl.DocumentAnalyzerWithAiResumeEduAndExp;
import com.csci.hrrs.visa.vo.StaffAttachmentVo;
import com.csci.hrrs.visa.vo.VisaStaffVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/visa/professional/attachmentAiAnalyzer")
@RestController
@Slf4j
@Tag(name = "附件AI解析文档内容", description = "附件AI解析文档内容")
public class AttachmentAiProfessionalAnalyzerController {

    @Resource
    private DocumentAnalyzerWithAiResumeEduAndExp resumeEduAndExp;

    @PostMapping("/analyzeResumeEduAndExpByAttachmentId")
    @Operation(summary = "分析简历教育经历和工作经历", description = "分析简历教育经历和工作经历", method = "POST")
    public ResultBean<VisaStaffVo> analyzeResumeEduAndExpByAttachmentId(@RequestBody StaffAttachmentVo staffAttachmentVo) {
        log.info("analyzeResumeEduAndExpByAttachmentId params = {}",JSONObject.toJSONString(staffAttachmentVo));
        return new ResultBean<>( resumeEduAndExp.getResult(staffAttachmentVo.getId()));
    }
}
