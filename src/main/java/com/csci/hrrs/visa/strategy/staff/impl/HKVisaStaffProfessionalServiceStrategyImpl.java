package com.csci.hrrs.visa.strategy.staff.impl;

import cn.smallbun.screw.core.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.util.context.model.UserInfo;
import com.csci.hrrs.visa.constant.*;
import com.csci.hrrs.visa.dto.VisaStaffAttachQueryDTO;
import com.csci.hrrs.visa.dto.VisaStaffDTO;
import com.csci.hrrs.visa.dto.VisaStaffQueryDTO;
import com.csci.hrrs.visa.exception.VisaServiceException;
import com.csci.hrrs.visa.model.*;
import com.csci.hrrs.visa.service.*;
import com.csci.hrrs.visa.strategy.staff.BaseVisaStaffServiceStrategyAbstract;
import com.csci.hrrs.visa.vo.VisaStaffQueryVo;
import com.csci.hrrs.visa.vo.VisaStaffVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class HKVisaStaffProfessionalServiceStrategyImpl extends BaseVisaStaffServiceStrategyAbstract {

    @Resource
    private BusinessOrganizationService businessOrganizationService;

    @Resource
    private VisaStaffService visaStaffService;

    @Resource
    private VisaStaffWorkExperienceService experienceService;

    @Resource
    private VisaStaffEducationService educationService;

    @Resource
    private VisaStaffAttachService attachService;


    public HKVisaStaffProfessionalServiceStrategyImpl() {
        this.businessCityCode = VisaBusinessCityEnum.HK.getCode();
        this.visaType = VisaTypeEnum.PROFESSIONAL.getCode();
        this.businessOrganizationNo = VisaBusinessOrganizationConstant.HK_NO;
        this.businessOrganizationCode = VisaBusinessOrganizationConstant.HK_ORGANIZATION_CODE;
    }

    @Transactional(rollbackFor = Exception.class)
    public int save(VisaStaffDTO visaStaffDTO) {
        this.validate(visaStaffDTO);
        BusinessOrganization businessOrganization = businessOrganizationService.selectByBusinessOrganizationCode(visaStaffDTO.getBusinessCityCode(), visaStaffDTO.getBusinessOrganizationCode());
        if (businessOrganization == null) {
            throw new VisaServiceException("Business organization not found");
        }
        visaStaffDTO.setBusinessOrganizationId(businessOrganization.getId());
        visaStaffDTO.setBusinessCityCode(businessOrganization.getBusinessCityCode());
        visaStaffDTO.setBusinessOrganizationCode(businessOrganization.getBusinessOrganizationCode());
        visaStaffDTO.setBusinessOrganizationNo(businessOrganization.getBusinessOrganizationNo());
        visaStaffDTO.setBusinessCityName(businessOrganization.getBusinessCityName());

        VisaStaff visaStaff = new VisaStaff();
        BeanUtils.copyProperties(visaStaffDTO, visaStaff);


        List<VisaStaffAttach> attaches = visaStaffDTO.getAttaches();

        for (VisaStaffAttach attach : attaches) {
            if (StringUtils.isNotBlank(attach.getId())) {
                // 查询到用户上传的附件（如职位申请表）
                VisaStaffAttach original = attachService.selectById(attach.getId());
                if (original == null || !VisaDataStatus.ACTIVE.getCode().equals(original.getStatus())) {
                    throw new VisaServiceException("附件不存在或已失效或员工信息被删除，请检查附件如：职位申请表");
                }
                // 复制附件信息到新的对象中
                original.setVisaStaffId(visaStaffDTO.getId());
                original.setMinioAttachmentId(original.getMinioAttachmentId());
                original.setCode(attach.getCode());
                original.setType(attach.getType());
                original.setLastUpdateUsername(ContextUtils.getCurrentUser().getName());
                original.setLastUpdateTime(LocalDateTime.now());
                VisaStaffAttachQueryDTO visaStaffAttachQueryDTO = new VisaStaffAttachQueryDTO();
                visaStaffAttachQueryDTO.setType(attach.getType());
                visaStaffAttachQueryDTO.setVisaStaffId(visaStaffDTO.getId());
                visaStaffAttachQueryDTO.setAttachCode(attach.getCode());
                List<VisaStaffAttach> visaStaffAttaches = attachService.selectByVisaStaffIdAndCode(visaStaffAttachQueryDTO);
                if (CollectionUtils.isNotEmpty(visaStaffAttaches)) {
                    for (VisaStaffAttach attachOriginal : visaStaffAttaches) {
                        attachService.deleteById(attachOriginal.getId());
                    }
                }
                attachService.save(original);
            } else {
                VisaStaffAttachQueryDTO visaStaffAttachQueryDTO = new VisaStaffAttachQueryDTO();
                visaStaffAttachQueryDTO.setType(attach.getType());
                visaStaffAttachQueryDTO.setVisaStaffId(visaStaffDTO.getId());
                visaStaffAttachQueryDTO.setAttachCode(attach.getCode());
                List<VisaStaffAttach> visaStaffAttaches = attachService.selectByVisaStaffIdAndCode(visaStaffAttachQueryDTO);
                if (CollectionUtils.isNotEmpty(visaStaffAttaches)) {
                    for (VisaStaffAttach attachOriginal : visaStaffAttaches) {
                        attachService.deleteById(attachOriginal.getId());
                    }
                }
                attachService.save(attach);
            }
        }


        List<VisaStaffEducation> staffEducations = visaStaffDTO.getStaffEducations();
        educationService.bathSaveOrUpdateOrDelete(staffEducations, visaStaff.getId());


        List<VisaStaffWorkExperience> workExperiences = visaStaffDTO.getWorkExperiences();
        experienceService.bathSaveOrUpdateOrDelete(workExperiences, visaStaff.getId());
        UserInfo currentUser = ContextUtils.getCurrentUser();
        visaStaff.setOperator(currentUser.getName());
        visaStaff.setOperator(currentUser.getName());
        visaStaff.setOperateTime(LocalDateTime.now());
        visaStaff.setState(VisaStaffState.HR_PROCESSING.getCode());
        return visaStaffService.saveOrUpdate(visaStaff);
    }


    @Transactional(rollbackFor = Exception.class)
    public int submit(VisaStaffDTO visaStaffDTO) {
        this.validate(visaStaffDTO);
        return save(visaStaffDTO);
    }

    public List<VisaStaffDTO> selectByOrgCodeAndCityCodePage(VisaStaffQueryDTO visaStaffQueryDTO) {
        return visaStaffService.selectByOrgCodeAndCityCodePage(visaStaffQueryDTO);
    }

    public Integer countByOrgCodeAndCityCode(VisaStaffQueryDTO visaStaffQueryDTO) {
        return visaStaffService.countByOrgCodeAndCityCode(visaStaffQueryDTO);
    }

    public VisaStaff selectById(VisaStaffQueryVo vo) {
        return visaStaffService.selectById(vo.getVisaStaffId());
    }

    /**
     * HK-专才校验签证申请信息
     *
     * @param visaStaff
     * @return
     */
    public boolean validate(VisaStaffDTO visaStaff) {
        if (visaStaff.getBusinessOrganizationNo() == null || visaStaff.getBusinessOrganizationNo().isEmpty()) {
            throw new VisaServiceException("Business organization no cannot be null or empty");
        }
        if (visaStaff.getBusinessOrganizationCode() == null || visaStaff.getBusinessOrganizationCode().isEmpty()) {
            throw new VisaServiceException("Business organization code cannot be null or empty");
        }
        if (visaStaff.getBusinessCityCode() == null || visaStaff.getBusinessCityCode().isEmpty()) {
            throw new VisaServiceException("Business city code cannot be null or empty");
        }
        if (visaStaff.getVisaType() == null || visaStaff.getVisaType().isEmpty()) {
            throw new VisaServiceException("Visa type cannot be null or empty");
        }
        if ("外部员工".equals(visaStaff.getStaffCategory())) {
            List<VisaStaffAttach> attaches = visaStaff.getAttaches();
            if (CollectionUtils.isEmpty(attaches)) {
                throw new VisaServiceException("请上传职位申请表");
            }
            for (VisaStaffAttach attach : attaches) {
                VisaStaffAttach staffAttach = attachService.selectById(attach.getId());
                if(StringUtils.isBlank(attach.getId())){
                    throw new VisaServiceException("职位申请表标识不能为空，请检查附件如：职位申请表并上传");
                }
                if (Objects.isNull(staffAttach)){
                    throw new VisaServiceException("附件不存在或已失效或员工信息被删除，请检查附件如：职位申请表");
                }
                if (staffAttach.getCode().equals(AttachmentCodeEnum.POSITION_APPLICATION_FORM.getCode()) && staffAttach.getType().equals(AttachmentTypeEnum.VISA_PROFESSIONAL.getCode())) {
                    return true;
                }
            }
            throw new VisaServiceException("请上传职位申请表");
        }

        return true;
    }

    public boolean validate(VisaStaffVo visaStaff) {
        if (visaStaff.getBusinessOrganizationNo() == null || visaStaff.getBusinessOrganizationNo().isEmpty()) {
            throw new VisaServiceException("Business organization no cannot be null or empty");
        }
        if (visaStaff.getBusinessOrganizationCode() == null || visaStaff.getBusinessOrganizationCode().isEmpty()) {
            throw new VisaServiceException("Business organization code cannot be null or empty");
        }
        if (visaStaff.getBusinessCityCode() == null || visaStaff.getBusinessCityCode().isEmpty()) {
            throw new VisaServiceException("Business city code cannot be null or empty");
        }
        if (visaStaff.getVisaType() == null || visaStaff.getVisaType().isEmpty()) {
            throw new VisaServiceException("Visa type cannot be null or empty");
        }
        // 其他非空校验
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public int reject(VisaStaffVo visaStaffVo) {
        this.validate(visaStaffVo);

        VisaStaff visaStaff = visaStaffService.selectById(visaStaffVo.getId());
        if (Objects.isNull(visaStaff)) {
            throw new VisaServiceException("才签人员信息不存在，无需撤回！");
        }
        VisaStaffAttachQueryDTO visaStaffAttachQueryDTO = new VisaStaffAttachQueryDTO();
        visaStaffAttachQueryDTO.setVisaStaffId(visaStaffVo.getId());
        visaStaffAttachQueryDTO.setAttachCode(AttachmentCodeEnum.CONSENT_TO_HK.getCode());
        visaStaffAttachQueryDTO.setType(AttachmentTypeEnum.VISA_PROFESSIONAL.getCode());
        List<VisaStaffAttach> visaStaffAttaches = attachService.selectByVisaStaffIdAndCode(visaStaffAttachQueryDTO);
        if (CollectionUtils.isNotEmpty(visaStaffAttaches)) {
            attachService.deleteById(visaStaffAttaches.get(0).getId());
        }


        VisaStaffAttachQueryDTO visaStaffAttachQueryReason = new VisaStaffAttachQueryDTO();
        visaStaffAttachQueryReason.setVisaStaffId(visaStaffVo.getId());
        visaStaffAttachQueryReason.setAttachCode(AttachmentCodeEnum.RECRUITMENT_REASON.getCode());
        visaStaffAttachQueryReason.setType(AttachmentTypeEnum.VISA_PROFESSIONAL.getCode());
        List<VisaStaffAttach> visaStaffReasonAttaches = attachService.selectByVisaStaffIdAndCode(visaStaffAttachQueryReason);
        if (CollectionUtils.isNotEmpty(visaStaffReasonAttaches)) {
            attachService.deleteById(visaStaffReasonAttaches.get(0).getId());
        }
        visaStaff.setState(VisaStaffState.HR_PROCESSING.getCode());
        visaStaff.setLastUpdateVersion(visaStaffVo.getLastUpdateVersion());
        return visaStaffService.update(visaStaff);
    }


    @Transactional(rollbackFor = Exception.class)
    public int deleteById(VisaStaffVo visaStaffVo) {

       VisaStaffDTO visaStaffDTO = visaStaffService.selectByStaffId(visaStaffVo.getId());
        if (Objects.isNull(visaStaffDTO)) {
            throw new VisaServiceException("才签人员信息不存在，无需删除！");
        }

        if (VisaStaffState.COMPLETE.getCode().equals(visaStaffDTO.getState())) {
            throw new VisaServiceException("已完成的人员信息，不允许删除！");
        }
        if (CollectionUtils.isNotEmpty(visaStaffDTO.getAttaches())) {
            int deleteStaffAttach = attachService.deleteByStaffId(visaStaffVo.getId());
            if (deleteStaffAttach == 0) {
                throw new VisaServiceException("删除才签人员信息失败，请重试！");
            }
        }
        if (CollectionUtils.isNotEmpty(visaStaffDTO.getWorkExperiences())) {
            int deleteStaffExperience = experienceService.deleteByStaffId(visaStaffVo.getId());
            if (deleteStaffExperience == 0) {
                throw new VisaServiceException("删除才签人员信息失败，请重试！");
            }
        }
        if (CollectionUtils.isNotEmpty(visaStaffDTO.getStaffEducations())) {
            int deleteStaffEducation = educationService.deleteByStaffId(visaStaffVo.getId());
            if (deleteStaffEducation == 0) {
                throw new VisaServiceException("删除才签人员信息失败，请重试！");
            }
        }
        VisaStaff visaStaff = new VisaStaff();
        BeanUtils.copyProperties(visaStaffDTO, visaStaff);
        visaStaff.setStatus(VisaDataStatus.INACTIVE.getCode());
        visaStaff.setIsDelete(true);
        visaStaff.setOperator(ContextUtils.getCurrentUser().getName());
        visaStaff.setOperateTime(LocalDateTime.now());
        visaStaff.setLastUpdateVersion(visaStaffVo.getLastUpdateVersion());
        int deleteStaff = visaStaffService.update(visaStaff);
        if (deleteStaff == 0) {
            throw new VisaServiceException("删除才签人员信息失败，请重试！");
        }
        return 1;
    }


    public String getVisaNo(VisaStaffVo vo) {

        // 加分布式锁
        String lockKey = HrrsConsts.PROJECT_PREFIX + "visa" + "generate_visa_no_lock:" + vo.getBusinessCityCode() + ":" + vo.getBusinessOrganizationCode() + ":" + vo.getVisaType();
        String visaNo = "";
        try {
            // 默认120s
            if (RedisLockUtil.lock(lockKey)) {
                visaNo = visaStaffService.getVisaNo(vo);
            }
        } catch (Exception e) {
            log.error("上传才签编码失败：", e);
            throw new VisaServiceException("上传才签编码失败,请重试！");
        } finally {
            // 释放锁
            RedisLockUtil.unlock(lockKey);
        }
        return visaNo;
    }

}
