package com.csci.hrrs.apply.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.apply.mapper.ApplySnapshotsMapper;
import com.csci.hrrs.apply.model.ApplySnapshots;
import com.csci.hrrs.constant.DatasourceContextEnum;
import org.springframework.stereotype.Service;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
public class ApplySnapshotsService extends ServiceImpl<ApplySnapshotsMapper, ApplySnapshots> {
}