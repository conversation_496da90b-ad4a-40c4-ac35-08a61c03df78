package com.csci.hrrs.apply.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_apply_emp_pro_qualification
 */
@TableName("t_apply_emp_pro_qualification")
public class ApplyEmpProQualification {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.id
     *
     * @mbg.generated
     */
    @TableField("id")
    @TableId
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.emp_id
     *
     * @mbg.generated
     */
    @TableField("emp_id")
    private String empId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.cert_body
     *
     * @mbg.generated
     */
    @TableField("cert_body")
    private String certBody;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.issue_award_date
     *
     * @mbg.generated
     */
    @TableField("issue_award_date")
    private LocalDate issueAwardDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.cert_no
     *
     * @mbg.generated
     */
    @TableField("cert_no")
    private String certNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.cert_type_code
     *
     * @mbg.generated
     */
    @TableField("cert_type_code")
    private String certTypeCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.cert_type_name
     *
     * @mbg.generated
     */
    @TableField("cert_type_name")
    private String certTypeName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.major_code
     *
     * @mbg.generated
     */
    @TableField("major_code")
    private String majorCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.major_name
     *
     * @mbg.generated
     */
    @TableField("major_name")
    private String majorName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.qualification_code
     *
     * @mbg.generated
     */
    @TableField("qualification_code")
    private String qualificationCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.qualification_name
     *
     * @mbg.generated
     */
    @TableField("qualification_name")
    private String qualificationName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.level_code
     *
     * @mbg.generated
     */
    @TableField("level_code")
    private String levelCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_apply_emp_pro_qualification.level_name
     *
     * @mbg.generated
     */
    @TableField("level_name")
    private String levelName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.id
     *
     * @return the value of t_apply_emp_pro_qualification.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.id
     *
     * @param id the value for t_apply_emp_pro_qualification.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.emp_id
     *
     * @return the value of t_apply_emp_pro_qualification.emp_id
     *
     * @mbg.generated
     */
    public String getEmpId() {
        return empId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.emp_id
     *
     * @param empId the value for t_apply_emp_pro_qualification.emp_id
     *
     * @mbg.generated
     */
    public void setEmpId(String empId) {
        this.empId = empId == null ? null : empId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.cert_body
     *
     * @return the value of t_apply_emp_pro_qualification.cert_body
     *
     * @mbg.generated
     */
    public String getCertBody() {
        return certBody;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.cert_body
     *
     * @param certBody the value for t_apply_emp_pro_qualification.cert_body
     *
     * @mbg.generated
     */
    public void setCertBody(String certBody) {
        this.certBody = certBody == null ? null : certBody.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.issue_award_date
     *
     * @return the value of t_apply_emp_pro_qualification.issue_award_date
     *
     * @mbg.generated
     */
    public LocalDate getIssueAwardDate() {
        return issueAwardDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.issue_award_date
     *
     * @param issueAwardDate the value for t_apply_emp_pro_qualification.issue_award_date
     *
     * @mbg.generated
     */
    public void setIssueAwardDate(LocalDate issueAwardDate) {
        this.issueAwardDate = issueAwardDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.cert_no
     *
     * @return the value of t_apply_emp_pro_qualification.cert_no
     *
     * @mbg.generated
     */
    public String getCertNo() {
        return certNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.cert_no
     *
     * @param certNo the value for t_apply_emp_pro_qualification.cert_no
     *
     * @mbg.generated
     */
    public void setCertNo(String certNo) {
        this.certNo = certNo == null ? null : certNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.cert_type_code
     *
     * @return the value of t_apply_emp_pro_qualification.cert_type_code
     *
     * @mbg.generated
     */
    public String getCertTypeCode() {
        return certTypeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.cert_type_code
     *
     * @param certTypeCode the value for t_apply_emp_pro_qualification.cert_type_code
     *
     * @mbg.generated
     */
    public void setCertTypeCode(String certTypeCode) {
        this.certTypeCode = certTypeCode == null ? null : certTypeCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.cert_type_name
     *
     * @return the value of t_apply_emp_pro_qualification.cert_type_name
     *
     * @mbg.generated
     */
    public String getCertTypeName() {
        return certTypeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.cert_type_name
     *
     * @param certTypeName the value for t_apply_emp_pro_qualification.cert_type_name
     *
     * @mbg.generated
     */
    public void setCertTypeName(String certTypeName) {
        this.certTypeName = certTypeName == null ? null : certTypeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.major_code
     *
     * @return the value of t_apply_emp_pro_qualification.major_code
     *
     * @mbg.generated
     */
    public String getMajorCode() {
        return majorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.major_code
     *
     * @param majorCode the value for t_apply_emp_pro_qualification.major_code
     *
     * @mbg.generated
     */
    public void setMajorCode(String majorCode) {
        this.majorCode = majorCode == null ? null : majorCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.major_name
     *
     * @return the value of t_apply_emp_pro_qualification.major_name
     *
     * @mbg.generated
     */
    public String getMajorName() {
        return majorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.major_name
     *
     * @param majorName the value for t_apply_emp_pro_qualification.major_name
     *
     * @mbg.generated
     */
    public void setMajorName(String majorName) {
        this.majorName = majorName == null ? null : majorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.qualification_code
     *
     * @return the value of t_apply_emp_pro_qualification.qualification_code
     *
     * @mbg.generated
     */
    public String getQualificationCode() {
        return qualificationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.qualification_code
     *
     * @param qualificationCode the value for t_apply_emp_pro_qualification.qualification_code
     *
     * @mbg.generated
     */
    public void setQualificationCode(String qualificationCode) {
        this.qualificationCode = qualificationCode == null ? null : qualificationCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.qualification_name
     *
     * @return the value of t_apply_emp_pro_qualification.qualification_name
     *
     * @mbg.generated
     */
    public String getQualificationName() {
        return qualificationName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.qualification_name
     *
     * @param qualificationName the value for t_apply_emp_pro_qualification.qualification_name
     *
     * @mbg.generated
     */
    public void setQualificationName(String qualificationName) {
        this.qualificationName = qualificationName == null ? null : qualificationName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.level_code
     *
     * @return the value of t_apply_emp_pro_qualification.level_code
     *
     * @mbg.generated
     */
    public String getLevelCode() {
        return levelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.level_code
     *
     * @param levelCode the value for t_apply_emp_pro_qualification.level_code
     *
     * @mbg.generated
     */
    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode == null ? null : levelCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_apply_emp_pro_qualification.level_name
     *
     * @return the value of t_apply_emp_pro_qualification.level_name
     *
     * @mbg.generated
     */
    public String getLevelName() {
        return levelName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_apply_emp_pro_qualification.level_name
     *
     * @param levelName the value for t_apply_emp_pro_qualification.level_name
     *
     * @mbg.generated
     */
    public void setLevelName(String levelName) {
        this.levelName = levelName == null ? null : levelName.trim();
    }
}