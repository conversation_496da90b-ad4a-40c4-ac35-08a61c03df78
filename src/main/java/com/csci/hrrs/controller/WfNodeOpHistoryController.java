package com.csci.hrrs.controller;

import com.csci.common.model.ResultBase;
import com.csci.hrrs.service.WfNodeOpHistoryService;
import com.csci.hrrs.vo.WfNodeOpHistoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/wf/node/op/history")
@Tag(name = "工作流节点操作历史", description = "工作流节点操作历史相关API")
public class WfNodeOpHistoryController {

    @Resource
    private WfNodeOpHistoryService wfNodeOpHistoryService;

    @PostMapping("/save")
    @Operation(summary = "保存工作流节点操作历史记录")
    ResultBase saveWfNodeOpHistory(@RequestBody WfNodeOpHistoryVO wfNodeOpHistoryVO) {
        wfNodeOpHistoryService.saveWfNodeOpHistory(wfNodeOpHistoryVO.getFlowControlId(),
                wfNodeOpHistoryVO.getNodeId(), wfNodeOpHistoryVO.getOperateHistory(), wfNodeOpHistoryVO.getOperateReason());
        return ResultBase.success("操作历史记录保存成功");
    }

}
