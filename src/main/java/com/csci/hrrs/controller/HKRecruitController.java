package com.csci.hrrs.controller;

import com.csci.common.model.ResultBean;
import com.csci.common.model.ResultList;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.facade.HKRecruitFacade;
import com.csci.hrrs.qo.HKDashQO;
import com.csci.hrrs.qo.HKManagerQO;
import com.csci.hrrs.qo.HKPositionQO;
import com.csci.hrrs.qo.HKTalentQO;
import com.csci.hrrs.vo.ChartVO;
import com.csci.hrrs.vo.HKRecruitOverviewVO;
import com.csci.hrrs.vo.NameCountRateVO;
import com.csci.hrrs.vo.YearlyStaffStatVO;
import com.csci.hrrs.vo.subsidiary.RosterDetailCategoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/hkRecruit")
@Tag(name = "HKRecruitController", description = "港聘面板相关接口")
public class HKRecruitController {

    @Resource
    private HKRecruitFacade hkRecruitFacade;

    @PostMapping("/getHKRecruitOverview")
    @Operation(summary = "获取港聘面板数据")
    public ResultBean<HKRecruitOverviewVO> getHKRecruitOverview(@RequestBody HKDashQO qo) {
        return new ResultBean<>(hkRecruitFacade.getHKRecruitOverview(qo));
    }

    @PostMapping("/distributeHKRecruitBySubsidiary")
    @Operation(summary = "获取港聘分公司分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitBySubsidiary(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeHKRecruitBySubsidiary(qo));
    }

    @PostMapping("/distributeHKRecruitByManageType")
    @Operation(summary = "获取港聘管理类型分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitByManageType(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeByManageType(qo));
    }

    @PostMapping("/distributeHKRecruitByManageType/list")
    @Operation(summary = "查询港聘管理类型人员列表")
    public ResultPage<RosterDetailCategoryVO> distributeHKRecruitByManageTypeList(@RequestBody HKManagerQO qo, @RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkRecruitFacade.distributeHKRecruitByManageTypeList(qo,pageNum,pageSize);
    }

    @PostMapping("/distributeHKRecruitByManageType/export")
    @Operation(summary = "导出港聘字处理类型人员列表")
    public void exportDistributeHKRecruitByManageTypeList(@RequestBody HKManagerQO qo, HttpServletResponse response) throws IOException{
        hkRecruitFacade.exportDistributeByManageTypeList(qo,response);
    }

    @PostMapping("/distributeHKRecruitByTalentType")
    @Operation(summary = "获取港聘人才类型分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitByTalentType(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeByTalentType(qo));
    }

    @PostMapping("/distributeHKRecruitByTalentType/list")
    @Operation(summary = "查询港聘人才类型分布数据列表")
    public ResultPage<RosterDetailCategoryVO> distributeHKRecruitByTalentTypeList(@RequestBody HKTalentQO qo, @RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkRecruitFacade.distributeHKRecruitByTalentTypeList(qo,pageNum,pageSize);
    }

    @PostMapping("/distributeHKRecruitByTalentType/export")
    @Operation(summary = "导出港聘人才类型分布数据列表")
    public void exportDistributeHKRecruitByTalentTypeList(@RequestBody HKTalentQO qo, HttpServletResponse response) throws IOException {
        hkRecruitFacade.exportDistributeHKRecruitByTalentTypeList(qo,response);
    }

    @PostMapping("/distributeHKRecruitByAge")
    @Operation(summary = "获取港聘年龄分布数据")
    public ResultBean<ChartVO> distributeHKRecruitByAge(@RequestBody HKDashQO qo) {
        return new ResultBean<>(hkRecruitFacade.distributeByAge(qo));
    }

    @PostMapping("/distributeHKRecruitByGender")
    @Operation(summary = "获取港聘性别分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitByGender(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeByGender(qo));
    }

    @PostMapping("/distributeHKRecruitByEducation")
    @Operation(summary = "获取港聘学历分布数据")
    public ResultBean<ChartVO> distributeHKRecruitByEducation(@RequestBody HKDashQO qo) {
        return new ResultBean<>(hkRecruitFacade.distributeByEducation(qo));
    }

    @PostMapping("/distributeHKRecruitByCohlSeniority")
    @Operation(summary = "获取港聘司龄分布数据")
    public ResultBean<ChartVO> distributeHKRecruitByCohlSeniority(@RequestBody HKDashQO qo) {
        return new ResultBean<>(hkRecruitFacade.distributeByCohlSeniority(qo));
    }

    @PostMapping("/distributeHKRecruitByJobType")
    @Operation(summary = "获取港聘职位分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitByJobType(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeByJobType(qo));
    }

    @PostMapping("/distributeHKRecruitByJobType/list")
    @Operation(summary = "查询港聘职位分布列表")
    public ResultPage<RosterDetailCategoryVO> distributeHKRecruitByJobTypeList(@RequestBody HKPositionQO qo,@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize){
        return hkRecruitFacade.distributeByJobTypeList(qo,pageNum,pageSize);
    }
    @PostMapping("/distributeHKRecruitByJobType/export")
    @Operation(summary = "导出港聘职位分布列表")
    public void exportDistributeHKRecruitByJobTypeList(@RequestBody HKPositionQO qo,HttpServletResponse response) throws IOException {
        hkRecruitFacade.exportDistributeByJobTypeList(qo,response);
    }

    @PostMapping("/distributeHKRecruitByPosition")
    @Operation(summary = "获取港聘职级分布数据")
    public ResultList<NameCountRateVO> distributeHKRecruitByPosition(@RequestBody HKDashQO qo) {
        return new ResultList<>(hkRecruitFacade.distributeByPosition(qo));
    }

    @PostMapping("/distributeHKRecruitByPosition/list")
    @Operation(summary = "查询港聘职级分布人员列表")
    public ResultPage<RosterDetailCategoryVO> distributeHKRecruitByPositionList(@RequestBody HKPositionQO qo,@RequestParam(value = "pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize){
        return hkRecruitFacade.distributeByPositionList(qo,pageNum,pageSize);
    }

    @PostMapping("/distributeHKRecruitByPosition/export")
    @Operation(summary = "导出港聘职级分布人员列表")
    public void exportDistributeHKRecruitByPositionList(@RequestBody HKPositionQO qo,HttpServletResponse response) throws IOException {
        hkRecruitFacade.exportDistributeByPositionList(qo,response);
    }

    @PostMapping("/getYearlyStaffStat")
    @Operation(summary = "获取港聘年度员工离职统计数据")
    public ResultBean<YearlyStaffStatVO> getYearlyStaffStat(@RequestBody HKDashQO qo) {
        return new ResultBean<>(hkRecruitFacade.getYearlyStaffStat(qo));
    }

    @PostMapping("/getYearlyStaffStat/list")
    @Operation(summary = "查询港聘年度员工离职列表")
    public ResultPage<RosterDetailCategoryVO> getYearlyStaffStatList(@RequestBody HKDashQO qo,@RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum,@RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkRecruitFacade.getYearlyStaffStatList(qo,pageNum,pageSize);
    }

    @PostMapping("/getYearlyStaffStat/export")
    @Operation(summary = "导出港聘年度员工离职列表")
    public void exportYearlyStaffStatList(@RequestBody HKDashQO qo,HttpServletResponse response) throws IOException {
        hkRecruitFacade.exportYearlyStaffStatList(qo,response);
    }
}
