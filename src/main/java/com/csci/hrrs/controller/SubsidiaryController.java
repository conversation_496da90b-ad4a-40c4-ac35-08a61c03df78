package com.csci.hrrs.controller;

import com.csci.common.model.ResultBean;
import com.csci.common.model.ResultList;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.hk.HKDashFacade;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.qo.*;
import com.csci.hrrs.vo.*;
import com.csci.hrrs.vo.subsidiary.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/subsidiary")
@Tag(name = "SubsidiaryController", description = "工程公司面板接口")
public class SubsidiaryController {

    @Resource
    private HKDashFacade hkDashFacade;

    @GetMapping("/org-list")
    @Operation(summary = "获取工程公司列表", description = "获取工程公司列表")
    ResultList<SimpleOrgVO> getOrgList() {
        return new ResultList<>(hkDashFacade.listSubsidiaryOrgs());
    }

    @PostMapping("/person-count")
    @Operation(summary = "获取工程公司人数统计", description = "获取工程公司人员统计:在职人数，当月入职，当月离职，年度平均人数等")
    public ResultBean<HKPersonCountVO> getHKPersonCount(@RequestBody HKDashQO hkDashQO) {
        return new ResultBean<>(hkDashFacade.getSUPersonCount(hkDashQO));
    }

    @PostMapping("/person-list")
    @Operation(summary = "查询工程公司人列表",description = "查询工程公司人员:在职人数，当月入职，当月离职人员列表")
    public ResultPage<RosterDetailCategoryVO> getHKPersonList(@RequestBody HKClassificationQO qo,@RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum,@RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkDashFacade.getPersonList(qo,pageNum,pageSize);
    }

    @PostMapping("/person-export")
    @Operation(summary = "导出工程公司人列表",description = "导出工程公司人员:在职人数，当月入职，当月离职人员列表")
    public void exportHKPersonList(@RequestBody HKClassificationQO qo,HttpServletResponse response) throws IOException {
        hkDashFacade.exportPersonList(qo,response);
    }

    @PostMapping("/element-distribution")
    @Operation(summary = "获取要素分布", description = "获取按要素分布人员比例关系")
    public ResultBean<ElementDistributionVO> getElementDistribution(@RequestBody HKDashQO hkDashQO) {
        return new ResultBean<>(hkDashFacade.listElementDistribution(hkDashQO));
    }

    @PostMapping("/roster-detail-category")
    @Operation(summary = "工程公司-要素分布-获取人员花名册", description = "获取人员花名册-按人员类型分类")
    public ResultPage<RosterDetailCategoryVO> getRosterDetailCategory(@RequestBody HKDashQO hkDashQO,
                                                                      @RequestParam(value = "personType", required = true) String personType,
                                                                      @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                                      @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return hkDashFacade.getElementRosterDetailCategory(hkDashQO, personType, pageNum, pageSize);
    }

    @PostMapping("/roster-detail-category/export")
    @Operation(summary = "工程公司-要素分布-导出人员花名册",description = "导出人员花名册-按人员类型分类")
    public void exportRosterDetailCategory(@RequestBody HKDashQO hkDashQO,
                                           @RequestParam(value = "personType", required = true) String personType, HttpServletResponse response) throws IOException {
        hkDashFacade.exportElementRosterDetailCategory(hkDashQO,personType,response);
    }

    /**
     * @return 获取人员类别
     */
    @PostMapping("/person-category")
    @Operation(summary = "工程公司-获取人员类别", description = "获取按人员类别比例关系")
    public ResultList<RateVO> getPersonCategory(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getPersonCategory(hkDashQO, HrrsConsts.PersonCategory.LEADER));
    }

    @PostMapping("/roster-detail-person-category")
    @Operation(summary = "工程公司-人员类别-获取人员花名册", description = "获取人员花名册-按人员类型分类")
    public ResultPage<RosterDetailCategoryVO> getPersonRosterDetailCategory(@RequestBody HKDashQO hkDashQO,
                                                                            @RequestParam(value = "personType", required = true) String personType,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                                            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return hkDashFacade.getPersonRosterDetailCategory(hkDashQO, HrrsConsts.PersonCategory.LEADER, personType, pageNum, pageSize);
    }

    @PostMapping("/roster-detail-person-category/export")
    @Operation(summary = "工程公司-人员类别-导出人员花名册", description = "导出人员花名册-按人员类型分类")
    public void exportPersonRosterDetailCategory(@RequestBody HKDashQO hkDashQO,
                                              @RequestParam(value = "personType", required = true) String personType,HttpServletResponse response) throws IOException {
        hkDashFacade.exportPersonRosterDetailCategory(hkDashQO,HrrsConsts.PersonCategory.LEADER, personType,response);
    }

    @PostMapping("/outsource-roster-detail-person-category")
    @Operation(summary = "工程公司-人员类别-获取外包人员花名册", description = "获取人员花名册-按人员类型分类")
    public ResultPage<RosterDetailCategoryVO> getOutsourcePersonRosterDetailCategory(@RequestBody HKDashQO hkDashQO,
                                                                                     @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                                                     @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return hkDashFacade.getOutsourcePersonRosterDetailCategory(hkDashQO, HrrsConsts.PersonCategory.LEADER, pageNum, pageSize);
    }

    @PostMapping("/outsource-roster-detail-person-category/export")
    @Operation(summary = "工程公司-人员类别-导出外包人员花名册", description = "导出人员花名册-按人员类型分类")
    public void exportOutsourcePersonRosterDetailCategory(@RequestBody HKDashQO hkDashQO,HttpServletResponse response) throws IOException {
        hkDashFacade.exportOutsourcePersonRosterDetailCategory(hkDashQO,response);
    }

    @PostMapping("/distribution-by-category")
    @Operation(summary = "获取维度分布", description = "内地、内派、核心港聘、自有日薪、地盘基层")
    public ResultList<RateTreeVO> getDistributionByCategory(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getDistributionByCategory(hkDashQO));
    }

    @PostMapping("/roster-detail-distribution-category")
    @Operation(summary = "工程公司-维度分布-获取人员花名册", description = "内地、内派、核心港聘、自有日薪、地盘基层")
    public ResultPage<RosterDetailCategoryVO> getRosterDetailDistributionByCategory(@RequestBody HKDashQO hkDashQO,
                                                                                    @RequestParam(value = "personType", required = true) String personType,
                                                                                    @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                                                    @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return hkDashFacade.getRosterDetailDistributionByCategory(hkDashQO, personType, pageNum, pageSize);
    }
    @PostMapping("/roster-detail-distribution-category/export")
    @Operation(summary = "工程公司-维度分布-导出人员花名册", description = "内地、内派、核心港聘、自有日薪、地盘基层")
    public void exportRosterDetailDistributionByCategory(@RequestBody HKDashQO hkDashQO,
                                                         @RequestParam(value = "personType", required = true) String personType,HttpServletResponse response) throws IOException {
        hkDashFacade.exportRosterDetailDistributionByCategory(hkDashQO, personType,response);
    }

    @PostMapping("/subsidiary-person-count")
    @Operation(summary = "获取工程公司人数统计", description = "获取工程公司人数统计：当前平均人数，当前编制使用率，当前薪酬总额，当前薪酬使用率，人均产值，百元产值人工成本")
    public ResultBean<SubsidiaryPersonCountVO> getSubsidiaryPersonCount(@RequestBody HKDashQO hkDashQO) {
        return new ResultBean<>(hkDashFacade.getSubsidiaryPersonCount(hkDashQO));
    }

    @PostMapping("/job-level-distribution")
    @Operation(summary = "获取职级分布", description = "\"助总及以上/总监Ⅰ\", \"总监Ⅱ/副总监/助理总监/高级经理/经理级\", \"副经理/助理经理/高级工程师\", \"工程师/助理工程师\", \"学徒/文员/司机\"")
    public ResultList<RateVO> getJobLevelDistribution(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getJobLevelDistribution(hkDashQO));
    }
    @PostMapping("/job-level-distribution/list")
    @Operation(summary = "查询职级分布人员列表",description = "\"助总及以上/总监Ⅰ\", \"总监Ⅱ/副总监/助理总监/高级经理/经理级\", \"副经理/助理经理/高级工程师\", \"工程师/助理工程师\", \"学徒/文员/司机\"")
    public ResultPage<RosterDetailCategoryVO> getJobLevelDistributionList(@RequestBody HKJobDistributionQO qo,@RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum,@RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkDashFacade. selectHKManagerRosterListByDetermineJobLevelName(qo,pageNum,pageSize);
    }

    @PostMapping("/job-level-distribution/export")
    public void exportJobLevelDistributionList(@RequestBody  HKJobDistributionQO qo,HttpServletResponse response) throws IOException {
        hkDashFacade.exportHKManagerRosterListByDetermineJobLevelName(qo,response);
    }

    @PostMapping("/stat-by-major")
    @Operation(summary = "获取专业分布", description = "获取专业分布")
    public ResultList<RateVO> getStatByMajor(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getStatByMajor(hkDashQO));
    }

    @PostMapping("/stat-by-major/list")
    @Operation(summary = "查询专业分布",description = "查询专业分布")
    public ResultPage<RosterDetailCategoryVO> getStatByMajorList(@RequestBody HKMajorQO qo, @RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkDashFacade.getStatByMajorList(qo,pageNum,pageSize);
    }

    @PostMapping("/stat-by-major/export")
    @Operation(summary = "导出专业分布",description = "导出专业分布")
    public void exportStatByMajor(@RequestBody HKMajorQO qo,HttpServletResponse response) throws IOException {
        hkDashFacade.exportStatByMajor(qo,response);
    }


    @PostMapping("/person-salary-warning")
    @Operation(summary = "获取人员薪酬预警", description = "获取人员薪酬预警")
    public ResultList<PersonSalaryWarningVO> getPersonSalaryWarning(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getPersonSalaryWarning(hkDashQO));
    }

    @PostMapping("/full-period/ranking")
    @Operation(summary = "获取全周期龍虎榜信息", description = "地盘龙虎榜-全周期-获取龍虎榜信息")
    public ResultList<RankingVO> getFullPeriodRanking(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getFullPeriodRanking(hkDashQO));
    }

    @PostMapping("/current-year/ranking")
    @Operation(summary = "获取本年度龍虎榜信息", description = "地盘龙虎榜-本年度-获取龍虎榜信息")
    public ResultList<RankingVO> getCurrentYearRanking(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getCurrentYearRanking(hkDashQO));
    }

    @PostMapping("/full-period/staff")
    @Operation(summary = "获取全周期人员信息", description = "地盘龙虎榜-全周期-获取人员信息")
    public ResultList<StaffVO> getFullPeriodStaff(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getFullPeriodStaff(hkDashQO));
    }

    @PostMapping("/current-year/staff")
    @Operation(summary = "获取本年度人员信息", description = "地盘龙虎榜-本年度-获取人员信息")
    public ResultList<StaffVO> getCurrentYearStaff(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getCurrentYearStaff(hkDashQO));
    }

    @PostMapping("/full-period/salary")
    @Operation(summary = "获取全周期费用信息", description = "地盘龙虎榜-全周期-获取费用信息")
    public ResultList<SalaryVO> getFullPeriodSalary(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getFullPeriodSalary(hkDashQO));
    }

    @PostMapping("/current-year/salary")
    @Operation(summary = "获取本年度费用信息", description = "地盘龙虎榜-本年度-获取费用信息")
    public ResultList<SalaryVO> getCurrentYearSalary(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getCurrentYearSalary(hkDashQO));
    }

    @PostMapping("/full-period/efficiency")
    @Operation(summary = "获取全周期人效信息", description = "地盘龙虎榜-全周期-获取人效信息")
    public ResultList<EfficiencyVO> getFullPeriodEfficiency(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getFullPeriodEfficiency(hkDashQO));
    }

    @PostMapping("/current-year/efficiency")
    @Operation(summary = "获取本年度人效信息", description = "地盘龙虎榜-本年度-获取人效信息")
    public ResultList<EfficiencyVO> getCurrentYearEfficiency(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getCurrentYearEfficiency(hkDashQO));
    }

    @PostMapping("/efficiency-ranking")
    @Operation(summary = "获取效能排名", description = "全周期人均效能排名: 地盘/项目产值数据需要跟财务确认后再提供")
    public ResultBean<EfficiencyRankingVO> getEfficiencyRanking(@RequestBody HKDashQO hkDashQO) {
        return new ResultBean<>(hkDashFacade.getEfficiencyRanking(hkDashQO));
    }

    @PostMapping("/annual-loss-rate")
    @Operation(summary = "获取年度流失率", description = "获取年度流失率")
    public ResultList<AnnualLossRateVO> getAnnualLossRate(@RequestBody HKDashQO hkDashQO) {
        return new ResultList<>(hkDashFacade.getAnnualLossRate(hkDashQO));
    }

    @PostMapping("/annual-loss-rate/list")
    @Operation(summary = "查询年度流失人员列表",description = "查询年度流失人员列表")
    public ResultPage<RosterDetailCategoryVO> getAnnualLostList(@RequestBody HKLossQO qo,@RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum,@RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        return hkDashFacade.getAnnualLossList(qo,pageNum,pageSize);
    }

    @PostMapping("/annual-loss-rate/export")
    @Operation(summary = "导出年度流失人员列表",description = "导出年度流失人员列表")
    public void exportAnnualLostList(@RequestBody HKLossQO qo,HttpServletResponse response) throws IOException {
        hkDashFacade.exportAnnualLossList(qo,response);
    }

}
