package com.csci.hrrs.controller;

import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.UserRoleFacade;
import com.csci.hrrs.model.ResultBean;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.model.Role;
import com.csci.hrrs.qo.RolePageableQO;
import com.csci.hrrs.service.RoleService;
import com.csci.hrrs.vo.RoleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/role", produces = "application/json")
@Tag(name = "14. 角色管理", description = "提供处理角色相关的接口")
@LogMethod
public class RoleController {

    @Resource
    private RoleService roleService;

    @Resource
    private UserRoleFacade userRoleFacade;

    /**
     * 查找 角色 数据列表
     *
     * @param roleQO 查询
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "查询", description = "分页查询角色列表，可指定用户名查询")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"))
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfRole.class)))
    public ResultPage<RoleVO> list(RolePageableQO roleQO) {
        return userRoleFacade.listRole(roleQO);
    }

    @PostMapping("/save")
    @Operation(summary = "保存", description = "保存角色，如果传入id则为更新，否则为新增")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"))
    public ResultBean<String> save(@RequestBody RoleVO roleVO) {
        return new ResultBean<>(roleService.saveRole(roleVO));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除", description = "删除指定id的角色.系统角色不允许删除")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"))
    public ResultBean<Integer> delete(@PathVariable String id) {
        return new ResultBean<>(roleService.deleteRole(id));
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "查询单个数据", description = "根据id查询角色信息")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"))
    public ResultBean<RoleVO> getRole(@PathVariable String id) {
        return new ResultBean<>(roleService.getRoleById(id));
    }

    private class ResultPageOfRole extends ResultPage<Role> {
        public ResultPageOfRole(List<?> page) {
            super(page);
        }
    }
}
