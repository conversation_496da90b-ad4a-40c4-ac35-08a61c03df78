package com.csci.hrrs.controller;

import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.facade.WorkflowFacade;
import com.csci.hrrs.model.ResultBase;
import com.csci.hrrs.model.ResultBean;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.qo.WorkflowControlQO;
import com.csci.hrrs.qo.WorkflowQO;
import com.csci.hrrs.service.WorkflowControlService;
import com.csci.hrrs.service.WorkflowService;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.vo.WorkflowControlVO;
import com.csci.hrrs.vo.WorkflowVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/workflow", produces = "application/json")
@Tag(name = "17. 工作流接口展示", description = "用于接口调试")
@LogMethod
public class WorkflowController {

    @Resource
    private WorkflowService workflowService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private WorkflowControlService workflowControlService;

    @GetMapping("/list")
    @Operation(description = "分页查询工作流列表")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<WorkflowVO> listWorkflow(WorkflowQO workflowQO) {
        return workflowService.listWorkflow(workflowQO);
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id获取工作流")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<WorkflowVO> getWorkflow(@PathVariable String id) {
        return new ResultBean<>(workflowService.findWorkflowById(id));
    }

    @PostMapping("/save")
    @Operation(description = "保存工作流")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> saveWorkflow(@RequestBody WorkflowVO workflowVO) {
        String lockKey;
        if (StringUtils.isNotBlank(workflowVO.getId())) {
            lockKey = "lock:saveWorkflow:" + workflowVO.getId();
        } else {
            lockKey = "lock:saveWorkflow:" + workflowVO.getOrganizationId() + ":" + workflowVO.getFormId();
        }

        if (!RedisLockUtil.lockSimple(lockKey, 60)) {
            throw new com.csci.common.exception.ServiceException("请求处理中，请勿重复提交");
        }
        try {
            return new ResultBean<>(workflowFacade.saveWorkflow(workflowVO));
        } finally {
            RedisLockUtil.unLockSimple(lockKey);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除工作流")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> deleteWorkflow(@PathVariable String id) {
        return new ResultBean<>(workflowService.inactiveWorkflow(id));
    }

    @GetMapping("/control/list")
    @Operation(description = "分页查询工作流控制列表")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<WorkflowControlVO> listWorkflowControl(WorkflowControlQO workflowControlQO) {
        return workflowControlService.listWorkflowControlByPage(workflowControlQO);
    }

    @GetMapping("/control/get")
    @Operation(description = "根据业务id获取工作流控制记录")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<WorkflowControlVO> getWorkflowControl(String businessId) {
        return new ResultBean<>(workflowControlService.getWorkflowControlByBusinessId(businessId));
    }

}
