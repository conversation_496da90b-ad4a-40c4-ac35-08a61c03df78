package com.csci.hrrs.controller;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultBase;
import com.csci.common.model.ResultBean;
import com.csci.common.model.ResultList;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.apply.vo.AggregateSalaryVO;
import com.csci.hrrs.configuration.parallel.ParallelExecutor;
import com.csci.hrrs.facade.SalaryStatAuthorityCheckFacade;
import com.csci.hrrs.facade.SalaryStatFacade;
import com.csci.hrrs.facade.UserSalaryCategoryFacade;
import com.csci.hrrs.qo.SalaryStatQO;
import com.csci.hrrs.service.SalaryCategoryService;
import com.csci.hrrs.util.RedisLockUtil;
import com.csci.hrrs.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/salary/stat")
@Tag(name = "薪酬统计", description = "薪酬统计相关接口")
@LogMethod
@Order(2)
public class SalaryStatController {

    @Resource
    private SalaryStatFacade salaryStatFacade;

    @Resource
    private SalaryCategoryService salaryCategoryService;

    @Resource
    private UserSalaryCategoryFacade userSalaryCategoryFacade;

    @Resource
    private SalaryStatAuthorityCheckFacade salaryStatAuthorityCheckFacade;

    @GetMapping("/fill/org-list")
    @Operation(summary = "薪酬统计填报组织机构列表", description = "查询薪酬统计填报组织机构列表")
    ResultList<String> listUserOrgCodesForFill() {
        return new ResultList<>(salaryStatFacade.listCurrentUsersOrgCodes());
    }

    @GetMapping("/fill/pay-group-list")
    @Operation(summary = "薪酬统计填报小组列表", description = "查询当前用户所属的薪酬统计填报小组列表")
    ResultList<PayGroupVO> listCurrentUserPayGroups(String organizationCode) {
        return new ResultList<>(salaryStatFacade.listCurrentUserPayGroups(organizationCode));
    }

    @GetMapping("/fill/list")
    @Operation(summary = "薪酬统计填报列表", description = "查询薪酬统计填报列表")
    public ResultList<SalaryStatVO> listSalaryStatFill(SalaryStatQO qo) {
        return new ResultList<>(salaryStatFacade.listSalaryStatFill(qo));
    }

    @GetMapping("/list")
    @Operation(summary = "薪酬统计列表", description = "查询薪酬统计列表")
    public ResultBean<EditSalaryStatVO> listSalaryStat(SalaryStatQO qo) {
        return new ResultBean<>(salaryStatFacade.getSalaryStat(qo));
    }

    @PostMapping("/save")
    @Operation(summary = "保存薪酬统计", description = "保存薪酬统计，如果没有则新增，有则更新")
    public ResultBase saveSalaryStat(@RequestBody FillSalaryStatVO editSalaryStatVO) {
        String lock = "saveSalaryStat:" + editSalaryStatVO.getOrganizationCode() + ":" + editSalaryStatVO.getYear();

        salaryStatAuthorityCheckFacade.checkAuthority(editSalaryStatVO.getOrganizationCode(), editSalaryStatVO.getPayGroupId());
        if (RedisLockUtil.lock(lock)) {
            try {
                salaryStatFacade.saveSalaryStat(editSalaryStatVO);
                ParallelExecutor.submit(() -> {
                    // 异步处理，不影响当前用户的操作
                    salaryStatFacade.calculateOrgAll(editSalaryStatVO.getOrganizationCode(), editSalaryStatVO.getYear());
                });
                return new ResultBase();
            } finally {
                RedisLockUtil.unlock(lock);
            }
        } else {
            throw new ServiceException("该单位的数据正在保存中，请稍后再试");
        }


    }

    /*查询所有薪酬类别列表*/
    @GetMapping("/category/list")
    @Operation(summary = "薪酬类别列表", description = "查询所有薪酬类别列表")
    public ResultList<SalaryCategoryVO> listSalaryCategories() {
        return new ResultList<>(salaryCategoryService.listCategory());
    }

    /*查询用户拥有访问权限的薪酬类别*/
    @GetMapping("/user/category/list")
    @Operation(summary = "用户薪酬类别列表", description = "查询用户拥有访问权限的薪酬类别")
    public ResultList<SalaryCategoryVO> listUsersSalaryCategories(String username) {
        return new ResultList<>(userSalaryCategoryFacade.listUsersSalaryCategories(username));
    }

    /*保存用户的薪酬访问权限类别*/
    @PostMapping("/user/category/save")
    @Operation(summary = "保存用户薪酬类别", description = "保存用户的薪酬访问权限类别")
    public ResultBase saveUserSalaryCategories(@RequestBody SaveUserSalaryCategoryVO saveUserSalaryCategoryVO) {
        userSalaryCategoryFacade.saveUserSalaryCategoryAccess(saveUserSalaryCategoryVO.getUsername(), saveUserSalaryCategoryVO.getSalaryCategoryCodeList());
        return new ResultBase();
    }

    @PostMapping("/aggregate")
    @Operation(summary = "薪酬统计汇总", description = "薪酬统计汇总")
    ResultBase aggregateSalaryStat(@RequestBody AggregateSalaryVO aggregateSalaryVO) {
        String lock = "aggregateSalaryStat:" + aggregateSalaryVO.getOrganizationCode() + ":" + aggregateSalaryVO.getYear();
        if (RedisLockUtil.lock(lock)) {
            try {
                salaryStatFacade.aggregateSalaryStat(aggregateSalaryVO.getOrganizationCode(), aggregateSalaryVO.getYear());
                return new ResultBase();
            } finally {
                RedisLockUtil.unlock(lock);
            }
        } else {
            throw new ServiceException("该单位的数据正在汇总中，请稍后再试");
        }

    }

}
