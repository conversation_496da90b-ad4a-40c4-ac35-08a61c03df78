package com.csci.hrrs.controller;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultBase;
import com.csci.common.model.ResultBean;
import com.csci.common.model.ResultList;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.configuration.parallel.ParallelExecutor;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.qo.WorkHourQO;
import com.csci.hrrs.service.WorkHourService;
import com.csci.hrrs.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/workHour")
@Tag(name = "工時统计", description = "工時统计相关接口")
@LogMethod
public class WorkHourController {
    @Resource
    private WorkHourService workHourService;

    @PostMapping("/get-work-overtime-stat")
    @Operation(summary = "查询工时统计信息", description = "查询工时统计信息")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    ResultBean<WorkOvertimeVO> getWorkOvertimeStat(@RequestBody WorkHourQO qo) {
        return new ResultBean<>(workHourService.getWorkOvertimeStat(qo));
    }

    @PostMapping("/get-work-overtime-pay-stat")
    @Operation(summary = "查询加班費信息", description = "查询加班費信息")
    @Parameter(in = ParameterIn.HEADER, name = HrrsConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    ResultBean<List<WorkOvertimePayVO>> getWorkOvertimePayStat(@RequestBody WorkHourQO qo) {
        return new ResultBean<>(workHourService.getWorkOvertimePayStat(qo));
    }

}
