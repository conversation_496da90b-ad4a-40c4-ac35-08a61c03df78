package com.csci.hrrs.controller;

import com.csci.hrrs.dto.ImportPlanFullDTO;
import com.csci.hrrs.dto.ImportPlanYearlyDTO;
import com.csci.hrrs.dto.group.ImportPlanGroup;
import com.csci.hrrs.model.ResultBean;
import com.csci.hrrs.model.ResultList;
import com.csci.hrrs.qo.plan.StaffCountQO;
import com.csci.hrrs.service.ImportPlanService;
import com.csci.hrrs.vo.plan.ImportPlanFullVO;
import com.csci.hrrs.vo.plan.ImportPlanYearlyVO;
import com.csci.hrrs.vo.plan.ManagerBaseVO;
import com.csci.hrrs.vo.plan.OrgBaseVO;
import com.csci.hrrs.vo.plan.OutputValueVO;
import com.csci.hrrs.vo.plan.StaffCountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName: ImportPlanController
 * @Auther: <EMAIL>
 * @Date: 2025/1/2 下午7:47
 * @Description: 编制导入相关接口
 */
@RestController
@RequestMapping("/import-plan")
@Tag(name = "ImportPlanController", description = "编制导入处理接口")
public class ImportPlanController {

    @Resource
    private ImportPlanService importPlanService;


    @GetMapping("/query-base-org")
    @Operation(summary = "基本信息-根据组织代码查询组织机构", description = "根据组织代码查询组织机构")
    public ResultBean<OrgBaseVO> queryBaseByOrgInfo(@RequestParam(value = "orgCode", required = true) String orgCode) {
        return new ResultBean<>(importPlanService.queryBaseByOrgInfo(orgCode));
    }

    @GetMapping("/query-base-manager")
    @Operation(summary = "基本信息-根据关键词查询管理人员", description = "根据关键词查询管理人员")
    public ResultList<ManagerBaseVO> queryBaseByManagerInfo(@RequestParam("keyWord") String keyWord) {
        return new ResultList<>(importPlanService.queryBaseByManagerInfo(keyWord));
    }

    @PostMapping("/query-staff-full-adjust")
    @Operation(summary = "基本信息-全周期-根据编制计划ID获取编制信息(人员编制数、薪酬总预算数)", description = "根据编制计划ID获取编制人员信息")
    public ResultBean<StaffCountVO> queryBaseByStaffFullAdjust(@RequestBody @Validated(value = {ImportPlanGroup.Full.class}) StaffCountQO qo) {
        return new ResultBean<>(importPlanService.queryBaseByStaffFullAdjust(qo));
    }

    @PostMapping("/query-staff-yearly-adjust")
    @Operation(summary = "基本信息-年度-根据编制计划ID获取编制信息(人员编制数、薪酬总预算数)", description = "根据编制计划ID获取编制人员信息")
    public ResultBean<StaffCountVO> queryBaseByStaffYearlyAdjust(@RequestBody @Validated(value = {ImportPlanGroup.Yearly.class}) StaffCountQO dto) {
        return new ResultBean<>(importPlanService.queryBaseByStaffYearlyAdjust(dto));
    }

    @PostMapping("/query-full-output-value")
    @Operation(summary = "基本信息-全周期-根据编制计划ID获取信息（产值、百元成本）", description = "根据编制计划ID获取信息")
    public ResultBean<OutputValueVO> queryBaseByPlanFullOutputValue(@RequestBody @Validated(value = {ImportPlanGroup.Full.class}) StaffCountQO dto) {
        return new ResultBean<>(importPlanService.queryBaseByPlanFullOutputValue(dto));
    }

    @PostMapping("/add-full")
    @Operation(summary = "编制导入-全周期编织计划导入", description = "全周期编织计划导入")
    public ResultBean<ImportPlanFullVO> saveFullBaseInfo(@RequestBody @Validated(value = {ImportPlanGroup.Insert.class}) ImportPlanFullDTO dto) {
        return importPlanService.saveFullBaseInfo(dto);
    }

    @PostMapping("/save-full")
    @Operation(summary = "暂存-全周期编织计划导入", description = "全周期编织计划导入")
    public ResultBean<ImportPlanFullVO> saveFullImportPlan(@RequestBody @Validated(value = {ImportPlanGroup.Insert.class}) ImportPlanFullDTO dto) {
        return importPlanService.saveFullImportPlan(dto);
    }

    @GetMapping("/save-draft-full")
    @Operation(summary = "编制导入-生成草稿-全周期", description = "编织计划导入-生成草稿-全周期")
    public ResultBean<ImportPlanFullVO> getImportPlanFullDraft(@RequestParam(value = "orgCode", required = true) String orgCode) {
        return importPlanService.getImportPlanFullDraft(orgCode);
    }

    @GetMapping("/query-full")
    @Operation(summary = "编制导入-查询指定类型指定状态的数据", description = "编织计划导入-查询指定类型指定状态的数据")
    public ResultBean<ImportPlanFullVO> queryImportPlanFullSubmit(@RequestParam(value = "orgCode", required = true) String orgCode,
                                                                  @RequestParam(value = "planType", required = true) String planType,
                                                                  @RequestParam(value = "status", required = true) String status) {
        return importPlanService.queryImportPlanFullSubmit(orgCode, planType, status);
    }

    @GetMapping("/save-draft-year")
    @Operation(summary = "编制导入-生成草稿-年度", description = "编织计划导入-生成草稿-年度")
    public ResultBean<ImportPlanYearlyVO> getImportPlanYearlyDraft(@RequestParam(value = "orgCode", required = true) String orgCode) {
        return importPlanService.getImportPlanYearlyDraft(orgCode);
    }

    @PostMapping("/add-year")
    @Operation(summary = "编制导入-年度编织计划导入", description = "年度编织计划导入")
    public ResultBean<ImportPlanYearlyVO> saveYearBaseInfo(@RequestBody @Validated(value = {ImportPlanGroup.Insert.class}) ImportPlanYearlyDTO dto) {
        return importPlanService.saveYearBaseInfo(dto);
    }

    @PostMapping("/save-year")
    @Operation(summary = "暂存-年度编织计划导入", description = "年度编织计划导入")
    public ResultBean<ImportPlanYearlyVO> saveYearImportPlan(@RequestBody @Validated(value = {ImportPlanGroup.Insert.class}) ImportPlanYearlyDTO dto) {
        return importPlanService.saveYearImportPlan(dto);
    }

}
