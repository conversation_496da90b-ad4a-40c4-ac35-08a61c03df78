package com.csci.hrrs.perf830.util;

import com.csci.hrrs.perf830.vo.OverallStatVO;
import com.csci.hrrs.vo.RosterHeadIdByYearMonthCountVO;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * @ClassName: CommonUtil
 * @Auther: <EMAIL>
 * @Date: 2025/4/25 10:02
 * @Description:
 */
public class CommonUtil {

    public static OverallStatVO buildPersonCountVO(long onJobCount, long currentPlanCount,
                                                      long entryCount, long leaveCount) {
        OverallStatVO vo = new OverallStatVO();
        vo.setOnJobCount(onJobCount);
        vo.setStaffCount(currentPlanCount);
        vo.setEntryCount(entryCount);
        vo.setLeaveCount(leaveCount);
        return vo;
    }

    /**
     * 判断 RosterHeadIdByYearMonthCountVO 是否为当前年月
     */
    public static boolean isCurrentYearAndMonth(RosterHeadIdByYearMonthCountVO rosterHead, LocalDate statDate) {
        return statDate.getYear() == rosterHead.getYear() && statDate.getMonthValue() == rosterHead.getMonth();
    }
}
