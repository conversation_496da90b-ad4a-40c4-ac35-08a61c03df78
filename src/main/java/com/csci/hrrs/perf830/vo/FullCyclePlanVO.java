package com.csci.hrrs.perf830.vo;

import com.csci.hrrs.dto.group.ImportPlanGroup;
import com.csci.hrrs.model.ImportPlan;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: FullCyclePlanVO
 * @Auther: <EMAIL>
 * @Date: 2025/5/19 11:43
 * @Description: 全周期编制申报
 */
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class FullCyclePlanVO {
    @Schema(description = "导入计划ID")
    @NotEmpty(message = "编制导入ID", groups = {ImportPlanGroup.Update.class, ImportPlanGroup.Insert.class})
    private String id;
    @Schema(description = "组织编码")
    @NotEmpty(message = "组织编码", groups = {ImportPlanGroup.Insert.class, ImportPlanGroup.Update.class})
    private String orgCode;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "地盘合约额")
    private BigDecimal contractAmount;
    @Schema(description = "负责人,使用username，负责人接口查询结果：managerEngName")
    private String siteLeaderUsername;
    @Schema(description = "负责人,使用name,负责人接口查询结果：managerName")
    private String siteLeaderName;
    @Schema(description = "开工时间")
    private LocalDate startDate;
    @Schema(description = "完工时间")
    private LocalDate endDate;
    @Schema(description = "月人均产值")
    private BigDecimal monthAvgValue;
    @Schema(description = "工资产值率")
    private BigDecimal laborCostRate;
    @Schema(description = "情况说明")
    private String situation;
    @Schema(description = "备注：选其他原因时，详细原因")
    private String remarks;
    @Schema(description = "编制计划类型:full=全周期，yearly=年度计划")
    private final String planType = "full";
    @Schema(description = "状态:draft=草稿, submitted=已提交, approved=审批完成")
    private String status;

    // 调整前指标
    @Schema(description = "全周期总编制数（调整前）")
    private Integer totalStaffBefore;  // 全周期总编制数（调整前）
    @Schema(description = "全周期薪酬总预算（调整前）")
    private BigDecimal totalSalaryBefore; // 全周期薪酬总预算（调整前）

    // 调整后指标
    @Schema(description = "全周期总编制数（调整后）")
    private Integer totalStaffAfter;  // 全周期总编制数（调整后）
    @Schema(description = "全周期薪酬总预算（调整后）")
    private BigDecimal totalSalaryAfter; // 全周期薪酬总预算（调整后）

    public static FullCyclePlanVO convertToFullCycleVO(ImportPlan plan) {
        FullCyclePlanVO vo = new FullCyclePlanVO();
        BeanUtils.copyProperties(plan, vo);
        vo.setMonthAvgValue(plan.getMonthAvgSubmittedValue());
        vo.setLaborCostRate(plan.getLaborCostPerHundredOutputSubmitted());
        vo.setTotalStaffBefore(plan.getTotalStaffCountBefore());
        vo.setTotalSalaryBefore(plan.getTotalBudgetBefore());
        vo.setTotalStaffAfter(plan.getTotalStaffCountAfter());
        vo.setTotalSalaryAfter(plan.getTotalBudgetAfter());
        return vo;
    }

    public static ImportPlan convertToImportPlan(FullCyclePlanVO vo) {
        ImportPlan plan = new ImportPlan();
        BeanUtils.copyProperties(vo, plan);
        plan.setMonthAvgSubmittedValue(vo.getMonthAvgValue());
        plan.setLaborCostPerHundredOutputSubmitted(vo.getLaborCostRate());
        plan.setTotalStaffCountBefore(vo.getTotalStaffBefore());
        plan.setTotalBudgetBefore(vo.getTotalSalaryBefore());
        plan.setTotalStaffCountAfter(vo.getTotalStaffAfter());
        plan.setTotalBudgetAfter(vo.getTotalSalaryAfter());
        plan.setIsDeleted(false);
        return plan;
    }
}

