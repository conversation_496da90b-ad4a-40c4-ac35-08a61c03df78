package com.csci.hrrs.perf830.vo;

import com.csci.hrrs.dto.group.ImportPlanGroup;
import com.csci.hrrs.model.ImportPlan;
import com.csci.hrrs.perf830.constant.PlanStatus;
import com.csci.hrrs.perf830.constant.PlanTypes;
import com.csci.hrrs.service.StaffCountSubjectService;
import com.csci.hrrs.service.UserService;
import com.csci.hrrs.util.SpringContextHolder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * @ClassName: PlanRecordVO
 * @Auther: <EMAIL>
 * @Date: 2025/5/19 14:53
 * @Description: 编制记录
 */
@Data
@NoArgsConstructor
public class PlanRecordVO {
    @Schema(description = "编制记录ID")
    private String id;
    @Schema(description = "组织编码")
    @NotEmpty(message = "组织编码", groups = {ImportPlanGroup.Insert.class, ImportPlanGroup.Update.class})
    private String orgCode;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "申报类型")
    private String planName;
    @Schema(description = "地盘合约额（港元）")
    private BigDecimal contractAmount;
    @Schema(description = "开工日期")
    private LocalDate startDate;
    @Schema(description = "完工日期")
    private LocalDate endDate;
    @Schema(description = "负责人")
    private String siteLeaderName;
    @Schema(description = "申报人")
    private String applicantName;
    @Schema(description = "申报日期")
    private LocalDate applyDate;
    @Schema(description = "状态名称")
    private String statusName;
    @Schema(description = "状态编码")
    private String statusCode;
    @Schema(description = "审核日期")
    private LocalDate reviewDate;

    public static PlanRecordVO fromModel(ImportPlan importPlan) {
        PlanRecordVO planRecordVO = new PlanRecordVO();
        planRecordVO.setId(importPlan.getId());
        planRecordVO.setOrgCode(importPlan.getOrgCode());
        planRecordVO.setOrgName(importPlan.getOrgName());
        int staffYear = Optional.ofNullable(importPlan.getStaffYear()).orElse(0);
        planRecordVO.setPlanName(importPlan.getPlanType(), staffYear);
        planRecordVO.setContractAmount(importPlan.getContractAmount());
        planRecordVO.setStartDate(importPlan.getStartDate());
        planRecordVO.setEndDate(importPlan.getEndDate());
        planRecordVO.setSiteLeaderName(importPlan.getSiteLeaderName());
        planRecordVO.setApplicantName(importPlan.getLastUpdateUsername());
        planRecordVO.setApplyDate(importPlan.getApplyDate());
        planRecordVO.setStatusName(importPlan.getStatus());
        planRecordVO.setStatusCode(importPlan.getStatus());
        planRecordVO.setReviewDate(importPlan.getStatus(), importPlan.getLastUpdateTime());
        return planRecordVO;
    }


    public void setPlanName(String planType, int staffYear) {
        if (PlanTypes.FULL.getCode().equals(planType)) {
            this.planName = PlanTypes.FULL.getName(); // "全周期"
        } else if (PlanTypes.YEARLY.getCode().equals(planType)) {
            this.planName = staffYear + PlanTypes.YEARLY.getName(); // "2023年度"
        } else {
            this.planName = PlanTypes.getNameByCode(planType); // 默认处理
        }
    }


    public void setApplicantName(String applicantName) {
        UserService userService = SpringContextHolder.getBean(UserService.class);
        this.applicantName = userService.getByUserName(applicantName);
    }

    public void setStatusName(String statusName) {
        this.statusName = PlanStatus.getNameByCode(statusName);
    }

    public void setReviewDate(String status, LocalDateTime reviewDate) {
        if (status.equals("approved")) {
            this.reviewDate = reviewDate.toLocalDate();
        }
    }


}
