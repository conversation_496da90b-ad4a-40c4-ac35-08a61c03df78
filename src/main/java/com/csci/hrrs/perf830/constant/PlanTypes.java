package com.csci.hrrs.perf830.constant;

import java.util.Arrays;

public enum PlanTypes {
    FULL("full", "全周期"),
    YEARLY("yearly", "年度");

    private final String code;
    private final String name;

    PlanTypes(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 根据 code 获取 name
    public static String getNameByCode(String code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst()
                .map(PlanTypes::getName)
                .orElse("未知类型");
    }
}
