package com.csci.hrrs.perf830.qo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: FarEastQO
 * @Auther: <EMAIL>
 * @Date: 2025/6/4 9:36
 * @Description: 远东香港-公共入参
 */
@Data
public class FarEastQO {
    @Schema(description = "统计日期, 格式: yyyy-MM-dd 或 yyyy-MM")
    private LocalDate statDate;

    @Schema(description = "组织机构编码")
    private List<String> organizationCodeList;

    @Schema(description = "是否全部产值")
    private Boolean isAllRevenue = false;

    // 强制参数是ArrayList，防止只有一个orgCode时，类型是ImmutableCollections，修改organizationCodeList异常
    public void setOrganizationCodeList(List<String> organizationCodeList) {
        this.organizationCodeList = new ArrayList<>(organizationCodeList);
    }
}
