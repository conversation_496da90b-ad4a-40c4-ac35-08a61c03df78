package com.csci.hrrs.perf830.controller;

import com.csci.common.model.ResultBase;
import com.csci.common.model.ResultList;
import com.csci.hrrs.perf830.qo.EfficiencyQO;
import com.csci.hrrs.perf830.service.FarEastEfficiencyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @ClassName: FarEastEfficiencyController
 * @Auther: <EMAIL>
 * @Date: 2025/5/7 11:04
 * @Description: 远东香港-工效系统相关接口
 */
@RestController
@RequestMapping("/efficiency-management")
@Tag(name = "工效管理", description = "工效管理相关接口")
public class FarEastEfficiencyController {
    @Resource
    private FarEastEfficiencyService farEastEfficiencyService;

    @PostMapping("/list")
    @Operation(summary = "获取工效管理列表", description = "根据组织机构编码和员工类别获取工效管理列表")
    public ResultList<Map<String, Object>> listSalaryRecord(@RequestBody EfficiencyQO qo) {
        return new ResultList<>(farEastEfficiencyService.listEffectiveRecord(qo));
    }

    @PostMapping("/export")
    @Operation(summary = "导出工效数据", description = "根据查询条件导出工效数据")
    public void exportEfficiencyData(@RequestBody EfficiencyQO qo, HttpServletResponse response) {
        farEastEfficiencyService.exportEfficiencyData(qo, response);
    }

    @PostMapping("/import")
    @Operation(summary = "导入工效数据", description = "导入Excel格式的工效数据")
    public ResultBase importEfficiencyData(@RequestPart("file") MultipartFile file) {
        farEastEfficiencyService.importEfficiencyData(file);
        return ResultBase.success();
    }

    @PostMapping("/template")
    @Operation(summary = "工效模板", description = "下载工效模板")
    public void downloadTemplate(HttpServletResponse response, @RequestBody EfficiencyQO qo) {
        farEastEfficiencyService.downloadTemplate(response, qo);
    }

}
