package com.csci.hrrs.perf830.controller;

import com.csci.common.model.ResultList;
import com.csci.hrrs.perf830.facade.FarEastDashExportFacade;
import com.csci.hrrs.perf830.facade.FarEastDashboardFacade;
import com.csci.hrrs.perf830.qo.FarEastQO;
import com.csci.hrrs.qo.DashboardQO;
import com.csci.hrrs.vo.RosterDistrOrgVO;
import com.csci.hrrs.vo.SimpleOrgVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName: FarEast
 * @Auther: <EMAIL>
 * @Date: 2025/4/18 14:37
 * @Description: 远东香港编制大屏接口
 */

@RestController
@RequestMapping(value = "/far-east-dashboard", produces = "application/json;charset=UTF-8")
@Tag(name = "FarEastDashboard", description = "远东香港编制大屏接口")
@Order(1)
public class FarEastDashboardController {

    @Resource
    private FarEastDashboardFacade farEastDashboardFacade;
    @Resource
    private FarEastDashExportFacade farEastDashExportFacade;

    @GetMapping("/list-all-orgs")
    @Operation(summary = "获取远东香港所有组织机构", description = "获取所有组织机构信息，不分页")
    public ResultList<SimpleOrgVO> listAllFarEastOrgs() {
        return new ResultList<>(farEastDashboardFacade.listAllOrgs());
    }

    @GetMapping("/list-current-user-orgs")
    @Operation(summary = "获取当前用户所属组织机构", description = "获取当前用户所属组织机构信息，不分页")
    public ResultList<SimpleOrgVO> listCurrentUserOrgs(@RequestParam(required = false) Boolean displayAll, @RequestParam(required = false) String tabType) {
        boolean displayAllFlag = displayAll == null || displayAll;
        return new ResultList<>(farEastDashboardFacade.listCurrentUserOrgs(displayAllFlag, tabType));
    }

    @PostMapping("/export-roster")
    @Operation(summary = "远东香港大屏导出花名册", description = "type:1-总人数、2-要素分布、3-人员类型")
    public void exportRoster(HttpServletResponse response, @RequestBody FarEastQO qo,
                             @RequestParam(value = "personType", required = true) String personType,
                             @RequestParam(value = "tabType", defaultValue = "office") String tabType,
                             @RequestParam(value = "type", defaultValue = "1") int type) throws IOException {
        farEastDashExportFacade.exportRosterForFarEastHKDash(response, qo, personType, tabType, type);
    }

    @PostMapping("/roster-distr-org")
    @Operation(summary = "按组织机构查询花名册分布")
    ResultList<RosterDistrOrgVO> getRosterDistrOrg(@RequestBody DashboardQO dashboardQO) {
        return farEastDashboardFacade.listRosterDistrByOrg(dashboardQO);
    }

}
