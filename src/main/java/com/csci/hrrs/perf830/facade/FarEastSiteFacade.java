package com.csci.hrrs.perf830.facade;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultBean;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.facade.hk.StaffCountHelper;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.model.fis.RawRevenueSite;
import com.csci.hrrs.perf830.constant.PersonTypeEnum;
import com.csci.hrrs.perf830.model.StaffOvertime;
import com.csci.hrrs.perf830.service.FarEastRosterDetailService;
import com.csci.hrrs.perf830.service.FarEastSiteService;
import com.csci.hrrs.perf830.service.Revenue830Service;
import com.csci.hrrs.perf830.service.StaffOvertimeService;
import com.csci.hrrs.perf830.util.CalculationUtils;
import com.csci.hrrs.perf830.util.OrgInfoUtil;
import com.csci.hrrs.perf830.util.StaffCountFullUtil;
import com.csci.hrrs.perf830.vo.ChartResult;
import com.csci.hrrs.perf830.vo.ChartVO;
import com.csci.hrrs.perf830.vo.MonthlyRateData;
import com.csci.hrrs.perf830.vo.OverallStatVO;
import com.csci.hrrs.perf830.vo.RateVO;
import com.csci.hrrs.perf830.vo.StaffDistributionVO;
import com.csci.hrrs.perf830.vo.WorkEfficVO;
import com.csci.hrrs.perf830.vo.AnnualLossRateVO;
import com.csci.hrrs.perf830.vo.WorkOvertimeNameListVO;
import com.csci.hrrs.perf830.vo.YearMonthStaffCountInfoVO;
import com.csci.hrrs.perf830.vo.WorkOvertimeVO;
import com.csci.hrrs.perf830.qo.FarEastQO;
import com.csci.hrrs.perf830.qo.HKSiteDashQO;
import com.csci.hrrs.service.ExchangeRateService;
import com.csci.hrrs.service.OrganizationService;
import com.csci.hrrs.service.RawRevenueSiteService;
import com.csci.hrrs.service.RosterHeadService;
import com.csci.hrrs.service.SalaryRecordService;
import com.csci.hrrs.service.ServiceHelper;
import com.csci.hrrs.service.StaffCountFullService;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.util.StaffCountFullHelper;
import com.csci.hrrs.vo.CodeNameCountVO;
import com.csci.hrrs.vo.NameCountVO;
import com.csci.hrrs.vo.RosterHeadIdByYearMonthCountVO;
import com.csci.hrrs.vo.SalaryRecordVO;
import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.csci.hrrs.perf830.util.CommonUtil.buildPersonCountVO;
import static com.csci.hrrs.perf830.util.CommonUtil.isCurrentYearAndMonth;
import static com.csci.hrrs.service.ServiceHelper.checkExist;

/**
 * @ClassName: FarEastSiteFacade
 * @Auther: <EMAIL>
 * @Date: 2025/4/25 9:47
 * @Description: 地盘
 */
@Component
@LogMethod
@Log4j2
public class FarEastSiteFacade {
    private static final String XINGYE_CODE = "00200001";
    @Resource
    private RosterHeadService rosterHeadService;
    @Resource
    private FarEastSiteService farEastSiteService;
    @Resource
    private StaffCountFullService staffCountFullService;
    @Resource
    private SalaryRecordService salaryRecordService;
    @Resource
    private FarEastRosterDetailService farEastRosterDetailService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private ExchangeRateService exchangeRateService;
    @Resource
    private Revenue830Service revenue830Service;
    @Resource
    private StaffOvertimeService staffOvertimeService;


    public OverallStatVO getFarEastHKSitePersonCount(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");

        HeadIdParams params = getHeadIdParams(qo);

        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService
                .selectByDateRange(params.startOfMonth, params.endOfMonth);

        Map<String, Long> personCountMap = getSitePersonCountMap(params.orgCodes,
                rosterHeadIdList.stream().map(RosterHeadIdByYearMonthCountVO::getId).toList());

        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, params.statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);

        long onJobCount = currentHeadId != null ? personCountMap.getOrDefault(currentHeadId, 0L) : 0L;
        long currentPlanCount = calculateStaffCount(params.orgCodes, params.startOfMonth, params.endOfMonth);
        long entryCount = farEastSiteService.getSiteJoin830Count(
                params.orgCodes, currentHeadId, params.startOfMonth, params.endOfMonth);
        long leaveCount = farEastSiteService.getSiteLeaveCount(
                params.orgCodes, currentHeadId, params.startOfMonth, params.endOfMonth);

        return buildPersonCountVO(onJobCount, currentPlanCount, entryCount, leaveCount);
    }

    private long calculateStaffCount(List<String> orgCodes, LocalDate startDate, LocalDate endDate) {
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(orgCodes);
        return StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, startDate, endDate);
    }

    /**
     * 根据花名册时间头分组
     *
     * @param orgCodes
     * @param headIdList
     * @return
     */
    private Map<String, Long> getSitePersonCountMap(List<String> orgCodes, List<String> headIdList) {
        return farEastSiteService.selectSitePersonCount(orgCodes, headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
    }

    /**
     * 根据组织编码分组
     *
     * @param orgCodes
     * @param headIdList
     * @return
     */
    private Map<String, Long> getSitePersonCodeCountMap(List<String> orgCodes, List<String> headIdList) {
        return farEastSiteService.selectSitePersonCountByStatDate(orgCodes, headIdList).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
    }

    public List<RosterDetail> getFarEastHKSitePersonListPage(FarEastQO qo, String personType, int pageNum, int pageSize) {
        checkExist(personType, "查询人员类型不能为空");
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");

        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);

        return switch (personType) {
            case "onJob" -> farEastSiteService.siteOnJobPerson(params.orgCodes, params.headId);
            case "entry" -> farEastSiteService.siteEntryPerson(
                    params.orgCodes, params.headId, params.startOfMonth, params.endOfMonth);
            case "leave" -> farEastSiteService.siteLeavePerson(
                    params.orgCodes, params.headId, params.startOfMonth, params.endOfMonth);
            default -> throw new IllegalArgumentException("无效的人员类型: " + personType);
        };
    }

    /**
     * 内派：员工类别(staffclass3311) = 内派
     * 内地：员工类别(staffclass3311) = 内地 或 （员工类别(staffclass3311) = 境外聘 & 工作地点(workplace) = 国内）
     * 港聘：员工类别(staffclass3311) = 境外聘 & 工作地点(workplace) ！= 国内
     *
     * @param qo
     * @return
     */
    public List<RateVO> listElementDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        // 过滤出is_project = true 的组织
        List<Organization> orgList = organizationService.listByCodes(params.orgCodes);
        List<String> projectOrgCodes = orgList.stream().filter(org -> org.getIsProject())
                .map(Organization::getCode).toList();

        return farEastRosterDetailService.selectElementDistributionList(
                projectOrgCodes, params.headId, params.statDate.getYear(), params.statDate.getMonthValue());
    }

    public List<RateVO> listDimensionDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        return farEastSiteService.listDimensionDistribution(
                params.orgCodes, params.headId, params.statDate.getYear(), params.statDate.getMonthValue());
    }


    public List<StaffDistributionVO> getSiteRankDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        return farEastSiteService.getSiteRankDistribution(params.orgCodes, params.headId);
    }

    public List<StaffDistributionVO> getSiteMajorDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        return farEastSiteService.getSiteMajorDistribution(params.orgCodes, params.headId);
    }

    /**
     * 累计用工人数：每月在职人数累计数
     * 计划编制人数：每月编制数累计数
     * 累计编制使用率：每月累计人数/累计编制数
     * 累计薪酬总额（万港元）：每月薪酬累计数
     * 预算：每月薪酬预算累计数
     * 累计薪酬使用率：每月薪酬数累计/全年度薪酬预算总数
     * 人均产值（万港元/年/人）：每月【营业额】累计/每月累计人数*月份数
     * 工资产值率：每月【营业额】累计/每月薪酬数累计
     *
     * @param qo
     * @return
     */
    public WorkEfficVO getSiteWorkEfficiency(FarEastQO qo, Boolean isAllRevenue) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        WorkEfficVO vo = new WorkEfficVO();
        HeadIdParams params = getHeadIdParams(qo);

        // 获取当前月份和年份信息
        int monthValue = params.statDate.getMonthValue(); // 当前月份(1-12)

        // 2. 累计编制使用率 = 累计用工人数 / 累计编制数 (保留4位小数)
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService
                .selectByDateRange(params.startOfYear, params.statDate);
        List<String> headIdList = rosterHeadIdList.stream().map(RosterHeadIdByYearMonthCountVO::getId).toList();
        Map<String, Long> personCountMap = getSitePersonCountMap(params.orgCodes, headIdList);
        long totalPersonCount = personCountMap.values().stream().reduce(0L, Long::sum);// 累计用工人数:personCountMap全部累加
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(params.orgCodes);
        long totalStaffCount = StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, params.startOfYear, params.endOfYear); // 累计编制数
        BigDecimal staffRatio = totalStaffCount == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(totalPersonCount).divide(BigDecimal.valueOf(totalStaffCount), 4, RoundingMode.HALF_UP);

        // 4. 累计薪酬总额（万港元）= 每月累计薪酬数 / 10,000 (保留4位小数)
        List<SalaryRecordVO> salaryRecords = salaryRecordService.listYearTotalHKSalaryRecord(params.orgCodes, params.statDate);
        List<String> orgCodeList = OrgInfoUtil.subOrgCodeList(organizationService); // 香港设计部、澳门设计部 及其下属部门
        // 工效管控-累计薪酬总额字段，排除掉【内地类型 远东香港设计部、远东澳门设计部（及下属组织）的薪酬总额】
        salaryRecords = salaryRecords.stream().filter(x -> !orgCodeList.contains(x.getOrganizationCode()) || !x.getEmployeeCategory().equals("内地")).toList();

        BigDecimal salaryTotal = salaryRecords.stream().map(SalaryRecordVO::getTotalSalary).reduce(BigDecimal.ZERO, CommonUtils::add);
        BigDecimal cumulativeTotalSalary = salaryTotal.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP); // 转换为万港元

        // 5. 累计薪酬使用率 = 每月薪酬数累计 / 全年度薪酬预算总数累计 (保留4位小数)
        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);
        BigDecimal totalSalaryBudget = StaffCountFullUtil.calcSalaryByDate(staffCountFullList, params.startOfYear, params.endOfYear, rate); // 薪酬预算总数(港元)
        BigDecimal cumulativeSalaryBudget = totalSalaryBudget.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP); // 薪酬预算总数(万港元)
        BigDecimal salaryRatio = totalSalaryBudget.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : salaryTotal.divide(totalSalaryBudget, 4, RoundingMode.HALF_UP); // 港元

        // 6. 人均产值（万港元/年/人）= (每月营业额累计 / 每月累计人数) * 月份数 (保留4位小数)
        List<MonthlyRateData> currentYearRevenueList = getYearRevenue(params.orgCodes, params.startOfYear, params.endOfMonth, isAllRevenue);
        BigDecimal yearTotalRevenue = CalculationUtils.sumMonthlyAmounts(currentYearRevenueList); // 每月营业额累计(万港元)

        BigDecimal personRevenue = totalPersonCount == 0 ? BigDecimal.ZERO : yearTotalRevenue.divide(BigDecimal.valueOf(totalPersonCount), 4, RoundingMode.HALF_UP) // 月人均产值
                .multiply(BigDecimal.valueOf(monthValue));

        // 7. 工资产值率 = 每月营业额累计 / 每月薪酬数累计 (保留4位小数)
        BigDecimal salaryRevenueRatio = cumulativeTotalSalary.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                yearTotalRevenue.divide(cumulativeTotalSalary, 4, RoundingMode.HALF_UP);

        // 年总产值（万港元）
        BigDecimal cumulativeValueYear = StaffCountFullHelper.calcTargetOutputValueYearByDate(staffCountFullList, params.statDate);
        // 全年编制数
        int totalYearStaffCount = StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, params.startOfYear, params.endOfYear);


        // 目标值(产值) 所选年月全年的目标产值汇总/全年编制数汇总 * 12个月
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        if (totalYearStaffCount != 0) {
            targetOutputValue = CalculationUtils.safeDivide(cumulativeValueYear, BigDecimal.valueOf(totalYearStaffCount), 4)
                    .multiply(BigDecimal.valueOf(12));
        }

        // 目标值(工资)  所选年月全年目标产值汇总/全年薪酬预算数汇总
        BigDecimal targetSalaryValue = BigDecimal.ZERO;
        if (cumulativeSalaryBudget.compareTo(BigDecimal.ZERO) != 0) {
            targetSalaryValue = CalculationUtils.safeDivide(cumulativeValueYear, cumulativeSalaryBudget, 4);
        }

        // 设置VO对象
        vo.setTotalPersonCount(totalPersonCount);    // 累计用工人数
        vo.setTotalStaffCount(totalStaffCount);      // 累计编制数
        vo.setCumStaffUseRatio(staffRatio);          // 累计编制使用率
        vo.setCumSalaryTotal(cumulativeTotalSalary);  // 累计薪酬总额(万港元)
        vo.setCumSalaryBudget(cumulativeSalaryBudget);      // 累计薪酬预算总数(万港元)
        vo.setCumSalaryUseRatio(salaryRatio); // 累计薪酬使用率
        vo.setCumAvgOutput(personRevenue);      // 人均产值(万港元/年/人)
        vo.setCumPayOutputRatio(salaryRevenueRatio);         // 工资产值率
        vo.setTargetOutputValue(targetOutputValue);          // 全年目标产值(万港元)
        vo.setTargetSalaryValue(targetSalaryValue);           // 全年目标薪酬(万港元)

        return vo;
    }

    private List<MonthlyRateData> getYearRevenue(List<String> orgCodes, LocalDate start, LocalDate end, boolean isAllRevenue) {
        // TODO: 从Revenue830Service获取年度营业额数据
        if (orgCodes == null || orgCodes.isEmpty()) {
            return new ArrayList<>();
        }
        return revenue830Service.listYearRevenue(orgCodes, start, end, isAllRevenue);
    }

    /**
     * 提取各地盘的数据展示出来，允许点击每列数据进行排序
     * 地盘：取地盘名称（受左侧组织架构树控制）
     * 点击地盘名称，可以跳转至【单地盘】页面，并选中点击的地盘
     * 地盘经理：取后台组织机构管理中，【地盘负责人】字段
     * 以下数据均可分为本年度/全周期两种累计数：
     * 全周期：从每个地盘的开工时间~所选年月，如果所选年月>完工时间，则仅统计开工时间~完工时间
     * 本年度：从所选年月当年初 ~ 所选年月；如果所选年月>完工时间，则仅统计所选年月当年初~完工时间
     * 费用预警
     * 累计用工人数：每月在职人数累计数
     * 累计薪酬总额：每月薪酬总额累计数
     * 累计薪酬使用率：每月薪酬总额累计数/每月薪酬预算（全量）累计数
     * 薪酬预算按全量：即全周期按开工日期~完工日期，本年度按本年初~本年末（完工日期<本年末时，用完工日期）统计
     * 人效排名
     * 人均产值：每月营业额累计数/每月在职人数累计数
     * 工资产值率：每月营业额累计数/每月薪酬累计数
     * <p>
     * 可单击表头对指定列进行升序/降序，并将当前排序的列字体标红加粗
     * 默认：按人均产值降序排列
     * 地盘、地盘经理、累计使用人月、累计薪酬、人均产值、工资产值率：按地盘名称升序/降序
     * 累计薪酬使用率B（自优向差）：优（85%<A<=95%降序）、良（95%<B<=100% 升序，80%<B<=85% 降序）、中（B<80%降序）、差（A>100%升序）
     *
     * @param qo
     * @return
     */
    public List<ChartVO> getFullCharts(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);

        // 过滤不需要排名的父级部门
        List<Organization> organizations = organizationService.listByCodes(params.orgCodes)
                .stream()
                .filter(org -> !"00200011".equals(org.getCode()) && !"50001433".equals(org.getCode()))
                .collect(Collectors.toList());

        // 预加载预算数据
        Map<String, List<StaffCountFull>> budgetMap = staffCountFullService.listByOrgCodes(params.orgCodes)
                .stream()
                .collect(Collectors.groupingBy(StaffCountFull::getOrganizationCode));

        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);

        // 处理每个组织的数据
        List<ChartResult> results = organizations.parallelStream()
                .map(org -> processOrganizationData(org, params, budgetMap, true, qo.getIsAllRevenue(), rate))
                .collect(Collectors.toList());

        return buildFinalResult(results);
    }

    /**
     * 提取各地盘的数据展示出来，允许点击每列数据进行排序
     * 地盘：取地盘名称（受左侧组织架构树控制）
     * 点击地盘名称，可以跳转至【单地盘】页面，并选中点击的地盘
     * 地盘经理：取后台组织机构管理中，【地盘负责人】字段
     * 以下数据均可分为本年度/全周期两种累计数：
     * 全周期：从每个地盘的开工时间~所选年月，如果所选年月>完工时间，则仅统计开工时间~完工时间
     * 本年度：从所选年月当年初 ~ 所选年月；如果所选年月>完工时间，则仅统计所选年月当年初~完工时间
     * 费用预警
     * 累计用工人数：每月在职人数累计数
     * 累计薪酬总额：每月薪酬总额累计数
     * 累计薪酬使用率：每月薪酬总额累计数/每月薪酬预算（全量）累计数
     * 薪酬预算按全量：即全周期按开工日期~完工日期，本年度按本年初~本年末（完工日期<本年末时，用完工日期）统计
     * 人效排名
     * 人均产值：每月营业额累计数/每月平均在职人数累计数
     * 工资产值率：每月营业额累计数/每月薪酬累计数
     * <p>
     * 可单击表头对指定列进行升序/降序，并将当前排序的列字体标红加粗
     * 默认：按人均产值降序排列
     * 地盘、地盘经理、累计使用人月、累计薪酬、人均产值、工资产值率：按地盘名称升序/降序
     * 累计薪酬使用率B（自优向差）：优（85%<A<=95%降序）、良（95%<B<=100% 升序，80%<B<=85% 降序）、中（B<80%降序）、差（A>100%升序）
     *
     * @param qo
     * @return
     */
    public List<ChartVO> getYearCharts(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);

        // 过滤不需要排名的父级部门
        List<Organization> organizations = organizationService.listByCodes(params.orgCodes)
                .stream()
                .filter(org -> !"00200011".equals(org.getCode()) && !"50001433".equals(org.getCode()))
                .toList();

        // 预加载预算数据
        Map<String, List<StaffCountFull>> budgetMap = staffCountFullService.listByOrgCodes(params.orgCodes)
                .stream()
                .collect(Collectors.groupingBy(StaffCountFull::getOrganizationCode));

        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);

        // 处理每个组织的数据
        List<ChartResult> results = organizations.parallelStream()
                .map(org -> processOrganizationData(org, params, budgetMap, false, qo.getIsAllRevenue(), rate))
                .collect(Collectors.toList());

        return buildFinalResult(results);
    }

    private ChartResult processOrganizationData(Organization org, HeadIdParams params, Map<String, List<StaffCountFull>> budgetMap,
                                                boolean isFullCycle, boolean isAllRevenue, Map<String, BigDecimal> rate) {
        // 1. 设置基本信息
        ChartVO chart = new ChartVO();
        chart.setProjectCode(org.getCode());
        chart.setProjectName(org.getName());
        chart.setProjectManager(org.getSiteLeaderName());

        // 2. 计算时间范围 - 使用final变量
        final LocalDate startDate = isFullCycle ?
                Optional.ofNullable(org.getStartDate()).orElse(LocalDate.of(1900, 1, 1)) :
                params.startOfYear;

        final LocalDate endDate = calculateEndDate(org, params, isFullCycle, startDate);
        int monthCount = calculateMonthCount(startDate, endDate);

        // 3. 获取人员数据
        long personCount = farEastSiteService.getSitePersonCount(List.of(org.getCode()), startDate, endDate);
        BigDecimal avgPersonCount = monthCount > 0 ?
                BigDecimal.valueOf(personCount).divide(BigDecimal.valueOf(monthCount), 4, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

        // 4. 累计薪酬总额（转换为万港元）
        List<SalaryRecordVO> salaries = isFullCycle ?
                salaryRecordService.listFullYearTotalSalaryRecord(List.of(org.getCode()), startDate, endDate) :
                salaryRecordService.listYearTotalHKSalaryRecord(List.of(org.getCode()), endDate);

        BigDecimal totalSalary = salaries.stream()
                .map(SalaryRecordVO::getTotalSalary)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);

        // 5. 计算薪酬预算（万港元）
        BigDecimal salaryBudget = StaffCountFullUtil.calcSalaryByDate(
                        budgetMap.getOrDefault(org.getCode(), Collections.emptyList()),
                        startDate, endDate, rate)
                .divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);

        // 6. 计算营业额（万港元）
        List<MonthlyRateData> revenueList = getYearRevenue(List.of(org.getCode()), startDate, endDate, isAllRevenue);
        BigDecimal totalRevenue = CalculationUtils.sumMonthlyAmounts(revenueList);

        // 7. 计算指标
        chart.setSalaryUsageRate(
                salaryBudget.compareTo(BigDecimal.ZERO) != 0 ?
                        totalSalary.divide(salaryBudget, 4, RoundingMode.HALF_UP) :
                        BigDecimal.ZERO
        );

        if (avgPersonCount.compareTo(BigDecimal.ZERO) > 0) {
            chart.setAvgProductValue(totalRevenue.divide(avgPersonCount, 4, RoundingMode.HALF_UP));
        }

        if (totalSalary.compareTo(BigDecimal.ZERO) != 0) {
            chart.setHundredThousandCost(
                    totalRevenue.divide(totalSalary, 4, RoundingMode.HALF_UP)
            );
        }

        // 设置基础数据
        chart.setTotalPersonCount(BigDecimal.valueOf(personCount));
        chart.setTotalSalary(totalSalary);
        chart.setTotalBudget(salaryBudget);

        return new ChartResult(chart, totalSalary, salaryBudget, totalRevenue,
                BigDecimal.valueOf(personCount), avgPersonCount);
    }

    // 提取结束日期计算方法
    private LocalDate calculateEndDate(Organization org, HeadIdParams params, boolean isFullCycle, LocalDate startDate) {
        LocalDate endDate = isFullCycle ?
                Optional.ofNullable(org.getEndDate())
                        .filter(e -> e.isBefore(params.statDate))
                        .orElse(params.endOfMonth) :
                Optional.ofNullable(org.getEndDate())
                        .filter(e -> e.isAfter(startDate) && e.isBefore(params.statDate))
                        .orElse(params.endOfMonth);

        return endDate.isBefore(startDate) ? startDate : endDate;
    }

    private List<ChartVO> buildFinalResult(List<ChartResult> results) {
        // 计算总计
        ChartTotals totals = results.stream()
                .reduce(new ChartTotals(),
                        (t, r) -> new ChartTotals(
                                t.salary.add(r.cumulativeTotalSalary()),
                                t.budget.add(r.totalSalaryBudget()),
                                t.revenue.add(r.totalRevenue()),
                                t.personCount.add(r.totalPersonCount()),
                                t.avgPersonCount.add(r.totalAvgPersonCount())
                        ),
                        (t1, t2) -> new ChartTotals(
                                t1.salary.add(t2.salary),
                                t1.budget.add(t2.budget),
                                t1.revenue.add(t2.revenue),
                                t1.personCount.add(t2.personCount),
                                t1.avgPersonCount.add(t2.avgPersonCount)
                        ));

        // 构建结果列表
        List<ChartVO> charts = results.stream()
                .map(ChartResult::chart)
                .sorted(Comparator.comparing(
                        ChartVO::getAvgProductValue,
                        Comparator.nullsLast(Comparator.reverseOrder())  // 按人均产值降序排列
                ))
                .collect(Collectors.toList());

        // 3. 添加总计行
        ChartVO total = new ChartVO();
        total.setProjectName("总计");
        total.setTotalPersonCount(totals.personCount);
        total.setTotalSalary(totals.salary);
        total.setTotalBudget(totals.budget);
        total.setSalaryUsageRate(totals.budget.compareTo(BigDecimal.ZERO) != 0 ?
                totals.salary.divide(totals.budget, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);

        BigDecimal revenue = totals.revenue;
        if (totals.personCount.compareTo(BigDecimal.ZERO) > 0) {
            total.setAvgProductValue(revenue.divide(totals.avgPersonCount, 4, RoundingMode.HALF_UP));
        }

        if (totals.salary.compareTo(BigDecimal.ZERO) != 0) {
            total.setHundredThousandCost(revenue.divide(totals.salary, 4, RoundingMode.HALF_UP));
        }

        charts.add(total);
        return charts;
    }

    private int calculateMonthCount(LocalDate start, LocalDate end) {
        return (int) ChronoUnit.MONTHS.between(
                start.withDayOfMonth(1),
                end.withDayOfMonth(1)
        ) + 1; // +1 包含起始月
    }

    /**
     * 主动离职定义：jobchange里离职原因为主动辞职、员工提出-劳动合同期满不续签两类
     * <p>
     * 年度流失率排名:
     * 地盘：总体流失率降序
     * 遗憾流失率：本年度截止所选年月，上年度KPI=B+、A的本年度离职人数/本年度平均人数  --》修改成 上年度KPI=B+、A的当年当月离职人数/当年当月平均人数
     * 总体流失率：本年度截止所选年月，本年度离职人数/本年度平均人数  --》修改成 当年当月离职人数/当年当月平均人数
     * 合计数
     * 遗憾流失率：本年度截止所选年月，所选地盘上年度KPI=B+、A的本年度离职人数之和/所选地盘本年度平均人数之和
     * 总体流失率：本年度截止所选年月，所选地盘本年度离职人数之和/所选地盘本年度平均人数之和
     *
     * @param qo
     * @return
     */
    public List<AnnualLossRateVO> getAnnualLossRate(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);

        // 1. 获取统计时间范围（本年度）
        LocalDate yearStart = params.startOfYear; // 当前年份的第一天
        LocalDate yearEnd = params.endOfMonth; // 当前月份的最后一天

        // 2. 获取组织信息
        List<Organization> orgList = organizationService.listByCodes(qo.getOrganizationCodeList());
        // 过滤出地盘数据，即isProject=1的数据
        List<Organization> farEastSiteList = orgList.stream().filter(Organization::getIsProject).toList();


        // 本年度headIds
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService.selectByDateRange(yearStart, yearEnd);
        List<String> headIds = rosterHeadIdList.stream().map(RosterHeadIdByYearMonthCountVO::getId).toList();

        // 3. 本年度在职人数
        List<NameCountVO> avgPersonCountList = farEastSiteService.selectSitePersonToNameCount(qo.getOrganizationCodeList(), headIds);
        // 年度平均人数：年度在职人数/月份
        Map<String, BigDecimal> mapAvgOrgPersonCount = avgPersonCountList.stream().collect(Collectors.toMap(NameCountVO::getName, nameCountVO ->
                DemoUtils.divide(BigDecimal.valueOf(nameCountVO.getCount()), BigDecimal.valueOf(yearEnd.getMonthValue()), 4)));

        // 4. 查询本年度主动离职人数
        List<NameCountVO> totalDismissList = farEastSiteService.selectSiteLeaveToNameCount(qo.getOrganizationCodeList(), headIds, yearStart, yearEnd);
        Map<String, BigDecimal> totalDismissMap = totalDismissList.stream()
                .collect(Collectors.toMap(NameCountVO::getName, vo -> BigDecimal.valueOf(vo.getCount())));

        // 5. 查询本年度核心离职人数
        List<NameCountVO> coreDismissList = farEastSiteService.selectSiteDismissToNameCount(qo.getOrganizationCodeList(), yearStart, yearEnd);
        Map<String, BigDecimal> coreDismissMap = coreDismissList.stream().collect(Collectors.toMap(NameCountVO::getName, vo -> BigDecimal.valueOf(vo.getCount())));

        // 6.查询本年度绩效A/B+，主动离职人数
        List<NameCountVO> performanceList = farEastSiteService.selectSitePerformanceToNameCount(qo.getOrganizationCodeList(), yearStart, yearEnd);
        Map<String, BigDecimal> performanceMap = performanceList.stream().collect(Collectors.toMap(NameCountVO::getName, vo -> BigDecimal.valueOf(vo.getCount())));

        // 7. 计算各组织流失率
        List<AnnualLossRateVO> result = new ArrayList<>();

        // 计算总计数据
        BigDecimal totalDismissCount = BigDecimal.valueOf(totalDismissList.stream().mapToLong(NameCountVO::getCount).sum());
        BigDecimal coreDismissCount = BigDecimal.valueOf(coreDismissList.stream().mapToLong(NameCountVO::getCount).sum());
        BigDecimal avgOnJobCountTotal = avgPersonCountList.stream().map(NameCountVO::getCount).map(BigDecimal::valueOf)
                .reduce(BigDecimal::add).map(count -> count.divide(BigDecimal.valueOf(yearEnd.getMonthValue()), 4, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        BigDecimal performanceCountTotal = BigDecimal.valueOf(performanceList.stream().mapToLong(NameCountVO::getCount).sum());

        for (Organization org : farEastSiteList) {
            AnnualLossRateVO vo = new AnnualLossRateVO();
            vo.setName(org.getName());
            vo.setCode(org.getCode());

            // 年度平均人数
            BigDecimal avgCount = mapAvgOrgPersonCount.getOrDefault(org.getName(), BigDecimal.ZERO);
            // 年度离职人数
            BigDecimal dismissCount = totalDismissMap.getOrDefault(org.getName(), BigDecimal.ZERO);
            // 年度核心离职人数
            BigDecimal coreCount = coreDismissMap.getOrDefault(org.getName(), BigDecimal.ZERO);
            // 年度绩效A/B+
            BigDecimal performanceCount = performanceMap.getOrDefault(org.getName(), BigDecimal.ZERO);

            // 计算流失率
            vo.setTotalLossRate(DemoUtils.divide(dismissCount, avgCount, 4)); // 总体流失率= 累计离职人数/平均在职人数
            vo.setCoreLossRate(DemoUtils.divide(coreCount, avgCount, 4)); // 核心流失率= 核心离职人数/平均在职人数
            vo.setRegretLossRate(DemoUtils.divide(performanceCount, avgCount, 4)); // 遗憾流失率= 绩效A/B+离职人数/平均在职人数

            result.add(vo);
        }
        // 按总体流失率降序
        result.sort(Comparator.comparing(AnnualLossRateVO::getTotalLossRate, Comparator.nullsLast(Comparator.reverseOrder())));

        // 7. 添加总计行
        AnnualLossRateVO totalVo = new AnnualLossRateVO();
        totalVo.setName("总计");
        // 总体流失率= 本年度离职人数之和/本年度平均人数
        totalVo.setTotalLossRate(DemoUtils.divide(totalDismissCount, avgOnJobCountTotal, 4));
        // 核心流失率= 核心离职人数之和/本年度平均人数
        totalVo.setCoreLossRate(DemoUtils.divide(coreDismissCount, avgOnJobCountTotal, 4));
        // 遗憾流失率= 绩效A/B+人数之和/本年度平均人数
        totalVo.setRegretLossRate(DemoUtils.divide(performanceCountTotal, avgOnJobCountTotal, 4));
        result.add(totalVo);
        return result;
    }

    public List<YearMonthStaffCountInfoVO> listLast6MonthStaffCount(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        // 前六个月
/*        LocalDate startDate = params.statDate.minusMonths(5).withDayOfMonth(1);
        LocalDate endDate = params.endDate; // 当前月份的最后一天*/

        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(params.orgCodes);
        LocalDate[] dateRange = CollectionUtils.isEmpty(staffCountFullList) ? null : StaffCountHelper.getStartDateAndEndDateForList(staffCountFullList);
        LocalDate startStaffCountDate = dateRange != null ? dateRange[0] : null;
        LocalDate endStaffCountDate = dateRange != null ? dateRange[1] : null;
        // 获取地盘员工类型数据
        List<CodeNameCountVO> siteEmpTypeList = farEastSiteService.selectSiteEmpType(qo.getOrganizationCodeList(), null, null); //
        LocalDate startSiteEmpTypeDate = siteEmpTypeList.stream().map(codeNameCountVO -> LocalDate.parse(codeNameCountVO.getCode() + "-01"))
                .min(LocalDate::compareTo).orElse(null);


        // 确定 startDate
        LocalDate startDate = Stream.of(startStaffCountDate, startSiteEmpTypeDate)
                .filter(Objects::nonNull)
                .min(LocalDate::compareTo)
                .orElse(null);

        // 确定 endDate
        LocalDate endDate = Stream.of(params.statDate, endStaffCountDate)
                .filter(Objects::nonNull)
                .max(LocalDate::compareTo)
                .map(date -> date.plusYears(2)) // 对找到的最大日期加两年
                .orElse(null); // 如果找不到最大日期，返回 null

        // 健壮性检查
        if (startDate == null || endDate == null) {
            log.warn("无法确定有效的时间范围");
            return Collections.emptyList();
        }
        if (startDate.isAfter(endDate)) {
            log.warn("无效的日期范围: {} 到 {}", startDate, endDate);
            return Collections.emptyList();
        }

        List<YearMonthStaffCountInfoVO> lstResult = new ArrayList<>();
        for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusMonths(1)) {
            // 组成 yyyy-MM的格式
            String yearMonth = DateTimeFormatter.ofPattern("yyyy-MM").format(date);

            YearMonthStaffCountInfoVO yearMonthStaffCountInfoVO = new YearMonthStaffCountInfoVO();
            yearMonthStaffCountInfoVO.setYearMonth(yearMonth);
            // 人数
            yearMonthStaffCountInfoVO.setValue1(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "地盘核心层".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue2(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "地盘基层".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue3(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "其他".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue4(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "未归类".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            // 内派和内地员工人数
            yearMonthStaffCountInfoVO.setValue5(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "内派".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            yearMonthStaffCountInfoVO.setValue6(siteEmpTypeList.stream().filter(codeNameCountVO -> yearMonth.equals(codeNameCountVO.getCode()) && "内地".equals(codeNameCountVO.getName())).map(CodeNameCountVO::getCount).findFirst().orElse(0L));
            // 总编制数
            yearMonthStaffCountInfoVO.setValue8(StaffCountFullHelper.calcStaffCountByYearMonth(staffCountFullList, date));

            // 编制人数
            yearMonthStaffCountInfoVO.setValue1StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.02"));
            yearMonthStaffCountInfoVO.setValue2StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.03"));
            yearMonthStaffCountInfoVO.setValue3StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.04"));
            yearMonthStaffCountInfoVO.setValue4StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.07")); // TODO：未归类编码待确认
            // 内派和内地编制数据
            yearMonthStaffCountInfoVO.setValue5StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.01"));
            yearMonthStaffCountInfoVO.setValue6StaffCount(StaffCountFullHelper.calcStaffCountByDateAndCode(staffCountFullList, date, "BZ.830.06"));

            // 合计
            yearMonthStaffCountInfoVO.setValue7(yearMonthStaffCountInfoVO.getValue1() + yearMonthStaffCountInfoVO.getValue2() + yearMonthStaffCountInfoVO.getValue3() + yearMonthStaffCountInfoVO.getValue4()
                    + yearMonthStaffCountInfoVO.getValue5() + yearMonthStaffCountInfoVO.getValue6());
            yearMonthStaffCountInfoVO.setValue7StaffCount(yearMonthStaffCountInfoVO.getValue1StaffCount() + yearMonthStaffCountInfoVO.getValue2StaffCount() + yearMonthStaffCountInfoVO.getValue3StaffCount() +
                    yearMonthStaffCountInfoVO.getValue4StaffCount() + yearMonthStaffCountInfoVO.getValue5StaffCount() + yearMonthStaffCountInfoVO.getValue6StaffCount());

            lstResult.add(yearMonthStaffCountInfoVO);
        }
        return lstResult;
    }

    public WorkOvertimeVO getWorkOvertimeStat(FarEastQO qo) {
        // 🔴 重点：加班表code问题 TODO
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        String yearMonth = params.statDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<StaffOvertime> records = staffOvertimeService.findDistinctByOrganizationAndMonth(params.orgCodes, yearMonth);
        // 4. 处理数据并排序
        List<WorkOvertimeNameListVO> resultList = records.stream()
                .map(r -> {
                    String formattedHours = formatHours(r.getMonthlyOvertimeHours());
                    if (formattedHours == null || formattedHours.isEmpty() || "0.0".equals(formattedHours)) {
                        return null;
                    }
                    WorkOvertimeNameListVO vo = new WorkOvertimeNameListVO();
                    vo.setSeq(r.getOvertimeRank().intValue());
                    vo.setName(r.getStaffName());
                    vo.setOvertimeHour(formattedHours);
                    return vo;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        // 5. 计算统计值
        OvertimeSummary summary = calculateSummary(records);

        // 6. 构建返回对象
        return WorkOvertimeVO.forModel(summary.avgHours, summary.totalHours, resultList);
    }

    private String formatHours(BigDecimal hours) {
        return hours.setScale(1, RoundingMode.HALF_UP).toString();
    }

    private OvertimeSummary calculateSummary(List<StaffOvertime> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new OvertimeSummary(0, 0);
        }

        BigDecimal total = records.stream()
                .map(StaffOvertime::getMonthlyOvertimeHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        int avg = total.divide(
                new BigDecimal(records.size()),
                0, RoundingMode.HALF_UP).intValue();

        return new OvertimeSummary(total.intValue(), avg);
    }


    public WorkEfficVO getFullCycleSiteStaffOverview(HKSiteDashQO qo) {
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");
        ServiceHelper.checkExist(qo.getStatDate(), "统计日期不能为空");
        HeadIdParams params = getHeadIdParams(qo);

        Organization organization = organizationService.getByCode(params.orgCodes.get(0));
        Objects.requireNonNull(organization, "组织不存在");
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(params.orgCodes);
        List<StaffCountFull> salaryBudgetList = staffCountFullService.listByOrgCodes(params.orgCodes);


        WorkEfficVO vo = new WorkEfficVO();
        vo.setSelectedWorkTime(calcCurrentMonth(organization, params.statDate));
        vo.setTotalWorkTime(calcTotalMonth(organization));
        vo.setCumTimeRatio(calcCumulativeTimeRatio(organization, params.statDate));

        // 从每个地盘的开工时间~所选年月，如果所选年月>完工时间，则仅统计开工时间~完工时间
        LocalDate startDate = organization.getStatDate();
        LocalDate endDate = params.statDate;

        // 如果开工日期不存在，则使用1900-01-01
        if (startDate == null) {
            startDate = LocalDate.of(1900, 1, 1);
        }

        // 如果完工日期存在且早于选择的日期，则使用完工日期
        if (organization.getEndDate() != null && organization.getEndDate().isBefore(endDate)) {
            endDate = organization.getEndDate();
        }

        // 完工日期不存在，则使用9999-12-31
        if (organization.getEndDate() == null) {
            endDate = LocalDate.of(9999, 12, 31);
        }

        // 全周期累计用工人数
        long totalPersonCount = farEastSiteService.getSitePersonCount(params.orgCodes, startDate, endDate);
        vo.setTotalPersonCount(totalPersonCount);

        long totalStaffCount = StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, startDate, endDate); // 累计编制数
        BigDecimal staffRatio = totalStaffCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(totalPersonCount).divide(BigDecimal.valueOf(totalStaffCount), 4, RoundingMode.HALF_UP);
        vo.setTotalStaffCount(totalStaffCount);
        vo.setCumStaffUseRatio(staffRatio);

        // 全周期累计薪酬总额（万港元）= 每月累计薪酬数 / 10,000 (保留4位小数)
        List<SalaryRecordVO> salaryRecords = salaryRecordService.listFullYearTotalSalaryRecord(params.orgCodes, startDate, endDate);
        List<String> orgCodeList = OrgInfoUtil.subOrgCodeList(organizationService); // 香港设计部、澳门设计部 及其下属部门
        // 工效管控-累计薪酬总额字段，排除掉【内地类型 远东香港设计部、远东澳门设计部（及下属组织）的薪酬总额】
        salaryRecords = salaryRecords.stream().filter(x -> !orgCodeList.contains(x.getOrganizationCode()) || !x.getEmployeeCategory().equals("内地")).toList();

        BigDecimal salaryTotal = salaryRecords.stream().map(SalaryRecordVO::getTotalSalary).reduce(BigDecimal.ZERO, CommonUtils::add);
        BigDecimal cumulativeTotalSalary = salaryTotal.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP); // 转换为万港元
        vo.setCumSalaryTotal(cumulativeTotalSalary);

        // 全周期累计薪酬使用率 = 每月薪酬数累计 / 薪酬预算总数累计 (保留4位小数)
        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);
        BigDecimal totalSalaryBudget = StaffCountFullUtil.calcSalaryByDate(salaryBudgetList, startDate, endDate, rate); // 薪酬预算总数(港元)
        BigDecimal budgetTotal = totalSalaryBudget.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);// 转换为万港元
        BigDecimal salaryRatio = totalSalaryBudget.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : salaryTotal.divide(totalSalaryBudget, 4, RoundingMode.HALF_UP);
        vo.setCumSalaryBudget(budgetTotal);
        vo.setCumSalaryUseRatio(salaryRatio);

        // 人均产值（万港元/年/人）= (每月营业额累计 / 每月累计人数) * 月份数 (保留4位小数)
        List<MonthlyRateData> currentYearRevenueList = getYearRevenue(params.orgCodes, startDate, endDate, qo.getIsAllRevenue());
        BigDecimal yearTotalRevenue = CalculationUtils.sumMonthlyAmounts(currentYearRevenueList); // 每月营业额累计(万港元)
        // 获取当前月份和年份信息
        int monthValue = params.statDate.getMonthValue(); // 当前月份(1-12)
        BigDecimal personRevenue = totalPersonCount == 0 ? BigDecimal.ZERO : yearTotalRevenue.divide(BigDecimal.valueOf(totalPersonCount), 4, RoundingMode.HALF_UP) // 月人均产值
                .multiply(BigDecimal.valueOf(monthValue));
        vo.setCumAvgOutput(personRevenue);

        // 工资产值率 = 每月营业额累计 / (每月薪酬数累计) (保留4位小数)
        BigDecimal salaryRevenueRatio = cumulativeTotalSalary.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                yearTotalRevenue.divide(cumulativeTotalSalary, 4, RoundingMode.HALF_UP);
        vo.setCumPayOutputRatio(salaryRevenueRatio);

        return vo;
    }

    public WorkEfficVO getYearlySiteStaffOverview(HKSiteDashQO qo) {
        ServiceHelper.checkExist(qo.getOrganizationCodeList(), "组织代码列表不能为空");
        ServiceHelper.checkExist(qo.getStatDate(), "统计日期不能为空");
        HeadIdParams params = getHeadIdParams(qo);

        Organization organization = organizationService.getByCode(params.orgCodes.get(0));
        Objects.requireNonNull(organization, "组织不存在");
        List<StaffCountFull> salaryBudgetList = staffCountFullService.listByOrgCodes(params.orgCodes);
        List<SalaryRecordVO> salaryRecords = salaryRecordService.listYearTotalHKSalaryRecord(params.orgCodes, params.statDate);

        WorkEfficVO vo = new WorkEfficVO();
        vo.setSelectedWorkTime(calcCurrentMonth(organization, params.statDate));
        vo.setTotalWorkTime(calcTotalMonth(organization));
        vo.setCumTimeRatio(calcCumulativeTimeRatio(organization, params.statDate));

        // 2. 计算本年度时间范围: 从所选年月当年初 ~ 所选年月；如果所选年月>完工时间，则仅统计所选年月当年初~完工时间
        LocalDate yearStart = params.startOfYear; // 当前年份的第一天
        LocalDate yearEnd = params.startOfMonth; // 所选年月

        // 添加完工日期的完整范围校验
        if (organization.getEndDate() != null) {
            // 情况1：完工日期在统计年份范围内（yearStart < endDate < yearEnd）
            if (organization.getEndDate().isAfter(yearStart) && organization.getEndDate().isBefore(yearEnd)) {
                yearEnd = organization.getEndDate();
            }
            // 情况2：完工日期早于统计年份（endDate <= yearStart）
            else if (organization.getEndDate().isBefore(yearStart) || organization.getEndDate().isEqual(yearStart)) {
                // 如果完工日期早于或等于年初，则无需统计（时间范围无效）
                throw new ServiceException("项目完工日期[" + organization.getEndDate() + "]早于统计起始日期[" + yearStart + "]");
            }
            // 情况3：完工日期晚于或等于yearEnd（endDate >= yearEnd），则保持原yearEnd不变
        }

        // 本年度累计用工人数
        long totalPersonCount = farEastSiteService.getSitePersonCount(params.orgCodes, yearStart, yearEnd);
        vo.setTotalPersonCount(totalPersonCount);
        // 本年度累计编制数
        long totalStaffCount = calculateStaffCount(params.orgCodes, yearStart, yearEnd); // 累计编制数
        BigDecimal staffRatio = totalStaffCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(totalPersonCount).divide(BigDecimal.valueOf(totalStaffCount), 4, RoundingMode.HALF_UP);
        vo.setTotalStaffCount(totalStaffCount);
        vo.setCumStaffUseRatio(staffRatio);

        // 本年度累计薪酬总额（万港元）= 每月累计薪酬数 / 10,000 (保留4位小数)
        List<String> orgCodeList = OrgInfoUtil.subOrgCodeList(organizationService); // 香港设计部、澳门设计部 及其下属部门
        // 工效管控-累计薪酬总额字段，排除掉【内地类型 远东香港设计部、远东澳门设计部（及下属组织）的薪酬总额】
        salaryRecords = salaryRecords.stream().filter(x -> !orgCodeList.contains(x.getOrganizationCode()) || !x.getEmployeeCategory().equals("内地")).toList();
        BigDecimal salaryTotal = salaryRecords.stream().map(SalaryRecordVO::getTotalSalary).reduce(BigDecimal.ZERO, CommonUtils::add);
        BigDecimal cumulativeTotalSalary = salaryTotal.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP); // 转换为万港元
        vo.setCumSalaryTotal(cumulativeTotalSalary);

        // 本年度累计薪酬使用率 = 每月薪酬数累计 / 薪酬预算总数累计 (保留4位小数)
        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);
        BigDecimal totalSalaryBudget = StaffCountFullUtil.calcSalaryByDate(salaryBudgetList, yearStart, yearEnd, rate); // 薪酬预算总数(港元)
        BigDecimal budgetTotal = totalSalaryBudget.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);// 转换为万港元
        BigDecimal salaryRatio = totalSalaryBudget.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : salaryTotal.divide(totalSalaryBudget, 4, RoundingMode.HALF_UP);
        vo.setCumSalaryBudget(budgetTotal);
        vo.setCumSalaryUseRatio(salaryRatio);

        // 人均产值（万港元/年/人）= (每月营业额累计 / 每月累计人数) * 月份数 (保留4位小数)
        List<MonthlyRateData> currentYearRevenueList = getYearRevenue(params.orgCodes, yearStart, yearEnd, qo.getIsAllRevenue());
        BigDecimal yearTotalRevenue = CalculationUtils.sumMonthlyAmounts(currentYearRevenueList); // 每月营业额累计(万港元)
        // 获取当前月份和年份信息
        int monthValue = params.statDate.getMonthValue(); // 当前月份(1-12)
        BigDecimal personRevenue = totalPersonCount == 0 ? BigDecimal.ZERO : yearTotalRevenue.divide(BigDecimal.valueOf(totalPersonCount), 4, RoundingMode.HALF_UP) // 月人均产值
                .multiply(BigDecimal.valueOf(monthValue)); // 转换为万港元
        vo.setCumAvgOutput(personRevenue);

        // 工资产值率 = 每月营业额累计 / (每月薪酬数累计) (保留4位小数)
        BigDecimal salaryRevenueRatio = cumulativeTotalSalary.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                yearTotalRevenue.divide(cumulativeTotalSalary, 4, RoundingMode.HALF_UP);
        vo.setCumPayOutputRatio(salaryRevenueRatio);

        return vo;
    }

    /**
     * 计算累计时间比例：当前工期月/全周期月份数
     *
     * @return
     */
    private BigDecimal calcCumulativeTimeRatio(Organization organization, LocalDate date) {
        long totalMonths = calcTotalMonth(organization);
        long currentMonths = calcCurrentMonth(organization, date);
        if (currentMonths == -1) {
            return BigDecimal.ONE;
        }
        return DemoUtils.divide(currentMonths, totalMonths);
    }

    public static long calcTotalMonth(Organization organization) {
        if (organization.getStartDate() != null && organization.getEndDate() != null) {
            return ChronoUnit.MONTHS.between(organization.getStartDate(), organization.getEndDate().plusMonths(1));
        }
        return 0;
    }

    public static long calcCurrentMonth(Organization organization, LocalDate statDate) {
        if (organization.getStartDate() != null && organization.getEndDate() != null) {
            if (statDate.isBefore(organization.getEndDate())) {
                return ChronoUnit.MONTHS.between(organization.getStartDate(), statDate.plusMonths(1));
            } else {
                return ChronoUnit.MONTHS.between(organization.getStartDate(), organization.getEndDate().plusMonths(1));
            }
        }
        return 0;
    }

    public List<RosterDetail> getElementDistributionRoster(FarEastQO qo, String projectType, int pageNum, int pageSize) {
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastRosterDetailService.selectFarEastHKRosterTypeList(params.orgCodes, params.headId, projectType);
    }

    public List<RosterDetail> getDimensionDistributionRoster(FarEastQO qo, String projectType, int pageNum, int pageSize) {
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastSiteService.selectDimensionDistributionRoster(params.orgCodes, params.headId, projectType);
    }

    public List<RosterDetail> getSiteRankRoster(FarEastQO qo, String rankType, int pageNum, int pageSize) {
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastSiteService.selectSiteRankRoster(params.orgCodes, params.headId, rankType);
    }

    public List<RosterDetail> getSiteMajorRoster(FarEastQO qo, String majorType, int pageNum, int pageSize) {
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastSiteService.selectSiteMajorRoster(params.orgCodes, params.headId, majorType);
    }

    public List<RosterDetail> getAnnualLossRateRoster(FarEastQO qo, String personType, int pageNum, int pageSize) {
        checkExist(personType, "查询人员类型不能为空");
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        // 1. 参数校验
        PersonTypeEnum staffType = PersonTypeEnum.fromCode(personType);
        boolean isRegretLoss = "regret".equals(staffType.getCode());

        HeadIdParams params = getHeadIdParams(qo);
        LocalDate yearStart = params.startOfYear;
        LocalDate yearEnd = params.endOfMonth;

        PageHelper.startPage(pageNum, pageSize);

        return farEastSiteService.selectAnnualLossRateRoster(params.orgCodes, isRegretLoss, yearStart, yearEnd);
    }

    private static class HeadIdParams {
        final LocalDate statDate;
        final LocalDate startOfMonth;
        final LocalDate endOfMonth;
        final LocalDate startOfYear;
        final LocalDate endOfYear;
        final String headId;
        final List<String> orgCodes;

        HeadIdParams(LocalDate statDate, LocalDate startOfMonth, LocalDate endOfMonth,
                     LocalDate startOfYear, LocalDate endOfYear,
                     String headId, List<String> orgCodes) {
            this.statDate = statDate;
            this.startOfMonth = startOfMonth;
            this.endOfMonth = endOfMonth;
            this.startOfYear = startOfYear;
            this.endOfYear = endOfYear;
            this.headId = headId;
            this.orgCodes = orgCodes;
        }
    }

    private HeadIdParams getHeadIdParams(FarEastQO qo) {
        LocalDate statDate = Optional.ofNullable(qo.getStatDate()).orElseGet(LocalDate::now);

        // 使用标准库方法获取日期
        LocalDate startOfYear = statDate.with(TemporalAdjusters.firstDayOfYear()); // 当前年份的第一天
        LocalDate startOfMonth = statDate.withDayOfMonth(1); // 当前月份的第一天
        LocalDate endOfMonth = statDate.with(TemporalAdjusters.lastDayOfMonth()); // 当前月份的最后一天
        LocalDate endOfYear = startOfYear.with(TemporalAdjusters.lastDayOfYear()); // 当前年份的最后一天

        String headId = farEastSiteService.getHeadId(statDate.getYear(), statDate.getMonthValue());

        return new HeadIdParams(
                statDate, // 当前选择日期
                startOfMonth, // 当前月的第一天
                endOfMonth, // 当前月的最后一天
                startOfYear, // 当前年第一天
                endOfYear, // 当前年最后一天
                headId,
                qo.getOrganizationCodeList()
        );
    }

    // 辅助记录类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ChartTotals {
        BigDecimal salary = BigDecimal.ZERO;
        BigDecimal budget = BigDecimal.ZERO;
        BigDecimal revenue = BigDecimal.ZERO;
        BigDecimal personCount = BigDecimal.ZERO;
        BigDecimal avgPersonCount = BigDecimal.ZERO;
    }

    @Data
    private static class OvertimeSummary {
        final int totalHours;
        final int avgHours;

        OvertimeSummary(int totalHours, int avgHours) {
            this.totalHours = totalHours;
            this.avgHours = avgHours;
        }
    }
}
