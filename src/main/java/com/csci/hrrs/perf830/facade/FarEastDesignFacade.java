package com.csci.hrrs.perf830.facade;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.perf830.entity.EfficiencyData;
import com.csci.hrrs.perf830.mapper.FarEastRosterDetailMapper;
import com.csci.hrrs.perf830.service.FarEastDesignService;
import com.csci.hrrs.perf830.service.FarEastEfficiencyService;
import com.csci.hrrs.perf830.service.FarEastSalaryService;
import com.csci.hrrs.perf830.service.Revenue830Service;
import com.csci.hrrs.perf830.util.CalculationUtils;
import com.csci.hrrs.perf830.util.OrgInfoUtil;
import com.csci.hrrs.perf830.util.StaffCountFullUtil;
import com.csci.hrrs.perf830.vo.MonthlyData;
import com.csci.hrrs.perf830.vo.MonthlyRateData;
import com.csci.hrrs.perf830.vo.OutValueVO;
import com.csci.hrrs.perf830.vo.OverallStatVO;
import com.csci.hrrs.perf830.vo.SalaryTotalVO;
import com.csci.hrrs.perf830.vo.StaffDistributionVO;
import com.csci.hrrs.perf830.vo.WorkEfficVO;
import com.csci.hrrs.perf830.qo.FarEastQO;
import com.csci.hrrs.service.ExchangeRateService;
import com.csci.hrrs.service.OrganizationService;
import com.csci.hrrs.service.RosterHeadService;
import com.csci.hrrs.service.SalaryRecordService;
import com.csci.hrrs.service.StaffCountFullService;
import com.csci.hrrs.util.StaffCountFullHelper;
import com.csci.hrrs.vo.NameCountVO;
import com.csci.hrrs.vo.RosterHeadIdByYearMonthCountVO;
import com.csci.hrrs.vo.SalaryRecordVO;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.csci.hrrs.perf830.util.CommonUtil.buildPersonCountVO;
import static com.csci.hrrs.perf830.util.CommonUtil.isCurrentYearAndMonth;
import static com.csci.hrrs.service.ServiceHelper.checkExist;

/**
 * @ClassName: FarEastDesignFacade
 * @Auther: <EMAIL>
 * @Date: 2025/4/25 9:47
 * @Description: 设计人员
 */
@Service
@LogMethod
@DS(DatasourceContextEnum.HRRS)
public class FarEastDesignFacade extends ServiceImpl<FarEastRosterDetailMapper, RosterDetail> {
    private static final String HK_DESIGN_CODE = "00200013"; // 香港设计部
    private static final String MACAO_DESIGN_CODE = "10200004"; // 澳门设计部
    private static final String FAR_EAST_HK_NO = "00200002";
    private static final String XINGYE_CODE = "00200001";
    @Resource
    private RosterHeadService rosterHeadService;
    @Resource
    private FarEastDesignService farEastDesignService;
    @Resource
    private StaffCountFullService staffCountFullService;
    @Resource
    private SalaryRecordService salaryRecordService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private FarEastEfficiencyService farEastEfficiencyService;
    @Resource
    private ExchangeRateService exchangeRateService;
    @Resource
    private FarEastSalaryService farEastSalaryService;

    public OverallStatVO getDesignPersonCount(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");

        HeadIdParams params = getHeadIdParams(qo);

        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService
                .selectByDateRange(params.startOfMonth, params.endOfMonth);

        Map<String, Long> personCountMap = getDesignPersonCountMap(params.orgCodes,
                rosterHeadIdList.stream().map(RosterHeadIdByYearMonthCountVO::getId).toList());

        String currentHeadId = rosterHeadIdList.stream()
                .filter(x -> isCurrentYearAndMonth(x, params.statDate))
                .findFirst()
                .map(RosterHeadIdByYearMonthCountVO::getId)
                .orElse(null);

        long onJobCount = currentHeadId != null ? personCountMap.getOrDefault(currentHeadId, 0L) : 0L;
        long currentPlanCount = calculateStaffCountOfMonth(params.orgCodes, params.statDate);
        long entryCount = farEastDesignService.getDesignJoin830Count(
                params.orgCodes, currentHeadId, params.startOfMonth, params.endOfMonth);
        long leaveCount = farEastDesignService.getDesignLeaveCount(
                params.orgCodes, currentHeadId, params.startOfMonth, params.endOfMonth);

        return buildPersonCountVO(onJobCount, currentPlanCount, entryCount, leaveCount);
    }

    private long calculateStaffCountOfMonth(List<String> orgCodes, LocalDate statDate) {
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(orgCodes);
        return StaffCountFullHelper.calcStaffCountOfMonth(staffCountFullList, YearMonth.from(statDate));
    }

    private long calculateStaffCountYear(List<String> orgCodes, LocalDate startDate, LocalDate endDate) {
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(orgCodes);
        return StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, startDate, endDate);
    }

    private Map<String, Long> getDesignPersonCountMap(List<String> orgCodes, List<String> headIds) {
        return farEastDesignService.selectDesignPersonCount(orgCodes, headIds).stream()
                .filter(nameCountVO -> nameCountVO.getCount() > 0)
                .collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
    }

    public List<RosterDetail> getDesignPersonListPage(FarEastQO qo, String personType, int pageNum, int pageSize) {
        checkExist(personType, "查询人员类型不能为空");
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");

        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);

        return switch (personType) {
            case "onJob" -> farEastDesignService.designOnJobPerson(params.orgCodes, params.headId);
            case "entry" -> farEastDesignService.designEntryPerson(
                    params.orgCodes, params.headId, params.startOfMonth, params.endOfMonth);
            case "leave" -> farEastDesignService.designLeavePerson(
                    params.orgCodes, params.headId, params.startOfMonth, params.endOfMonth);
            default -> throw new IllegalArgumentException("无效的人员类型: " + personType);
        };
    }

    public List<StaffDistributionVO> getDesignRankDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        return farEastDesignService.getDesignRankDistribution(params.orgCodes, params.headId);
    }

    public List<StaffDistributionVO> getDesignMajorDistribution(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        return farEastDesignService.getDesignMajorDistribution(params.orgCodes, params.headId);
    }

    public WorkEfficVO getDesignWorkEfficiency(FarEastQO qo) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        WorkEfficVO vo = new WorkEfficVO();
        HeadIdParams params = getHeadIdParams(qo);

        // 获取当前月份和年份信息
        int monthValue = params.statDate.getMonthValue(); // 当前月份(1-12)

        // 2. 累计编制使用率 = 累计用工人数 / 累计编制数 (保留4位小数)
        List<RosterHeadIdByYearMonthCountVO> rosterHeadIdList = rosterHeadService
                .selectByDateRange(params.startOfYear, params.statDate);
        List<String> headIdList = rosterHeadIdList.stream().map(RosterHeadIdByYearMonthCountVO::getId).toList();
        Map<String, Long> personCountMap = getDesignPersonCountMap(params.orgCodes, headIdList);
        long totalPersonCount = personCountMap.values().stream().reduce(0L, Long::sum);// 累计用工人数:personCountMap全部累加
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(params.orgCodes);
        long totalStaffCount = StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, params.startOfYear, params.statDate); // 累计编制数
        BigDecimal staffRatio = totalStaffCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(totalPersonCount).divide(BigDecimal.valueOf(totalStaffCount), 4, RoundingMode.HALF_UP);

        // 4. 累计薪酬总额（万港元）= 每月累计薪酬数 / 10,000 (保留4位小数)
        List<SalaryRecordVO> salaryRecords = salaryRecordService.listYearTotalHKSalaryRecord(params.orgCodes, params.statDate);
        List<String> orgCodeList = OrgInfoUtil.subOrgCodeList(organizationService); // 香港设计部、澳门设计部 及其下属部门
        // 工效管控-累计薪酬总额字段，排除掉【内地类型 远东香港设计部、远东澳门设计部（及下属组织）的薪酬总额】
        salaryRecords = salaryRecords.stream().filter(x -> !orgCodeList.contains(x.getOrganizationCode()) || !x.getEmployeeCategory().equals("内地")).toList();

        BigDecimal salaryTotal = salaryRecords.stream().map(SalaryRecordVO::getTotalSalary).reduce(BigDecimal.ZERO, CommonUtils::add);
        BigDecimal cumulativeTotalSalary = salaryTotal.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP); // 转换为万港元

        // 5. 累计薪酬使用率 = 每月薪酬数累计 / 薪酬预算总数累计 (保留4位小数)
        Map<String, BigDecimal> rate = exchangeRateService.getRate(XINGYE_CODE);
        List<StaffCountFull> salaryBudgetList = staffCountFullService.listByOrgCodes(params.orgCodes);
        BigDecimal totalSalaryBudget = StaffCountFullUtil.calcSalaryByDate(salaryBudgetList, params.startOfYear, params.endOfYear, rate); // 薪酬预算总数(港元)
        BigDecimal cumulativeBudgetSalary = totalSalaryBudget.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);
        BigDecimal salaryRatio = totalSalaryBudget.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : salaryTotal.divide(totalSalaryBudget, 4, RoundingMode.HALF_UP); // 港元

        // 6. 人均产值（万港元/年/人）= (每月设计产值累计 / 每月累计人数) * 月份数 (保留4位小数) --> 年度设计产值/本年度平均人数
        List<EfficiencyData> currentYearDesigns = farEastEfficiencyService.listBySubjectAndDateRange(
                "设计产值", params.startOfYear, params.endOfMonth, Collections.singletonList(FAR_EAST_HK_NO));
        BigDecimal yearTotalRevenue = CalculationUtils.convertEfficiency(currentYearDesigns); // 年度设计产值(万港元)

        BigDecimal personRevenue = totalPersonCount == 0 ? BigDecimal.ZERO : yearTotalRevenue.divide(BigDecimal.valueOf(totalPersonCount), 4, RoundingMode.HALF_UP) // 月人均产值
                .multiply(BigDecimal.valueOf(monthValue));

        // 7. 工资产值率 = 每月设计产值累计 / (每月薪酬数累计 * 10,000) (保留4位小数)
        BigDecimal salaryRevenueRatio = cumulativeTotalSalary.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                yearTotalRevenue.divide(cumulativeTotalSalary, 4, RoundingMode.HALF_UP);

        // 年总产值（万港元）
        BigDecimal cumulativeValueYear = StaffCountFullHelper.calcTargetOutputValueYearByDate(staffCountFullList, params.statDate);
        // 全年编制数
        int totalYearStaffCount = StaffCountFullUtil.calcStaffCountByDate(staffCountFullList, params.startOfYear, params.endOfYear);

        // 目标值(产值) 所选年月全年的目标产值汇总/全年编制数汇总 * 12个月
        BigDecimal targetOutputValue = BigDecimal.ZERO;
        if (totalYearStaffCount != 0) {
            targetOutputValue = CalculationUtils.safeDivide(cumulativeValueYear, BigDecimal.valueOf(totalYearStaffCount), 4)
                    .multiply(BigDecimal.valueOf(12));
        }

        // 目标值(工资)  所选年月全年目标产值汇总/全年（XZYS.830.01~06）的薪酬预算数汇总
        BigDecimal targetSalaryValue = BigDecimal.ZERO;
        if (cumulativeBudgetSalary.compareTo(BigDecimal.ZERO) != 0) {
            targetSalaryValue = CalculationUtils.safeDivide(cumulativeValueYear, cumulativeBudgetSalary, 4);
        }

        // 设置VO对象
        vo.setTotalPersonCount(totalPersonCount);    // 累计用工人数
        vo.setTotalStaffCount(totalStaffCount);      // 累计编制数
        vo.setCumStaffUseRatio(staffRatio);          // 累计编制使用率
        vo.setCumSalaryTotal(cumulativeTotalSalary);  // 累计薪酬总额(万港元)
        vo.setCumSalaryBudget(cumulativeBudgetSalary);      // 累计薪酬预算总数(万港元)
        vo.setCumSalaryUseRatio(salaryRatio); // 累计薪酬使用率
        vo.setCumAvgOutput(personRevenue);      // 人均产值(万港元/年/人)
        vo.setCumPayOutputRatio(salaryRevenueRatio);         // 工资产值率
        vo.setTargetOutputValue(targetOutputValue);          // 全年目标产值(万港元)
        vo.setTargetSalaryValue(targetSalaryValue);           // 全年目标薪酬(万港元)

        return vo;
    }


    public OutValueVO getLatestTwoYearsDesignAssets(FarEastQO qo) {
        // 1. 参数校验和准备
        HeadIdParams params = getHeadIdParams(qo);
        // 获取设计部门及其子部门
        /*List<String> designDeptCodes = Arrays.asList(HK_DESIGN_CODE);
        List<String> subOrgCodeList = organizationService.listByParentCode(designDeptCodes);*/

        // 当前年度
        List<EfficiencyData> currentYearDesigns = farEastEfficiencyService.listBySubjectAndDateRange(
                "设计产值", params.startOfYear, params.endOfYear, Collections.singletonList(FAR_EAST_HK_NO));
        // 去年年度
        LocalDate lastYearStart = params.startOfYear.minusYears(1);
        LocalDate lastYearEnd = params.endOfYear.minusYears(1);
        List<EfficiencyData> lastYearDesigns = farEastEfficiencyService.listBySubjectAndDateRange(
                "设计产值", lastYearStart, lastYearEnd, Collections.singletonList(FAR_EAST_HK_NO));

        // 处理当前年度数据（只到当前月）
        List<MonthlyData> currentYearData = IntStream.rangeClosed(1, params.statDate.getMonthValue())
                .mapToObj(month -> {
                    LocalDate monthDate = params.startOfYear.withMonth(month);
                    BigDecimal monthValue = currentYearDesigns.stream()
                            .filter(data -> data.getYearMonth().getMonthValue() == month)
                            .map(EfficiencyData::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return new MonthlyData(
                            String.format("%02d", month), // 格式化月份为两位
                            monthValue
                    );
                })
                .collect(Collectors.toList());

        // 处理去年全年数据
        List<MonthlyData> lastYearData = IntStream.rangeClosed(1, 12)
                .mapToObj(month -> {
                    LocalDate monthDate = lastYearStart.withMonth(month);
                    BigDecimal monthValue = lastYearDesigns.stream()
                            .filter(data -> data.getYearMonth().getMonthValue() == month)
                            .map(EfficiencyData::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return new MonthlyData(
                            String.format("%02d", month),
                            monthValue
                    );
                })
                .collect(Collectors.toList());

        // 构建返回结果
        OutValueVO result = new OutValueVO();
        result.setCurrentYearData(currentYearData);
        result.setLastYearData(lastYearData);
        return result;
    }

    public List<RosterDetail> getDesignRankListPage(FarEastQO qo, String personType, int pageNum, int pageSize) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastDesignService.getDesignRankList(params.orgCodes, params.headId, personType);
    }

    public List<RosterDetail> getDesignMajorListPage(FarEastQO qo, String personType, int pageNum, int pageSize) {
        checkExist(qo.getOrganizationCodeList(), "组织编码不能为空");
        HeadIdParams params = getHeadIdParams(qo);
        PageHelper.startPage(pageNum, pageSize);
        return farEastDesignService.getDesignMajorList(params.orgCodes, params.headId, personType);
    }

    public SalaryTotalVO getDesignSalaryTotal(FarEastQO qo) {
        HeadIdParams params = getHeadIdParams(qo);
        List<String> subOrgCodeList = OrgInfoUtil.subOrgCodeList(organizationService);
        return farEastSalaryService.queryDesignSalaryTotal(subOrgCodeList, params.startOfYear, params.endOfMonth, Arrays.asList("设计部", "内地", "香港"));
    }

    /**
     * 查询参数封装类
     */
    private static class HeadIdParams {
        final LocalDate statDate;
        final LocalDate startOfMonth;
        final LocalDate endOfMonth;
        final LocalDate startOfYear;
        final LocalDate endOfYear;
        final String headId;
        final List<String> orgCodes;

        HeadIdParams(LocalDate statDate, LocalDate startOfMonth, LocalDate endOfMonth,
                     LocalDate startOfYear, LocalDate endOfYear,
                     String headId, List<String> orgCodes) {
            this.statDate = statDate;
            this.startOfMonth = startOfMonth;
            this.endOfMonth = endOfMonth;
            this.startOfYear = startOfYear;
            this.endOfYear = endOfYear;
            this.headId = headId;
            this.orgCodes = orgCodes;
        }
    }

    private HeadIdParams getHeadIdParams(FarEastQO qo) {
        LocalDate statDate = Optional.ofNullable(qo.getStatDate()).orElse(LocalDate.now());
        String headId = farEastDesignService.getHeadId(statDate.getYear(), statDate.getMonthValue());
        return new HeadIdParams(
                statDate, // 当前选择日期
                statDate.withDayOfMonth(1), // 当前月的第一天
                statDate.with(TemporalAdjusters.lastDayOfMonth()), // 当前月的最后一天
                statDate.withDayOfYear(1), // 当前年第一天
                statDate.with(TemporalAdjusters.lastDayOfYear()), // 当前年最后一天
                headId,
                qo.getOrganizationCodeList()
        );
    }
}
