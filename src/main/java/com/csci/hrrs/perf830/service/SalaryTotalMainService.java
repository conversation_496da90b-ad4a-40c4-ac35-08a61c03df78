package com.csci.hrrs.perf830.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.constant.DatasourceContextEnum;
import com.csci.hrrs.perf830.entity.MainDataKey;
import com.csci.hrrs.perf830.mapper.SalaryTotalMainMapper;
import com.csci.hrrs.perf830.model.SalaryTotalMain;
import org.springframework.stereotype.Service;

/**
 * @ClassName: SalaryTotalMainService
 * @Auther: <EMAIL>
 * @Date: 2025/5/30 21:01
 * @Description:
 */
@Service
@DS(DatasourceContextEnum.HRRS)
public class SalaryTotalMainService extends ServiceImpl<SalaryTotalMainMapper, SalaryTotalMain> {
    public SalaryTotalMain queryExistingMainData(MainDataKey mainDataKey) {
        return this.lambdaQuery()
                .eq(SalaryTotalMain::getOrgCode, mainDataKey.getOrgCode())
                .eq(SalaryTotalMain::getOrgAlias, mainDataKey.getOrgAlias())
                .eq(SalaryTotalMain::getYear, mainDataKey.getYear())
                .eq(SalaryTotalMain::getProjectName, mainDataKey.getProjectName())
                .one();
    }
}
