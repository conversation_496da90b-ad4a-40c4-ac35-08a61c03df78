package com.csci.hrrs.perf830.gen;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;

import java.nio.file.Paths;

public class GenCode {
    public static void main(String[] args) {
        // **********************************************************************
        FastAutoGenerator.create("**********************************************************************", "devroster", "lksEkSA82&#dw")
//        FastAutoGenerator.create("***************************************************", "operating_data", "h37CO68u5t1a5DQ5")
        //FastAutoGenerator.create("***************************************************************************;", "SSIS", "Csci@3311")
                         .globalConfig(builder -> builder
                                               .author("Yvonne")
                                               .outputDir(Paths.get(System.getProperty("user.dir")) + "/src/main/java")
                                               .commentDate("yyyy-MM-dd")
                                               .disableServiceInterface()
                                      )
                         .packageConfig(builder -> builder
                                                .parent("com.csci.hrrs.perf830")
                                                .entity("model")
                                                .mapper("mapper")
                                       )
                         .strategyConfig(builder -> builder
                                                 .addInclude(getTableNames())
                                                 .addTablePrefix("t_")
                                                 .entityBuilder()
                                                 .disableSerialVersionUID()
                                                 .enableTableFieldAnnotation()
                                                 .enableLombok()
                                        )
                         .templateEngine(new TemplateEngine())
                         .execute();
    }

    public static String[] getTableNames() {
        return new String[]{
                ""
        };
    }
}
