package com.csci.hrrs.perf830.dto;

import com.csci.hrrs.model.ImportPlan;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.perf830.vo.YearlyPlanVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;


import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: YearlyPlanDTO
 * @Auther: <EMAIL>
 * @Date: 2025/5/19 14:33
 * @Description: 本年度编制申报
 */
@Data
public class YearlyPlanDTO {
    @Schema(description = "计划编制ID")
    private String id;
    @Schema(description = "年度")
    private Integer staffYear;
    @Schema(description = "组织编码")
    private String orgCode;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "合约额")
    private BigDecimal contractAmount;
    @Schema(description = "负责人")
    private String siteLeaderName;
    @Schema(description = "负责人账户")
    private String siteLeaderUsername;
    @Schema(description = "开工时间")
    private LocalDate startDate;
    @Schema(description = "完工时间")
    private LocalDate endDate;

    // 继承自全周期计划
    @Schema(description = "全周期总编制数(调整前)")
    private Integer totalStaffCountBefore;
    @Schema(description = "全周期总编制数(调整后)")
    private Integer totalStaffCountAfter;
    @Schema(description = "全周期薪酬总预算(调整前)")
    private BigDecimal totalBudgetBefore;
    @Schema(description = "全周期薪酬总预算(调整后)")
    private BigDecimal totalBudgetAfter;
    // 本年度
    @Schema(description = "本年度总编制数(调整前)")
    private Integer totalYearCountBefore;
    @Schema(description = "本年度总编制数(调整后)")
    private Integer totalYearCountAfter;
    @Schema(description = "本年度薪酬总预算(调整前)")
    private BigDecimal totalYearBudgetBefore;
    @Schema(description = "本年度薪酬总预算(调整后)")
    private BigDecimal totalYearBudgetAfter;

    public static YearlyPlanVO fromModel(YearlyPlanDTO dto) {
        YearlyPlanVO vo = new YearlyPlanVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    public static ImportPlan convertToImportPlan(YearlyPlanDTO dto) {
        ImportPlan plan = new ImportPlan();
        BeanUtils.copyProperties(dto, plan);
        return plan;
    }
}
