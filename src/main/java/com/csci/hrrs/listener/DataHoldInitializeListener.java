package com.csci.hrrs.listener;

import com.csci.hrrs.util.holder.DataHolderRefresher;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Profile({/* "dev", */ "test", "prod"})
public class DataHoldInitializeListener implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private DataHolderRefresher dataHolderRefresher;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 所有 Bean 加载完成后的代码
        System.out.println("所有 Bean 已加载完成，执行初始化代码。");
        // 在这里添加你的逻辑
        dataHolderRefresher.refreshAllDataHolders();
    }
}
