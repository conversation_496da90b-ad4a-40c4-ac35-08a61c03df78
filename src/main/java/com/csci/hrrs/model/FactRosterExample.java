package com.csci.hrrs.model;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class FactRosterExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public FactRosterExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andPernrIsNull() {
            addCriterion("PERNR is null");
            return (Criteria) this;
        }

        public Criteria andPernrIsNotNull() {
            addCriterion("PERNR is not null");
            return (Criteria) this;
        }

        public Criteria andPernrEqualTo(String value) {
            addCriterion("PERNR =", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotEqualTo(String value) {
            addCriterion("PERNR <>", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThan(String value) {
            addCriterion("PERNR >", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThanOrEqualTo(String value) {
            addCriterion("PERNR >=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThan(String value) {
            addCriterion("PERNR <", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThanOrEqualTo(String value) {
            addCriterion("PERNR <=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLike(String value) {
            addCriterion("PERNR like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotLike(String value) {
            addCriterion("PERNR not like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrIn(List<String> values) {
            addCriterion("PERNR in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotIn(List<String> values) {
            addCriterion("PERNR not in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrBetween(String value1, String value2) {
            addCriterion("PERNR between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotBetween(String value1, String value2) {
            addCriterion("PERNR not between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andEnglishnameIsNull() {
            addCriterion("englishname is null");
            return (Criteria) this;
        }

        public Criteria andEnglishnameIsNotNull() {
            addCriterion("englishname is not null");
            return (Criteria) this;
        }

        public Criteria andEnglishnameEqualTo(String value) {
            addCriterion("englishname =", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameNotEqualTo(String value) {
            addCriterion("englishname <>", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameGreaterThan(String value) {
            addCriterion("englishname >", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameGreaterThanOrEqualTo(String value) {
            addCriterion("englishname >=", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameLessThan(String value) {
            addCriterion("englishname <", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameLessThanOrEqualTo(String value) {
            addCriterion("englishname <=", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameLike(String value) {
            addCriterion("englishname like", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameNotLike(String value) {
            addCriterion("englishname not like", value, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameIn(List<String> values) {
            addCriterion("englishname in", values, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameNotIn(List<String> values) {
            addCriterion("englishname not in", values, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameBetween(String value1, String value2) {
            addCriterion("englishname between", value1, value2, "englishname");
            return (Criteria) this;
        }

        public Criteria andEnglishnameNotBetween(String value1, String value2) {
            addCriterion("englishname not between", value1, value2, "englishname");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNull() {
            addCriterion("company is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNotNull() {
            addCriterion("company is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEqualTo(String value) {
            addCriterion("company =", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotEqualTo(String value) {
            addCriterion("company <>", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThan(String value) {
            addCriterion("company >", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("company >=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThan(String value) {
            addCriterion("company <", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThanOrEqualTo(String value) {
            addCriterion("company <=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLike(String value) {
            addCriterion("company like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotLike(String value) {
            addCriterion("company not like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyIn(List<String> values) {
            addCriterion("company in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotIn(List<String> values) {
            addCriterion("company not in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyBetween(String value1, String value2) {
            addCriterion("company between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotBetween(String value1, String value2) {
            addCriterion("company not between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andJobIsNull() {
            addCriterion("job is null");
            return (Criteria) this;
        }

        public Criteria andJobIsNotNull() {
            addCriterion("job is not null");
            return (Criteria) this;
        }

        public Criteria andJobEqualTo(String value) {
            addCriterion("job =", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotEqualTo(String value) {
            addCriterion("job <>", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThan(String value) {
            addCriterion("job >", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThanOrEqualTo(String value) {
            addCriterion("job >=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThan(String value) {
            addCriterion("job <", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThanOrEqualTo(String value) {
            addCriterion("job <=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLike(String value) {
            addCriterion("job like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotLike(String value) {
            addCriterion("job not like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobIn(List<String> values) {
            addCriterion("job in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotIn(List<String> values) {
            addCriterion("job not in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobBetween(String value1, String value2) {
            addCriterion("job between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotBetween(String value1, String value2) {
            addCriterion("job not between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andParttimeJobIsNull() {
            addCriterion("parttime_job is null");
            return (Criteria) this;
        }

        public Criteria andParttimeJobIsNotNull() {
            addCriterion("parttime_job is not null");
            return (Criteria) this;
        }

        public Criteria andParttimeJobEqualTo(String value) {
            addCriterion("parttime_job =", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobNotEqualTo(String value) {
            addCriterion("parttime_job <>", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobGreaterThan(String value) {
            addCriterion("parttime_job >", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobGreaterThanOrEqualTo(String value) {
            addCriterion("parttime_job >=", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobLessThan(String value) {
            addCriterion("parttime_job <", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobLessThanOrEqualTo(String value) {
            addCriterion("parttime_job <=", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobLike(String value) {
            addCriterion("parttime_job like", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobNotLike(String value) {
            addCriterion("parttime_job not like", value, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobIn(List<String> values) {
            addCriterion("parttime_job in", values, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobNotIn(List<String> values) {
            addCriterion("parttime_job not in", values, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobBetween(String value1, String value2) {
            addCriterion("parttime_job between", value1, value2, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andParttimeJobNotBetween(String value1, String value2) {
            addCriterion("parttime_job not between", value1, value2, "parttimeJob");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleIsNull() {
            addCriterion("is_responsible is null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleIsNotNull() {
            addCriterion("is_responsible is not null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEqualTo(String value) {
            addCriterion("is_responsible =", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleNotEqualTo(String value) {
            addCriterion("is_responsible <>", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleGreaterThan(String value) {
            addCriterion("is_responsible >", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleGreaterThanOrEqualTo(String value) {
            addCriterion("is_responsible >=", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleLessThan(String value) {
            addCriterion("is_responsible <", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleLessThanOrEqualTo(String value) {
            addCriterion("is_responsible <=", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleLike(String value) {
            addCriterion("is_responsible like", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleNotLike(String value) {
            addCriterion("is_responsible not like", value, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleIn(List<String> values) {
            addCriterion("is_responsible in", values, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleNotIn(List<String> values) {
            addCriterion("is_responsible not in", values, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleBetween(String value1, String value2) {
            addCriterion("is_responsible between", value1, value2, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleNotBetween(String value1, String value2) {
            addCriterion("is_responsible not between", value1, value2, "isResponsible");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIsNull() {
            addCriterion("work_place is null");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIsNotNull() {
            addCriterion("work_place is not null");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceEqualTo(String value) {
            addCriterion("work_place =", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotEqualTo(String value) {
            addCriterion("work_place <>", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceGreaterThan(String value) {
            addCriterion("work_place >", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("work_place >=", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLessThan(String value) {
            addCriterion("work_place <", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLessThanOrEqualTo(String value) {
            addCriterion("work_place <=", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLike(String value) {
            addCriterion("work_place like", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotLike(String value) {
            addCriterion("work_place not like", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIn(List<String> values) {
            addCriterion("work_place in", values, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotIn(List<String> values) {
            addCriterion("work_place not in", values, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceBetween(String value1, String value2) {
            addCriterion("work_place between", value1, value2, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotBetween(String value1, String value2) {
            addCriterion("work_place not between", value1, value2, "workPlace");
            return (Criteria) this;
        }

        public Criteria andJobTypeIsNull() {
            addCriterion("job_type is null");
            return (Criteria) this;
        }

        public Criteria andJobTypeIsNotNull() {
            addCriterion("job_type is not null");
            return (Criteria) this;
        }

        public Criteria andJobTypeEqualTo(String value) {
            addCriterion("job_type =", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotEqualTo(String value) {
            addCriterion("job_type <>", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeGreaterThan(String value) {
            addCriterion("job_type >", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeGreaterThanOrEqualTo(String value) {
            addCriterion("job_type >=", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeLessThan(String value) {
            addCriterion("job_type <", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeLessThanOrEqualTo(String value) {
            addCriterion("job_type <=", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeLike(String value) {
            addCriterion("job_type like", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotLike(String value) {
            addCriterion("job_type not like", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeIn(List<String> values) {
            addCriterion("job_type in", values, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotIn(List<String> values) {
            addCriterion("job_type not in", values, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeBetween(String value1, String value2) {
            addCriterion("job_type between", value1, value2, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotBetween(String value1, String value2) {
            addCriterion("job_type not between", value1, value2, "jobType");
            return (Criteria) this;
        }

        public Criteria andSpecialityIsNull() {
            addCriterion("speciality is null");
            return (Criteria) this;
        }

        public Criteria andSpecialityIsNotNull() {
            addCriterion("speciality is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialityEqualTo(String value) {
            addCriterion("speciality =", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityNotEqualTo(String value) {
            addCriterion("speciality <>", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityGreaterThan(String value) {
            addCriterion("speciality >", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityGreaterThanOrEqualTo(String value) {
            addCriterion("speciality >=", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityLessThan(String value) {
            addCriterion("speciality <", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityLessThanOrEqualTo(String value) {
            addCriterion("speciality <=", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityLike(String value) {
            addCriterion("speciality like", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityNotLike(String value) {
            addCriterion("speciality not like", value, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityIn(List<String> values) {
            addCriterion("speciality in", values, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityNotIn(List<String> values) {
            addCriterion("speciality not in", values, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityBetween(String value1, String value2) {
            addCriterion("speciality between", value1, value2, "speciality");
            return (Criteria) this;
        }

        public Criteria andSpecialityNotBetween(String value1, String value2) {
            addCriterion("speciality not between", value1, value2, "speciality");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIsNull() {
            addCriterion("job_level_level is null");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIsNotNull() {
            addCriterion("job_level_level is not null");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelEqualTo(String value) {
            addCriterion("job_level_level =", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotEqualTo(String value) {
            addCriterion("job_level_level <>", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelGreaterThan(String value) {
            addCriterion("job_level_level >", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelGreaterThanOrEqualTo(String value) {
            addCriterion("job_level_level >=", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLessThan(String value) {
            addCriterion("job_level_level <", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLessThanOrEqualTo(String value) {
            addCriterion("job_level_level <=", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLike(String value) {
            addCriterion("job_level_level like", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotLike(String value) {
            addCriterion("job_level_level not like", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIn(List<String> values) {
            addCriterion("job_level_level in", values, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotIn(List<String> values) {
            addCriterion("job_level_level not in", values, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelBetween(String value1, String value2) {
            addCriterion("job_level_level between", value1, value2, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotBetween(String value1, String value2) {
            addCriterion("job_level_level not between", value1, value2, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNull() {
            addCriterion("job_level is null");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNotNull() {
            addCriterion("job_level is not null");
            return (Criteria) this;
        }

        public Criteria andJobLevelEqualTo(String value) {
            addCriterion("job_level =", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotEqualTo(String value) {
            addCriterion("job_level <>", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThan(String value) {
            addCriterion("job_level >", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThanOrEqualTo(String value) {
            addCriterion("job_level >=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThan(String value) {
            addCriterion("job_level <", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThanOrEqualTo(String value) {
            addCriterion("job_level <=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLike(String value) {
            addCriterion("job_level like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotLike(String value) {
            addCriterion("job_level not like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelIn(List<String> values) {
            addCriterion("job_level in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotIn(List<String> values) {
            addCriterion("job_level not in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelBetween(String value1, String value2) {
            addCriterion("job_level between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotBetween(String value1, String value2) {
            addCriterion("job_level not between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andRankIsNull() {
            addCriterion("rank is null");
            return (Criteria) this;
        }

        public Criteria andRankIsNotNull() {
            addCriterion("rank is not null");
            return (Criteria) this;
        }

        public Criteria andRankEqualTo(String value) {
            addCriterion("rank =", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotEqualTo(String value) {
            addCriterion("rank <>", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThan(String value) {
            addCriterion("rank >", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThanOrEqualTo(String value) {
            addCriterion("rank >=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThan(String value) {
            addCriterion("rank <", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThanOrEqualTo(String value) {
            addCriterion("rank <=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLike(String value) {
            addCriterion("rank like", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotLike(String value) {
            addCriterion("rank not like", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankIn(List<String> values) {
            addCriterion("rank in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotIn(List<String> values) {
            addCriterion("rank not in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankBetween(String value1, String value2) {
            addCriterion("rank between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotBetween(String value1, String value2) {
            addCriterion("rank not between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankHkIsNull() {
            addCriterion("rank_hk is null");
            return (Criteria) this;
        }

        public Criteria andRankHkIsNotNull() {
            addCriterion("rank_hk is not null");
            return (Criteria) this;
        }

        public Criteria andRankHkEqualTo(String value) {
            addCriterion("rank_hk =", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkNotEqualTo(String value) {
            addCriterion("rank_hk <>", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkGreaterThan(String value) {
            addCriterion("rank_hk >", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkGreaterThanOrEqualTo(String value) {
            addCriterion("rank_hk >=", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkLessThan(String value) {
            addCriterion("rank_hk <", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkLessThanOrEqualTo(String value) {
            addCriterion("rank_hk <=", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkLike(String value) {
            addCriterion("rank_hk like", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkNotLike(String value) {
            addCriterion("rank_hk not like", value, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkIn(List<String> values) {
            addCriterion("rank_hk in", values, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkNotIn(List<String> values) {
            addCriterion("rank_hk not in", values, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkBetween(String value1, String value2) {
            addCriterion("rank_hk between", value1, value2, "rankHk");
            return (Criteria) this;
        }

        public Criteria andRankHkNotBetween(String value1, String value2) {
            addCriterion("rank_hk not between", value1, value2, "rankHk");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("sex is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("sex is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(String value) {
            addCriterion("sex =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(String value) {
            addCriterion("sex <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(String value) {
            addCriterion("sex >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(String value) {
            addCriterion("sex >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(String value) {
            addCriterion("sex <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(String value) {
            addCriterion("sex <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLike(String value) {
            addCriterion("sex like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotLike(String value) {
            addCriterion("sex not like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<String> values) {
            addCriterion("sex in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<String> values) {
            addCriterion("sex not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(String value1, String value2) {
            addCriterion("sex between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(String value1, String value2) {
            addCriterion("sex not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateIsNull() {
            addCriterion("star_work_date is null");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateIsNotNull() {
            addCriterion("star_work_date is not null");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateEqualTo(String value) {
            addCriterion("star_work_date =", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateNotEqualTo(String value) {
            addCriterion("star_work_date <>", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateGreaterThan(String value) {
            addCriterion("star_work_date >", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateGreaterThanOrEqualTo(String value) {
            addCriterion("star_work_date >=", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateLessThan(String value) {
            addCriterion("star_work_date <", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateLessThanOrEqualTo(String value) {
            addCriterion("star_work_date <=", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateLike(String value) {
            addCriterion("star_work_date like", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateNotLike(String value) {
            addCriterion("star_work_date not like", value, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateIn(List<String> values) {
            addCriterion("star_work_date in", values, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateNotIn(List<String> values) {
            addCriterion("star_work_date not in", values, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateBetween(String value1, String value2) {
            addCriterion("star_work_date between", value1, value2, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andStarWorkDateNotBetween(String value1, String value2) {
            addCriterion("star_work_date not between", value1, value2, "starWorkDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateIsNull() {
            addCriterion("join_zh_date is null");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateIsNotNull() {
            addCriterion("join_zh_date is not null");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateEqualTo(String value) {
            addCriterion("join_zh_date =", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateNotEqualTo(String value) {
            addCriterion("join_zh_date <>", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateGreaterThan(String value) {
            addCriterion("join_zh_date >", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateGreaterThanOrEqualTo(String value) {
            addCriterion("join_zh_date >=", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateLessThan(String value) {
            addCriterion("join_zh_date <", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateLessThanOrEqualTo(String value) {
            addCriterion("join_zh_date <=", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateLike(String value) {
            addCriterion("join_zh_date like", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateNotLike(String value) {
            addCriterion("join_zh_date not like", value, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateIn(List<String> values) {
            addCriterion("join_zh_date in", values, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateNotIn(List<String> values) {
            addCriterion("join_zh_date not in", values, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateBetween(String value1, String value2) {
            addCriterion("join_zh_date between", value1, value2, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoinZhDateNotBetween(String value1, String value2) {
            addCriterion("join_zh_date not between", value1, value2, "joinZhDate");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateIsNull() {
            addCriterion("join_3311_date is null");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateIsNotNull() {
            addCriterion("join_3311_date is not null");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateEqualTo(String value) {
            addCriterion("join_3311_date =", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateNotEqualTo(String value) {
            addCriterion("join_3311_date <>", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateGreaterThan(String value) {
            addCriterion("join_3311_date >", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateGreaterThanOrEqualTo(String value) {
            addCriterion("join_3311_date >=", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateLessThan(String value) {
            addCriterion("join_3311_date <", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateLessThanOrEqualTo(String value) {
            addCriterion("join_3311_date <=", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateLike(String value) {
            addCriterion("join_3311_date like", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateNotLike(String value) {
            addCriterion("join_3311_date not like", value, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateIn(List<String> values) {
            addCriterion("join_3311_date in", values, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateNotIn(List<String> values) {
            addCriterion("join_3311_date not in", values, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateBetween(String value1, String value2) {
            addCriterion("join_3311_date between", value1, value2, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andJoin3311DateNotBetween(String value1, String value2) {
            addCriterion("join_3311_date not between", value1, value2, "join3311Date");
            return (Criteria) this;
        }

        public Criteria andAbroadDateIsNull() {
            addCriterion("abroad_date is null");
            return (Criteria) this;
        }

        public Criteria andAbroadDateIsNotNull() {
            addCriterion("abroad_date is not null");
            return (Criteria) this;
        }

        public Criteria andAbroadDateEqualTo(String value) {
            addCriterion("abroad_date =", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateNotEqualTo(String value) {
            addCriterion("abroad_date <>", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateGreaterThan(String value) {
            addCriterion("abroad_date >", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateGreaterThanOrEqualTo(String value) {
            addCriterion("abroad_date >=", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateLessThan(String value) {
            addCriterion("abroad_date <", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateLessThanOrEqualTo(String value) {
            addCriterion("abroad_date <=", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateLike(String value) {
            addCriterion("abroad_date like", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateNotLike(String value) {
            addCriterion("abroad_date not like", value, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateIn(List<String> values) {
            addCriterion("abroad_date in", values, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateNotIn(List<String> values) {
            addCriterion("abroad_date not in", values, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateBetween(String value1, String value2) {
            addCriterion("abroad_date between", value1, value2, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andAbroadDateNotBetween(String value1, String value2) {
            addCriterion("abroad_date not between", value1, value2, "abroadDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateIsNull() {
            addCriterion("current_position_date is null");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateIsNotNull() {
            addCriterion("current_position_date is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateEqualTo(String value) {
            addCriterion("current_position_date =", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateNotEqualTo(String value) {
            addCriterion("current_position_date <>", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateGreaterThan(String value) {
            addCriterion("current_position_date >", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateGreaterThanOrEqualTo(String value) {
            addCriterion("current_position_date >=", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateLessThan(String value) {
            addCriterion("current_position_date <", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateLessThanOrEqualTo(String value) {
            addCriterion("current_position_date <=", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateLike(String value) {
            addCriterion("current_position_date like", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateNotLike(String value) {
            addCriterion("current_position_date not like", value, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateIn(List<String> values) {
            addCriterion("current_position_date in", values, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateNotIn(List<String> values) {
            addCriterion("current_position_date not in", values, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateBetween(String value1, String value2) {
            addCriterion("current_position_date between", value1, value2, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionDateNotBetween(String value1, String value2) {
            addCriterion("current_position_date not between", value1, value2, "currentPositionDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateIsNull() {
            addCriterion("current_joblevel_date is null");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateIsNotNull() {
            addCriterion("current_joblevel_date is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateEqualTo(String value) {
            addCriterion("current_joblevel_date =", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateNotEqualTo(String value) {
            addCriterion("current_joblevel_date <>", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateGreaterThan(String value) {
            addCriterion("current_joblevel_date >", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateGreaterThanOrEqualTo(String value) {
            addCriterion("current_joblevel_date >=", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateLessThan(String value) {
            addCriterion("current_joblevel_date <", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateLessThanOrEqualTo(String value) {
            addCriterion("current_joblevel_date <=", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateLike(String value) {
            addCriterion("current_joblevel_date like", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateNotLike(String value) {
            addCriterion("current_joblevel_date not like", value, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateIn(List<String> values) {
            addCriterion("current_joblevel_date in", values, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateNotIn(List<String> values) {
            addCriterion("current_joblevel_date not in", values, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateBetween(String value1, String value2) {
            addCriterion("current_joblevel_date between", value1, value2, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelDateNotBetween(String value1, String value2) {
            addCriterion("current_joblevel_date not between", value1, value2, "currentJoblevelDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateIsNull() {
            addCriterion("current_rank_date is null");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateIsNotNull() {
            addCriterion("current_rank_date is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateEqualTo(String value) {
            addCriterion("current_rank_date =", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateNotEqualTo(String value) {
            addCriterion("current_rank_date <>", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateGreaterThan(String value) {
            addCriterion("current_rank_date >", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateGreaterThanOrEqualTo(String value) {
            addCriterion("current_rank_date >=", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateLessThan(String value) {
            addCriterion("current_rank_date <", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateLessThanOrEqualTo(String value) {
            addCriterion("current_rank_date <=", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateLike(String value) {
            addCriterion("current_rank_date like", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateNotLike(String value) {
            addCriterion("current_rank_date not like", value, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateIn(List<String> values) {
            addCriterion("current_rank_date in", values, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateNotIn(List<String> values) {
            addCriterion("current_rank_date not in", values, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateBetween(String value1, String value2) {
            addCriterion("current_rank_date between", value1, value2, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andCurrentRankDateNotBetween(String value1, String value2) {
            addCriterion("current_rank_date not between", value1, value2, "currentRankDate");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNull() {
            addCriterion("birthdate is null");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNotNull() {
            addCriterion("birthdate is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdateEqualTo(String value) {
            addCriterion("birthdate =", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotEqualTo(String value) {
            addCriterion("birthdate <>", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThan(String value) {
            addCriterion("birthdate >", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThanOrEqualTo(String value) {
            addCriterion("birthdate >=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThan(String value) {
            addCriterion("birthdate <", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThanOrEqualTo(String value) {
            addCriterion("birthdate <=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLike(String value) {
            addCriterion("birthdate like", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotLike(String value) {
            addCriterion("birthdate not like", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateIn(List<String> values) {
            addCriterion("birthdate in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotIn(List<String> values) {
            addCriterion("birthdate not in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateBetween(String value1, String value2) {
            addCriterion("birthdate between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotBetween(String value1, String value2) {
            addCriterion("birthdate not between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andNationIsNull() {
            addCriterion("nation is null");
            return (Criteria) this;
        }

        public Criteria andNationIsNotNull() {
            addCriterion("nation is not null");
            return (Criteria) this;
        }

        public Criteria andNationEqualTo(String value) {
            addCriterion("nation =", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotEqualTo(String value) {
            addCriterion("nation <>", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationGreaterThan(String value) {
            addCriterion("nation >", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationGreaterThanOrEqualTo(String value) {
            addCriterion("nation >=", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLessThan(String value) {
            addCriterion("nation <", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLessThanOrEqualTo(String value) {
            addCriterion("nation <=", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLike(String value) {
            addCriterion("nation like", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotLike(String value) {
            addCriterion("nation not like", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationIn(List<String> values) {
            addCriterion("nation in", values, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotIn(List<String> values) {
            addCriterion("nation not in", values, "nation");
            return (Criteria) this;
        }

        public Criteria andNationBetween(String value1, String value2) {
            addCriterion("nation between", value1, value2, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotBetween(String value1, String value2) {
            addCriterion("nation not between", value1, value2, "nation");
            return (Criteria) this;
        }

        public Criteria andNativePlaceIsNull() {
            addCriterion("native_place is null");
            return (Criteria) this;
        }

        public Criteria andNativePlaceIsNotNull() {
            addCriterion("native_place is not null");
            return (Criteria) this;
        }

        public Criteria andNativePlaceEqualTo(String value) {
            addCriterion("native_place =", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceNotEqualTo(String value) {
            addCriterion("native_place <>", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceGreaterThan(String value) {
            addCriterion("native_place >", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceGreaterThanOrEqualTo(String value) {
            addCriterion("native_place >=", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceLessThan(String value) {
            addCriterion("native_place <", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceLessThanOrEqualTo(String value) {
            addCriterion("native_place <=", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceLike(String value) {
            addCriterion("native_place like", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceNotLike(String value) {
            addCriterion("native_place not like", value, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceIn(List<String> values) {
            addCriterion("native_place in", values, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceNotIn(List<String> values) {
            addCriterion("native_place not in", values, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceBetween(String value1, String value2) {
            addCriterion("native_place between", value1, value2, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andNativePlaceNotBetween(String value1, String value2) {
            addCriterion("native_place not between", value1, value2, "nativePlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNull() {
            addCriterion("birth_place is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNotNull() {
            addCriterion("birth_place is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceEqualTo(String value) {
            addCriterion("birth_place =", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotEqualTo(String value) {
            addCriterion("birth_place <>", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThan(String value) {
            addCriterion("birth_place >", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("birth_place >=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThan(String value) {
            addCriterion("birth_place <", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThanOrEqualTo(String value) {
            addCriterion("birth_place <=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLike(String value) {
            addCriterion("birth_place like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotLike(String value) {
            addCriterion("birth_place not like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIn(List<String> values) {
            addCriterion("birth_place in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotIn(List<String> values) {
            addCriterion("birth_place not in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceBetween(String value1, String value2) {
            addCriterion("birth_place between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotBetween(String value1, String value2) {
            addCriterion("birth_place not between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceIsNull() {
            addCriterion("family_place is null");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceIsNotNull() {
            addCriterion("family_place is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceEqualTo(String value) {
            addCriterion("family_place =", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceNotEqualTo(String value) {
            addCriterion("family_place <>", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceGreaterThan(String value) {
            addCriterion("family_place >", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("family_place >=", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceLessThan(String value) {
            addCriterion("family_place <", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceLessThanOrEqualTo(String value) {
            addCriterion("family_place <=", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceLike(String value) {
            addCriterion("family_place like", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceNotLike(String value) {
            addCriterion("family_place not like", value, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceIn(List<String> values) {
            addCriterion("family_place in", values, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceNotIn(List<String> values) {
            addCriterion("family_place not in", values, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceBetween(String value1, String value2) {
            addCriterion("family_place between", value1, value2, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceNotBetween(String value1, String value2) {
            addCriterion("family_place not between", value1, value2, "familyPlace");
            return (Criteria) this;
        }

        public Criteria andMarriageIsNull() {
            addCriterion("marriage is null");
            return (Criteria) this;
        }

        public Criteria andMarriageIsNotNull() {
            addCriterion("marriage is not null");
            return (Criteria) this;
        }

        public Criteria andMarriageEqualTo(String value) {
            addCriterion("marriage =", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotEqualTo(String value) {
            addCriterion("marriage <>", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageGreaterThan(String value) {
            addCriterion("marriage >", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageGreaterThanOrEqualTo(String value) {
            addCriterion("marriage >=", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLessThan(String value) {
            addCriterion("marriage <", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLessThanOrEqualTo(String value) {
            addCriterion("marriage <=", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLike(String value) {
            addCriterion("marriage like", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotLike(String value) {
            addCriterion("marriage not like", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageIn(List<String> values) {
            addCriterion("marriage in", values, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotIn(List<String> values) {
            addCriterion("marriage not in", values, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageBetween(String value1, String value2) {
            addCriterion("marriage between", value1, value2, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotBetween(String value1, String value2) {
            addCriterion("marriage not between", value1, value2, "marriage");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToIsNull() {
            addCriterion("give_birth_to is null");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToIsNotNull() {
            addCriterion("give_birth_to is not null");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToEqualTo(String value) {
            addCriterion("give_birth_to =", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToNotEqualTo(String value) {
            addCriterion("give_birth_to <>", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToGreaterThan(String value) {
            addCriterion("give_birth_to >", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToGreaterThanOrEqualTo(String value) {
            addCriterion("give_birth_to >=", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToLessThan(String value) {
            addCriterion("give_birth_to <", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToLessThanOrEqualTo(String value) {
            addCriterion("give_birth_to <=", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToLike(String value) {
            addCriterion("give_birth_to like", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToNotLike(String value) {
            addCriterion("give_birth_to not like", value, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToIn(List<String> values) {
            addCriterion("give_birth_to in", values, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToNotIn(List<String> values) {
            addCriterion("give_birth_to not in", values, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToBetween(String value1, String value2) {
            addCriterion("give_birth_to between", value1, value2, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andGiveBirthToNotBetween(String value1, String value2) {
            addCriterion("give_birth_to not between", value1, value2, "giveBirthTo");
            return (Criteria) this;
        }

        public Criteria andEduIsNull() {
            addCriterion("edu is null");
            return (Criteria) this;
        }

        public Criteria andEduIsNotNull() {
            addCriterion("edu is not null");
            return (Criteria) this;
        }

        public Criteria andEduEqualTo(String value) {
            addCriterion("edu =", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotEqualTo(String value) {
            addCriterion("edu <>", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduGreaterThan(String value) {
            addCriterion("edu >", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduGreaterThanOrEqualTo(String value) {
            addCriterion("edu >=", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLessThan(String value) {
            addCriterion("edu <", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLessThanOrEqualTo(String value) {
            addCriterion("edu <=", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLike(String value) {
            addCriterion("edu like", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotLike(String value) {
            addCriterion("edu not like", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduIn(List<String> values) {
            addCriterion("edu in", values, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotIn(List<String> values) {
            addCriterion("edu not in", values, "edu");
            return (Criteria) this;
        }

        public Criteria andEduBetween(String value1, String value2) {
            addCriterion("edu between", value1, value2, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotBetween(String value1, String value2) {
            addCriterion("edu not between", value1, value2, "edu");
            return (Criteria) this;
        }

        public Criteria andEduSchoolIsNull() {
            addCriterion("edu_school is null");
            return (Criteria) this;
        }

        public Criteria andEduSchoolIsNotNull() {
            addCriterion("edu_school is not null");
            return (Criteria) this;
        }

        public Criteria andEduSchoolEqualTo(String value) {
            addCriterion("edu_school =", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolNotEqualTo(String value) {
            addCriterion("edu_school <>", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolGreaterThan(String value) {
            addCriterion("edu_school >", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolGreaterThanOrEqualTo(String value) {
            addCriterion("edu_school >=", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolLessThan(String value) {
            addCriterion("edu_school <", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolLessThanOrEqualTo(String value) {
            addCriterion("edu_school <=", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolLike(String value) {
            addCriterion("edu_school like", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolNotLike(String value) {
            addCriterion("edu_school not like", value, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolIn(List<String> values) {
            addCriterion("edu_school in", values, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolNotIn(List<String> values) {
            addCriterion("edu_school not in", values, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolBetween(String value1, String value2) {
            addCriterion("edu_school between", value1, value2, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andEduSchoolNotBetween(String value1, String value2) {
            addCriterion("edu_school not between", value1, value2, "eduSchool");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andPracQualificationIsNull() {
            addCriterion("prac_qualification is null");
            return (Criteria) this;
        }

        public Criteria andPracQualificationIsNotNull() {
            addCriterion("prac_qualification is not null");
            return (Criteria) this;
        }

        public Criteria andPracQualificationEqualTo(String value) {
            addCriterion("prac_qualification =", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationNotEqualTo(String value) {
            addCriterion("prac_qualification <>", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationGreaterThan(String value) {
            addCriterion("prac_qualification >", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationGreaterThanOrEqualTo(String value) {
            addCriterion("prac_qualification >=", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationLessThan(String value) {
            addCriterion("prac_qualification <", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationLessThanOrEqualTo(String value) {
            addCriterion("prac_qualification <=", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationLike(String value) {
            addCriterion("prac_qualification like", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationNotLike(String value) {
            addCriterion("prac_qualification not like", value, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationIn(List<String> values) {
            addCriterion("prac_qualification in", values, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationNotIn(List<String> values) {
            addCriterion("prac_qualification not in", values, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationBetween(String value1, String value2) {
            addCriterion("prac_qualification between", value1, value2, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andPracQualificationNotBetween(String value1, String value2) {
            addCriterion("prac_qualification not between", value1, value2, "pracQualification");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andAdnameIsNull() {
            addCriterion("ADname is null");
            return (Criteria) this;
        }

        public Criteria andAdnameIsNotNull() {
            addCriterion("ADname is not null");
            return (Criteria) this;
        }

        public Criteria andAdnameEqualTo(String value) {
            addCriterion("ADname =", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameNotEqualTo(String value) {
            addCriterion("ADname <>", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameGreaterThan(String value) {
            addCriterion("ADname >", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameGreaterThanOrEqualTo(String value) {
            addCriterion("ADname >=", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameLessThan(String value) {
            addCriterion("ADname <", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameLessThanOrEqualTo(String value) {
            addCriterion("ADname <=", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameLike(String value) {
            addCriterion("ADname like", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameNotLike(String value) {
            addCriterion("ADname not like", value, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameIn(List<String> values) {
            addCriterion("ADname in", values, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameNotIn(List<String> values) {
            addCriterion("ADname not in", values, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameBetween(String value1, String value2) {
            addCriterion("ADname between", value1, value2, "adname");
            return (Criteria) this;
        }

        public Criteria andAdnameNotBetween(String value1, String value2) {
            addCriterion("ADname not between", value1, value2, "adname");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneIsNull() {
            addCriterion("family_Phone is null");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneIsNotNull() {
            addCriterion("family_Phone is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneEqualTo(String value) {
            addCriterion("family_Phone =", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneNotEqualTo(String value) {
            addCriterion("family_Phone <>", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneGreaterThan(String value) {
            addCriterion("family_Phone >", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("family_Phone >=", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneLessThan(String value) {
            addCriterion("family_Phone <", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneLessThanOrEqualTo(String value) {
            addCriterion("family_Phone <=", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneLike(String value) {
            addCriterion("family_Phone like", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneNotLike(String value) {
            addCriterion("family_Phone not like", value, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneIn(List<String> values) {
            addCriterion("family_Phone in", values, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneNotIn(List<String> values) {
            addCriterion("family_Phone not in", values, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneBetween(String value1, String value2) {
            addCriterion("family_Phone between", value1, value2, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andFamilyPhoneNotBetween(String value1, String value2) {
            addCriterion("family_Phone not between", value1, value2, "familyPhone");
            return (Criteria) this;
        }

        public Criteria andPersonEmailIsNull() {
            addCriterion("person_email is null");
            return (Criteria) this;
        }

        public Criteria andPersonEmailIsNotNull() {
            addCriterion("person_email is not null");
            return (Criteria) this;
        }

        public Criteria andPersonEmailEqualTo(String value) {
            addCriterion("person_email =", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailNotEqualTo(String value) {
            addCriterion("person_email <>", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailGreaterThan(String value) {
            addCriterion("person_email >", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailGreaterThanOrEqualTo(String value) {
            addCriterion("person_email >=", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailLessThan(String value) {
            addCriterion("person_email <", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailLessThanOrEqualTo(String value) {
            addCriterion("person_email <=", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailLike(String value) {
            addCriterion("person_email like", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailNotLike(String value) {
            addCriterion("person_email not like", value, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailIn(List<String> values) {
            addCriterion("person_email in", values, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailNotIn(List<String> values) {
            addCriterion("person_email not in", values, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailBetween(String value1, String value2) {
            addCriterion("person_email between", value1, value2, "personEmail");
            return (Criteria) this;
        }

        public Criteria andPersonEmailNotBetween(String value1, String value2) {
            addCriterion("person_email not between", value1, value2, "personEmail");
            return (Criteria) this;
        }

        public Criteria andWechatIsNull() {
            addCriterion("wechat is null");
            return (Criteria) this;
        }

        public Criteria andWechatIsNotNull() {
            addCriterion("wechat is not null");
            return (Criteria) this;
        }

        public Criteria andWechatEqualTo(String value) {
            addCriterion("wechat =", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatNotEqualTo(String value) {
            addCriterion("wechat <>", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatGreaterThan(String value) {
            addCriterion("wechat >", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatGreaterThanOrEqualTo(String value) {
            addCriterion("wechat >=", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatLessThan(String value) {
            addCriterion("wechat <", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatLessThanOrEqualTo(String value) {
            addCriterion("wechat <=", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatLike(String value) {
            addCriterion("wechat like", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatNotLike(String value) {
            addCriterion("wechat not like", value, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatIn(List<String> values) {
            addCriterion("wechat in", values, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatNotIn(List<String> values) {
            addCriterion("wechat not in", values, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatBetween(String value1, String value2) {
            addCriterion("wechat between", value1, value2, "wechat");
            return (Criteria) this;
        }

        public Criteria andWechatNotBetween(String value1, String value2) {
            addCriterion("wechat not between", value1, value2, "wechat");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311IsNull() {
            addCriterion("staff_class3311 is null");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311IsNotNull() {
            addCriterion("staff_class3311 is not null");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311EqualTo(String value) {
            addCriterion("staff_class3311 =", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311NotEqualTo(String value) {
            addCriterion("staff_class3311 <>", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311GreaterThan(String value) {
            addCriterion("staff_class3311 >", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311GreaterThanOrEqualTo(String value) {
            addCriterion("staff_class3311 >=", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311LessThan(String value) {
            addCriterion("staff_class3311 <", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311LessThanOrEqualTo(String value) {
            addCriterion("staff_class3311 <=", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311Like(String value) {
            addCriterion("staff_class3311 like", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311NotLike(String value) {
            addCriterion("staff_class3311 not like", value, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311In(List<String> values) {
            addCriterion("staff_class3311 in", values, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311NotIn(List<String> values) {
            addCriterion("staff_class3311 not in", values, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311Between(String value1, String value2) {
            addCriterion("staff_class3311 between", value1, value2, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andStaffClass3311NotBetween(String value1, String value2) {
            addCriterion("staff_class3311 not between", value1, value2, "staffClass3311");
            return (Criteria) this;
        }

        public Criteria andHzzYearIsNull() {
            addCriterion("hzz_year is null");
            return (Criteria) this;
        }

        public Criteria andHzzYearIsNotNull() {
            addCriterion("hzz_year is not null");
            return (Criteria) this;
        }

        public Criteria andHzzYearEqualTo(String value) {
            addCriterion("hzz_year =", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearNotEqualTo(String value) {
            addCriterion("hzz_year <>", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearGreaterThan(String value) {
            addCriterion("hzz_year >", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearGreaterThanOrEqualTo(String value) {
            addCriterion("hzz_year >=", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearLessThan(String value) {
            addCriterion("hzz_year <", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearLessThanOrEqualTo(String value) {
            addCriterion("hzz_year <=", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearLike(String value) {
            addCriterion("hzz_year like", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearNotLike(String value) {
            addCriterion("hzz_year not like", value, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearIn(List<String> values) {
            addCriterion("hzz_year in", values, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearNotIn(List<String> values) {
            addCriterion("hzz_year not in", values, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearBetween(String value1, String value2) {
            addCriterion("hzz_year between", value1, value2, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzYearNotBetween(String value1, String value2) {
            addCriterion("hzz_year not between", value1, value2, "hzzYear");
            return (Criteria) this;
        }

        public Criteria andHzzSortIsNull() {
            addCriterion("hzz_sort is null");
            return (Criteria) this;
        }

        public Criteria andHzzSortIsNotNull() {
            addCriterion("hzz_sort is not null");
            return (Criteria) this;
        }

        public Criteria andHzzSortEqualTo(String value) {
            addCriterion("hzz_sort =", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortNotEqualTo(String value) {
            addCriterion("hzz_sort <>", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortGreaterThan(String value) {
            addCriterion("hzz_sort >", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortGreaterThanOrEqualTo(String value) {
            addCriterion("hzz_sort >=", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortLessThan(String value) {
            addCriterion("hzz_sort <", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortLessThanOrEqualTo(String value) {
            addCriterion("hzz_sort <=", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortLike(String value) {
            addCriterion("hzz_sort like", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortNotLike(String value) {
            addCriterion("hzz_sort not like", value, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortIn(List<String> values) {
            addCriterion("hzz_sort in", values, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortNotIn(List<String> values) {
            addCriterion("hzz_sort not in", values, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortBetween(String value1, String value2) {
            addCriterion("hzz_sort between", value1, value2, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andHzzSortNotBetween(String value1, String value2) {
            addCriterion("hzz_sort not between", value1, value2, "hzzSort");
            return (Criteria) this;
        }

        public Criteria andPoliticIsNull() {
            addCriterion("politic is null");
            return (Criteria) this;
        }

        public Criteria andPoliticIsNotNull() {
            addCriterion("politic is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticEqualTo(String value) {
            addCriterion("politic =", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotEqualTo(String value) {
            addCriterion("politic <>", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticGreaterThan(String value) {
            addCriterion("politic >", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticGreaterThanOrEqualTo(String value) {
            addCriterion("politic >=", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLessThan(String value) {
            addCriterion("politic <", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLessThanOrEqualTo(String value) {
            addCriterion("politic <=", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLike(String value) {
            addCriterion("politic like", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotLike(String value) {
            addCriterion("politic not like", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticIn(List<String> values) {
            addCriterion("politic in", values, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotIn(List<String> values) {
            addCriterion("politic not in", values, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticBetween(String value1, String value2) {
            addCriterion("politic between", value1, value2, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotBetween(String value1, String value2) {
            addCriterion("politic not between", value1, value2, "politic");
            return (Criteria) this;
        }

        public Criteria andDatePartyIsNull() {
            addCriterion("date_party is null");
            return (Criteria) this;
        }

        public Criteria andDatePartyIsNotNull() {
            addCriterion("date_party is not null");
            return (Criteria) this;
        }

        public Criteria andDatePartyEqualTo(String value) {
            addCriterion("date_party =", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyNotEqualTo(String value) {
            addCriterion("date_party <>", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyGreaterThan(String value) {
            addCriterion("date_party >", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyGreaterThanOrEqualTo(String value) {
            addCriterion("date_party >=", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyLessThan(String value) {
            addCriterion("date_party <", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyLessThanOrEqualTo(String value) {
            addCriterion("date_party <=", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyLike(String value) {
            addCriterion("date_party like", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyNotLike(String value) {
            addCriterion("date_party not like", value, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyIn(List<String> values) {
            addCriterion("date_party in", values, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyNotIn(List<String> values) {
            addCriterion("date_party not in", values, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyBetween(String value1, String value2) {
            addCriterion("date_party between", value1, value2, "dateParty");
            return (Criteria) this;
        }

        public Criteria andDatePartyNotBetween(String value1, String value2) {
            addCriterion("date_party not between", value1, value2, "dateParty");
            return (Criteria) this;
        }

        public Criteria andInterestIsNull() {
            addCriterion("interest is null");
            return (Criteria) this;
        }

        public Criteria andInterestIsNotNull() {
            addCriterion("interest is not null");
            return (Criteria) this;
        }

        public Criteria andInterestEqualTo(String value) {
            addCriterion("interest =", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotEqualTo(String value) {
            addCriterion("interest <>", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestGreaterThan(String value) {
            addCriterion("interest >", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestGreaterThanOrEqualTo(String value) {
            addCriterion("interest >=", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestLessThan(String value) {
            addCriterion("interest <", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestLessThanOrEqualTo(String value) {
            addCriterion("interest <=", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestLike(String value) {
            addCriterion("interest like", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotLike(String value) {
            addCriterion("interest not like", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestIn(List<String> values) {
            addCriterion("interest in", values, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotIn(List<String> values) {
            addCriterion("interest not in", values, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestBetween(String value1, String value2) {
            addCriterion("interest between", value1, value2, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotBetween(String value1, String value2) {
            addCriterion("interest not between", value1, value2, "interest");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceIsNull() {
            addCriterion("spouse_place is null");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceIsNotNull() {
            addCriterion("spouse_place is not null");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceEqualTo(String value) {
            addCriterion("spouse_place =", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceNotEqualTo(String value) {
            addCriterion("spouse_place <>", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceGreaterThan(String value) {
            addCriterion("spouse_place >", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceGreaterThanOrEqualTo(String value) {
            addCriterion("spouse_place >=", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceLessThan(String value) {
            addCriterion("spouse_place <", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceLessThanOrEqualTo(String value) {
            addCriterion("spouse_place <=", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceLike(String value) {
            addCriterion("spouse_place like", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceNotLike(String value) {
            addCriterion("spouse_place not like", value, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceIn(List<String> values) {
            addCriterion("spouse_place in", values, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceNotIn(List<String> values) {
            addCriterion("spouse_place not in", values, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceBetween(String value1, String value2) {
            addCriterion("spouse_place between", value1, value2, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andSpousePlaceNotBetween(String value1, String value2) {
            addCriterion("spouse_place not between", value1, value2, "spousePlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceIsNull() {
            addCriterion("parent_place is null");
            return (Criteria) this;
        }

        public Criteria andParentPlaceIsNotNull() {
            addCriterion("parent_place is not null");
            return (Criteria) this;
        }

        public Criteria andParentPlaceEqualTo(String value) {
            addCriterion("parent_place =", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceNotEqualTo(String value) {
            addCriterion("parent_place <>", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceGreaterThan(String value) {
            addCriterion("parent_place >", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("parent_place >=", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceLessThan(String value) {
            addCriterion("parent_place <", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceLessThanOrEqualTo(String value) {
            addCriterion("parent_place <=", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceLike(String value) {
            addCriterion("parent_place like", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceNotLike(String value) {
            addCriterion("parent_place not like", value, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceIn(List<String> values) {
            addCriterion("parent_place in", values, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceNotIn(List<String> values) {
            addCriterion("parent_place not in", values, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceBetween(String value1, String value2) {
            addCriterion("parent_place between", value1, value2, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andParentPlaceNotBetween(String value1, String value2) {
            addCriterion("parent_place not between", value1, value2, "parentPlace");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIsNull() {
            addCriterion("blood_type is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIsNotNull() {
            addCriterion("blood_type is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEqualTo(String value) {
            addCriterion("blood_type =", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotEqualTo(String value) {
            addCriterion("blood_type <>", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeGreaterThan(String value) {
            addCriterion("blood_type >", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type >=", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLessThan(String value) {
            addCriterion("blood_type <", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLessThanOrEqualTo(String value) {
            addCriterion("blood_type <=", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLike(String value) {
            addCriterion("blood_type like", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotLike(String value) {
            addCriterion("blood_type not like", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIn(List<String> values) {
            addCriterion("blood_type in", values, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotIn(List<String> values) {
            addCriterion("blood_type not in", values, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeBetween(String value1, String value2) {
            addCriterion("blood_type between", value1, value2, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotBetween(String value1, String value2) {
            addCriterion("blood_type not between", value1, value2, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMIsNull() {
            addCriterion("blood_type_M is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMIsNotNull() {
            addCriterion("blood_type_M is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMEqualTo(String value) {
            addCriterion("blood_type_M =", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMNotEqualTo(String value) {
            addCriterion("blood_type_M <>", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMGreaterThan(String value) {
            addCriterion("blood_type_M >", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type_M >=", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMLessThan(String value) {
            addCriterion("blood_type_M <", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMLessThanOrEqualTo(String value) {
            addCriterion("blood_type_M <=", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMLike(String value) {
            addCriterion("blood_type_M like", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMNotLike(String value) {
            addCriterion("blood_type_M not like", value, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMIn(List<String> values) {
            addCriterion("blood_type_M in", values, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMNotIn(List<String> values) {
            addCriterion("blood_type_M not in", values, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMBetween(String value1, String value2) {
            addCriterion("blood_type_M between", value1, value2, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeMNotBetween(String value1, String value2) {
            addCriterion("blood_type_M not between", value1, value2, "bloodTypeM");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEIsNull() {
            addCriterion("blood_type_E is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEIsNotNull() {
            addCriterion("blood_type_E is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEEqualTo(String value) {
            addCriterion("blood_type_E =", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeENotEqualTo(String value) {
            addCriterion("blood_type_E <>", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEGreaterThan(String value) {
            addCriterion("blood_type_E >", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type_E >=", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeELessThan(String value) {
            addCriterion("blood_type_E <", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeELessThanOrEqualTo(String value) {
            addCriterion("blood_type_E <=", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeELike(String value) {
            addCriterion("blood_type_E like", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeENotLike(String value) {
            addCriterion("blood_type_E not like", value, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEIn(List<String> values) {
            addCriterion("blood_type_E in", values, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeENotIn(List<String> values) {
            addCriterion("blood_type_E not in", values, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEBetween(String value1, String value2) {
            addCriterion("blood_type_E between", value1, value2, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andBloodTypeENotBetween(String value1, String value2) {
            addCriterion("blood_type_E not between", value1, value2, "bloodTypeE");
            return (Criteria) this;
        }

        public Criteria andHealthIsNull() {
            addCriterion("health is null");
            return (Criteria) this;
        }

        public Criteria andHealthIsNotNull() {
            addCriterion("health is not null");
            return (Criteria) this;
        }

        public Criteria andHealthEqualTo(String value) {
            addCriterion("health =", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthNotEqualTo(String value) {
            addCriterion("health <>", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthGreaterThan(String value) {
            addCriterion("health >", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthGreaterThanOrEqualTo(String value) {
            addCriterion("health >=", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthLessThan(String value) {
            addCriterion("health <", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthLessThanOrEqualTo(String value) {
            addCriterion("health <=", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthLike(String value) {
            addCriterion("health like", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthNotLike(String value) {
            addCriterion("health not like", value, "health");
            return (Criteria) this;
        }

        public Criteria andHealthIn(List<String> values) {
            addCriterion("health in", values, "health");
            return (Criteria) this;
        }

        public Criteria andHealthNotIn(List<String> values) {
            addCriterion("health not in", values, "health");
            return (Criteria) this;
        }

        public Criteria andHealthBetween(String value1, String value2) {
            addCriterion("health between", value1, value2, "health");
            return (Criteria) this;
        }

        public Criteria andHealthNotBetween(String value1, String value2) {
            addCriterion("health not between", value1, value2, "health");
            return (Criteria) this;
        }

        public Criteria andHealthMIsNull() {
            addCriterion("health_M is null");
            return (Criteria) this;
        }

        public Criteria andHealthMIsNotNull() {
            addCriterion("health_M is not null");
            return (Criteria) this;
        }

        public Criteria andHealthMEqualTo(String value) {
            addCriterion("health_M =", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMNotEqualTo(String value) {
            addCriterion("health_M <>", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMGreaterThan(String value) {
            addCriterion("health_M >", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMGreaterThanOrEqualTo(String value) {
            addCriterion("health_M >=", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMLessThan(String value) {
            addCriterion("health_M <", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMLessThanOrEqualTo(String value) {
            addCriterion("health_M <=", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMLike(String value) {
            addCriterion("health_M like", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMNotLike(String value) {
            addCriterion("health_M not like", value, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMIn(List<String> values) {
            addCriterion("health_M in", values, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMNotIn(List<String> values) {
            addCriterion("health_M not in", values, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMBetween(String value1, String value2) {
            addCriterion("health_M between", value1, value2, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthMNotBetween(String value1, String value2) {
            addCriterion("health_M not between", value1, value2, "healthM");
            return (Criteria) this;
        }

        public Criteria andHealthEIsNull() {
            addCriterion("health_E is null");
            return (Criteria) this;
        }

        public Criteria andHealthEIsNotNull() {
            addCriterion("health_E is not null");
            return (Criteria) this;
        }

        public Criteria andHealthEEqualTo(String value) {
            addCriterion("health_E =", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthENotEqualTo(String value) {
            addCriterion("health_E <>", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthEGreaterThan(String value) {
            addCriterion("health_E >", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthEGreaterThanOrEqualTo(String value) {
            addCriterion("health_E >=", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthELessThan(String value) {
            addCriterion("health_E <", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthELessThanOrEqualTo(String value) {
            addCriterion("health_E <=", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthELike(String value) {
            addCriterion("health_E like", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthENotLike(String value) {
            addCriterion("health_E not like", value, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthEIn(List<String> values) {
            addCriterion("health_E in", values, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthENotIn(List<String> values) {
            addCriterion("health_E not in", values, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthEBetween(String value1, String value2) {
            addCriterion("health_E between", value1, value2, "healthE");
            return (Criteria) this;
        }

        public Criteria andHealthENotBetween(String value1, String value2) {
            addCriterion("health_E not between", value1, value2, "healthE");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceIsNull() {
            addCriterion("files_place is null");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceIsNotNull() {
            addCriterion("files_place is not null");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceEqualTo(String value) {
            addCriterion("files_place =", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceNotEqualTo(String value) {
            addCriterion("files_place <>", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceGreaterThan(String value) {
            addCriterion("files_place >", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("files_place >=", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceLessThan(String value) {
            addCriterion("files_place <", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceLessThanOrEqualTo(String value) {
            addCriterion("files_place <=", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceLike(String value) {
            addCriterion("files_place like", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceNotLike(String value) {
            addCriterion("files_place not like", value, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceIn(List<String> values) {
            addCriterion("files_place in", values, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceNotIn(List<String> values) {
            addCriterion("files_place not in", values, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceBetween(String value1, String value2) {
            addCriterion("files_place between", value1, value2, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andFilesPlaceNotBetween(String value1, String value2) {
            addCriterion("files_place not between", value1, value2, "filesPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceIsNull() {
            addCriterion("resident_place is null");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceIsNotNull() {
            addCriterion("resident_place is not null");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceEqualTo(String value) {
            addCriterion("resident_place =", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceNotEqualTo(String value) {
            addCriterion("resident_place <>", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceGreaterThan(String value) {
            addCriterion("resident_place >", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("resident_place >=", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceLessThan(String value) {
            addCriterion("resident_place <", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceLessThanOrEqualTo(String value) {
            addCriterion("resident_place <=", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceLike(String value) {
            addCriterion("resident_place like", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceNotLike(String value) {
            addCriterion("resident_place not like", value, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceIn(List<String> values) {
            addCriterion("resident_place in", values, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceNotIn(List<String> values) {
            addCriterion("resident_place not in", values, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceBetween(String value1, String value2) {
            addCriterion("resident_place between", value1, value2, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentPlaceNotBetween(String value1, String value2) {
            addCriterion("resident_place not between", value1, value2, "residentPlace");
            return (Criteria) this;
        }

        public Criteria andResidentTypeIsNull() {
            addCriterion("resident_type is null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeIsNotNull() {
            addCriterion("resident_type is not null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEqualTo(String value) {
            addCriterion("resident_type =", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeNotEqualTo(String value) {
            addCriterion("resident_type <>", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeGreaterThan(String value) {
            addCriterion("resident_type >", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeGreaterThanOrEqualTo(String value) {
            addCriterion("resident_type >=", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeLessThan(String value) {
            addCriterion("resident_type <", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeLessThanOrEqualTo(String value) {
            addCriterion("resident_type <=", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeLike(String value) {
            addCriterion("resident_type like", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeNotLike(String value) {
            addCriterion("resident_type not like", value, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeIn(List<String> values) {
            addCriterion("resident_type in", values, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeNotIn(List<String> values) {
            addCriterion("resident_type not in", values, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeBetween(String value1, String value2) {
            addCriterion("resident_type between", value1, value2, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeNotBetween(String value1, String value2) {
            addCriterion("resident_type not between", value1, value2, "residentType");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMIsNull() {
            addCriterion("resident_type_M is null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMIsNotNull() {
            addCriterion("resident_type_M is not null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMEqualTo(String value) {
            addCriterion("resident_type_M =", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMNotEqualTo(String value) {
            addCriterion("resident_type_M <>", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMGreaterThan(String value) {
            addCriterion("resident_type_M >", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMGreaterThanOrEqualTo(String value) {
            addCriterion("resident_type_M >=", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMLessThan(String value) {
            addCriterion("resident_type_M <", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMLessThanOrEqualTo(String value) {
            addCriterion("resident_type_M <=", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMLike(String value) {
            addCriterion("resident_type_M like", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMNotLike(String value) {
            addCriterion("resident_type_M not like", value, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMIn(List<String> values) {
            addCriterion("resident_type_M in", values, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMNotIn(List<String> values) {
            addCriterion("resident_type_M not in", values, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMBetween(String value1, String value2) {
            addCriterion("resident_type_M between", value1, value2, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeMNotBetween(String value1, String value2) {
            addCriterion("resident_type_M not between", value1, value2, "residentTypeM");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEIsNull() {
            addCriterion("resident_type_E is null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEIsNotNull() {
            addCriterion("resident_type_E is not null");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEEqualTo(String value) {
            addCriterion("resident_type_E =", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeENotEqualTo(String value) {
            addCriterion("resident_type_E <>", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEGreaterThan(String value) {
            addCriterion("resident_type_E >", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEGreaterThanOrEqualTo(String value) {
            addCriterion("resident_type_E >=", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeELessThan(String value) {
            addCriterion("resident_type_E <", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeELessThanOrEqualTo(String value) {
            addCriterion("resident_type_E <=", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeELike(String value) {
            addCriterion("resident_type_E like", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeENotLike(String value) {
            addCriterion("resident_type_E not like", value, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEIn(List<String> values) {
            addCriterion("resident_type_E in", values, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeENotIn(List<String> values) {
            addCriterion("resident_type_E not in", values, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeEBetween(String value1, String value2) {
            addCriterion("resident_type_E between", value1, value2, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andResidentTypeENotBetween(String value1, String value2) {
            addCriterion("resident_type_E not between", value1, value2, "residentTypeE");
            return (Criteria) this;
        }

        public Criteria andCardidInlandIsNull() {
            addCriterion("cardid_inland is null");
            return (Criteria) this;
        }

        public Criteria andCardidInlandIsNotNull() {
            addCriterion("cardid_inland is not null");
            return (Criteria) this;
        }

        public Criteria andCardidInlandEqualTo(String value) {
            addCriterion("cardid_inland =", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandNotEqualTo(String value) {
            addCriterion("cardid_inland <>", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandGreaterThan(String value) {
            addCriterion("cardid_inland >", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandGreaterThanOrEqualTo(String value) {
            addCriterion("cardid_inland >=", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandLessThan(String value) {
            addCriterion("cardid_inland <", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandLessThanOrEqualTo(String value) {
            addCriterion("cardid_inland <=", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandLike(String value) {
            addCriterion("cardid_inland like", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandNotLike(String value) {
            addCriterion("cardid_inland not like", value, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandIn(List<String> values) {
            addCriterion("cardid_inland in", values, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandNotIn(List<String> values) {
            addCriterion("cardid_inland not in", values, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandBetween(String value1, String value2) {
            addCriterion("cardid_inland between", value1, value2, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidInlandNotBetween(String value1, String value2) {
            addCriterion("cardid_inland not between", value1, value2, "cardidInland");
            return (Criteria) this;
        }

        public Criteria andCardidHmIsNull() {
            addCriterion("cardid_hm is null");
            return (Criteria) this;
        }

        public Criteria andCardidHmIsNotNull() {
            addCriterion("cardid_hm is not null");
            return (Criteria) this;
        }

        public Criteria andCardidHmEqualTo(String value) {
            addCriterion("cardid_hm =", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmNotEqualTo(String value) {
            addCriterion("cardid_hm <>", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmGreaterThan(String value) {
            addCriterion("cardid_hm >", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmGreaterThanOrEqualTo(String value) {
            addCriterion("cardid_hm >=", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmLessThan(String value) {
            addCriterion("cardid_hm <", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmLessThanOrEqualTo(String value) {
            addCriterion("cardid_hm <=", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmLike(String value) {
            addCriterion("cardid_hm like", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmNotLike(String value) {
            addCriterion("cardid_hm not like", value, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmIn(List<String> values) {
            addCriterion("cardid_hm in", values, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmNotIn(List<String> values) {
            addCriterion("cardid_hm not in", values, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmBetween(String value1, String value2) {
            addCriterion("cardid_hm between", value1, value2, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmNotBetween(String value1, String value2) {
            addCriterion("cardid_hm not between", value1, value2, "cardidHm");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateIsNull() {
            addCriterion("cardid_hm_expdate is null");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateIsNotNull() {
            addCriterion("cardid_hm_expdate is not null");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateEqualTo(String value) {
            addCriterion("cardid_hm_expdate =", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateNotEqualTo(String value) {
            addCriterion("cardid_hm_expdate <>", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateGreaterThan(String value) {
            addCriterion("cardid_hm_expdate >", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateGreaterThanOrEqualTo(String value) {
            addCriterion("cardid_hm_expdate >=", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateLessThan(String value) {
            addCriterion("cardid_hm_expdate <", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateLessThanOrEqualTo(String value) {
            addCriterion("cardid_hm_expdate <=", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateLike(String value) {
            addCriterion("cardid_hm_expdate like", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateNotLike(String value) {
            addCriterion("cardid_hm_expdate not like", value, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateIn(List<String> values) {
            addCriterion("cardid_hm_expdate in", values, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateNotIn(List<String> values) {
            addCriterion("cardid_hm_expdate not in", values, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateBetween(String value1, String value2) {
            addCriterion("cardid_hm_expdate between", value1, value2, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidHmExpdateNotBetween(String value1, String value2) {
            addCriterion("cardid_hm_expdate not between", value1, value2, "cardidHmExpdate");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandIsNull() {
            addCriterion("cardid_outland is null");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandIsNotNull() {
            addCriterion("cardid_outland is not null");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandEqualTo(String value) {
            addCriterion("cardid_outland =", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandNotEqualTo(String value) {
            addCriterion("cardid_outland <>", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandGreaterThan(String value) {
            addCriterion("cardid_outland >", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandGreaterThanOrEqualTo(String value) {
            addCriterion("cardid_outland >=", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandLessThan(String value) {
            addCriterion("cardid_outland <", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandLessThanOrEqualTo(String value) {
            addCriterion("cardid_outland <=", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandLike(String value) {
            addCriterion("cardid_outland like", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandNotLike(String value) {
            addCriterion("cardid_outland not like", value, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandIn(List<String> values) {
            addCriterion("cardid_outland in", values, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandNotIn(List<String> values) {
            addCriterion("cardid_outland not in", values, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandBetween(String value1, String value2) {
            addCriterion("cardid_outland between", value1, value2, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andCardidOutlandNotBetween(String value1, String value2) {
            addCriterion("cardid_outland not between", value1, value2, "cardidOutland");
            return (Criteria) this;
        }

        public Criteria andEntryTypeIsNull() {
            addCriterion("entry_type is null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeIsNotNull() {
            addCriterion("entry_type is not null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEqualTo(String value) {
            addCriterion("entry_type =", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeNotEqualTo(String value) {
            addCriterion("entry_type <>", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeGreaterThan(String value) {
            addCriterion("entry_type >", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("entry_type >=", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeLessThan(String value) {
            addCriterion("entry_type <", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeLessThanOrEqualTo(String value) {
            addCriterion("entry_type <=", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeLike(String value) {
            addCriterion("entry_type like", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeNotLike(String value) {
            addCriterion("entry_type not like", value, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeIn(List<String> values) {
            addCriterion("entry_type in", values, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeNotIn(List<String> values) {
            addCriterion("entry_type not in", values, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeBetween(String value1, String value2) {
            addCriterion("entry_type between", value1, value2, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeNotBetween(String value1, String value2) {
            addCriterion("entry_type not between", value1, value2, "entryType");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMIsNull() {
            addCriterion("entry_type_M is null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMIsNotNull() {
            addCriterion("entry_type_M is not null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMEqualTo(String value) {
            addCriterion("entry_type_M =", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMNotEqualTo(String value) {
            addCriterion("entry_type_M <>", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMGreaterThan(String value) {
            addCriterion("entry_type_M >", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMGreaterThanOrEqualTo(String value) {
            addCriterion("entry_type_M >=", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMLessThan(String value) {
            addCriterion("entry_type_M <", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMLessThanOrEqualTo(String value) {
            addCriterion("entry_type_M <=", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMLike(String value) {
            addCriterion("entry_type_M like", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMNotLike(String value) {
            addCriterion("entry_type_M not like", value, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMIn(List<String> values) {
            addCriterion("entry_type_M in", values, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMNotIn(List<String> values) {
            addCriterion("entry_type_M not in", values, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMBetween(String value1, String value2) {
            addCriterion("entry_type_M between", value1, value2, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeMNotBetween(String value1, String value2) {
            addCriterion("entry_type_M not between", value1, value2, "entryTypeM");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEIsNull() {
            addCriterion("entry_type_E is null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEIsNotNull() {
            addCriterion("entry_type_E is not null");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEEqualTo(String value) {
            addCriterion("entry_type_E =", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeENotEqualTo(String value) {
            addCriterion("entry_type_E <>", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEGreaterThan(String value) {
            addCriterion("entry_type_E >", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEGreaterThanOrEqualTo(String value) {
            addCriterion("entry_type_E >=", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeELessThan(String value) {
            addCriterion("entry_type_E <", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeELessThanOrEqualTo(String value) {
            addCriterion("entry_type_E <=", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeELike(String value) {
            addCriterion("entry_type_E like", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeENotLike(String value) {
            addCriterion("entry_type_E not like", value, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEIn(List<String> values) {
            addCriterion("entry_type_E in", values, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeENotIn(List<String> values) {
            addCriterion("entry_type_E not in", values, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeEBetween(String value1, String value2) {
            addCriterion("entry_type_E between", value1, value2, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andEntryTypeENotBetween(String value1, String value2) {
            addCriterion("entry_type_E not between", value1, value2, "entryTypeE");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyIsNull() {
            addCriterion("social_care_company is null");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyIsNotNull() {
            addCriterion("social_care_company is not null");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyEqualTo(String value) {
            addCriterion("social_care_company =", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyNotEqualTo(String value) {
            addCriterion("social_care_company <>", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyGreaterThan(String value) {
            addCriterion("social_care_company >", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("social_care_company >=", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyLessThan(String value) {
            addCriterion("social_care_company <", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyLessThanOrEqualTo(String value) {
            addCriterion("social_care_company <=", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyLike(String value) {
            addCriterion("social_care_company like", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyNotLike(String value) {
            addCriterion("social_care_company not like", value, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyIn(List<String> values) {
            addCriterion("social_care_company in", values, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyNotIn(List<String> values) {
            addCriterion("social_care_company not in", values, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyBetween(String value1, String value2) {
            addCriterion("social_care_company between", value1, value2, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andSocialCareCompanyNotBetween(String value1, String value2) {
            addCriterion("social_care_company not between", value1, value2, "socialCareCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyIsNull() {
            addCriterion("acc_fund_company is null");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyIsNotNull() {
            addCriterion("acc_fund_company is not null");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyEqualTo(String value) {
            addCriterion("acc_fund_company =", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyNotEqualTo(String value) {
            addCriterion("acc_fund_company <>", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyGreaterThan(String value) {
            addCriterion("acc_fund_company >", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("acc_fund_company >=", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyLessThan(String value) {
            addCriterion("acc_fund_company <", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyLessThanOrEqualTo(String value) {
            addCriterion("acc_fund_company <=", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyLike(String value) {
            addCriterion("acc_fund_company like", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyNotLike(String value) {
            addCriterion("acc_fund_company not like", value, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyIn(List<String> values) {
            addCriterion("acc_fund_company in", values, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyNotIn(List<String> values) {
            addCriterion("acc_fund_company not in", values, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyBetween(String value1, String value2) {
            addCriterion("acc_fund_company between", value1, value2, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andAccFundCompanyNotBetween(String value1, String value2) {
            addCriterion("acc_fund_company not between", value1, value2, "accFundCompany");
            return (Criteria) this;
        }

        public Criteria andNotesIsNull() {
            addCriterion("notes is null");
            return (Criteria) this;
        }

        public Criteria andNotesIsNotNull() {
            addCriterion("notes is not null");
            return (Criteria) this;
        }

        public Criteria andNotesEqualTo(String value) {
            addCriterion("notes =", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotEqualTo(String value) {
            addCriterion("notes <>", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThan(String value) {
            addCriterion("notes >", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThanOrEqualTo(String value) {
            addCriterion("notes >=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThan(String value) {
            addCriterion("notes <", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThanOrEqualTo(String value) {
            addCriterion("notes <=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLike(String value) {
            addCriterion("notes like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotLike(String value) {
            addCriterion("notes not like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesIn(List<String> values) {
            addCriterion("notes in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotIn(List<String> values) {
            addCriterion("notes not in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesBetween(String value1, String value2) {
            addCriterion("notes between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotBetween(String value1, String value2) {
            addCriterion("notes not between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesMIsNull() {
            addCriterion("notes_M is null");
            return (Criteria) this;
        }

        public Criteria andNotesMIsNotNull() {
            addCriterion("notes_M is not null");
            return (Criteria) this;
        }

        public Criteria andNotesMEqualTo(String value) {
            addCriterion("notes_M =", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMNotEqualTo(String value) {
            addCriterion("notes_M <>", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMGreaterThan(String value) {
            addCriterion("notes_M >", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMGreaterThanOrEqualTo(String value) {
            addCriterion("notes_M >=", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMLessThan(String value) {
            addCriterion("notes_M <", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMLessThanOrEqualTo(String value) {
            addCriterion("notes_M <=", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMLike(String value) {
            addCriterion("notes_M like", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMNotLike(String value) {
            addCriterion("notes_M not like", value, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMIn(List<String> values) {
            addCriterion("notes_M in", values, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMNotIn(List<String> values) {
            addCriterion("notes_M not in", values, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMBetween(String value1, String value2) {
            addCriterion("notes_M between", value1, value2, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesMNotBetween(String value1, String value2) {
            addCriterion("notes_M not between", value1, value2, "notesM");
            return (Criteria) this;
        }

        public Criteria andNotesEIsNull() {
            addCriterion("notes_E is null");
            return (Criteria) this;
        }

        public Criteria andNotesEIsNotNull() {
            addCriterion("notes_E is not null");
            return (Criteria) this;
        }

        public Criteria andNotesEEqualTo(String value) {
            addCriterion("notes_E =", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesENotEqualTo(String value) {
            addCriterion("notes_E <>", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesEGreaterThan(String value) {
            addCriterion("notes_E >", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesEGreaterThanOrEqualTo(String value) {
            addCriterion("notes_E >=", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesELessThan(String value) {
            addCriterion("notes_E <", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesELessThanOrEqualTo(String value) {
            addCriterion("notes_E <=", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesELike(String value) {
            addCriterion("notes_E like", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesENotLike(String value) {
            addCriterion("notes_E not like", value, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesEIn(List<String> values) {
            addCriterion("notes_E in", values, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesENotIn(List<String> values) {
            addCriterion("notes_E not in", values, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesEBetween(String value1, String value2) {
            addCriterion("notes_E between", value1, value2, "notesE");
            return (Criteria) this;
        }

        public Criteria andNotesENotBetween(String value1, String value2) {
            addCriterion("notes_E not between", value1, value2, "notesE");
            return (Criteria) this;
        }

        public Criteria andOrgehIsNull() {
            addCriterion("ORGEH is null");
            return (Criteria) this;
        }

        public Criteria andOrgehIsNotNull() {
            addCriterion("ORGEH is not null");
            return (Criteria) this;
        }

        public Criteria andOrgehEqualTo(String value) {
            addCriterion("ORGEH =", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehNotEqualTo(String value) {
            addCriterion("ORGEH <>", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehGreaterThan(String value) {
            addCriterion("ORGEH >", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehGreaterThanOrEqualTo(String value) {
            addCriterion("ORGEH >=", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehLessThan(String value) {
            addCriterion("ORGEH <", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehLessThanOrEqualTo(String value) {
            addCriterion("ORGEH <=", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehLike(String value) {
            addCriterion("ORGEH like", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehNotLike(String value) {
            addCriterion("ORGEH not like", value, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehIn(List<String> values) {
            addCriterion("ORGEH in", values, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehNotIn(List<String> values) {
            addCriterion("ORGEH not in", values, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehBetween(String value1, String value2) {
            addCriterion("ORGEH between", value1, value2, "orgeh");
            return (Criteria) this;
        }

        public Criteria andOrgehNotBetween(String value1, String value2) {
            addCriterion("ORGEH not between", value1, value2, "orgeh");
            return (Criteria) this;
        }

        public Criteria andBukrsIsNull() {
            addCriterion("BUKRS is null");
            return (Criteria) this;
        }

        public Criteria andBukrsIsNotNull() {
            addCriterion("BUKRS is not null");
            return (Criteria) this;
        }

        public Criteria andBukrsEqualTo(String value) {
            addCriterion("BUKRS =", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsNotEqualTo(String value) {
            addCriterion("BUKRS <>", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsGreaterThan(String value) {
            addCriterion("BUKRS >", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsGreaterThanOrEqualTo(String value) {
            addCriterion("BUKRS >=", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsLessThan(String value) {
            addCriterion("BUKRS <", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsLessThanOrEqualTo(String value) {
            addCriterion("BUKRS <=", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsLike(String value) {
            addCriterion("BUKRS like", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsNotLike(String value) {
            addCriterion("BUKRS not like", value, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsIn(List<String> values) {
            addCriterion("BUKRS in", values, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsNotIn(List<String> values) {
            addCriterion("BUKRS not in", values, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsBetween(String value1, String value2) {
            addCriterion("BUKRS between", value1, value2, "bukrs");
            return (Criteria) this;
        }

        public Criteria andBukrsNotBetween(String value1, String value2) {
            addCriterion("BUKRS not between", value1, value2, "bukrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsIsNull() {
            addCriterion("ABKRS is null");
            return (Criteria) this;
        }

        public Criteria andAbkrsIsNotNull() {
            addCriterion("ABKRS is not null");
            return (Criteria) this;
        }

        public Criteria andAbkrsEqualTo(String value) {
            addCriterion("ABKRS =", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsNotEqualTo(String value) {
            addCriterion("ABKRS <>", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsGreaterThan(String value) {
            addCriterion("ABKRS >", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsGreaterThanOrEqualTo(String value) {
            addCriterion("ABKRS >=", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsLessThan(String value) {
            addCriterion("ABKRS <", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsLessThanOrEqualTo(String value) {
            addCriterion("ABKRS <=", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsLike(String value) {
            addCriterion("ABKRS like", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsNotLike(String value) {
            addCriterion("ABKRS not like", value, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsIn(List<String> values) {
            addCriterion("ABKRS in", values, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsNotIn(List<String> values) {
            addCriterion("ABKRS not in", values, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsBetween(String value1, String value2) {
            addCriterion("ABKRS between", value1, value2, "abkrs");
            return (Criteria) this;
        }

        public Criteria andAbkrsNotBetween(String value1, String value2) {
            addCriterion("ABKRS not between", value1, value2, "abkrs");
            return (Criteria) this;
        }

        public Criteria andWerksIsNull() {
            addCriterion("WERKS is null");
            return (Criteria) this;
        }

        public Criteria andWerksIsNotNull() {
            addCriterion("WERKS is not null");
            return (Criteria) this;
        }

        public Criteria andWerksEqualTo(String value) {
            addCriterion("WERKS =", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotEqualTo(String value) {
            addCriterion("WERKS <>", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThan(String value) {
            addCriterion("WERKS >", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThanOrEqualTo(String value) {
            addCriterion("WERKS >=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThan(String value) {
            addCriterion("WERKS <", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThanOrEqualTo(String value) {
            addCriterion("WERKS <=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLike(String value) {
            addCriterion("WERKS like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotLike(String value) {
            addCriterion("WERKS not like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksIn(List<String> values) {
            addCriterion("WERKS in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotIn(List<String> values) {
            addCriterion("WERKS not in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksBetween(String value1, String value2) {
            addCriterion("WERKS between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotBetween(String value1, String value2) {
            addCriterion("WERKS not between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andBtrtlIsNull() {
            addCriterion("BTRTL is null");
            return (Criteria) this;
        }

        public Criteria andBtrtlIsNotNull() {
            addCriterion("BTRTL is not null");
            return (Criteria) this;
        }

        public Criteria andBtrtlEqualTo(String value) {
            addCriterion("BTRTL =", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlNotEqualTo(String value) {
            addCriterion("BTRTL <>", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlGreaterThan(String value) {
            addCriterion("BTRTL >", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlGreaterThanOrEqualTo(String value) {
            addCriterion("BTRTL >=", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlLessThan(String value) {
            addCriterion("BTRTL <", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlLessThanOrEqualTo(String value) {
            addCriterion("BTRTL <=", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlLike(String value) {
            addCriterion("BTRTL like", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlNotLike(String value) {
            addCriterion("BTRTL not like", value, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlIn(List<String> values) {
            addCriterion("BTRTL in", values, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlNotIn(List<String> values) {
            addCriterion("BTRTL not in", values, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlBetween(String value1, String value2) {
            addCriterion("BTRTL between", value1, value2, "btrtl");
            return (Criteria) this;
        }

        public Criteria andBtrtlNotBetween(String value1, String value2) {
            addCriterion("BTRTL not between", value1, value2, "btrtl");
            return (Criteria) this;
        }

        public Criteria andPersgIsNull() {
            addCriterion("PERSG is null");
            return (Criteria) this;
        }

        public Criteria andPersgIsNotNull() {
            addCriterion("PERSG is not null");
            return (Criteria) this;
        }

        public Criteria andPersgEqualTo(String value) {
            addCriterion("PERSG =", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgNotEqualTo(String value) {
            addCriterion("PERSG <>", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgGreaterThan(String value) {
            addCriterion("PERSG >", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgGreaterThanOrEqualTo(String value) {
            addCriterion("PERSG >=", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgLessThan(String value) {
            addCriterion("PERSG <", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgLessThanOrEqualTo(String value) {
            addCriterion("PERSG <=", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgLike(String value) {
            addCriterion("PERSG like", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgNotLike(String value) {
            addCriterion("PERSG not like", value, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgIn(List<String> values) {
            addCriterion("PERSG in", values, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgNotIn(List<String> values) {
            addCriterion("PERSG not in", values, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgBetween(String value1, String value2) {
            addCriterion("PERSG between", value1, value2, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgNotBetween(String value1, String value2) {
            addCriterion("PERSG not between", value1, value2, "persg");
            return (Criteria) this;
        }

        public Criteria andPersgtextIsNull() {
            addCriterion("PERSGTEXT is null");
            return (Criteria) this;
        }

        public Criteria andPersgtextIsNotNull() {
            addCriterion("PERSGTEXT is not null");
            return (Criteria) this;
        }

        public Criteria andPersgtextEqualTo(String value) {
            addCriterion("PERSGTEXT =", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextNotEqualTo(String value) {
            addCriterion("PERSGTEXT <>", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextGreaterThan(String value) {
            addCriterion("PERSGTEXT >", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextGreaterThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT >=", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextLessThan(String value) {
            addCriterion("PERSGTEXT <", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextLessThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT <=", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextLike(String value) {
            addCriterion("PERSGTEXT like", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextNotLike(String value) {
            addCriterion("PERSGTEXT not like", value, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextIn(List<String> values) {
            addCriterion("PERSGTEXT in", values, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextNotIn(List<String> values) {
            addCriterion("PERSGTEXT not in", values, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextBetween(String value1, String value2) {
            addCriterion("PERSGTEXT between", value1, value2, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersgtextNotBetween(String value1, String value2) {
            addCriterion("PERSGTEXT not between", value1, value2, "persgtext");
            return (Criteria) this;
        }

        public Criteria andPersklIsNull() {
            addCriterion("PERSKL is null");
            return (Criteria) this;
        }

        public Criteria andPersklIsNotNull() {
            addCriterion("PERSKL is not null");
            return (Criteria) this;
        }

        public Criteria andPersklEqualTo(String value) {
            addCriterion("PERSKL =", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklNotEqualTo(String value) {
            addCriterion("PERSKL <>", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklGreaterThan(String value) {
            addCriterion("PERSKL >", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklGreaterThanOrEqualTo(String value) {
            addCriterion("PERSKL >=", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklLessThan(String value) {
            addCriterion("PERSKL <", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklLessThanOrEqualTo(String value) {
            addCriterion("PERSKL <=", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklLike(String value) {
            addCriterion("PERSKL like", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklNotLike(String value) {
            addCriterion("PERSKL not like", value, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklIn(List<String> values) {
            addCriterion("PERSKL in", values, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklNotIn(List<String> values) {
            addCriterion("PERSKL not in", values, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklBetween(String value1, String value2) {
            addCriterion("PERSKL between", value1, value2, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersklNotBetween(String value1, String value2) {
            addCriterion("PERSKL not between", value1, value2, "perskl");
            return (Criteria) this;
        }

        public Criteria andPersktextIsNull() {
            addCriterion("PERSKTEXT is null");
            return (Criteria) this;
        }

        public Criteria andPersktextIsNotNull() {
            addCriterion("PERSKTEXT is not null");
            return (Criteria) this;
        }

        public Criteria andPersktextEqualTo(String value) {
            addCriterion("PERSKTEXT =", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextNotEqualTo(String value) {
            addCriterion("PERSKTEXT <>", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextGreaterThan(String value) {
            addCriterion("PERSKTEXT >", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextGreaterThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT >=", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextLessThan(String value) {
            addCriterion("PERSKTEXT <", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextLessThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT <=", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextLike(String value) {
            addCriterion("PERSKTEXT like", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextNotLike(String value) {
            addCriterion("PERSKTEXT not like", value, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextIn(List<String> values) {
            addCriterion("PERSKTEXT in", values, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextNotIn(List<String> values) {
            addCriterion("PERSKTEXT not in", values, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextBetween(String value1, String value2) {
            addCriterion("PERSKTEXT between", value1, value2, "persktext");
            return (Criteria) this;
        }

        public Criteria andPersktextNotBetween(String value1, String value2) {
            addCriterion("PERSKTEXT not between", value1, value2, "persktext");
            return (Criteria) this;
        }

        public Criteria andRecoverydateIsNull() {
            addCriterion("recoverydate is null");
            return (Criteria) this;
        }

        public Criteria andRecoverydateIsNotNull() {
            addCriterion("recoverydate is not null");
            return (Criteria) this;
        }

        public Criteria andRecoverydateEqualTo(String value) {
            addCriterion("recoverydate =", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateNotEqualTo(String value) {
            addCriterion("recoverydate <>", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateGreaterThan(String value) {
            addCriterion("recoverydate >", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateGreaterThanOrEqualTo(String value) {
            addCriterion("recoverydate >=", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateLessThan(String value) {
            addCriterion("recoverydate <", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateLessThanOrEqualTo(String value) {
            addCriterion("recoverydate <=", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateLike(String value) {
            addCriterion("recoverydate like", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateNotLike(String value) {
            addCriterion("recoverydate not like", value, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateIn(List<String> values) {
            addCriterion("recoverydate in", values, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateNotIn(List<String> values) {
            addCriterion("recoverydate not in", values, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateBetween(String value1, String value2) {
            addCriterion("recoverydate between", value1, value2, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverydateNotBetween(String value1, String value2) {
            addCriterion("recoverydate not between", value1, value2, "recoverydate");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthIsNull() {
            addCriterion("recoverymonth is null");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthIsNotNull() {
            addCriterion("recoverymonth is not null");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthEqualTo(String value) {
            addCriterion("recoverymonth =", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthNotEqualTo(String value) {
            addCriterion("recoverymonth <>", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthGreaterThan(String value) {
            addCriterion("recoverymonth >", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthGreaterThanOrEqualTo(String value) {
            addCriterion("recoverymonth >=", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthLessThan(String value) {
            addCriterion("recoverymonth <", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthLessThanOrEqualTo(String value) {
            addCriterion("recoverymonth <=", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthLike(String value) {
            addCriterion("recoverymonth like", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthNotLike(String value) {
            addCriterion("recoverymonth not like", value, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthIn(List<String> values) {
            addCriterion("recoverymonth in", values, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthNotIn(List<String> values) {
            addCriterion("recoverymonth not in", values, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthBetween(String value1, String value2) {
            addCriterion("recoverymonth between", value1, value2, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andRecoverymonthNotBetween(String value1, String value2) {
            addCriterion("recoverymonth not between", value1, value2, "recoverymonth");
            return (Criteria) this;
        }

        public Criteria andDimissiondateIsNull() {
            addCriterion("dimissiondate is null");
            return (Criteria) this;
        }

        public Criteria andDimissiondateIsNotNull() {
            addCriterion("dimissiondate is not null");
            return (Criteria) this;
        }

        public Criteria andDimissiondateEqualTo(String value) {
            addCriterion("dimissiondate =", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateNotEqualTo(String value) {
            addCriterion("dimissiondate <>", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateGreaterThan(String value) {
            addCriterion("dimissiondate >", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateGreaterThanOrEqualTo(String value) {
            addCriterion("dimissiondate >=", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateLessThan(String value) {
            addCriterion("dimissiondate <", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateLessThanOrEqualTo(String value) {
            addCriterion("dimissiondate <=", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateLike(String value) {
            addCriterion("dimissiondate like", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateNotLike(String value) {
            addCriterion("dimissiondate not like", value, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateIn(List<String> values) {
            addCriterion("dimissiondate in", values, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateNotIn(List<String> values) {
            addCriterion("dimissiondate not in", values, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateBetween(String value1, String value2) {
            addCriterion("dimissiondate between", value1, value2, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andDimissiondateNotBetween(String value1, String value2) {
            addCriterion("dimissiondate not between", value1, value2, "dimissiondate");
            return (Criteria) this;
        }

        public Criteria andNationMIsNull() {
            addCriterion("nation_M is null");
            return (Criteria) this;
        }

        public Criteria andNationMIsNotNull() {
            addCriterion("nation_M is not null");
            return (Criteria) this;
        }

        public Criteria andNationMEqualTo(String value) {
            addCriterion("nation_M =", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMNotEqualTo(String value) {
            addCriterion("nation_M <>", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMGreaterThan(String value) {
            addCriterion("nation_M >", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMGreaterThanOrEqualTo(String value) {
            addCriterion("nation_M >=", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMLessThan(String value) {
            addCriterion("nation_M <", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMLessThanOrEqualTo(String value) {
            addCriterion("nation_M <=", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMLike(String value) {
            addCriterion("nation_M like", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMNotLike(String value) {
            addCriterion("nation_M not like", value, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMIn(List<String> values) {
            addCriterion("nation_M in", values, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMNotIn(List<String> values) {
            addCriterion("nation_M not in", values, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMBetween(String value1, String value2) {
            addCriterion("nation_M between", value1, value2, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationMNotBetween(String value1, String value2) {
            addCriterion("nation_M not between", value1, value2, "nationM");
            return (Criteria) this;
        }

        public Criteria andNationEIsNull() {
            addCriterion("nation_E is null");
            return (Criteria) this;
        }

        public Criteria andNationEIsNotNull() {
            addCriterion("nation_E is not null");
            return (Criteria) this;
        }

        public Criteria andNationEEqualTo(String value) {
            addCriterion("nation_E =", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationENotEqualTo(String value) {
            addCriterion("nation_E <>", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationEGreaterThan(String value) {
            addCriterion("nation_E >", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationEGreaterThanOrEqualTo(String value) {
            addCriterion("nation_E >=", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationELessThan(String value) {
            addCriterion("nation_E <", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationELessThanOrEqualTo(String value) {
            addCriterion("nation_E <=", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationELike(String value) {
            addCriterion("nation_E like", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationENotLike(String value) {
            addCriterion("nation_E not like", value, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationEIn(List<String> values) {
            addCriterion("nation_E in", values, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationENotIn(List<String> values) {
            addCriterion("nation_E not in", values, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationEBetween(String value1, String value2) {
            addCriterion("nation_E between", value1, value2, "nationE");
            return (Criteria) this;
        }

        public Criteria andNationENotBetween(String value1, String value2) {
            addCriterion("nation_E not between", value1, value2, "nationE");
            return (Criteria) this;
        }

        public Criteria andPoliticMIsNull() {
            addCriterion("politic_M is null");
            return (Criteria) this;
        }

        public Criteria andPoliticMIsNotNull() {
            addCriterion("politic_M is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticMEqualTo(String value) {
            addCriterion("politic_M =", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMNotEqualTo(String value) {
            addCriterion("politic_M <>", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMGreaterThan(String value) {
            addCriterion("politic_M >", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMGreaterThanOrEqualTo(String value) {
            addCriterion("politic_M >=", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMLessThan(String value) {
            addCriterion("politic_M <", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMLessThanOrEqualTo(String value) {
            addCriterion("politic_M <=", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMLike(String value) {
            addCriterion("politic_M like", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMNotLike(String value) {
            addCriterion("politic_M not like", value, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMIn(List<String> values) {
            addCriterion("politic_M in", values, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMNotIn(List<String> values) {
            addCriterion("politic_M not in", values, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMBetween(String value1, String value2) {
            addCriterion("politic_M between", value1, value2, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticMNotBetween(String value1, String value2) {
            addCriterion("politic_M not between", value1, value2, "politicM");
            return (Criteria) this;
        }

        public Criteria andPoliticEIsNull() {
            addCriterion("politic_E is null");
            return (Criteria) this;
        }

        public Criteria andPoliticEIsNotNull() {
            addCriterion("politic_E is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticEEqualTo(String value) {
            addCriterion("politic_E =", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticENotEqualTo(String value) {
            addCriterion("politic_E <>", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticEGreaterThan(String value) {
            addCriterion("politic_E >", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticEGreaterThanOrEqualTo(String value) {
            addCriterion("politic_E >=", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticELessThan(String value) {
            addCriterion("politic_E <", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticELessThanOrEqualTo(String value) {
            addCriterion("politic_E <=", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticELike(String value) {
            addCriterion("politic_E like", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticENotLike(String value) {
            addCriterion("politic_E not like", value, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticEIn(List<String> values) {
            addCriterion("politic_E in", values, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticENotIn(List<String> values) {
            addCriterion("politic_E not in", values, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticEBetween(String value1, String value2) {
            addCriterion("politic_E between", value1, value2, "politicE");
            return (Criteria) this;
        }

        public Criteria andPoliticENotBetween(String value1, String value2) {
            addCriterion("politic_E not between", value1, value2, "politicE");
            return (Criteria) this;
        }

        public Criteria andCompanyMIsNull() {
            addCriterion("company_M is null");
            return (Criteria) this;
        }

        public Criteria andCompanyMIsNotNull() {
            addCriterion("company_M is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyMEqualTo(String value) {
            addCriterion("company_M =", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMNotEqualTo(String value) {
            addCriterion("company_M <>", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMGreaterThan(String value) {
            addCriterion("company_M >", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMGreaterThanOrEqualTo(String value) {
            addCriterion("company_M >=", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMLessThan(String value) {
            addCriterion("company_M <", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMLessThanOrEqualTo(String value) {
            addCriterion("company_M <=", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMLike(String value) {
            addCriterion("company_M like", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMNotLike(String value) {
            addCriterion("company_M not like", value, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMIn(List<String> values) {
            addCriterion("company_M in", values, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMNotIn(List<String> values) {
            addCriterion("company_M not in", values, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMBetween(String value1, String value2) {
            addCriterion("company_M between", value1, value2, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyMNotBetween(String value1, String value2) {
            addCriterion("company_M not between", value1, value2, "companyM");
            return (Criteria) this;
        }

        public Criteria andCompanyEIsNull() {
            addCriterion("company_E is null");
            return (Criteria) this;
        }

        public Criteria andCompanyEIsNotNull() {
            addCriterion("company_E is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEEqualTo(String value) {
            addCriterion("company_E =", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyENotEqualTo(String value) {
            addCriterion("company_E <>", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyEGreaterThan(String value) {
            addCriterion("company_E >", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyEGreaterThanOrEqualTo(String value) {
            addCriterion("company_E >=", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyELessThan(String value) {
            addCriterion("company_E <", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyELessThanOrEqualTo(String value) {
            addCriterion("company_E <=", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyELike(String value) {
            addCriterion("company_E like", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyENotLike(String value) {
            addCriterion("company_E not like", value, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyEIn(List<String> values) {
            addCriterion("company_E in", values, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyENotIn(List<String> values) {
            addCriterion("company_E not in", values, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyEBetween(String value1, String value2) {
            addCriterion("company_E between", value1, value2, "companyE");
            return (Criteria) this;
        }

        public Criteria andCompanyENotBetween(String value1, String value2) {
            addCriterion("company_E not between", value1, value2, "companyE");
            return (Criteria) this;
        }

        public Criteria andDepartmentMIsNull() {
            addCriterion("department_M is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentMIsNotNull() {
            addCriterion("department_M is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentMEqualTo(String value) {
            addCriterion("department_M =", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMNotEqualTo(String value) {
            addCriterion("department_M <>", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMGreaterThan(String value) {
            addCriterion("department_M >", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMGreaterThanOrEqualTo(String value) {
            addCriterion("department_M >=", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMLessThan(String value) {
            addCriterion("department_M <", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMLessThanOrEqualTo(String value) {
            addCriterion("department_M <=", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMLike(String value) {
            addCriterion("department_M like", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMNotLike(String value) {
            addCriterion("department_M not like", value, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMIn(List<String> values) {
            addCriterion("department_M in", values, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMNotIn(List<String> values) {
            addCriterion("department_M not in", values, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMBetween(String value1, String value2) {
            addCriterion("department_M between", value1, value2, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentMNotBetween(String value1, String value2) {
            addCriterion("department_M not between", value1, value2, "departmentM");
            return (Criteria) this;
        }

        public Criteria andDepartmentEIsNull() {
            addCriterion("department_E is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEIsNotNull() {
            addCriterion("department_E is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEEqualTo(String value) {
            addCriterion("department_E =", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentENotEqualTo(String value) {
            addCriterion("department_E <>", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentEGreaterThan(String value) {
            addCriterion("department_E >", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentEGreaterThanOrEqualTo(String value) {
            addCriterion("department_E >=", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentELessThan(String value) {
            addCriterion("department_E <", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentELessThanOrEqualTo(String value) {
            addCriterion("department_E <=", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentELike(String value) {
            addCriterion("department_E like", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentENotLike(String value) {
            addCriterion("department_E not like", value, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentEIn(List<String> values) {
            addCriterion("department_E in", values, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentENotIn(List<String> values) {
            addCriterion("department_E not in", values, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentEBetween(String value1, String value2) {
            addCriterion("department_E between", value1, value2, "departmentE");
            return (Criteria) this;
        }

        public Criteria andDepartmentENotBetween(String value1, String value2) {
            addCriterion("department_E not between", value1, value2, "departmentE");
            return (Criteria) this;
        }

        public Criteria andProjectMIsNull() {
            addCriterion("project_M is null");
            return (Criteria) this;
        }

        public Criteria andProjectMIsNotNull() {
            addCriterion("project_M is not null");
            return (Criteria) this;
        }

        public Criteria andProjectMEqualTo(String value) {
            addCriterion("project_M =", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMNotEqualTo(String value) {
            addCriterion("project_M <>", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMGreaterThan(String value) {
            addCriterion("project_M >", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMGreaterThanOrEqualTo(String value) {
            addCriterion("project_M >=", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMLessThan(String value) {
            addCriterion("project_M <", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMLessThanOrEqualTo(String value) {
            addCriterion("project_M <=", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMLike(String value) {
            addCriterion("project_M like", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMNotLike(String value) {
            addCriterion("project_M not like", value, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMIn(List<String> values) {
            addCriterion("project_M in", values, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMNotIn(List<String> values) {
            addCriterion("project_M not in", values, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMBetween(String value1, String value2) {
            addCriterion("project_M between", value1, value2, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectMNotBetween(String value1, String value2) {
            addCriterion("project_M not between", value1, value2, "projectM");
            return (Criteria) this;
        }

        public Criteria andProjectEIsNull() {
            addCriterion("project_E is null");
            return (Criteria) this;
        }

        public Criteria andProjectEIsNotNull() {
            addCriterion("project_E is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEEqualTo(String value) {
            addCriterion("project_E =", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectENotEqualTo(String value) {
            addCriterion("project_E <>", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectEGreaterThan(String value) {
            addCriterion("project_E >", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectEGreaterThanOrEqualTo(String value) {
            addCriterion("project_E >=", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectELessThan(String value) {
            addCriterion("project_E <", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectELessThanOrEqualTo(String value) {
            addCriterion("project_E <=", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectELike(String value) {
            addCriterion("project_E like", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectENotLike(String value) {
            addCriterion("project_E not like", value, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectEIn(List<String> values) {
            addCriterion("project_E in", values, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectENotIn(List<String> values) {
            addCriterion("project_E not in", values, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectEBetween(String value1, String value2) {
            addCriterion("project_E between", value1, value2, "projectE");
            return (Criteria) this;
        }

        public Criteria andProjectENotBetween(String value1, String value2) {
            addCriterion("project_E not between", value1, value2, "projectE");
            return (Criteria) this;
        }

        public Criteria andTalentIsNull() {
            addCriterion("talent is null");
            return (Criteria) this;
        }

        public Criteria andTalentIsNotNull() {
            addCriterion("talent is not null");
            return (Criteria) this;
        }

        public Criteria andTalentEqualTo(String value) {
            addCriterion("talent =", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentNotEqualTo(String value) {
            addCriterion("talent <>", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentGreaterThan(String value) {
            addCriterion("talent >", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentGreaterThanOrEqualTo(String value) {
            addCriterion("talent >=", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentLessThan(String value) {
            addCriterion("talent <", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentLessThanOrEqualTo(String value) {
            addCriterion("talent <=", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentLike(String value) {
            addCriterion("talent like", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentNotLike(String value) {
            addCriterion("talent not like", value, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentIn(List<String> values) {
            addCriterion("talent in", values, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentNotIn(List<String> values) {
            addCriterion("talent not in", values, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentBetween(String value1, String value2) {
            addCriterion("talent between", value1, value2, "talent");
            return (Criteria) this;
        }

        public Criteria andTalentNotBetween(String value1, String value2) {
            addCriterion("talent not between", value1, value2, "talent");
            return (Criteria) this;
        }

        public Criteria andAppraisal1IsNull() {
            addCriterion("appraisal_1 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisal1IsNotNull() {
            addCriterion("appraisal_1 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisal1EqualTo(String value) {
            addCriterion("appraisal_1 =", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1NotEqualTo(String value) {
            addCriterion("appraisal_1 <>", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1GreaterThan(String value) {
            addCriterion("appraisal_1 >", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_1 >=", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1LessThan(String value) {
            addCriterion("appraisal_1 <", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1LessThanOrEqualTo(String value) {
            addCriterion("appraisal_1 <=", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1Like(String value) {
            addCriterion("appraisal_1 like", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1NotLike(String value) {
            addCriterion("appraisal_1 not like", value, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1In(List<String> values) {
            addCriterion("appraisal_1 in", values, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1NotIn(List<String> values) {
            addCriterion("appraisal_1 not in", values, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1Between(String value1, String value2) {
            addCriterion("appraisal_1 between", value1, value2, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal1NotBetween(String value1, String value2) {
            addCriterion("appraisal_1 not between", value1, value2, "appraisal1");
            return (Criteria) this;
        }

        public Criteria andAppraisal2IsNull() {
            addCriterion("appraisal_2 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisal2IsNotNull() {
            addCriterion("appraisal_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisal2EqualTo(String value) {
            addCriterion("appraisal_2 =", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2NotEqualTo(String value) {
            addCriterion("appraisal_2 <>", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2GreaterThan(String value) {
            addCriterion("appraisal_2 >", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_2 >=", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2LessThan(String value) {
            addCriterion("appraisal_2 <", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2LessThanOrEqualTo(String value) {
            addCriterion("appraisal_2 <=", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2Like(String value) {
            addCriterion("appraisal_2 like", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2NotLike(String value) {
            addCriterion("appraisal_2 not like", value, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2In(List<String> values) {
            addCriterion("appraisal_2 in", values, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2NotIn(List<String> values) {
            addCriterion("appraisal_2 not in", values, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2Between(String value1, String value2) {
            addCriterion("appraisal_2 between", value1, value2, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal2NotBetween(String value1, String value2) {
            addCriterion("appraisal_2 not between", value1, value2, "appraisal2");
            return (Criteria) this;
        }

        public Criteria andAppraisal3IsNull() {
            addCriterion("appraisal_3 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisal3IsNotNull() {
            addCriterion("appraisal_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisal3EqualTo(String value) {
            addCriterion("appraisal_3 =", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3NotEqualTo(String value) {
            addCriterion("appraisal_3 <>", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3GreaterThan(String value) {
            addCriterion("appraisal_3 >", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_3 >=", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3LessThan(String value) {
            addCriterion("appraisal_3 <", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3LessThanOrEqualTo(String value) {
            addCriterion("appraisal_3 <=", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3Like(String value) {
            addCriterion("appraisal_3 like", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3NotLike(String value) {
            addCriterion("appraisal_3 not like", value, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3In(List<String> values) {
            addCriterion("appraisal_3 in", values, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3NotIn(List<String> values) {
            addCriterion("appraisal_3 not in", values, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3Between(String value1, String value2) {
            addCriterion("appraisal_3 between", value1, value2, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andAppraisal3NotBetween(String value1, String value2) {
            addCriterion("appraisal_3 not between", value1, value2, "appraisal3");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMIsNull() {
            addCriterion("is_responsible_M is null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMIsNotNull() {
            addCriterion("is_responsible_M is not null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMEqualTo(String value) {
            addCriterion("is_responsible_M =", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMNotEqualTo(String value) {
            addCriterion("is_responsible_M <>", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMGreaterThan(String value) {
            addCriterion("is_responsible_M >", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMGreaterThanOrEqualTo(String value) {
            addCriterion("is_responsible_M >=", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMLessThan(String value) {
            addCriterion("is_responsible_M <", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMLessThanOrEqualTo(String value) {
            addCriterion("is_responsible_M <=", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMLike(String value) {
            addCriterion("is_responsible_M like", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMNotLike(String value) {
            addCriterion("is_responsible_M not like", value, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMIn(List<String> values) {
            addCriterion("is_responsible_M in", values, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMNotIn(List<String> values) {
            addCriterion("is_responsible_M not in", values, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMBetween(String value1, String value2) {
            addCriterion("is_responsible_M between", value1, value2, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleMNotBetween(String value1, String value2) {
            addCriterion("is_responsible_M not between", value1, value2, "isResponsibleM");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEIsNull() {
            addCriterion("is_responsible_E is null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEIsNotNull() {
            addCriterion("is_responsible_E is not null");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEEqualTo(String value) {
            addCriterion("is_responsible_E =", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleENotEqualTo(String value) {
            addCriterion("is_responsible_E <>", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEGreaterThan(String value) {
            addCriterion("is_responsible_E >", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEGreaterThanOrEqualTo(String value) {
            addCriterion("is_responsible_E >=", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleELessThan(String value) {
            addCriterion("is_responsible_E <", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleELessThanOrEqualTo(String value) {
            addCriterion("is_responsible_E <=", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleELike(String value) {
            addCriterion("is_responsible_E like", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleENotLike(String value) {
            addCriterion("is_responsible_E not like", value, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEIn(List<String> values) {
            addCriterion("is_responsible_E in", values, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleENotIn(List<String> values) {
            addCriterion("is_responsible_E not in", values, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleEBetween(String value1, String value2) {
            addCriterion("is_responsible_E between", value1, value2, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andIsResponsibleENotBetween(String value1, String value2) {
            addCriterion("is_responsible_E not between", value1, value2, "isResponsibleE");
            return (Criteria) this;
        }

        public Criteria andJobTypeMIsNull() {
            addCriterion("job_type_M is null");
            return (Criteria) this;
        }

        public Criteria andJobTypeMIsNotNull() {
            addCriterion("job_type_M is not null");
            return (Criteria) this;
        }

        public Criteria andJobTypeMEqualTo(String value) {
            addCriterion("job_type_M =", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMNotEqualTo(String value) {
            addCriterion("job_type_M <>", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMGreaterThan(String value) {
            addCriterion("job_type_M >", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMGreaterThanOrEqualTo(String value) {
            addCriterion("job_type_M >=", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMLessThan(String value) {
            addCriterion("job_type_M <", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMLessThanOrEqualTo(String value) {
            addCriterion("job_type_M <=", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMLike(String value) {
            addCriterion("job_type_M like", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMNotLike(String value) {
            addCriterion("job_type_M not like", value, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMIn(List<String> values) {
            addCriterion("job_type_M in", values, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMNotIn(List<String> values) {
            addCriterion("job_type_M not in", values, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMBetween(String value1, String value2) {
            addCriterion("job_type_M between", value1, value2, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeMNotBetween(String value1, String value2) {
            addCriterion("job_type_M not between", value1, value2, "jobTypeM");
            return (Criteria) this;
        }

        public Criteria andJobTypeEIsNull() {
            addCriterion("job_type_E is null");
            return (Criteria) this;
        }

        public Criteria andJobTypeEIsNotNull() {
            addCriterion("job_type_E is not null");
            return (Criteria) this;
        }

        public Criteria andJobTypeEEqualTo(String value) {
            addCriterion("job_type_E =", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeENotEqualTo(String value) {
            addCriterion("job_type_E <>", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeEGreaterThan(String value) {
            addCriterion("job_type_E >", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeEGreaterThanOrEqualTo(String value) {
            addCriterion("job_type_E >=", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeELessThan(String value) {
            addCriterion("job_type_E <", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeELessThanOrEqualTo(String value) {
            addCriterion("job_type_E <=", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeELike(String value) {
            addCriterion("job_type_E like", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeENotLike(String value) {
            addCriterion("job_type_E not like", value, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeEIn(List<String> values) {
            addCriterion("job_type_E in", values, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeENotIn(List<String> values) {
            addCriterion("job_type_E not in", values, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeEBetween(String value1, String value2) {
            addCriterion("job_type_E between", value1, value2, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andJobTypeENotBetween(String value1, String value2) {
            addCriterion("job_type_E not between", value1, value2, "jobTypeE");
            return (Criteria) this;
        }

        public Criteria andSexMIsNull() {
            addCriterion("sex_M is null");
            return (Criteria) this;
        }

        public Criteria andSexMIsNotNull() {
            addCriterion("sex_M is not null");
            return (Criteria) this;
        }

        public Criteria andSexMEqualTo(String value) {
            addCriterion("sex_M =", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMNotEqualTo(String value) {
            addCriterion("sex_M <>", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMGreaterThan(String value) {
            addCriterion("sex_M >", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMGreaterThanOrEqualTo(String value) {
            addCriterion("sex_M >=", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMLessThan(String value) {
            addCriterion("sex_M <", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMLessThanOrEqualTo(String value) {
            addCriterion("sex_M <=", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMLike(String value) {
            addCriterion("sex_M like", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMNotLike(String value) {
            addCriterion("sex_M not like", value, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMIn(List<String> values) {
            addCriterion("sex_M in", values, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMNotIn(List<String> values) {
            addCriterion("sex_M not in", values, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMBetween(String value1, String value2) {
            addCriterion("sex_M between", value1, value2, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexMNotBetween(String value1, String value2) {
            addCriterion("sex_M not between", value1, value2, "sexM");
            return (Criteria) this;
        }

        public Criteria andSexEIsNull() {
            addCriterion("sex_E is null");
            return (Criteria) this;
        }

        public Criteria andSexEIsNotNull() {
            addCriterion("sex_E is not null");
            return (Criteria) this;
        }

        public Criteria andSexEEqualTo(String value) {
            addCriterion("sex_E =", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexENotEqualTo(String value) {
            addCriterion("sex_E <>", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexEGreaterThan(String value) {
            addCriterion("sex_E >", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexEGreaterThanOrEqualTo(String value) {
            addCriterion("sex_E >=", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexELessThan(String value) {
            addCriterion("sex_E <", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexELessThanOrEqualTo(String value) {
            addCriterion("sex_E <=", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexELike(String value) {
            addCriterion("sex_E like", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexENotLike(String value) {
            addCriterion("sex_E not like", value, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexEIn(List<String> values) {
            addCriterion("sex_E in", values, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexENotIn(List<String> values) {
            addCriterion("sex_E not in", values, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexEBetween(String value1, String value2) {
            addCriterion("sex_E between", value1, value2, "sexE");
            return (Criteria) this;
        }

        public Criteria andSexENotBetween(String value1, String value2) {
            addCriterion("sex_E not between", value1, value2, "sexE");
            return (Criteria) this;
        }

        public Criteria andPersgtextMIsNull() {
            addCriterion("PERSGTEXT_M is null");
            return (Criteria) this;
        }

        public Criteria andPersgtextMIsNotNull() {
            addCriterion("PERSGTEXT_M is not null");
            return (Criteria) this;
        }

        public Criteria andPersgtextMEqualTo(String value) {
            addCriterion("PERSGTEXT_M =", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMNotEqualTo(String value) {
            addCriterion("PERSGTEXT_M <>", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMGreaterThan(String value) {
            addCriterion("PERSGTEXT_M >", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMGreaterThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT_M >=", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMLessThan(String value) {
            addCriterion("PERSGTEXT_M <", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMLessThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT_M <=", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMLike(String value) {
            addCriterion("PERSGTEXT_M like", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMNotLike(String value) {
            addCriterion("PERSGTEXT_M not like", value, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMIn(List<String> values) {
            addCriterion("PERSGTEXT_M in", values, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMNotIn(List<String> values) {
            addCriterion("PERSGTEXT_M not in", values, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMBetween(String value1, String value2) {
            addCriterion("PERSGTEXT_M between", value1, value2, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextMNotBetween(String value1, String value2) {
            addCriterion("PERSGTEXT_M not between", value1, value2, "persgtextM");
            return (Criteria) this;
        }

        public Criteria andPersgtextEIsNull() {
            addCriterion("PERSGTEXT_E is null");
            return (Criteria) this;
        }

        public Criteria andPersgtextEIsNotNull() {
            addCriterion("PERSGTEXT_E is not null");
            return (Criteria) this;
        }

        public Criteria andPersgtextEEqualTo(String value) {
            addCriterion("PERSGTEXT_E =", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextENotEqualTo(String value) {
            addCriterion("PERSGTEXT_E <>", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextEGreaterThan(String value) {
            addCriterion("PERSGTEXT_E >", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextEGreaterThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT_E >=", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextELessThan(String value) {
            addCriterion("PERSGTEXT_E <", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextELessThanOrEqualTo(String value) {
            addCriterion("PERSGTEXT_E <=", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextELike(String value) {
            addCriterion("PERSGTEXT_E like", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextENotLike(String value) {
            addCriterion("PERSGTEXT_E not like", value, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextEIn(List<String> values) {
            addCriterion("PERSGTEXT_E in", values, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextENotIn(List<String> values) {
            addCriterion("PERSGTEXT_E not in", values, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextEBetween(String value1, String value2) {
            addCriterion("PERSGTEXT_E between", value1, value2, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersgtextENotBetween(String value1, String value2) {
            addCriterion("PERSGTEXT_E not between", value1, value2, "persgtextE");
            return (Criteria) this;
        }

        public Criteria andPersktextMIsNull() {
            addCriterion("PERSKTEXT_M is null");
            return (Criteria) this;
        }

        public Criteria andPersktextMIsNotNull() {
            addCriterion("PERSKTEXT_M is not null");
            return (Criteria) this;
        }

        public Criteria andPersktextMEqualTo(String value) {
            addCriterion("PERSKTEXT_M =", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMNotEqualTo(String value) {
            addCriterion("PERSKTEXT_M <>", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMGreaterThan(String value) {
            addCriterion("PERSKTEXT_M >", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMGreaterThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT_M >=", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMLessThan(String value) {
            addCriterion("PERSKTEXT_M <", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMLessThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT_M <=", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMLike(String value) {
            addCriterion("PERSKTEXT_M like", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMNotLike(String value) {
            addCriterion("PERSKTEXT_M not like", value, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMIn(List<String> values) {
            addCriterion("PERSKTEXT_M in", values, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMNotIn(List<String> values) {
            addCriterion("PERSKTEXT_M not in", values, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMBetween(String value1, String value2) {
            addCriterion("PERSKTEXT_M between", value1, value2, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextMNotBetween(String value1, String value2) {
            addCriterion("PERSKTEXT_M not between", value1, value2, "persktextM");
            return (Criteria) this;
        }

        public Criteria andPersktextEIsNull() {
            addCriterion("PERSKTEXT_E is null");
            return (Criteria) this;
        }

        public Criteria andPersktextEIsNotNull() {
            addCriterion("PERSKTEXT_E is not null");
            return (Criteria) this;
        }

        public Criteria andPersktextEEqualTo(String value) {
            addCriterion("PERSKTEXT_E =", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextENotEqualTo(String value) {
            addCriterion("PERSKTEXT_E <>", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextEGreaterThan(String value) {
            addCriterion("PERSKTEXT_E >", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextEGreaterThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT_E >=", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextELessThan(String value) {
            addCriterion("PERSKTEXT_E <", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextELessThanOrEqualTo(String value) {
            addCriterion("PERSKTEXT_E <=", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextELike(String value) {
            addCriterion("PERSKTEXT_E like", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextENotLike(String value) {
            addCriterion("PERSKTEXT_E not like", value, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextEIn(List<String> values) {
            addCriterion("PERSKTEXT_E in", values, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextENotIn(List<String> values) {
            addCriterion("PERSKTEXT_E not in", values, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextEBetween(String value1, String value2) {
            addCriterion("PERSKTEXT_E between", value1, value2, "persktextE");
            return (Criteria) this;
        }

        public Criteria andPersktextENotBetween(String value1, String value2) {
            addCriterion("PERSKTEXT_E not between", value1, value2, "persktextE");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityIsNull() {
            addCriterion("current_position_seniority is null");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityIsNotNull() {
            addCriterion("current_position_seniority is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityEqualTo(Float value) {
            addCriterion("current_position_seniority =", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityNotEqualTo(Float value) {
            addCriterion("current_position_seniority <>", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityGreaterThan(Float value) {
            addCriterion("current_position_seniority >", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityGreaterThanOrEqualTo(Float value) {
            addCriterion("current_position_seniority >=", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityLessThan(Float value) {
            addCriterion("current_position_seniority <", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityLessThanOrEqualTo(Float value) {
            addCriterion("current_position_seniority <=", value, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityIn(List<Float> values) {
            addCriterion("current_position_seniority in", values, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityNotIn(List<Float> values) {
            addCriterion("current_position_seniority not in", values, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityBetween(Float value1, Float value2) {
            addCriterion("current_position_seniority between", value1, value2, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentPositionSeniorityNotBetween(Float value1, Float value2) {
            addCriterion("current_position_seniority not between", value1, value2, "currentPositionSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityIsNull() {
            addCriterion("current_joblevel_seniority is null");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityIsNotNull() {
            addCriterion("current_joblevel_seniority is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityEqualTo(Float value) {
            addCriterion("current_joblevel_seniority =", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityNotEqualTo(Float value) {
            addCriterion("current_joblevel_seniority <>", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityGreaterThan(Float value) {
            addCriterion("current_joblevel_seniority >", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityGreaterThanOrEqualTo(Float value) {
            addCriterion("current_joblevel_seniority >=", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityLessThan(Float value) {
            addCriterion("current_joblevel_seniority <", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityLessThanOrEqualTo(Float value) {
            addCriterion("current_joblevel_seniority <=", value, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityIn(List<Float> values) {
            addCriterion("current_joblevel_seniority in", values, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityNotIn(List<Float> values) {
            addCriterion("current_joblevel_seniority not in", values, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityBetween(Float value1, Float value2) {
            addCriterion("current_joblevel_seniority between", value1, value2, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentJoblevelSeniorityNotBetween(Float value1, Float value2) {
            addCriterion("current_joblevel_seniority not between", value1, value2, "currentJoblevelSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityIsNull() {
            addCriterion("current_rank_seniority is null");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityIsNotNull() {
            addCriterion("current_rank_seniority is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityEqualTo(Float value) {
            addCriterion("current_rank_seniority =", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityNotEqualTo(Float value) {
            addCriterion("current_rank_seniority <>", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityGreaterThan(Float value) {
            addCriterion("current_rank_seniority >", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityGreaterThanOrEqualTo(Float value) {
            addCriterion("current_rank_seniority >=", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityLessThan(Float value) {
            addCriterion("current_rank_seniority <", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityLessThanOrEqualTo(Float value) {
            addCriterion("current_rank_seniority <=", value, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityIn(List<Float> values) {
            addCriterion("current_rank_seniority in", values, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityNotIn(List<Float> values) {
            addCriterion("current_rank_seniority not in", values, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityBetween(Float value1, Float value2) {
            addCriterion("current_rank_seniority between", value1, value2, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andCurrentRankSeniorityNotBetween(Float value1, Float value2) {
            addCriterion("current_rank_seniority not between", value1, value2, "currentRankSeniority");
            return (Criteria) this;
        }

        public Criteria andPhotoIsNull() {
            addCriterion("photo is null");
            return (Criteria) this;
        }

        public Criteria andPhotoIsNotNull() {
            addCriterion("photo is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoEqualTo(String value) {
            addCriterion("photo =", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotEqualTo(String value) {
            addCriterion("photo <>", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoGreaterThan(String value) {
            addCriterion("photo >", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoGreaterThanOrEqualTo(String value) {
            addCriterion("photo >=", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLessThan(String value) {
            addCriterion("photo <", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLessThanOrEqualTo(String value) {
            addCriterion("photo <=", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLike(String value) {
            addCriterion("photo like", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotLike(String value) {
            addCriterion("photo not like", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoIn(List<String> values) {
            addCriterion("photo in", values, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotIn(List<String> values) {
            addCriterion("photo not in", values, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoBetween(String value1, String value2) {
            addCriterion("photo between", value1, value2, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotBetween(String value1, String value2) {
            addCriterion("photo not between", value1, value2, "photo");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpIsNull() {
            addCriterion("is_abroad_exp is null");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpIsNotNull() {
            addCriterion("is_abroad_exp is not null");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpEqualTo(Integer value) {
            addCriterion("is_abroad_exp =", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpNotEqualTo(Integer value) {
            addCriterion("is_abroad_exp <>", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpGreaterThan(Integer value) {
            addCriterion("is_abroad_exp >", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_abroad_exp >=", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpLessThan(Integer value) {
            addCriterion("is_abroad_exp <", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpLessThanOrEqualTo(Integer value) {
            addCriterion("is_abroad_exp <=", value, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpIn(List<Integer> values) {
            addCriterion("is_abroad_exp in", values, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpNotIn(List<Integer> values) {
            addCriterion("is_abroad_exp not in", values, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpBetween(Integer value1, Integer value2) {
            addCriterion("is_abroad_exp between", value1, value2, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andIsAbroadExpNotBetween(Integer value1, Integer value2) {
            addCriterion("is_abroad_exp not between", value1, value2, "isAbroadExp");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmIsNull() {
            addCriterion("family_place_hm is null");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmIsNotNull() {
            addCriterion("family_place_hm is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmEqualTo(String value) {
            addCriterion("family_place_hm =", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmNotEqualTo(String value) {
            addCriterion("family_place_hm <>", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmGreaterThan(String value) {
            addCriterion("family_place_hm >", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmGreaterThanOrEqualTo(String value) {
            addCriterion("family_place_hm >=", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmLessThan(String value) {
            addCriterion("family_place_hm <", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmLessThanOrEqualTo(String value) {
            addCriterion("family_place_hm <=", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmLike(String value) {
            addCriterion("family_place_hm like", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmNotLike(String value) {
            addCriterion("family_place_hm not like", value, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmIn(List<String> values) {
            addCriterion("family_place_hm in", values, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmNotIn(List<String> values) {
            addCriterion("family_place_hm not in", values, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmBetween(String value1, String value2) {
            addCriterion("family_place_hm between", value1, value2, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFamilyPlaceHmNotBetween(String value1, String value2) {
            addCriterion("family_place_hm not between", value1, value2, "familyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmIsNull() {
            addCriterion("Phone_hm is null");
            return (Criteria) this;
        }

        public Criteria andPhoneHmIsNotNull() {
            addCriterion("Phone_hm is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneHmEqualTo(String value) {
            addCriterion("Phone_hm =", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmNotEqualTo(String value) {
            addCriterion("Phone_hm <>", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmGreaterThan(String value) {
            addCriterion("Phone_hm >", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmGreaterThanOrEqualTo(String value) {
            addCriterion("Phone_hm >=", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmLessThan(String value) {
            addCriterion("Phone_hm <", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmLessThanOrEqualTo(String value) {
            addCriterion("Phone_hm <=", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmLike(String value) {
            addCriterion("Phone_hm like", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmNotLike(String value) {
            addCriterion("Phone_hm not like", value, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmIn(List<String> values) {
            addCriterion("Phone_hm in", values, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmNotIn(List<String> values) {
            addCriterion("Phone_hm not in", values, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmBetween(String value1, String value2) {
            addCriterion("Phone_hm between", value1, value2, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andPhoneHmNotBetween(String value1, String value2) {
            addCriterion("Phone_hm not between", value1, value2, "phoneHm");
            return (Criteria) this;
        }

        public Criteria andZzSjsjIsNull() {
            addCriterion("ZZ_SJSJ is null");
            return (Criteria) this;
        }

        public Criteria andZzSjsjIsNotNull() {
            addCriterion("ZZ_SJSJ is not null");
            return (Criteria) this;
        }

        public Criteria andZzSjsjEqualTo(String value) {
            addCriterion("ZZ_SJSJ =", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjNotEqualTo(String value) {
            addCriterion("ZZ_SJSJ <>", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjGreaterThan(String value) {
            addCriterion("ZZ_SJSJ >", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_SJSJ >=", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjLessThan(String value) {
            addCriterion("ZZ_SJSJ <", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjLessThanOrEqualTo(String value) {
            addCriterion("ZZ_SJSJ <=", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjLike(String value) {
            addCriterion("ZZ_SJSJ like", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjNotLike(String value) {
            addCriterion("ZZ_SJSJ not like", value, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjIn(List<String> values) {
            addCriterion("ZZ_SJSJ in", values, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjNotIn(List<String> values) {
            addCriterion("ZZ_SJSJ not in", values, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjBetween(String value1, String value2) {
            addCriterion("ZZ_SJSJ between", value1, value2, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andZzSjsjNotBetween(String value1, String value2) {
            addCriterion("ZZ_SJSJ not between", value1, value2, "zzSjsj");
            return (Criteria) this;
        }

        public Criteria andCtedtIsNull() {
            addCriterion("CTEDT is null");
            return (Criteria) this;
        }

        public Criteria andCtedtIsNotNull() {
            addCriterion("CTEDT is not null");
            return (Criteria) this;
        }

        public Criteria andCtedtEqualTo(String value) {
            addCriterion("CTEDT =", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtNotEqualTo(String value) {
            addCriterion("CTEDT <>", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtGreaterThan(String value) {
            addCriterion("CTEDT >", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtGreaterThanOrEqualTo(String value) {
            addCriterion("CTEDT >=", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtLessThan(String value) {
            addCriterion("CTEDT <", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtLessThanOrEqualTo(String value) {
            addCriterion("CTEDT <=", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtLike(String value) {
            addCriterion("CTEDT like", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtNotLike(String value) {
            addCriterion("CTEDT not like", value, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtIn(List<String> values) {
            addCriterion("CTEDT in", values, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtNotIn(List<String> values) {
            addCriterion("CTEDT not in", values, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtBetween(String value1, String value2) {
            addCriterion("CTEDT between", value1, value2, "ctedt");
            return (Criteria) this;
        }

        public Criteria andCtedtNotBetween(String value1, String value2) {
            addCriterion("CTEDT not between", value1, value2, "ctedt");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceIsNull() {
            addCriterion("full_work_place is null");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceIsNotNull() {
            addCriterion("full_work_place is not null");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceEqualTo(String value) {
            addCriterion("full_work_place =", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceNotEqualTo(String value) {
            addCriterion("full_work_place <>", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceGreaterThan(String value) {
            addCriterion("full_work_place >", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("full_work_place >=", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceLessThan(String value) {
            addCriterion("full_work_place <", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceLessThanOrEqualTo(String value) {
            addCriterion("full_work_place <=", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceLike(String value) {
            addCriterion("full_work_place like", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceNotLike(String value) {
            addCriterion("full_work_place not like", value, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceIn(List<String> values) {
            addCriterion("full_work_place in", values, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceNotIn(List<String> values) {
            addCriterion("full_work_place not in", values, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceBetween(String value1, String value2) {
            addCriterion("full_work_place between", value1, value2, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullWorkPlaceNotBetween(String value1, String value2) {
            addCriterion("full_work_place not between", value1, value2, "fullWorkPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceIsNull() {
            addCriterion("full_family_place is null");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceIsNotNull() {
            addCriterion("full_family_place is not null");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceEqualTo(String value) {
            addCriterion("full_family_place =", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceNotEqualTo(String value) {
            addCriterion("full_family_place <>", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceGreaterThan(String value) {
            addCriterion("full_family_place >", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("full_family_place >=", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceLessThan(String value) {
            addCriterion("full_family_place <", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceLessThanOrEqualTo(String value) {
            addCriterion("full_family_place <=", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceLike(String value) {
            addCriterion("full_family_place like", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceNotLike(String value) {
            addCriterion("full_family_place not like", value, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceIn(List<String> values) {
            addCriterion("full_family_place in", values, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceNotIn(List<String> values) {
            addCriterion("full_family_place not in", values, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceBetween(String value1, String value2) {
            addCriterion("full_family_place between", value1, value2, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceNotBetween(String value1, String value2) {
            addCriterion("full_family_place not between", value1, value2, "fullFamilyPlace");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmIsNull() {
            addCriterion("full_family_place_hm is null");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmIsNotNull() {
            addCriterion("full_family_place_hm is not null");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmEqualTo(String value) {
            addCriterion("full_family_place_hm =", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmNotEqualTo(String value) {
            addCriterion("full_family_place_hm <>", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmGreaterThan(String value) {
            addCriterion("full_family_place_hm >", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmGreaterThanOrEqualTo(String value) {
            addCriterion("full_family_place_hm >=", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmLessThan(String value) {
            addCriterion("full_family_place_hm <", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmLessThanOrEqualTo(String value) {
            addCriterion("full_family_place_hm <=", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmLike(String value) {
            addCriterion("full_family_place_hm like", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmNotLike(String value) {
            addCriterion("full_family_place_hm not like", value, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmIn(List<String> values) {
            addCriterion("full_family_place_hm in", values, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmNotIn(List<String> values) {
            addCriterion("full_family_place_hm not in", values, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmBetween(String value1, String value2) {
            addCriterion("full_family_place_hm between", value1, value2, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andFullFamilyPlaceHmNotBetween(String value1, String value2) {
            addCriterion("full_family_place_hm not between", value1, value2, "fullFamilyPlaceHm");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIsNull() {
            addCriterion("social_service is null");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIsNotNull() {
            addCriterion("social_service is not null");
            return (Criteria) this;
        }

        public Criteria andSocialServiceEqualTo(String value) {
            addCriterion("social_service =", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotEqualTo(String value) {
            addCriterion("social_service <>", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceGreaterThan(String value) {
            addCriterion("social_service >", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceGreaterThanOrEqualTo(String value) {
            addCriterion("social_service >=", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLessThan(String value) {
            addCriterion("social_service <", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLessThanOrEqualTo(String value) {
            addCriterion("social_service <=", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLike(String value) {
            addCriterion("social_service like", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotLike(String value) {
            addCriterion("social_service not like", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIn(List<String> values) {
            addCriterion("social_service in", values, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotIn(List<String> values) {
            addCriterion("social_service not in", values, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceBetween(String value1, String value2) {
            addCriterion("social_service between", value1, value2, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotBetween(String value1, String value2) {
            addCriterion("social_service not between", value1, value2, "socialService");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityIsNull() {
            addCriterion("Work_Seniority is null");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityIsNotNull() {
            addCriterion("Work_Seniority is not null");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityEqualTo(Float value) {
            addCriterion("Work_Seniority =", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityNotEqualTo(Float value) {
            addCriterion("Work_Seniority <>", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityGreaterThan(Float value) {
            addCriterion("Work_Seniority >", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityGreaterThanOrEqualTo(Float value) {
            addCriterion("Work_Seniority >=", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityLessThan(Float value) {
            addCriterion("Work_Seniority <", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityLessThanOrEqualTo(Float value) {
            addCriterion("Work_Seniority <=", value, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityIn(List<Float> values) {
            addCriterion("Work_Seniority in", values, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityNotIn(List<Float> values) {
            addCriterion("Work_Seniority not in", values, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityBetween(Float value1, Float value2) {
            addCriterion("Work_Seniority between", value1, value2, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andWorkSeniorityNotBetween(Float value1, Float value2) {
            addCriterion("Work_Seniority not between", value1, value2, "workSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityIsNull() {
            addCriterion("join_zh_Seniority is null");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityIsNotNull() {
            addCriterion("join_zh_Seniority is not null");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityEqualTo(Float value) {
            addCriterion("join_zh_Seniority =", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityNotEqualTo(Float value) {
            addCriterion("join_zh_Seniority <>", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityGreaterThan(Float value) {
            addCriterion("join_zh_Seniority >", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityGreaterThanOrEqualTo(Float value) {
            addCriterion("join_zh_Seniority >=", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityLessThan(Float value) {
            addCriterion("join_zh_Seniority <", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityLessThanOrEqualTo(Float value) {
            addCriterion("join_zh_Seniority <=", value, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityIn(List<Float> values) {
            addCriterion("join_zh_Seniority in", values, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityNotIn(List<Float> values) {
            addCriterion("join_zh_Seniority not in", values, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityBetween(Float value1, Float value2) {
            addCriterion("join_zh_Seniority between", value1, value2, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andJoinZhSeniorityNotBetween(Float value1, Float value2) {
            addCriterion("join_zh_Seniority not between", value1, value2, "joinZhSeniority");
            return (Criteria) this;
        }

        public Criteria andXzIsNull() {
            addCriterion("XZ is null");
            return (Criteria) this;
        }

        public Criteria andXzIsNotNull() {
            addCriterion("XZ is not null");
            return (Criteria) this;
        }

        public Criteria andXzEqualTo(String value) {
            addCriterion("XZ =", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzNotEqualTo(String value) {
            addCriterion("XZ <>", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzGreaterThan(String value) {
            addCriterion("XZ >", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzGreaterThanOrEqualTo(String value) {
            addCriterion("XZ >=", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzLessThan(String value) {
            addCriterion("XZ <", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzLessThanOrEqualTo(String value) {
            addCriterion("XZ <=", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzLike(String value) {
            addCriterion("XZ like", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzNotLike(String value) {
            addCriterion("XZ not like", value, "xz");
            return (Criteria) this;
        }

        public Criteria andXzIn(List<String> values) {
            addCriterion("XZ in", values, "xz");
            return (Criteria) this;
        }

        public Criteria andXzNotIn(List<String> values) {
            addCriterion("XZ not in", values, "xz");
            return (Criteria) this;
        }

        public Criteria andXzBetween(String value1, String value2) {
            addCriterion("XZ between", value1, value2, "xz");
            return (Criteria) this;
        }

        public Criteria andXzNotBetween(String value1, String value2) {
            addCriterion("XZ not between", value1, value2, "xz");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneIsNull() {
            addCriterion("Office_Phone is null");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneIsNotNull() {
            addCriterion("Office_Phone is not null");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneEqualTo(String value) {
            addCriterion("Office_Phone =", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneNotEqualTo(String value) {
            addCriterion("Office_Phone <>", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneGreaterThan(String value) {
            addCriterion("Office_Phone >", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneGreaterThanOrEqualTo(String value) {
            addCriterion("Office_Phone >=", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneLessThan(String value) {
            addCriterion("Office_Phone <", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneLessThanOrEqualTo(String value) {
            addCriterion("Office_Phone <=", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneLike(String value) {
            addCriterion("Office_Phone like", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneNotLike(String value) {
            addCriterion("Office_Phone not like", value, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneIn(List<String> values) {
            addCriterion("Office_Phone in", values, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneNotIn(List<String> values) {
            addCriterion("Office_Phone not in", values, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneBetween(String value1, String value2) {
            addCriterion("Office_Phone between", value1, value2, "officePhone");
            return (Criteria) this;
        }

        public Criteria andOfficePhoneNotBetween(String value1, String value2) {
            addCriterion("Office_Phone not between", value1, value2, "officePhone");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateIsNull() {
            addCriterion("JoinSysDate is null");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateIsNotNull() {
            addCriterion("JoinSysDate is not null");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateEqualTo(LocalDate value) {
            addCriterion("JoinSysDate =", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateNotEqualTo(LocalDate value) {
            addCriterion("JoinSysDate <>", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateGreaterThan(LocalDate value) {
            addCriterion("JoinSysDate >", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("JoinSysDate >=", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateLessThan(LocalDate value) {
            addCriterion("JoinSysDate <", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateLessThanOrEqualTo(LocalDate value) {
            addCriterion("JoinSysDate <=", value, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateIn(List<LocalDate> values) {
            addCriterion("JoinSysDate in", values, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateNotIn(List<LocalDate> values) {
            addCriterion("JoinSysDate not in", values, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("JoinSysDate between", value1, value2, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJoinsysdateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("JoinSysDate not between", value1, value2, "joinsysdate");
            return (Criteria) this;
        }

        public Criteria andJobCodeIsNull() {
            addCriterion("job_code is null");
            return (Criteria) this;
        }

        public Criteria andJobCodeIsNotNull() {
            addCriterion("job_code is not null");
            return (Criteria) this;
        }

        public Criteria andJobCodeEqualTo(String value) {
            addCriterion("job_code =", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotEqualTo(String value) {
            addCriterion("job_code <>", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeGreaterThan(String value) {
            addCriterion("job_code >", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeGreaterThanOrEqualTo(String value) {
            addCriterion("job_code >=", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLessThan(String value) {
            addCriterion("job_code <", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLessThanOrEqualTo(String value) {
            addCriterion("job_code <=", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLike(String value) {
            addCriterion("job_code like", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotLike(String value) {
            addCriterion("job_code not like", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeIn(List<String> values) {
            addCriterion("job_code in", values, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotIn(List<String> values) {
            addCriterion("job_code not in", values, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeBetween(String value1, String value2) {
            addCriterion("job_code between", value1, value2, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotBetween(String value1, String value2) {
            addCriterion("job_code not between", value1, value2, "jobCode");
            return (Criteria) this;
        }

        public Criteria andHireTypeIsNull() {
            addCriterion("hire_type is null");
            return (Criteria) this;
        }

        public Criteria andHireTypeIsNotNull() {
            addCriterion("hire_type is not null");
            return (Criteria) this;
        }

        public Criteria andHireTypeEqualTo(String value) {
            addCriterion("hire_type =", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeNotEqualTo(String value) {
            addCriterion("hire_type <>", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeGreaterThan(String value) {
            addCriterion("hire_type >", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeGreaterThanOrEqualTo(String value) {
            addCriterion("hire_type >=", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeLessThan(String value) {
            addCriterion("hire_type <", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeLessThanOrEqualTo(String value) {
            addCriterion("hire_type <=", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeLike(String value) {
            addCriterion("hire_type like", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeNotLike(String value) {
            addCriterion("hire_type not like", value, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeIn(List<String> values) {
            addCriterion("hire_type in", values, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeNotIn(List<String> values) {
            addCriterion("hire_type not in", values, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeBetween(String value1, String value2) {
            addCriterion("hire_type between", value1, value2, "hireType");
            return (Criteria) this;
        }

        public Criteria andHireTypeNotBetween(String value1, String value2) {
            addCriterion("hire_type not between", value1, value2, "hireType");
            return (Criteria) this;
        }

        public Criteria andManageTypeIsNull() {
            addCriterion("manage_type is null");
            return (Criteria) this;
        }

        public Criteria andManageTypeIsNotNull() {
            addCriterion("manage_type is not null");
            return (Criteria) this;
        }

        public Criteria andManageTypeEqualTo(String value) {
            addCriterion("manage_type =", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeNotEqualTo(String value) {
            addCriterion("manage_type <>", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeGreaterThan(String value) {
            addCriterion("manage_type >", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeGreaterThanOrEqualTo(String value) {
            addCriterion("manage_type >=", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeLessThan(String value) {
            addCriterion("manage_type <", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeLessThanOrEqualTo(String value) {
            addCriterion("manage_type <=", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeLike(String value) {
            addCriterion("manage_type like", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeNotLike(String value) {
            addCriterion("manage_type not like", value, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeIn(List<String> values) {
            addCriterion("manage_type in", values, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeNotIn(List<String> values) {
            addCriterion("manage_type not in", values, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeBetween(String value1, String value2) {
            addCriterion("manage_type between", value1, value2, "manageType");
            return (Criteria) this;
        }

        public Criteria andManageTypeNotBetween(String value1, String value2) {
            addCriterion("manage_type not between", value1, value2, "manageType");
            return (Criteria) this;
        }

        public Criteria andZwCodeIsNull() {
            addCriterion("zw_code is null");
            return (Criteria) this;
        }

        public Criteria andZwCodeIsNotNull() {
            addCriterion("zw_code is not null");
            return (Criteria) this;
        }

        public Criteria andZwCodeEqualTo(String value) {
            addCriterion("zw_code =", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeNotEqualTo(String value) {
            addCriterion("zw_code <>", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeGreaterThan(String value) {
            addCriterion("zw_code >", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeGreaterThanOrEqualTo(String value) {
            addCriterion("zw_code >=", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeLessThan(String value) {
            addCriterion("zw_code <", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeLessThanOrEqualTo(String value) {
            addCriterion("zw_code <=", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeLike(String value) {
            addCriterion("zw_code like", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeNotLike(String value) {
            addCriterion("zw_code not like", value, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeIn(List<String> values) {
            addCriterion("zw_code in", values, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeNotIn(List<String> values) {
            addCriterion("zw_code not in", values, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeBetween(String value1, String value2) {
            addCriterion("zw_code between", value1, value2, "zwCode");
            return (Criteria) this;
        }

        public Criteria andZwCodeNotBetween(String value1, String value2) {
            addCriterion("zw_code not between", value1, value2, "zwCode");
            return (Criteria) this;
        }

        public Criteria andIsonjobIsNull() {
            addCriterion("IsOnJob is null");
            return (Criteria) this;
        }

        public Criteria andIsonjobIsNotNull() {
            addCriterion("IsOnJob is not null");
            return (Criteria) this;
        }

        public Criteria andIsonjobEqualTo(Integer value) {
            addCriterion("IsOnJob =", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobNotEqualTo(Integer value) {
            addCriterion("IsOnJob <>", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobGreaterThan(Integer value) {
            addCriterion("IsOnJob >", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobGreaterThanOrEqualTo(Integer value) {
            addCriterion("IsOnJob >=", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobLessThan(Integer value) {
            addCriterion("IsOnJob <", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobLessThanOrEqualTo(Integer value) {
            addCriterion("IsOnJob <=", value, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobIn(List<Integer> values) {
            addCriterion("IsOnJob in", values, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobNotIn(List<Integer> values) {
            addCriterion("IsOnJob not in", values, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobBetween(Integer value1, Integer value2) {
            addCriterion("IsOnJob between", value1, value2, "isonjob");
            return (Criteria) this;
        }

        public Criteria andIsonjobNotBetween(Integer value1, Integer value2) {
            addCriterion("IsOnJob not between", value1, value2, "isonjob");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeIsNull() {
            addCriterion("recruit_type is null");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeIsNotNull() {
            addCriterion("recruit_type is not null");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeEqualTo(String value) {
            addCriterion("recruit_type =", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeNotEqualTo(String value) {
            addCriterion("recruit_type <>", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeGreaterThan(String value) {
            addCriterion("recruit_type >", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("recruit_type >=", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeLessThan(String value) {
            addCriterion("recruit_type <", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeLessThanOrEqualTo(String value) {
            addCriterion("recruit_type <=", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeLike(String value) {
            addCriterion("recruit_type like", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeNotLike(String value) {
            addCriterion("recruit_type not like", value, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeIn(List<String> values) {
            addCriterion("recruit_type in", values, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeNotIn(List<String> values) {
            addCriterion("recruit_type not in", values, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeBetween(String value1, String value2) {
            addCriterion("recruit_type between", value1, value2, "recruitType");
            return (Criteria) this;
        }

        public Criteria andRecruitTypeNotBetween(String value1, String value2) {
            addCriterion("recruit_type not between", value1, value2, "recruitType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fact_roster
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fact_roster
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}