package com.csci.hrrs.model;

import java.time.LocalDateTime;

public class SocialPerformanceHead {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.organization_id
     *
     * @mbg.generated
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.year
     *
     * @mbg.generated
     */
    private Integer year;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.month
     *
     * @mbg.generated
     */
    private Integer month;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.submit_date
     *
     * @mbg.generated
     */
    private LocalDateTime submitDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.is_active
     *
     * @mbg.generated
     */
    private Boolean active;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.approve_status
     *
     * @mbg.generated
     */
    private Integer approveStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.create_username
     *
     * @mbg.generated
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.create_user_id
     *
     * @mbg.generated
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.last_update_time
     *
     * @mbg.generated
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.last_update_username
     *
     * @mbg.generated
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.last_update_user_id
     *
     * @mbg.generated
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_head.last_update_version
     *
     * @mbg.generated
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.id
     *
     * @return the value of t_social_performance_head.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.id
     *
     * @param id the value for t_social_performance_head.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.organization_id
     *
     * @return the value of t_social_performance_head.organization_id
     *
     * @mbg.generated
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.organization_id
     *
     * @param organizationId the value for t_social_performance_head.organization_id
     *
     * @mbg.generated
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.year
     *
     * @return the value of t_social_performance_head.year
     *
     * @mbg.generated
     */
    public Integer getYear() {
        return year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.year
     *
     * @param year the value for t_social_performance_head.year
     *
     * @mbg.generated
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.month
     *
     * @return the value of t_social_performance_head.month
     *
     * @mbg.generated
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.month
     *
     * @param month the value for t_social_performance_head.month
     *
     * @mbg.generated
     */
    public void setMonth(Integer month) {
        this.month = month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.submit_date
     *
     * @return the value of t_social_performance_head.submit_date
     *
     * @mbg.generated
     */
    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.submit_date
     *
     * @param submitDate the value for t_social_performance_head.submit_date
     *
     * @mbg.generated
     */
    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.is_active
     *
     * @return the value of t_social_performance_head.is_active
     *
     * @mbg.generated
     */
    public Boolean getActive() {
        return active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.is_active
     *
     * @param active the value for t_social_performance_head.is_active
     *
     * @mbg.generated
     */
    public void setActive(Boolean active) {
        this.active = active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.approve_status
     *
     * @return the value of t_social_performance_head.approve_status
     *
     * @mbg.generated
     */
    public Integer getApproveStatus() {
        return approveStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.approve_status
     *
     * @param approveStatus the value for t_social_performance_head.approve_status
     *
     * @mbg.generated
     */
    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.creation_time
     *
     * @return the value of t_social_performance_head.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.creation_time
     *
     * @param creationTime the value for t_social_performance_head.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.create_username
     *
     * @return the value of t_social_performance_head.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.create_username
     *
     * @param createUsername the value for t_social_performance_head.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.create_user_id
     *
     * @return the value of t_social_performance_head.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.create_user_id
     *
     * @param createUserId the value for t_social_performance_head.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.last_update_time
     *
     * @return the value of t_social_performance_head.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.last_update_time
     *
     * @param lastUpdateTime the value for t_social_performance_head.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.last_update_username
     *
     * @return the value of t_social_performance_head.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.last_update_username
     *
     * @param lastUpdateUsername the value for t_social_performance_head.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.last_update_user_id
     *
     * @return the value of t_social_performance_head.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_social_performance_head.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_head.last_update_version
     *
     * @return the value of t_social_performance_head.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_head.last_update_version
     *
     * @param lastUpdateVersion the value for t_social_performance_head.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}