package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_hr_admin_line_detail
 */
@TableName("t_hr_admin_line_detail")
public class AdminLineDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.id
     *
     * @mbg.generated
     */
    @TableField("id")
    @TableId
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.head_id
     *
     * @mbg.generated
     */
    @TableField("head_id")
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.config_id
     *
     * @mbg.generated
     */
    @TableField("config_id")
    private String configId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.config_name
     *
     * @mbg.generated
     */
    @TableField("config_name")
    private String configName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_head_count
     *
     * @mbg.generated
     */
    @TableField("hr_head_count")
    private Integer hrHeadCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_two_level_abv
     *
     * @mbg.generated
     */
    @TableField("hr_two_level_abv")
    private Integer hrTwoLevelAbv;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_senior_mgr
     *
     * @mbg.generated
     */
    @TableField("hr_senior_mgr")
    private Integer hrSeniorMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_manager
     *
     * @mbg.generated
     */
    @TableField("hr_manager")
    private Integer hrManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_deputy_mgr
     *
     * @mbg.generated
     */
    @TableField("hr_deputy_mgr")
    private Integer hrDeputyMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_assis_mgr
     *
     * @mbg.generated
     */
    @TableField("hr_assis_mgr")
    private Integer hrAssisMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.hr_other
     *
     * @mbg.generated
     */
    @TableField("hr_other")
    private Integer hrOther;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_head_count
     *
     * @mbg.generated
     */
    @TableField("admin_head_count")
    private Integer adminHeadCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_two_level_abv
     *
     * @mbg.generated
     */
    @TableField("admin_two_level_abv")
    private Integer adminTwoLevelAbv;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_senior_mgr
     *
     * @mbg.generated
     */
    @TableField("admin_senior_mgr")
    private Integer adminSeniorMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_manager
     *
     * @mbg.generated
     */
    @TableField("admin_manager")
    private Integer adminManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_deputy_mgr
     *
     * @mbg.generated
     */
    @TableField("admin_deputy_mgr")
    private Integer adminDeputyMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_assis_mgr
     *
     * @mbg.generated
     */
    @TableField("admin_assis_mgr")
    private Integer adminAssisMgr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_other
     *
     * @mbg.generated
     */
    @TableField("admin_other")
    private Integer adminOther;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.admin_office
     *
     * @mbg.generated
     */
    @TableField("admin_office")
    private Integer adminOffice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.seq
     *
     * @mbg.generated
     */
    @TableField("seq")
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.is_deleted
     *
     * @mbg.generated
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.delete_version
     *
     * @mbg.generated
     */
    @TableField("delete_version")
    private Integer deleteVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.creation_time
     *
     * @mbg.generated
     */
    @TableField("creation_time")
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.create_user_id
     *
     * @mbg.generated
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.create_username
     *
     * @mbg.generated
     */
    @TableField("create_username")
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.last_update_time
     *
     * @mbg.generated
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.last_update_user_id
     *
     * @mbg.generated
     */
    @TableField("last_update_user_id")
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.last_update_username
     *
     * @mbg.generated
     */
    @TableField("last_update_username")
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_admin_line_detail.last_update_version
     *
     * @mbg.generated
     */
    @TableField("last_update_version")
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.id
     *
     * @return the value of t_hr_admin_line_detail.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.id
     *
     * @param id the value for t_hr_admin_line_detail.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.head_id
     *
     * @return the value of t_hr_admin_line_detail.head_id
     *
     * @mbg.generated
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.head_id
     *
     * @param headId the value for t_hr_admin_line_detail.head_id
     *
     * @mbg.generated
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.config_id
     *
     * @return the value of t_hr_admin_line_detail.config_id
     *
     * @mbg.generated
     */
    public String getConfigId() {
        return configId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.config_id
     *
     * @param configId the value for t_hr_admin_line_detail.config_id
     *
     * @mbg.generated
     */
    public void setConfigId(String configId) {
        this.configId = configId == null ? null : configId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.config_name
     *
     * @return the value of t_hr_admin_line_detail.config_name
     *
     * @mbg.generated
     */
    public String getConfigName() {
        return configName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.config_name
     *
     * @param configName the value for t_hr_admin_line_detail.config_name
     *
     * @mbg.generated
     */
    public void setConfigName(String configName) {
        this.configName = configName == null ? null : configName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_head_count
     *
     * @return the value of t_hr_admin_line_detail.hr_head_count
     *
     * @mbg.generated
     */
    public Integer getHrHeadCount() {
        return hrHeadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_head_count
     *
     * @param hrHeadCount the value for t_hr_admin_line_detail.hr_head_count
     *
     * @mbg.generated
     */
    public void setHrHeadCount(Integer hrHeadCount) {
        this.hrHeadCount = hrHeadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_two_level_abv
     *
     * @return the value of t_hr_admin_line_detail.hr_two_level_abv
     *
     * @mbg.generated
     */
    public Integer getHrTwoLevelAbv() {
        return hrTwoLevelAbv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_two_level_abv
     *
     * @param hrTwoLevelAbv the value for t_hr_admin_line_detail.hr_two_level_abv
     *
     * @mbg.generated
     */
    public void setHrTwoLevelAbv(Integer hrTwoLevelAbv) {
        this.hrTwoLevelAbv = hrTwoLevelAbv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_senior_mgr
     *
     * @return the value of t_hr_admin_line_detail.hr_senior_mgr
     *
     * @mbg.generated
     */
    public Integer getHrSeniorMgr() {
        return hrSeniorMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_senior_mgr
     *
     * @param hrSeniorMgr the value for t_hr_admin_line_detail.hr_senior_mgr
     *
     * @mbg.generated
     */
    public void setHrSeniorMgr(Integer hrSeniorMgr) {
        this.hrSeniorMgr = hrSeniorMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_manager
     *
     * @return the value of t_hr_admin_line_detail.hr_manager
     *
     * @mbg.generated
     */
    public Integer getHrManager() {
        return hrManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_manager
     *
     * @param hrManager the value for t_hr_admin_line_detail.hr_manager
     *
     * @mbg.generated
     */
    public void setHrManager(Integer hrManager) {
        this.hrManager = hrManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_deputy_mgr
     *
     * @return the value of t_hr_admin_line_detail.hr_deputy_mgr
     *
     * @mbg.generated
     */
    public Integer getHrDeputyMgr() {
        return hrDeputyMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_deputy_mgr
     *
     * @param hrDeputyMgr the value for t_hr_admin_line_detail.hr_deputy_mgr
     *
     * @mbg.generated
     */
    public void setHrDeputyMgr(Integer hrDeputyMgr) {
        this.hrDeputyMgr = hrDeputyMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_assis_mgr
     *
     * @return the value of t_hr_admin_line_detail.hr_assis_mgr
     *
     * @mbg.generated
     */
    public Integer getHrAssisMgr() {
        return hrAssisMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_assis_mgr
     *
     * @param hrAssisMgr the value for t_hr_admin_line_detail.hr_assis_mgr
     *
     * @mbg.generated
     */
    public void setHrAssisMgr(Integer hrAssisMgr) {
        this.hrAssisMgr = hrAssisMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.hr_other
     *
     * @return the value of t_hr_admin_line_detail.hr_other
     *
     * @mbg.generated
     */
    public Integer getHrOther() {
        return hrOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.hr_other
     *
     * @param hrOther the value for t_hr_admin_line_detail.hr_other
     *
     * @mbg.generated
     */
    public void setHrOther(Integer hrOther) {
        this.hrOther = hrOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_head_count
     *
     * @return the value of t_hr_admin_line_detail.admin_head_count
     *
     * @mbg.generated
     */
    public Integer getAdminHeadCount() {
        return adminHeadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_head_count
     *
     * @param adminHeadCount the value for t_hr_admin_line_detail.admin_head_count
     *
     * @mbg.generated
     */
    public void setAdminHeadCount(Integer adminHeadCount) {
        this.adminHeadCount = adminHeadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_two_level_abv
     *
     * @return the value of t_hr_admin_line_detail.admin_two_level_abv
     *
     * @mbg.generated
     */
    public Integer getAdminTwoLevelAbv() {
        return adminTwoLevelAbv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_two_level_abv
     *
     * @param adminTwoLevelAbv the value for t_hr_admin_line_detail.admin_two_level_abv
     *
     * @mbg.generated
     */
    public void setAdminTwoLevelAbv(Integer adminTwoLevelAbv) {
        this.adminTwoLevelAbv = adminTwoLevelAbv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_senior_mgr
     *
     * @return the value of t_hr_admin_line_detail.admin_senior_mgr
     *
     * @mbg.generated
     */
    public Integer getAdminSeniorMgr() {
        return adminSeniorMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_senior_mgr
     *
     * @param adminSeniorMgr the value for t_hr_admin_line_detail.admin_senior_mgr
     *
     * @mbg.generated
     */
    public void setAdminSeniorMgr(Integer adminSeniorMgr) {
        this.adminSeniorMgr = adminSeniorMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_manager
     *
     * @return the value of t_hr_admin_line_detail.admin_manager
     *
     * @mbg.generated
     */
    public Integer getAdminManager() {
        return adminManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_manager
     *
     * @param adminManager the value for t_hr_admin_line_detail.admin_manager
     *
     * @mbg.generated
     */
    public void setAdminManager(Integer adminManager) {
        this.adminManager = adminManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_deputy_mgr
     *
     * @return the value of t_hr_admin_line_detail.admin_deputy_mgr
     *
     * @mbg.generated
     */
    public Integer getAdminDeputyMgr() {
        return adminDeputyMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_deputy_mgr
     *
     * @param adminDeputyMgr the value for t_hr_admin_line_detail.admin_deputy_mgr
     *
     * @mbg.generated
     */
    public void setAdminDeputyMgr(Integer adminDeputyMgr) {
        this.adminDeputyMgr = adminDeputyMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_assis_mgr
     *
     * @return the value of t_hr_admin_line_detail.admin_assis_mgr
     *
     * @mbg.generated
     */
    public Integer getAdminAssisMgr() {
        return adminAssisMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_assis_mgr
     *
     * @param adminAssisMgr the value for t_hr_admin_line_detail.admin_assis_mgr
     *
     * @mbg.generated
     */
    public void setAdminAssisMgr(Integer adminAssisMgr) {
        this.adminAssisMgr = adminAssisMgr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_other
     *
     * @return the value of t_hr_admin_line_detail.admin_other
     *
     * @mbg.generated
     */
    public Integer getAdminOther() {
        return adminOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_other
     *
     * @param adminOther the value for t_hr_admin_line_detail.admin_other
     *
     * @mbg.generated
     */
    public void setAdminOther(Integer adminOther) {
        this.adminOther = adminOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.admin_office
     *
     * @return the value of t_hr_admin_line_detail.admin_office
     *
     * @mbg.generated
     */
    public Integer getAdminOffice() {
        return adminOffice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.admin_office
     *
     * @param adminOffice the value for t_hr_admin_line_detail.admin_office
     *
     * @mbg.generated
     */
    public void setAdminOffice(Integer adminOffice) {
        this.adminOffice = adminOffice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.seq
     *
     * @return the value of t_hr_admin_line_detail.seq
     *
     * @mbg.generated
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.seq
     *
     * @param seq the value for t_hr_admin_line_detail.seq
     *
     * @mbg.generated
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.is_deleted
     *
     * @return the value of t_hr_admin_line_detail.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.is_deleted
     *
     * @param isDeleted the value for t_hr_admin_line_detail.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.delete_version
     *
     * @return the value of t_hr_admin_line_detail.delete_version
     *
     * @mbg.generated
     */
    public Integer getDeleteVersion() {
        return deleteVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.delete_version
     *
     * @param deleteVersion the value for t_hr_admin_line_detail.delete_version
     *
     * @mbg.generated
     */
    public void setDeleteVersion(Integer deleteVersion) {
        this.deleteVersion = deleteVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.creation_time
     *
     * @return the value of t_hr_admin_line_detail.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.creation_time
     *
     * @param creationTime the value for t_hr_admin_line_detail.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.create_user_id
     *
     * @return the value of t_hr_admin_line_detail.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.create_user_id
     *
     * @param createUserId the value for t_hr_admin_line_detail.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.create_username
     *
     * @return the value of t_hr_admin_line_detail.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.create_username
     *
     * @param createUsername the value for t_hr_admin_line_detail.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.last_update_time
     *
     * @return the value of t_hr_admin_line_detail.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.last_update_time
     *
     * @param lastUpdateTime the value for t_hr_admin_line_detail.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.last_update_user_id
     *
     * @return the value of t_hr_admin_line_detail.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_hr_admin_line_detail.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.last_update_username
     *
     * @return the value of t_hr_admin_line_detail.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.last_update_username
     *
     * @param lastUpdateUsername the value for t_hr_admin_line_detail.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_admin_line_detail.last_update_version
     *
     * @return the value of t_hr_admin_line_detail.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_admin_line_detail.last_update_version
     *
     * @param lastUpdateVersion the value for t_hr_admin_line_detail.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}