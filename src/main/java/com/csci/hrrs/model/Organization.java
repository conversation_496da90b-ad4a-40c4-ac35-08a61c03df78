package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_organization
 */
@TableName("t_organization")
public class Organization {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.id
     *
     * @mbg.generated
     */
    @TableField("id")
    @TableId
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.parent_id
     *
     * @mbg.generated
     */
    @TableField("parent_id")
    private String parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.parent_code
     *
     * @mbg.generated
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.full_path
     *
     * @mbg.generated
     */
    @TableField("full_path")
    private String fullPath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.no
     *
     * @mbg.generated
     */
    @TableField("no")
    private String no;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.code
     *
     * @mbg.generated
     */
    @TableField("code")
    private String code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.name
     *
     * @mbg.generated
     */
    @TableField("name")
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.name_trad
     *
     * @mbg.generated
     */
    @TableField("name_trad")
    private String nameTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.name_eng
     *
     * @mbg.generated
     */
    @TableField("name_eng")
    private String nameEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.full_name
     *
     * @mbg.generated
     */
    @TableField("full_name")
    private String fullName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.full_name_trad
     *
     * @mbg.generated
     */
    @TableField("full_name_trad")
    private String fullNameTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.full_name_eng
     *
     * @mbg.generated
     */
    @TableField("full_name_eng")
    private String fullNameEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.company_name
     *
     * @mbg.generated
     */
    @TableField("company_name")
    private String companyName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.company_name_trad
     *
     * @mbg.generated
     */
    @TableField("company_name_trad")
    private String companyNameTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.company_name_eng
     *
     * @mbg.generated
     */
    @TableField("company_name_eng")
    private String companyNameEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.sub_company_dept
     *
     * @mbg.generated
     */
    @TableField("sub_company_dept")
    private String subCompanyDept;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.sub_company_dept_trad
     *
     * @mbg.generated
     */
    @TableField("sub_company_dept_trad")
    private String subCompanyDeptTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.sub_company_dept_eng
     *
     * @mbg.generated
     */
    @TableField("sub_company_dept_eng")
    private String subCompanyDeptEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.project_name
     *
     * @mbg.generated
     */
    @TableField("project_name")
    private String projectName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.project_name_trad
     *
     * @mbg.generated
     */
    @TableField("project_name_trad")
    private String projectNameTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.project_name_eng
     *
     * @mbg.generated
     */
    @TableField("project_name_eng")
    private String projectNameEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.title
     *
     * @mbg.generated
     */
    @TableField("title")
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.address
     *
     * @mbg.generated
     */
    @TableField("address")
    private String address;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.latitude
     *
     * @mbg.generated
     */
    @TableField("latitude")
    private String latitude;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.longitude
     *
     * @mbg.generated
     */
    @TableField("longitude")
    private String longitude;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.currency
     *
     * @mbg.generated
     */
    @TableField("currency")
    private String currency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.division
     *
     * @mbg.generated
     */
    @TableField("division")
    private String division;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.region
     *
     * @mbg.generated
     */
    @TableField("region")
    private String region;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.power_supply
     *
     * @mbg.generated
     */
    @TableField("power_supply")
    private String powerSupply;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.start_date
     *
     * @mbg.generated
     */
    @TableField(value = "start_date", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.DATE)
    private LocalDate startDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.end_date
     *
     * @mbg.generated
     */
    @TableField(value = "end_date", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.DATE)
    private LocalDate endDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_completed
     *
     * @mbg.generated
     */
    @TableField("is_completed")
    private String isCompleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.jv
     *
     * @mbg.generated
     */
    @TableField("jv")
    private String jv;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.total_cfa
     *
     * @mbg.generated
     */
    @TableField("total_cfa")
    private String totalCfa;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.sort
     *
     * @mbg.generated
     */
    @TableField("sort")
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.scene
     *
     * @mbg.generated
     */
    @TableField("scene")
    private String scene;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.area_id
     *
     * @mbg.generated
     */
    @TableField("area_id")
    private String areaId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_company
     *
     * @mbg.generated
     */
    @TableField("is_company")
    private Boolean isCompany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_deleted
     *
     * @mbg.generated
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.creation_time
     *
     * @mbg.generated
     */
    @TableField("creation_time")
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.create_username
     *
     * @mbg.generated
     */
    @TableField("create_username")
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.create_user_id
     *
     * @mbg.generated
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.last_update_time
     *
     * @mbg.generated
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.last_update_username
     *
     * @mbg.generated
     */
    @TableField("last_update_username")
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.last_update_user_id
     *
     * @mbg.generated
     */
    @TableField("last_update_user_id")
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.last_update_version
     *
     * @mbg.generated
     */
    @TableField("last_update_version")
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.alias
     *
     * @mbg.generated
     */
    @TableField("alias")
    private String alias;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.stat_org_name
     *
     * @mbg.generated
     */
    @TableField("stat_org_name")
    private String statOrgName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.stat_date
     *
     * @mbg.generated
     */
    @TableField("stat_date")
    private LocalDate statDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.from_recoverydate
     *
     * @mbg.generated
     */
    @TableField("from_recoverydate")
    private LocalDate fromRecoverydate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.site_code
     *
     * @mbg.generated
     */
    @TableField("site_code")
    private String siteCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_project
     *
     * @mbg.generated
     */
    @TableField("is_project")
    private Boolean isProject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.on_construction
     *
     * @mbg.generated
     */
    @TableField(value = "on_construction", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.BIT)
    private Boolean onConstruction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.team_leader_username
     *
     * @mbg.generated
     */
    @TableField(value = "team_leader_username", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String teamLeaderUsername;

    public String getTeamLeaderName() {
        return teamLeaderName;
    }

    public void setTeamLeaderName(String teamLeaderName) {
        this.teamLeaderName = teamLeaderName;
    }

    public String getSiteLeaderName() {
        return siteLeaderName;
    }

    public void setSiteLeaderName(String siteLeaderName) {
        this.siteLeaderName = siteLeaderName;
    }

    @TableField(value = "team_leader_name", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String teamLeaderName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.site_leader_username
     *
     * @mbg.generated
     */
    @TableField(value = "site_leader_username", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String siteLeaderUsername;

    @TableField(value = "site_leader_name", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String siteLeaderName;

    @TableField(value = "organization_category", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String organizationCategory;


    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.contract_amount
     *
     * @mbg.generated
     */
    @TableField(value = "contract_amount", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.DECIMAL)
    private BigDecimal contractAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.project_type
     *
     * @mbg.generated
     */
    @TableField(value = "project_type", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String projectType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.project_scale
     *
     * @mbg.generated
     */
    @TableField(value = "project_scale", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.NVARCHAR)
    private String projectScale;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.short_name
     *
     * @mbg.generated
     */
    @TableField("short_name")
    private String shortName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.contract_start_date
     *
     * @mbg.generated
     */
    @TableField(value = "contract_start_date", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.DATE)
    private LocalDate contractStartDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.contract_end_date
     *
     * @mbg.generated
     */
    @TableField(value = "contract_end_date", updateStrategy= FieldStrategy.ALWAYS, jdbcType = JdbcType.DATE)
    private LocalDate contractEndDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_start_date_modified
     *
     * @mbg.generated
     */
    @TableField("is_start_date_modified")
    private Boolean isStartDateModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_end_date_modified
     *
     * @mbg.generated
     */
    @TableField("is_end_date_modified ")
    private Boolean isEndDateModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_start_date_modified
     *
     * @mbg.generated
     */
    @TableField("is_contract_start_date_modified")
    private Boolean isContractStartDateModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_organization.is_end_date_modified
     *
     * @mbg.generated
     */
    @TableField("is_contract_end_date_modified ")
    private Boolean isContractEndDateModified;

    @TableField("is_on_construction_modified")
    private Boolean isOnConstructionModified;


    public Byte getIsContractAmountModified() {
        return isContractAmountModified;
    }

    public void setIsContractAmountModified(Byte isContractAmountModified) {
        this.isContractAmountModified = isContractAmountModified;
    }

    @TableField("is_contract_amount_modified")
    private Byte isContractAmountModified;


    public BigDecimal getSynchContractAmount() {
        return synchContractAmount;
    }

    public void setSynchContractAmount(BigDecimal synchContractAmount) {
        this.synchContractAmount = synchContractAmount;
    }

    @TableField("synch_contract_amount")
    private BigDecimal synchContractAmount;

    @TableField("org_attr_code")
    private String orgAttrCode;

    @TableField("org_attr_name")
    private String orgAttrName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.id
     *
     * @return the value of t_organization.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.id
     *
     * @param id the value for t_organization.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.parent_id
     *
     * @return the value of t_organization.parent_id
     *
     * @mbg.generated
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.parent_id
     *
     * @param parentId the value for t_organization.parent_id
     *
     * @mbg.generated
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.parent_code
     *
     * @return the value of t_organization.parent_code
     *
     * @mbg.generated
     */
    public String getParentCode() {
        return parentCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.parent_code
     *
     * @param parentCode the value for t_organization.parent_code
     *
     * @mbg.generated
     */
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.full_path
     *
     * @return the value of t_organization.full_path
     *
     * @mbg.generated
     */
    public String getFullPath() {
        return fullPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.full_path
     *
     * @param fullPath the value for t_organization.full_path
     *
     * @mbg.generated
     */
    public void setFullPath(String fullPath) {
        this.fullPath = fullPath == null ? null : fullPath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.no
     *
     * @return the value of t_organization.no
     *
     * @mbg.generated
     */
    public String getNo() {
        return no;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.no
     *
     * @param no the value for t_organization.no
     *
     * @mbg.generated
     */
    public void setNo(String no) {
        this.no = no == null ? null : no.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.code
     *
     * @return the value of t_organization.code
     *
     * @mbg.generated
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.code
     *
     * @param code the value for t_organization.code
     *
     * @mbg.generated
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.name
     *
     * @return the value of t_organization.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.name
     *
     * @param name the value for t_organization.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.name_trad
     *
     * @return the value of t_organization.name_trad
     *
     * @mbg.generated
     */
    public String getNameTrad() {
        return nameTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.name_trad
     *
     * @param nameTrad the value for t_organization.name_trad
     *
     * @mbg.generated
     */
    public void setNameTrad(String nameTrad) {
        this.nameTrad = nameTrad == null ? null : nameTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.name_eng
     *
     * @return the value of t_organization.name_eng
     *
     * @mbg.generated
     */
    public String getNameEng() {
        return nameEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.name_eng
     *
     * @param nameEng the value for t_organization.name_eng
     *
     * @mbg.generated
     */
    public void setNameEng(String nameEng) {
        this.nameEng = nameEng == null ? null : nameEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.full_name
     *
     * @return the value of t_organization.full_name
     *
     * @mbg.generated
     */
    public String getFullName() {
        return fullName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.full_name
     *
     * @param fullName the value for t_organization.full_name
     *
     * @mbg.generated
     */
    public void setFullName(String fullName) {
        this.fullName = fullName == null ? null : fullName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.full_name_trad
     *
     * @return the value of t_organization.full_name_trad
     *
     * @mbg.generated
     */
    public String getFullNameTrad() {
        return fullNameTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.full_name_trad
     *
     * @param fullNameTrad the value for t_organization.full_name_trad
     *
     * @mbg.generated
     */
    public void setFullNameTrad(String fullNameTrad) {
        this.fullNameTrad = fullNameTrad == null ? null : fullNameTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.full_name_eng
     *
     * @return the value of t_organization.full_name_eng
     *
     * @mbg.generated
     */
    public String getFullNameEng() {
        return fullNameEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.full_name_eng
     *
     * @param fullNameEng the value for t_organization.full_name_eng
     *
     * @mbg.generated
     */
    public void setFullNameEng(String fullNameEng) {
        this.fullNameEng = fullNameEng == null ? null : fullNameEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.company_name
     *
     * @return the value of t_organization.company_name
     *
     * @mbg.generated
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.company_name
     *
     * @param companyName the value for t_organization.company_name
     *
     * @mbg.generated
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.company_name_trad
     *
     * @return the value of t_organization.company_name_trad
     *
     * @mbg.generated
     */
    public String getCompanyNameTrad() {
        return companyNameTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.company_name_trad
     *
     * @param companyNameTrad the value for t_organization.company_name_trad
     *
     * @mbg.generated
     */
    public void setCompanyNameTrad(String companyNameTrad) {
        this.companyNameTrad = companyNameTrad == null ? null : companyNameTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.company_name_eng
     *
     * @return the value of t_organization.company_name_eng
     *
     * @mbg.generated
     */
    public String getCompanyNameEng() {
        return companyNameEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.company_name_eng
     *
     * @param companyNameEng the value for t_organization.company_name_eng
     *
     * @mbg.generated
     */
    public void setCompanyNameEng(String companyNameEng) {
        this.companyNameEng = companyNameEng == null ? null : companyNameEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.sub_company_dept
     *
     * @return the value of t_organization.sub_company_dept
     *
     * @mbg.generated
     */
    public String getSubCompanyDept() {
        return subCompanyDept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.sub_company_dept
     *
     * @param subCompanyDept the value for t_organization.sub_company_dept
     *
     * @mbg.generated
     */
    public void setSubCompanyDept(String subCompanyDept) {
        this.subCompanyDept = subCompanyDept == null ? null : subCompanyDept.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.sub_company_dept_trad
     *
     * @return the value of t_organization.sub_company_dept_trad
     *
     * @mbg.generated
     */
    public String getSubCompanyDeptTrad() {
        return subCompanyDeptTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.sub_company_dept_trad
     *
     * @param subCompanyDeptTrad the value for t_organization.sub_company_dept_trad
     *
     * @mbg.generated
     */
    public void setSubCompanyDeptTrad(String subCompanyDeptTrad) {
        this.subCompanyDeptTrad = subCompanyDeptTrad == null ? null : subCompanyDeptTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.sub_company_dept_eng
     *
     * @return the value of t_organization.sub_company_dept_eng
     *
     * @mbg.generated
     */
    public String getSubCompanyDeptEng() {
        return subCompanyDeptEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.sub_company_dept_eng
     *
     * @param subCompanyDeptEng the value for t_organization.sub_company_dept_eng
     *
     * @mbg.generated
     */
    public void setSubCompanyDeptEng(String subCompanyDeptEng) {
        this.subCompanyDeptEng = subCompanyDeptEng == null ? null : subCompanyDeptEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.project_name
     *
     * @return the value of t_organization.project_name
     *
     * @mbg.generated
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.project_name
     *
     * @param projectName the value for t_organization.project_name
     *
     * @mbg.generated
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.project_name_trad
     *
     * @return the value of t_organization.project_name_trad
     *
     * @mbg.generated
     */
    public String getProjectNameTrad() {
        return projectNameTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.project_name_trad
     *
     * @param projectNameTrad the value for t_organization.project_name_trad
     *
     * @mbg.generated
     */
    public void setProjectNameTrad(String projectNameTrad) {
        this.projectNameTrad = projectNameTrad == null ? null : projectNameTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.project_name_eng
     *
     * @return the value of t_organization.project_name_eng
     *
     * @mbg.generated
     */
    public String getProjectNameEng() {
        return projectNameEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.project_name_eng
     *
     * @param projectNameEng the value for t_organization.project_name_eng
     *
     * @mbg.generated
     */
    public void setProjectNameEng(String projectNameEng) {
        this.projectNameEng = projectNameEng == null ? null : projectNameEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.title
     *
     * @return the value of t_organization.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.title
     *
     * @param title the value for t_organization.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.address
     *
     * @return the value of t_organization.address
     *
     * @mbg.generated
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.address
     *
     * @param address the value for t_organization.address
     *
     * @mbg.generated
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.latitude
     *
     * @return the value of t_organization.latitude
     *
     * @mbg.generated
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.latitude
     *
     * @param latitude the value for t_organization.latitude
     *
     * @mbg.generated
     */
    public void setLatitude(String latitude) {
        this.latitude = latitude == null ? null : latitude.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.longitude
     *
     * @return the value of t_organization.longitude
     *
     * @mbg.generated
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.longitude
     *
     * @param longitude the value for t_organization.longitude
     *
     * @mbg.generated
     */
    public void setLongitude(String longitude) {
        this.longitude = longitude == null ? null : longitude.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.currency
     *
     * @return the value of t_organization.currency
     *
     * @mbg.generated
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.currency
     *
     * @param currency the value for t_organization.currency
     *
     * @mbg.generated
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.division
     *
     * @return the value of t_organization.division
     *
     * @mbg.generated
     */
    public String getDivision() {
        return division;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.division
     *
     * @param division the value for t_organization.division
     *
     * @mbg.generated
     */
    public void setDivision(String division) {
        this.division = division == null ? null : division.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.region
     *
     * @return the value of t_organization.region
     *
     * @mbg.generated
     */
    public String getRegion() {
        return region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.region
     *
     * @param region the value for t_organization.region
     *
     * @mbg.generated
     */
    public void setRegion(String region) {
        this.region = region == null ? null : region.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.power_supply
     *
     * @return the value of t_organization.power_supply
     *
     * @mbg.generated
     */
    public String getPowerSupply() {
        return powerSupply;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.power_supply
     *
     * @param powerSupply the value for t_organization.power_supply
     *
     * @mbg.generated
     */
    public void setPowerSupply(String powerSupply) {
        this.powerSupply = powerSupply == null ? null : powerSupply.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.start_date
     *
     * @return the value of t_organization.start_date
     *
     * @mbg.generated
     */
    public LocalDate getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.start_date
     *
     * @param startDate the value for t_organization.start_date
     *
     * @mbg.generated
     */
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.end_date
     *
     * @return the value of t_organization.end_date
     *
     * @mbg.generated
     */
    public LocalDate getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.end_date
     *
     * @param endDate the value for t_organization.end_date
     *
     * @mbg.generated
     */
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_completed
     *
     * @return the value of t_organization.is_completed
     *
     * @mbg.generated
     */
    public String getIsCompleted() {
        return isCompleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_completed
     *
     * @param isCompleted the value for t_organization.is_completed
     *
     * @mbg.generated
     */
    public void setIsCompleted(String isCompleted) {
        this.isCompleted = isCompleted == null ? null : isCompleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.jv
     *
     * @return the value of t_organization.jv
     *
     * @mbg.generated
     */
    public String getJv() {
        return jv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.jv
     *
     * @param jv the value for t_organization.jv
     *
     * @mbg.generated
     */
    public void setJv(String jv) {
        this.jv = jv == null ? null : jv.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.total_cfa
     *
     * @return the value of t_organization.total_cfa
     *
     * @mbg.generated
     */
    public String getTotalCfa() {
        return totalCfa;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.total_cfa
     *
     * @param totalCfa the value for t_organization.total_cfa
     *
     * @mbg.generated
     */
    public void setTotalCfa(String totalCfa) {
        this.totalCfa = totalCfa == null ? null : totalCfa.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.sort
     *
     * @return the value of t_organization.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.sort
     *
     * @param sort the value for t_organization.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.scene
     *
     * @return the value of t_organization.scene
     *
     * @mbg.generated
     */
    public String getScene() {
        return scene;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.scene
     *
     * @param scene the value for t_organization.scene
     *
     * @mbg.generated
     */
    public void setScene(String scene) {
        this.scene = scene == null ? null : scene.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.area_id
     *
     * @return the value of t_organization.area_id
     *
     * @mbg.generated
     */
    public String getAreaId() {
        return areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.area_id
     *
     * @param areaId the value for t_organization.area_id
     *
     * @mbg.generated
     */
    public void setAreaId(String areaId) {
        this.areaId = areaId == null ? null : areaId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_company
     *
     * @return the value of t_organization.is_company
     *
     * @mbg.generated
     */
    public Boolean getIsCompany() {
        return isCompany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_company
     *
     * @param isCompany the value for t_organization.is_company
     *
     * @mbg.generated
     */
    public void setIsCompany(Boolean isCompany) {
        this.isCompany = isCompany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_deleted
     *
     * @return the value of t_organization.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_deleted
     *
     * @param isDeleted the value for t_organization.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.creation_time
     *
     * @return the value of t_organization.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.creation_time
     *
     * @param creationTime the value for t_organization.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.create_username
     *
     * @return the value of t_organization.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.create_username
     *
     * @param createUsername the value for t_organization.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.create_user_id
     *
     * @return the value of t_organization.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.create_user_id
     *
     * @param createUserId the value for t_organization.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.last_update_time
     *
     * @return the value of t_organization.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.last_update_time
     *
     * @param lastUpdateTime the value for t_organization.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.last_update_username
     *
     * @return the value of t_organization.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.last_update_username
     *
     * @param lastUpdateUsername the value for t_organization.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.last_update_user_id
     *
     * @return the value of t_organization.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_organization.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.last_update_version
     *
     * @return the value of t_organization.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.last_update_version
     *
     * @param lastUpdateVersion the value for t_organization.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.alias
     *
     * @return the value of t_organization.alias
     *
     * @mbg.generated
     */
    public String getAlias() {
        return alias;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.alias
     *
     * @param alias the value for t_organization.alias
     *
     * @mbg.generated
     */
    public void setAlias(String alias) {
        this.alias = alias == null ? null : alias.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.stat_org_name
     *
     * @return the value of t_organization.stat_org_name
     *
     * @mbg.generated
     */
    public String getStatOrgName() {
        return statOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.stat_org_name
     *
     * @param statOrgName the value for t_organization.stat_org_name
     *
     * @mbg.generated
     */
    public void setStatOrgName(String statOrgName) {
        this.statOrgName = statOrgName == null ? null : statOrgName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.stat_date
     *
     * @return the value of t_organization.stat_date
     *
     * @mbg.generated
     */
    public LocalDate getStatDate() {
        return statDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.stat_date
     *
     * @param statDate the value for t_organization.stat_date
     *
     * @mbg.generated
     */
    public void setStatDate(LocalDate statDate) {
        this.statDate = statDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.from_recoverydate
     *
     * @return the value of t_organization.from_recoverydate
     *
     * @mbg.generated
     */
    public LocalDate getFromRecoverydate() {
        return fromRecoverydate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.from_recoverydate
     *
     * @param fromRecoverydate the value for t_organization.from_recoverydate
     *
     * @mbg.generated
     */
    public void setFromRecoverydate(LocalDate fromRecoverydate) {
        this.fromRecoverydate = fromRecoverydate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.site_code
     *
     * @return the value of t_organization.site_code
     *
     * @mbg.generated
     */
    public String getSiteCode() {
        return siteCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.site_code
     *
     * @param siteCode the value for t_organization.site_code
     *
     * @mbg.generated
     */
    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode == null ? null : siteCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_project
     *
     * @return the value of t_organization.is_project
     *
     * @mbg.generated
     */
    public Boolean getIsProject() {
        return isProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_project
     *
     * @param isProject the value for t_organization.is_project
     *
     * @mbg.generated
     */
    public void setIsProject(Boolean isProject) {
        this.isProject = isProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.on_construction
     *
     * @return the value of t_organization.on_construction
     *
     * @mbg.generated
     */
    public Boolean getOnConstruction() {
        return onConstruction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.on_construction
     *
     * @param onConstruction the value for t_organization.on_construction
     *
     * @mbg.generated
     */
    public void setOnConstruction(Boolean onConstruction) {
        this.onConstruction = onConstruction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.team_leader_username
     *
     * @return the value of t_organization.team_leader_username
     *
     * @mbg.generated
     */
    public String getTeamLeaderUsername() {
        return teamLeaderUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.team_leader_username
     *
     * @param teamLeaderUsername the value for t_organization.team_leader_username
     *
     * @mbg.generated
     */
    public void setTeamLeaderUsername(String teamLeaderUsername) {
        this.teamLeaderUsername = teamLeaderUsername == null ? null : teamLeaderUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.site_leader_username
     *
     * @return the value of t_organization.site_leader_username
     *
     * @mbg.generated
     */
    public String getSiteLeaderUsername() {
        return siteLeaderUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.site_leader_username
     *
     * @param siteLeaderUsername the value for t_organization.site_leader_username
     *
     * @mbg.generated
     */
    public void setSiteLeaderUsername(String siteLeaderUsername) {
        this.siteLeaderUsername = siteLeaderUsername == null ? null : siteLeaderUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.contract_amount
     *
     * @return the value of t_organization.contract_amount
     *
     * @mbg.generated
     */
    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.contract_amount
     *
     * @param contractAmount the value for t_organization.contract_amount
     *
     * @mbg.generated
     */
    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.project_type
     *
     * @return the value of t_organization.project_type
     *
     * @mbg.generated
     */
    public String getProjectType() {
        return projectType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.project_type
     *
     * @param projectType the value for t_organization.project_type
     *
     * @mbg.generated
     */
    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.project_scale
     *
     * @return the value of t_organization.project_scale
     *
     * @mbg.generated
     */
    public String getProjectScale() {
        return projectScale;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.project_scale
     *
     * @param projectScale the value for t_organization.project_scale
     *
     * @mbg.generated
     */
    public void setProjectScale(String projectScale) {
        this.projectScale = projectScale == null ? null : projectScale.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.short_name
     *
     * @return the value of t_organization.short_name
     *
     * @mbg.generated
     */
    public String getShortName() {
        return shortName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.short_name
     *
     * @param shortName the value for t_organization.short_name
     *
     * @mbg.generated
     */
    public void setShortName(String shortName) {
        this.shortName = shortName == null ? null : shortName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.contract_start_date
     *
     * @return the value of t_organization.contract_start_date
     *
     * @mbg.generated
     */
    public LocalDate getContractStartDate() {
        return contractStartDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.contract_start_date
     *
     * @param contractStartDate the value for t_organization.contract_start_date
     *
     * @mbg.generated
     */
    public void setContractStartDate(LocalDate contractStartDate) {
        this.contractStartDate = contractStartDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.contract_end_date
     *
     * @return the value of t_organization.contract_end_date
     *
     * @mbg.generated
     */
    public LocalDate getContractEndDate() {
        return contractEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.contract_end_date
     *
     * @param contractEndDate the value for t_organization.contract_end_date
     *
     * @mbg.generated
     */
    public void setContractEndDate(LocalDate contractEndDate) {
        this.contractEndDate = contractEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_start_date_modified
     *
     * @return the value of t_organization.is_start_date_modified
     *
     * @mbg.generated
     */
    public Boolean getIsStartDateModified() {
        return isStartDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_start_date_modified
     *
     * @param isStartDateModified the value for t_organization.is_start_date_modified
     *
     * @mbg.generated
     */
    public void setIsStartDateModified(Boolean isStartDateModified) {
        this.isStartDateModified = isStartDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_end_date_modified
     *
     * @return the value of t_organization.is_end_date_modified
     *
     * @mbg.generated
     */
    public Boolean getIsEndDateModified() {
        return isEndDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_end_date_modified
     *
     * @param isEndDateModified the value for t_organization.is_end_date_modified
     *
     * @mbg.generated
     */
    public void setIsEndDateModified(Boolean isEndDateModified) {
        this.isEndDateModified = isEndDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_contract_start_date_modified
     *
     * @return the value of t_organization.is_contract_start_date_modified
     *
     * @mbg.generated
     */
    public Boolean getIsContractStartDateModified() {
        return isContractStartDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_contract_start_date_modified
     *
     * @param isContractStartDateModified the value for t_organization.is_contract_start_date_modified
     *
     * @mbg.generated
     */
    public void setIsContractStartDateModified(Boolean isContractStartDateModified) {
        this.isContractStartDateModified = isContractStartDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_organization.is_contract_end_date_modified
     *
     * @return the value of t_organization.is_contract_end_date_modified
     *
     * @mbg.generated
     */
    public Boolean getIsContractEndDateModified() {
        return isContractEndDateModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_organization.is_contract_end_date_modified
     *
     * @param isContractEndDateModified the value for t_organization.is_contract_end_date_modified
     *
     * @mbg.generated
     */
    public void setIsContractEndDateModified(Boolean isContractEndDateModified) {
        this.isContractEndDateModified = isContractEndDateModified;
    }

    public String getOrgAttrCode() {
        return orgAttrCode;
    }

    public void setOrgAttrCode(String orgAttrCode) {
        this.orgAttrCode = orgAttrCode;
    }

    public String getOrgAttrName() {
        return orgAttrName;
    }

    public void setOrgAttrName(String orgAttrName) {
        this.orgAttrName = orgAttrName;
    }

    public Boolean getOnConstructionModified() {
        return isOnConstructionModified;
    }

    public void setOnConstructionModified(Boolean onConstructionModified) {
        isOnConstructionModified = onConstructionModified;
    }

    public String getOrganizationCategory() {
        return organizationCategory;
    }

    public void setOrganizationCategory(String organizationCategory) {
        this.organizationCategory = organizationCategory;
    }
}