package com.csci.hrrs.model;

import java.util.ArrayList;
import java.util.List;

public class InternalExperienceExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public InternalExperienceExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPernrIsNull() {
            addCriterion("PERNR is null");
            return (Criteria) this;
        }

        public Criteria andPernrIsNotNull() {
            addCriterion("PERNR is not null");
            return (Criteria) this;
        }

        public Criteria andPernrEqualTo(String value) {
            addCriterion("PERNR =", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotEqualTo(String value) {
            addCriterion("PERNR <>", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThan(String value) {
            addCriterion("PERNR >", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThanOrEqualTo(String value) {
            addCriterion("PERNR >=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThan(String value) {
            addCriterion("PERNR <", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThanOrEqualTo(String value) {
            addCriterion("PERNR <=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLike(String value) {
            addCriterion("PERNR like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotLike(String value) {
            addCriterion("PERNR not like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrIn(List<String> values) {
            addCriterion("PERNR in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotIn(List<String> values) {
            addCriterion("PERNR not in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrBetween(String value1, String value2) {
            addCriterion("PERNR between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotBetween(String value1, String value2) {
            addCriterion("PERNR not between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andBegdaIsNull() {
            addCriterion("BEGDA is null");
            return (Criteria) this;
        }

        public Criteria andBegdaIsNotNull() {
            addCriterion("BEGDA is not null");
            return (Criteria) this;
        }

        public Criteria andBegdaEqualTo(String value) {
            addCriterion("BEGDA =", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotEqualTo(String value) {
            addCriterion("BEGDA <>", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaGreaterThan(String value) {
            addCriterion("BEGDA >", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaGreaterThanOrEqualTo(String value) {
            addCriterion("BEGDA >=", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLessThan(String value) {
            addCriterion("BEGDA <", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLessThanOrEqualTo(String value) {
            addCriterion("BEGDA <=", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLike(String value) {
            addCriterion("BEGDA like", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotLike(String value) {
            addCriterion("BEGDA not like", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaIn(List<String> values) {
            addCriterion("BEGDA in", values, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotIn(List<String> values) {
            addCriterion("BEGDA not in", values, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaBetween(String value1, String value2) {
            addCriterion("BEGDA between", value1, value2, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotBetween(String value1, String value2) {
            addCriterion("BEGDA not between", value1, value2, "begda");
            return (Criteria) this;
        }

        public Criteria andEnddaIsNull() {
            addCriterion("ENDDA is null");
            return (Criteria) this;
        }

        public Criteria andEnddaIsNotNull() {
            addCriterion("ENDDA is not null");
            return (Criteria) this;
        }

        public Criteria andEnddaEqualTo(String value) {
            addCriterion("ENDDA =", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotEqualTo(String value) {
            addCriterion("ENDDA <>", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaGreaterThan(String value) {
            addCriterion("ENDDA >", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaGreaterThanOrEqualTo(String value) {
            addCriterion("ENDDA >=", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLessThan(String value) {
            addCriterion("ENDDA <", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLessThanOrEqualTo(String value) {
            addCriterion("ENDDA <=", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLike(String value) {
            addCriterion("ENDDA like", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotLike(String value) {
            addCriterion("ENDDA not like", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaIn(List<String> values) {
            addCriterion("ENDDA in", values, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotIn(List<String> values) {
            addCriterion("ENDDA not in", values, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaBetween(String value1, String value2) {
            addCriterion("ENDDA between", value1, value2, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotBetween(String value1, String value2) {
            addCriterion("ENDDA not between", value1, value2, "endda");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNull() {
            addCriterion("company is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNotNull() {
            addCriterion("company is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEqualTo(String value) {
            addCriterion("company =", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotEqualTo(String value) {
            addCriterion("company <>", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThan(String value) {
            addCriterion("company >", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("company >=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThan(String value) {
            addCriterion("company <", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThanOrEqualTo(String value) {
            addCriterion("company <=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLike(String value) {
            addCriterion("company like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotLike(String value) {
            addCriterion("company not like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyIn(List<String> values) {
            addCriterion("company in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotIn(List<String> values) {
            addCriterion("company not in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyBetween(String value1, String value2) {
            addCriterion("company between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotBetween(String value1, String value2) {
            addCriterion("company not between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(String value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(String value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(String value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(String value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(String value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(String value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLike(String value) {
            addCriterion("department_id like", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotLike(String value) {
            addCriterion("department_id not like", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<String> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<String> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(String value1, String value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(String value1, String value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andJobIsNull() {
            addCriterion("job is null");
            return (Criteria) this;
        }

        public Criteria andJobIsNotNull() {
            addCriterion("job is not null");
            return (Criteria) this;
        }

        public Criteria andJobEqualTo(String value) {
            addCriterion("job =", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotEqualTo(String value) {
            addCriterion("job <>", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThan(String value) {
            addCriterion("job >", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThanOrEqualTo(String value) {
            addCriterion("job >=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThan(String value) {
            addCriterion("job <", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThanOrEqualTo(String value) {
            addCriterion("job <=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLike(String value) {
            addCriterion("job like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotLike(String value) {
            addCriterion("job not like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobIn(List<String> values) {
            addCriterion("job in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotIn(List<String> values) {
            addCriterion("job not in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobBetween(String value1, String value2) {
            addCriterion("job between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotBetween(String value1, String value2) {
            addCriterion("job not between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxIsNull() {
            addCriterion("ZZ_CZYYHKTX is null");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxIsNotNull() {
            addCriterion("ZZ_CZYYHKTX is not null");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxEqualTo(String value) {
            addCriterion("ZZ_CZYYHKTX =", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxNotEqualTo(String value) {
            addCriterion("ZZ_CZYYHKTX <>", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxGreaterThan(String value) {
            addCriterion("ZZ_CZYYHKTX >", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_CZYYHKTX >=", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxLessThan(String value) {
            addCriterion("ZZ_CZYYHKTX <", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxLessThanOrEqualTo(String value) {
            addCriterion("ZZ_CZYYHKTX <=", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxLike(String value) {
            addCriterion("ZZ_CZYYHKTX like", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxNotLike(String value) {
            addCriterion("ZZ_CZYYHKTX not like", value, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxIn(List<String> values) {
            addCriterion("ZZ_CZYYHKTX in", values, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxNotIn(List<String> values) {
            addCriterion("ZZ_CZYYHKTX not in", values, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxBetween(String value1, String value2) {
            addCriterion("ZZ_CZYYHKTX between", value1, value2, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhktxNotBetween(String value1, String value2) {
            addCriterion("ZZ_CZYYHKTX not between", value1, value2, "zzCzyyhktx");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkIsNull() {
            addCriterion("ZZ_RSSJHK is null");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkIsNotNull() {
            addCriterion("ZZ_RSSJHK is not null");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkEqualTo(String value) {
            addCriterion("ZZ_RSSJHK =", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkNotEqualTo(String value) {
            addCriterion("ZZ_RSSJHK <>", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkGreaterThan(String value) {
            addCriterion("ZZ_RSSJHK >", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_RSSJHK >=", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkLessThan(String value) {
            addCriterion("ZZ_RSSJHK <", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkLessThanOrEqualTo(String value) {
            addCriterion("ZZ_RSSJHK <=", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkLike(String value) {
            addCriterion("ZZ_RSSJHK like", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkNotLike(String value) {
            addCriterion("ZZ_RSSJHK not like", value, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkIn(List<String> values) {
            addCriterion("ZZ_RSSJHK in", values, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkNotIn(List<String> values) {
            addCriterion("ZZ_RSSJHK not in", values, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkBetween(String value1, String value2) {
            addCriterion("ZZ_RSSJHK between", value1, value2, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzRssjhkNotBetween(String value1, String value2) {
            addCriterion("ZZ_RSSJHK not between", value1, value2, "zzRssjhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkIsNull() {
            addCriterion("ZZ_CZYYHK is null");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkIsNotNull() {
            addCriterion("ZZ_CZYYHK is not null");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkEqualTo(String value) {
            addCriterion("ZZ_CZYYHK =", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkNotEqualTo(String value) {
            addCriterion("ZZ_CZYYHK <>", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkGreaterThan(String value) {
            addCriterion("ZZ_CZYYHK >", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_CZYYHK >=", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkLessThan(String value) {
            addCriterion("ZZ_CZYYHK <", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkLessThanOrEqualTo(String value) {
            addCriterion("ZZ_CZYYHK <=", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkLike(String value) {
            addCriterion("ZZ_CZYYHK like", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkNotLike(String value) {
            addCriterion("ZZ_CZYYHK not like", value, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkIn(List<String> values) {
            addCriterion("ZZ_CZYYHK in", values, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkNotIn(List<String> values) {
            addCriterion("ZZ_CZYYHK not in", values, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkBetween(String value1, String value2) {
            addCriterion("ZZ_CZYYHK between", value1, value2, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzCzyyhkNotBetween(String value1, String value2) {
            addCriterion("ZZ_CZYYHK not between", value1, value2, "zzCzyyhk");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlIsNull() {
            addCriterion("ZZ_SFHYJL is null");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlIsNotNull() {
            addCriterion("ZZ_SFHYJL is not null");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlEqualTo(String value) {
            addCriterion("ZZ_SFHYJL =", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlNotEqualTo(String value) {
            addCriterion("ZZ_SFHYJL <>", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlGreaterThan(String value) {
            addCriterion("ZZ_SFHYJL >", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_SFHYJL >=", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlLessThan(String value) {
            addCriterion("ZZ_SFHYJL <", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlLessThanOrEqualTo(String value) {
            addCriterion("ZZ_SFHYJL <=", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlLike(String value) {
            addCriterion("ZZ_SFHYJL like", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlNotLike(String value) {
            addCriterion("ZZ_SFHYJL not like", value, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlIn(List<String> values) {
            addCriterion("ZZ_SFHYJL in", values, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlNotIn(List<String> values) {
            addCriterion("ZZ_SFHYJL not in", values, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlBetween(String value1, String value2) {
            addCriterion("ZZ_SFHYJL between", value1, value2, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzSfhyjlNotBetween(String value1, String value2) {
            addCriterion("ZZ_SFHYJL not between", value1, value2, "zzSfhyjl");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeIsNull() {
            addCriterion("ZZ_COMPANYCODE is null");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeIsNotNull() {
            addCriterion("ZZ_COMPANYCODE is not null");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeEqualTo(String value) {
            addCriterion("ZZ_COMPANYCODE =", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeNotEqualTo(String value) {
            addCriterion("ZZ_COMPANYCODE <>", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeGreaterThan(String value) {
            addCriterion("ZZ_COMPANYCODE >", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_COMPANYCODE >=", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeLessThan(String value) {
            addCriterion("ZZ_COMPANYCODE <", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeLessThanOrEqualTo(String value) {
            addCriterion("ZZ_COMPANYCODE <=", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeLike(String value) {
            addCriterion("ZZ_COMPANYCODE like", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeNotLike(String value) {
            addCriterion("ZZ_COMPANYCODE not like", value, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeIn(List<String> values) {
            addCriterion("ZZ_COMPANYCODE in", values, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeNotIn(List<String> values) {
            addCriterion("ZZ_COMPANYCODE not in", values, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeBetween(String value1, String value2) {
            addCriterion("ZZ_COMPANYCODE between", value1, value2, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andZzCompanycodeNotBetween(String value1, String value2) {
            addCriterion("ZZ_COMPANYCODE not between", value1, value2, "zzCompanycode");
            return (Criteria) this;
        }

        public Criteria andJobZjIsNull() {
            addCriterion("job_zj is null");
            return (Criteria) this;
        }

        public Criteria andJobZjIsNotNull() {
            addCriterion("job_zj is not null");
            return (Criteria) this;
        }

        public Criteria andJobZjEqualTo(String value) {
            addCriterion("job_zj =", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjNotEqualTo(String value) {
            addCriterion("job_zj <>", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjGreaterThan(String value) {
            addCriterion("job_zj >", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjGreaterThanOrEqualTo(String value) {
            addCriterion("job_zj >=", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjLessThan(String value) {
            addCriterion("job_zj <", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjLessThanOrEqualTo(String value) {
            addCriterion("job_zj <=", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjLike(String value) {
            addCriterion("job_zj like", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjNotLike(String value) {
            addCriterion("job_zj not like", value, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjIn(List<String> values) {
            addCriterion("job_zj in", values, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjNotIn(List<String> values) {
            addCriterion("job_zj not in", values, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjBetween(String value1, String value2) {
            addCriterion("job_zj between", value1, value2, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobZjNotBetween(String value1, String value2) {
            addCriterion("job_zj not between", value1, value2, "jobZj");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNull() {
            addCriterion("job_level is null");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNotNull() {
            addCriterion("job_level is not null");
            return (Criteria) this;
        }

        public Criteria andJobLevelEqualTo(String value) {
            addCriterion("job_level =", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotEqualTo(String value) {
            addCriterion("job_level <>", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThan(String value) {
            addCriterion("job_level >", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThanOrEqualTo(String value) {
            addCriterion("job_level >=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThan(String value) {
            addCriterion("job_level <", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThanOrEqualTo(String value) {
            addCriterion("job_level <=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLike(String value) {
            addCriterion("job_level like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotLike(String value) {
            addCriterion("job_level not like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelIn(List<String> values) {
            addCriterion("job_level in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotIn(List<String> values) {
            addCriterion("job_level not in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelBetween(String value1, String value2) {
            addCriterion("job_level between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotBetween(String value1, String value2) {
            addCriterion("job_level not between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andStaffChangeIsNull() {
            addCriterion("staff_change is null");
            return (Criteria) this;
        }

        public Criteria andStaffChangeIsNotNull() {
            addCriterion("staff_change is not null");
            return (Criteria) this;
        }

        public Criteria andStaffChangeEqualTo(String value) {
            addCriterion("staff_change =", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeNotEqualTo(String value) {
            addCriterion("staff_change <>", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeGreaterThan(String value) {
            addCriterion("staff_change >", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeGreaterThanOrEqualTo(String value) {
            addCriterion("staff_change >=", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeLessThan(String value) {
            addCriterion("staff_change <", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeLessThanOrEqualTo(String value) {
            addCriterion("staff_change <=", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeLike(String value) {
            addCriterion("staff_change like", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeNotLike(String value) {
            addCriterion("staff_change not like", value, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeIn(List<String> values) {
            addCriterion("staff_change in", values, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeNotIn(List<String> values) {
            addCriterion("staff_change not in", values, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeBetween(String value1, String value2) {
            addCriterion("staff_change between", value1, value2, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChangeNotBetween(String value1, String value2) {
            addCriterion("staff_change not between", value1, value2, "staffChange");
            return (Criteria) this;
        }

        public Criteria andStaffChange2IsNull() {
            addCriterion("staff_change2 is null");
            return (Criteria) this;
        }

        public Criteria andStaffChange2IsNotNull() {
            addCriterion("staff_change2 is not null");
            return (Criteria) this;
        }

        public Criteria andStaffChange2EqualTo(String value) {
            addCriterion("staff_change2 =", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2NotEqualTo(String value) {
            addCriterion("staff_change2 <>", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2GreaterThan(String value) {
            addCriterion("staff_change2 >", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2GreaterThanOrEqualTo(String value) {
            addCriterion("staff_change2 >=", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2LessThan(String value) {
            addCriterion("staff_change2 <", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2LessThanOrEqualTo(String value) {
            addCriterion("staff_change2 <=", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2Like(String value) {
            addCriterion("staff_change2 like", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2NotLike(String value) {
            addCriterion("staff_change2 not like", value, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2In(List<String> values) {
            addCriterion("staff_change2 in", values, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2NotIn(List<String> values) {
            addCriterion("staff_change2 not in", values, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2Between(String value1, String value2) {
            addCriterion("staff_change2 between", value1, value2, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andStaffChange2NotBetween(String value1, String value2) {
            addCriterion("staff_change2 not between", value1, value2, "staffChange2");
            return (Criteria) this;
        }

        public Criteria andWerksIsNull() {
            addCriterion("WERKS is null");
            return (Criteria) this;
        }

        public Criteria andWerksIsNotNull() {
            addCriterion("WERKS is not null");
            return (Criteria) this;
        }

        public Criteria andWerksEqualTo(String value) {
            addCriterion("WERKS =", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotEqualTo(String value) {
            addCriterion("WERKS <>", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThan(String value) {
            addCriterion("WERKS >", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThanOrEqualTo(String value) {
            addCriterion("WERKS >=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThan(String value) {
            addCriterion("WERKS <", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThanOrEqualTo(String value) {
            addCriterion("WERKS <=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLike(String value) {
            addCriterion("WERKS like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotLike(String value) {
            addCriterion("WERKS not like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksIn(List<String> values) {
            addCriterion("WERKS in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotIn(List<String> values) {
            addCriterion("WERKS not in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksBetween(String value1, String value2) {
            addCriterion("WERKS between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotBetween(String value1, String value2) {
            addCriterion("WERKS not between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIsNull() {
            addCriterion("job_level_level is null");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIsNotNull() {
            addCriterion("job_level_level is not null");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelEqualTo(String value) {
            addCriterion("job_level_level =", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotEqualTo(String value) {
            addCriterion("job_level_level <>", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelGreaterThan(String value) {
            addCriterion("job_level_level >", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelGreaterThanOrEqualTo(String value) {
            addCriterion("job_level_level >=", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLessThan(String value) {
            addCriterion("job_level_level <", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLessThanOrEqualTo(String value) {
            addCriterion("job_level_level <=", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelLike(String value) {
            addCriterion("job_level_level like", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotLike(String value) {
            addCriterion("job_level_level not like", value, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelIn(List<String> values) {
            addCriterion("job_level_level in", values, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotIn(List<String> values) {
            addCriterion("job_level_level not in", values, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelBetween(String value1, String value2) {
            addCriterion("job_level_level between", value1, value2, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLevelNotBetween(String value1, String value2) {
            addCriterion("job_level_level not between", value1, value2, "jobLevelLevel");
            return (Criteria) this;
        }

        public Criteria andRankIsNull() {
            addCriterion("rank is null");
            return (Criteria) this;
        }

        public Criteria andRankIsNotNull() {
            addCriterion("rank is not null");
            return (Criteria) this;
        }

        public Criteria andRankEqualTo(String value) {
            addCriterion("rank =", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotEqualTo(String value) {
            addCriterion("rank <>", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThan(String value) {
            addCriterion("rank >", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThanOrEqualTo(String value) {
            addCriterion("rank >=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThan(String value) {
            addCriterion("rank <", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThanOrEqualTo(String value) {
            addCriterion("rank <=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLike(String value) {
            addCriterion("rank like", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotLike(String value) {
            addCriterion("rank not like", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankIn(List<String> values) {
            addCriterion("rank in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotIn(List<String> values) {
            addCriterion("rank not in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankBetween(String value1, String value2) {
            addCriterion("rank between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotBetween(String value1, String value2) {
            addCriterion("rank not between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankseriesIsNull() {
            addCriterion("RankSeries is null");
            return (Criteria) this;
        }

        public Criteria andRankseriesIsNotNull() {
            addCriterion("RankSeries is not null");
            return (Criteria) this;
        }

        public Criteria andRankseriesEqualTo(String value) {
            addCriterion("RankSeries =", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesNotEqualTo(String value) {
            addCriterion("RankSeries <>", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesGreaterThan(String value) {
            addCriterion("RankSeries >", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesGreaterThanOrEqualTo(String value) {
            addCriterion("RankSeries >=", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesLessThan(String value) {
            addCriterion("RankSeries <", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesLessThanOrEqualTo(String value) {
            addCriterion("RankSeries <=", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesLike(String value) {
            addCriterion("RankSeries like", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesNotLike(String value) {
            addCriterion("RankSeries not like", value, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesIn(List<String> values) {
            addCriterion("RankSeries in", values, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesNotIn(List<String> values) {
            addCriterion("RankSeries not in", values, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesBetween(String value1, String value2) {
            addCriterion("RankSeries between", value1, value2, "rankseries");
            return (Criteria) this;
        }

        public Criteria andRankseriesNotBetween(String value1, String value2) {
            addCriterion("RankSeries not between", value1, value2, "rankseries");
            return (Criteria) this;
        }

        public Criteria andJobCodeIsNull() {
            addCriterion("job_code is null");
            return (Criteria) this;
        }

        public Criteria andJobCodeIsNotNull() {
            addCriterion("job_code is not null");
            return (Criteria) this;
        }

        public Criteria andJobCodeEqualTo(String value) {
            addCriterion("job_code =", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotEqualTo(String value) {
            addCriterion("job_code <>", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeGreaterThan(String value) {
            addCriterion("job_code >", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeGreaterThanOrEqualTo(String value) {
            addCriterion("job_code >=", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLessThan(String value) {
            addCriterion("job_code <", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLessThanOrEqualTo(String value) {
            addCriterion("job_code <=", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeLike(String value) {
            addCriterion("job_code like", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotLike(String value) {
            addCriterion("job_code not like", value, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeIn(List<String> values) {
            addCriterion("job_code in", values, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotIn(List<String> values) {
            addCriterion("job_code not in", values, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeBetween(String value1, String value2) {
            addCriterion("job_code between", value1, value2, "jobCode");
            return (Criteria) this;
        }

        public Criteria andJobCodeNotBetween(String value1, String value2) {
            addCriterion("job_code not between", value1, value2, "jobCode");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_INTERNAL_EXPERIENCE
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}