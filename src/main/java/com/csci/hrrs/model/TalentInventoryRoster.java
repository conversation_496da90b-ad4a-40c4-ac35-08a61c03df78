package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table v_talent_inventory_roster
 */
@TableName("v_talent_inventory_roster")
public class TalentInventoryRoster {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.id
     *
     * @mbg.generated
     */
    @TableField("id")
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.head_id
     *
     * @mbg.generated
     */
    @TableField("head_id")
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.pernr
     *
     * @mbg.generated
     */
    @TableField("pernr")
    private String pernr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.name
     *
     * @mbg.generated
     */
    @TableField("name")
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.pinyin_name
     *
     * @mbg.generated
     */
    @TableField("pinyin_name")
    private String pinyinName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.eng_name
     *
     * @mbg.generated
     */
    @TableField("eng_name")
    private String engName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.eng_name_display
     *
     * @mbg.generated
     */
    @TableField("eng_name_display")
    private String engNameDisplay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.platform
     *
     * @mbg.generated
     */
    @TableField("platform")
    private String platform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.platform_trad
     *
     * @mbg.generated
     */
    @TableField("platform_trad")
    private String platformTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.platform_eng
     *
     * @mbg.generated
     */
    @TableField("platform_eng")
    private String platformEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.subsidiary
     *
     * @mbg.generated
     */
    @TableField("subsidiary")
    private String subsidiary;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.subsidiary_trad
     *
     * @mbg.generated
     */
    @TableField("subsidiary_trad")
    private String subsidiaryTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.subsidiary_eng
     *
     * @mbg.generated
     */
    @TableField("subsidiary_eng")
    private String subsidiaryEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.sub_project
     *
     * @mbg.generated
     */
    @TableField("sub_project")
    private String subProject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.sub_project_trad
     *
     * @mbg.generated
     */
    @TableField("sub_project_trad")
    private String subProjectTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.sub_project_eng
     *
     * @mbg.generated
     */
    @TableField("sub_project_eng")
    private String subProjectEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.primary_position
     *
     * @mbg.generated
     */
    @TableField("primary_position")
    private String primaryPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.conc_position
     *
     * @mbg.generated
     */
    @TableField("conc_position")
    private String concPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.person_in_charge
     *
     * @mbg.generated
     */
    @TableField("person_in_charge")
    private String personInCharge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.person_in_charge_trad
     *
     * @mbg.generated
     */
    @TableField("person_in_charge_trad")
    private String personInChargeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.person_in_charge_eng
     *
     * @mbg.generated
     */
    @TableField("person_in_charge_eng")
    private String personInChargeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_category
     *
     * @mbg.generated
     */
    @TableField("employee_category")
    private String employeeCategory;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_category_name
     *
     * @mbg.generated
     */
    @TableField("employee_category_name")
    private String employeeCategoryName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.work_place
     *
     * @mbg.generated
     */
    @TableField("work_place")
    private String workPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_type
     *
     * @mbg.generated
     */
    @TableField("position_type")
    private String positionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_type_trad
     *
     * @mbg.generated
     */
    @TableField("position_type_trad")
    private String positionTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_type_eng
     *
     * @mbg.generated
     */
    @TableField("position_type_eng")
    private String positionTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.expertise
     *
     * @mbg.generated
     */
    @TableField("expertise")
    private String expertise;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_level
     *
     * @mbg.generated
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_grade
     *
     * @mbg.generated
     */
    @TableField("job_grade")
    private String jobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hk_job_grade
     *
     * @mbg.generated
     */
    @TableField("hk_job_grade")
    private String hkJobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.gender
     *
     * @mbg.generated
     */
    @TableField("gender")
    private Integer gender;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.start_work_time
     *
     * @mbg.generated
     */
    @TableField("start_work_time")
    private LocalDate startWorkTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.join_cohl_time
     *
     * @mbg.generated
     */
    @TableField("join_cohl_time")
    private LocalDate joinCohlTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.join_3311_time
     *
     * @mbg.generated
     */
    @TableField("join_3311_time")
    private LocalDate join3311Time;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.start_overseas_time
     *
     * @mbg.generated
     */
    @TableField("start_overseas_time")
    private LocalDate startOverseasTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.duration_current_pos
     *
     * @mbg.generated
     */
    @TableField("duration_current_pos")
    private LocalDate durationCurrentPos;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.duration_current_level
     *
     * @mbg.generated
     */
    @TableField("duration_current_level")
    private LocalDate durationCurrentLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.duration_cur_job_grade
     *
     * @mbg.generated
     */
    @TableField("duration_cur_job_grade")
    private LocalDate durationCurJobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.birthdate
     *
     * @mbg.generated
     */
    @TableField("birthdate")
    private LocalDate birthdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.age
     *
     * @mbg.generated
     */
    @TableField("age")
    private Integer age;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.ethnicity
     *
     * @mbg.generated
     */
    @TableField("ethnicity")
    private String ethnicity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.ethnicity_trad
     *
     * @mbg.generated
     */
    @TableField("ethnicity_trad")
    private String ethnicityTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.ethnicity_eng
     *
     * @mbg.generated
     */
    @TableField("ethnicity_eng")
    private String ethnicityEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hometown
     *
     * @mbg.generated
     */
    @TableField("hometown")
    private String hometown;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.birth_place
     *
     * @mbg.generated
     */
    @TableField("birth_place")
    private String birthPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.residence
     *
     * @mbg.generated
     */
    @TableField("residence")
    private String residence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.marital_status
     *
     * @mbg.generated
     */
    @TableField("marital_status")
    private String maritalStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.child_status
     *
     * @mbg.generated
     */
    @TableField("child_status")
    private String childStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.education
     *
     * @mbg.generated
     */
    @TableField("education")
    private String education;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.major
     *
     * @mbg.generated
     */
    @TableField("major")
    private String major;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_title
     *
     * @mbg.generated
     */
    @TableField("job_title")
    private String jobTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.credentials
     *
     * @mbg.generated
     */
    @TableField("credentials")
    private String credentials;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.public_office
     *
     * @mbg.generated
     */
    @TableField("public_office")
    private String publicOffice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.source
     *
     * @mbg.generated
     */
    @TableField("source")
    private String source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.mobile
     *
     * @mbg.generated
     */
    @TableField("mobile")
    private String mobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.email
     *
     * @mbg.generated
     */
    @TableField("email")
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.remark
     *
     * @mbg.generated
     */
    @TableField("remark")
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.remark_trad
     *
     * @mbg.generated
     */
    @TableField("remark_trad")
    private String remarkTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.remark_eng
     *
     * @mbg.generated
     */
    @TableField("remark_eng")
    private String remarkEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.organization_id
     *
     * @mbg.generated
     */
    @TableField("organization_id")
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.company_code
     *
     * @mbg.generated
     */
    @TableField("company_code")
    private String companyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.salary_code
     *
     * @mbg.generated
     */
    @TableField("salary_code")
    private String salaryCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.personnel_scope
     *
     * @mbg.generated
     */
    @TableField("personnel_scope")
    private String personnelScope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.personnel_subscope
     *
     * @mbg.generated
     */
    @TableField("personnel_subscope")
    private String personnelSubscope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_group
     *
     * @mbg.generated
     */
    @TableField("employee_group")
    private String employeeGroup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_group_text
     *
     * @mbg.generated
     */
    @TableField("employee_group_text")
    private String employeeGroupText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_group_text_trad
     *
     * @mbg.generated
     */
    @TableField("employee_group_text_trad")
    private String employeeGroupTextTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_group_text_eng
     *
     * @mbg.generated
     */
    @TableField("employee_group_text_eng")
    private String employeeGroupTextEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_subgroup
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup")
    private String employeeSubgroup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_subgroup_text
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text")
    private String employeeSubgroupText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text_trad")
    private String employeeSubgroupTextTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text_eng")
    private String employeeSubgroupTextEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.mainland_id_card
     *
     * @mbg.generated
     */
    @TableField("mainland_id_card")
    private String mainlandIdCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.foreign_id_card
     *
     * @mbg.generated
     */
    @TableField("foreign_id_card")
    private String foreignIdCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hk_mo_passport
     *
     * @mbg.generated
     */
    @TableField("hk_mo_passport")
    private String hkMoPassport;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    @TableField("hk_mo_end_expiry")
    private LocalDate hkMoEndExpiry;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employment_mode
     *
     * @mbg.generated
     */
    @TableField("employment_mode")
    private String employmentMode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employment_mode_trad
     *
     * @mbg.generated
     */
    @TableField("employment_mode_trad")
    private String employmentModeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.employment_mode_eng
     *
     * @mbg.generated
     */
    @TableField("employment_mode_eng")
    private String employmentModeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hzz_generation
     *
     * @mbg.generated
     */
    @TableField("hzz_generation")
    private String hzzGeneration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hzz_sequence
     *
     * @mbg.generated
     */
    @TableField("hzz_sequence")
    private String hzzSequence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.political_status
     *
     * @mbg.generated
     */
    @TableField("political_status")
    private String politicalStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.political_status_trad
     *
     * @mbg.generated
     */
    @TableField("political_status_trad")
    private String politicalStatusTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.political_status_eng
     *
     * @mbg.generated
     */
    @TableField("political_status_eng")
    private String politicalStatusEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.party_joining_date
     *
     * @mbg.generated
     */
    @TableField("party_joining_date")
    private LocalDate partyJoiningDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.specialty
     *
     * @mbg.generated
     */
    @TableField("specialty")
    private String specialty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.spouse_location
     *
     * @mbg.generated
     */
    @TableField("spouse_location")
    private String spouseLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.parents_location
     *
     * @mbg.generated
     */
    @TableField("parents_location")
    private String parentsLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.blood_type
     *
     * @mbg.generated
     */
    @TableField("blood_type")
    private String bloodType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.blood_type_trad
     *
     * @mbg.generated
     */
    @TableField("blood_type_trad")
    private String bloodTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.blood_type_eng
     *
     * @mbg.generated
     */
    @TableField("blood_type_eng")
    private String bloodTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.health_condition
     *
     * @mbg.generated
     */
    @TableField("health_condition")
    private String healthCondition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.health_condition_trad
     *
     * @mbg.generated
     */
    @TableField("health_condition_trad")
    private String healthConditionTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.health_condition_eng
     *
     * @mbg.generated
     */
    @TableField("health_condition_eng")
    private String healthConditionEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.interests_hobbies
     *
     * @mbg.generated
     */
    @TableField("interests_hobbies")
    private String interestsHobbies;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.archive_company
     *
     * @mbg.generated
     */
    @TableField("archive_company")
    private String archiveCompany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.res_loc
     *
     * @mbg.generated
     */
    @TableField("res_loc")
    private String resLoc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.res_type
     *
     * @mbg.generated
     */
    @TableField("res_type")
    private String resType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.res_type_trad
     *
     * @mbg.generated
     */
    @TableField("res_type_trad")
    private String resTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.res_type_eng
     *
     * @mbg.generated
     */
    @TableField("res_type_eng")
    private String resTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.seq
     *
     * @mbg.generated
     */
    @TableField("seq")
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.is_deleted
     *
     * @mbg.generated
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.is_sync
     *
     * @mbg.generated
     */
    @TableField("is_sync")
    private Boolean isSync;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.years_in_current_position
     *
     * @mbg.generated
     */
    @TableField("years_in_current_position")
    private BigDecimal yearsInCurrentPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.years_in_current_job_level
     *
     * @mbg.generated
     */
    @TableField("years_in_current_job_level")
    private BigDecimal yearsInCurrentJobLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.years_in_current_rank
     *
     * @mbg.generated
     */
    @TableField("years_in_current_rank")
    private BigDecimal yearsInCurrentRank;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.talent_inventory_placement
     *
     * @mbg.generated
     */
    @TableField("talent_inventory_placement")
    private String talentInventoryPlacement;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.appraisal_result_year1
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year1")
    private String appraisalResultYear1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.appraisal_result_year2
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year2")
    private String appraisalResultYear2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.appraisal_result_year3
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year3")
    private String appraisalResultYear3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.photo
     *
     * @mbg.generated
     */
    @TableField("photo")
    private String photo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.creation_time
     *
     * @mbg.generated
     */
    @TableField("creation_time")
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.create_username
     *
     * @mbg.generated
     */
    @TableField("create_username")
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.create_user_id
     *
     * @mbg.generated
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.last_update_time
     *
     * @mbg.generated
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.last_update_username
     *
     * @mbg.generated
     */
    @TableField("last_update_username")
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.last_update_user_id
     *
     * @mbg.generated
     */
    @TableField("last_update_user_id")
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.last_update_version
     *
     * @mbg.generated
     */
    @TableField("last_update_version")
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.has_overseas_work_exp
     *
     * @mbg.generated
     */
    @TableField("has_overseas_work_exp")
    private Boolean hasOverseasWorkExp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_level_code
     *
     * @mbg.generated
     */
    @TableField("position_level_code")
    private String positionLevelCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.social_service
     *
     * @mbg.generated
     */
    @TableField("social_service")
    private String socialService;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.province
     *
     * @mbg.generated
     */
    @TableField("province")
    private String province;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.is_on_job
     *
     * @mbg.generated
     */
    @TableField("is_on_job")
    private Byte isOnJob;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.work_seniority
     *
     * @mbg.generated
     */
    @TableField("work_seniority")
    private BigDecimal workSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.cohl_seniority
     *
     * @mbg.generated
     */
    @TableField("cohl_seniority")
    private BigDecimal cohlSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.organization_code
     *
     * @mbg.generated
     */
    @TableField("organization_code")
    private String organizationCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.platform_company_code
     *
     * @mbg.generated
     */
    @TableField("platform_company_code")
    private String platformCompanyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hire_type
     *
     * @mbg.generated
     */
    @TableField("hire_type")
    private String hireType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.manage_type
     *
     * @mbg.generated
     */
    @TableField("manage_type")
    private String manageType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.is_cadre
     *
     * @mbg.generated
     */
    @TableField("is_cadre")
    private Boolean isCadre;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.is_hzz
     *
     * @mbg.generated
     */
    @TableField("is_hzz")
    private Boolean isHzz;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.graduation_date
     *
     * @mbg.generated
     */
    @TableField("graduation_date")
    private String graduationDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.special_line
     *
     * @mbg.generated
     */
    @TableField("special_line")
    private String specialLine;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_level_code_hk
     *
     * @mbg.generated
     */
    @TableField("position_level_code_hk")
    private String positionLevelCodeHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.birthdate_month
     *
     * @mbg.generated
     */
    @TableField("birthdate_month")
    private Integer birthdateMonth;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.birthdate_day
     *
     * @mbg.generated
     */
    @TableField("birthdate_day")
    private Integer birthdateDay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.position_level_name
     *
     * @mbg.generated
     */
    @TableField("position_level_name")
    private String positionLevelName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.phone_overseas
     *
     * @mbg.generated
     */
    @TableField("phone_overseas")
    private String phoneOverseas;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.edu_all
     *
     * @mbg.generated
     */
    @TableField("edu_all")
    private String eduAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.school_all
     *
     * @mbg.generated
     */
    @TableField("school_all")
    private String schoolAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.major_all
     *
     * @mbg.generated
     */
    @TableField("major_all")
    private String majorAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.sex
     *
     * @mbg.generated
     */
    @TableField("sex")
    private String sex;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.staff_class3311
     *
     * @mbg.generated
     */
    @TableField("staff_class3311")
    private String staffClass3311;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.staff_class
     *
     * @mbg.generated
     */
    @TableField("staff_class")
    private String staffClass;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.dimission_date
     *
     * @mbg.generated
     */
    @TableField("dimission_date")
    private LocalDate dimissionDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.primary_position_pinyin
     *
     * @mbg.generated
     */
    @TableField("primary_position_pinyin")
    private String primaryPositionPinyin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hire_type_query
     *
     * @mbg.generated
     */
    @TableField("hire_type_query")
    private String hireTypeQuery;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.adname
     *
     * @mbg.generated
     */
    @TableField("adname")
    private String adname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.domain
     *
     * @mbg.generated
     */
    @TableField("domain")
    private String domain;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.username
     *
     * @mbg.generated
     */
    @TableField("username")
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.staff_class_macau
     *
     * @mbg.generated
     */
    @TableField("staff_class_macau")
    private String staffClassMacau;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.nationality
     *
     * @mbg.generated
     */
    @TableField("nationality")
    private String nationality;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_level_name
     *
     * @mbg.generated
     */
    @TableField("job_level_name")
    private String jobLevelName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_level_pilot
     *
     * @mbg.generated
     */
    @TableField("job_level_pilot")
    private String jobLevelPilot;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.name_simplified
     *
     * @mbg.generated
     */
    @TableField("name_simplified")
    private String nameSimplified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.school_type
     *
     * @mbg.generated
     */
    @TableField("school_type")
    private String schoolType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.personal_email
     *
     * @mbg.generated
     */
    @TableField("personal_email")
    private String personalEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.hk_mo_cert_expiry_date
     *
     * @mbg.generated
     */
    @TableField("hk_mo_cert_expiry_date")
    private LocalDate hkMoCertExpiryDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.height
     *
     * @mbg.generated
     */
    @TableField("height")
    private BigDecimal height;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_type_hk
     *
     * @mbg.generated
     */
    @TableField("job_type_hk")
    private String jobTypeHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.bank_account
     *
     * @mbg.generated
     */
    @TableField("bank_account")
    private String bankAccount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.party_regular_date
     *
     * @mbg.generated
     */
    @TableField("party_regular_date")
    private LocalDate partyRegularDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.eng_name_origin
     *
     * @mbg.generated
     */
    @TableField("eng_name_origin")
    private String engNameOrigin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.source_tag
     *
     * @mbg.generated
     */
    @TableField("source_tag")
    private String sourceTag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v_talent_inventory_roster.job_type_nd
     *
     * @mbg.generated
     */
    @TableField("job_type_nd")
    private String jobTypeNd;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.id
     *
     * @return the value of v_talent_inventory_roster.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.id
     *
     * @param id the value for v_talent_inventory_roster.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.head_id
     *
     * @return the value of v_talent_inventory_roster.head_id
     *
     * @mbg.generated
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.head_id
     *
     * @param headId the value for v_talent_inventory_roster.head_id
     *
     * @mbg.generated
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.pernr
     *
     * @return the value of v_talent_inventory_roster.pernr
     *
     * @mbg.generated
     */
    public String getPernr() {
        return pernr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.pernr
     *
     * @param pernr the value for v_talent_inventory_roster.pernr
     *
     * @mbg.generated
     */
    public void setPernr(String pernr) {
        this.pernr = pernr == null ? null : pernr.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.name
     *
     * @return the value of v_talent_inventory_roster.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.name
     *
     * @param name the value for v_talent_inventory_roster.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.pinyin_name
     *
     * @return the value of v_talent_inventory_roster.pinyin_name
     *
     * @mbg.generated
     */
    public String getPinyinName() {
        return pinyinName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.pinyin_name
     *
     * @param pinyinName the value for v_talent_inventory_roster.pinyin_name
     *
     * @mbg.generated
     */
    public void setPinyinName(String pinyinName) {
        this.pinyinName = pinyinName == null ? null : pinyinName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.eng_name
     *
     * @return the value of v_talent_inventory_roster.eng_name
     *
     * @mbg.generated
     */
    public String getEngName() {
        return engName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.eng_name
     *
     * @param engName the value for v_talent_inventory_roster.eng_name
     *
     * @mbg.generated
     */
    public void setEngName(String engName) {
        this.engName = engName == null ? null : engName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.eng_name_display
     *
     * @return the value of v_talent_inventory_roster.eng_name_display
     *
     * @mbg.generated
     */
    public String getEngNameDisplay() {
        return engNameDisplay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.eng_name_display
     *
     * @param engNameDisplay the value for v_talent_inventory_roster.eng_name_display
     *
     * @mbg.generated
     */
    public void setEngNameDisplay(String engNameDisplay) {
        this.engNameDisplay = engNameDisplay == null ? null : engNameDisplay.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.platform
     *
     * @return the value of v_talent_inventory_roster.platform
     *
     * @mbg.generated
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.platform
     *
     * @param platform the value for v_talent_inventory_roster.platform
     *
     * @mbg.generated
     */
    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.platform_trad
     *
     * @return the value of v_talent_inventory_roster.platform_trad
     *
     * @mbg.generated
     */
    public String getPlatformTrad() {
        return platformTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.platform_trad
     *
     * @param platformTrad the value for v_talent_inventory_roster.platform_trad
     *
     * @mbg.generated
     */
    public void setPlatformTrad(String platformTrad) {
        this.platformTrad = platformTrad == null ? null : platformTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.platform_eng
     *
     * @return the value of v_talent_inventory_roster.platform_eng
     *
     * @mbg.generated
     */
    public String getPlatformEng() {
        return platformEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.platform_eng
     *
     * @param platformEng the value for v_talent_inventory_roster.platform_eng
     *
     * @mbg.generated
     */
    public void setPlatformEng(String platformEng) {
        this.platformEng = platformEng == null ? null : platformEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.subsidiary
     *
     * @return the value of v_talent_inventory_roster.subsidiary
     *
     * @mbg.generated
     */
    public String getSubsidiary() {
        return subsidiary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.subsidiary
     *
     * @param subsidiary the value for v_talent_inventory_roster.subsidiary
     *
     * @mbg.generated
     */
    public void setSubsidiary(String subsidiary) {
        this.subsidiary = subsidiary == null ? null : subsidiary.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.subsidiary_trad
     *
     * @return the value of v_talent_inventory_roster.subsidiary_trad
     *
     * @mbg.generated
     */
    public String getSubsidiaryTrad() {
        return subsidiaryTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.subsidiary_trad
     *
     * @param subsidiaryTrad the value for v_talent_inventory_roster.subsidiary_trad
     *
     * @mbg.generated
     */
    public void setSubsidiaryTrad(String subsidiaryTrad) {
        this.subsidiaryTrad = subsidiaryTrad == null ? null : subsidiaryTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.subsidiary_eng
     *
     * @return the value of v_talent_inventory_roster.subsidiary_eng
     *
     * @mbg.generated
     */
    public String getSubsidiaryEng() {
        return subsidiaryEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.subsidiary_eng
     *
     * @param subsidiaryEng the value for v_talent_inventory_roster.subsidiary_eng
     *
     * @mbg.generated
     */
    public void setSubsidiaryEng(String subsidiaryEng) {
        this.subsidiaryEng = subsidiaryEng == null ? null : subsidiaryEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.sub_project
     *
     * @return the value of v_talent_inventory_roster.sub_project
     *
     * @mbg.generated
     */
    public String getSubProject() {
        return subProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.sub_project
     *
     * @param subProject the value for v_talent_inventory_roster.sub_project
     *
     * @mbg.generated
     */
    public void setSubProject(String subProject) {
        this.subProject = subProject == null ? null : subProject.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.sub_project_trad
     *
     * @return the value of v_talent_inventory_roster.sub_project_trad
     *
     * @mbg.generated
     */
    public String getSubProjectTrad() {
        return subProjectTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.sub_project_trad
     *
     * @param subProjectTrad the value for v_talent_inventory_roster.sub_project_trad
     *
     * @mbg.generated
     */
    public void setSubProjectTrad(String subProjectTrad) {
        this.subProjectTrad = subProjectTrad == null ? null : subProjectTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.sub_project_eng
     *
     * @return the value of v_talent_inventory_roster.sub_project_eng
     *
     * @mbg.generated
     */
    public String getSubProjectEng() {
        return subProjectEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.sub_project_eng
     *
     * @param subProjectEng the value for v_talent_inventory_roster.sub_project_eng
     *
     * @mbg.generated
     */
    public void setSubProjectEng(String subProjectEng) {
        this.subProjectEng = subProjectEng == null ? null : subProjectEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.primary_position
     *
     * @return the value of v_talent_inventory_roster.primary_position
     *
     * @mbg.generated
     */
    public String getPrimaryPosition() {
        return primaryPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.primary_position
     *
     * @param primaryPosition the value for v_talent_inventory_roster.primary_position
     *
     * @mbg.generated
     */
    public void setPrimaryPosition(String primaryPosition) {
        this.primaryPosition = primaryPosition == null ? null : primaryPosition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.conc_position
     *
     * @return the value of v_talent_inventory_roster.conc_position
     *
     * @mbg.generated
     */
    public String getConcPosition() {
        return concPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.conc_position
     *
     * @param concPosition the value for v_talent_inventory_roster.conc_position
     *
     * @mbg.generated
     */
    public void setConcPosition(String concPosition) {
        this.concPosition = concPosition == null ? null : concPosition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.person_in_charge
     *
     * @return the value of v_talent_inventory_roster.person_in_charge
     *
     * @mbg.generated
     */
    public String getPersonInCharge() {
        return personInCharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.person_in_charge
     *
     * @param personInCharge the value for v_talent_inventory_roster.person_in_charge
     *
     * @mbg.generated
     */
    public void setPersonInCharge(String personInCharge) {
        this.personInCharge = personInCharge == null ? null : personInCharge.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.person_in_charge_trad
     *
     * @return the value of v_talent_inventory_roster.person_in_charge_trad
     *
     * @mbg.generated
     */
    public String getPersonInChargeTrad() {
        return personInChargeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.person_in_charge_trad
     *
     * @param personInChargeTrad the value for v_talent_inventory_roster.person_in_charge_trad
     *
     * @mbg.generated
     */
    public void setPersonInChargeTrad(String personInChargeTrad) {
        this.personInChargeTrad = personInChargeTrad == null ? null : personInChargeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.person_in_charge_eng
     *
     * @return the value of v_talent_inventory_roster.person_in_charge_eng
     *
     * @mbg.generated
     */
    public String getPersonInChargeEng() {
        return personInChargeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.person_in_charge_eng
     *
     * @param personInChargeEng the value for v_talent_inventory_roster.person_in_charge_eng
     *
     * @mbg.generated
     */
    public void setPersonInChargeEng(String personInChargeEng) {
        this.personInChargeEng = personInChargeEng == null ? null : personInChargeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_category
     *
     * @return the value of v_talent_inventory_roster.employee_category
     *
     * @mbg.generated
     */
    public String getEmployeeCategory() {
        return employeeCategory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_category
     *
     * @param employeeCategory the value for v_talent_inventory_roster.employee_category
     *
     * @mbg.generated
     */
    public void setEmployeeCategory(String employeeCategory) {
        this.employeeCategory = employeeCategory == null ? null : employeeCategory.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_category_name
     *
     * @return the value of v_talent_inventory_roster.employee_category_name
     *
     * @mbg.generated
     */
    public String getEmployeeCategoryName() {
        return employeeCategoryName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_category_name
     *
     * @param employeeCategoryName the value for v_talent_inventory_roster.employee_category_name
     *
     * @mbg.generated
     */
    public void setEmployeeCategoryName(String employeeCategoryName) {
        this.employeeCategoryName = employeeCategoryName == null ? null : employeeCategoryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.work_place
     *
     * @return the value of v_talent_inventory_roster.work_place
     *
     * @mbg.generated
     */
    public String getWorkPlace() {
        return workPlace;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.work_place
     *
     * @param workPlace the value for v_talent_inventory_roster.work_place
     *
     * @mbg.generated
     */
    public void setWorkPlace(String workPlace) {
        this.workPlace = workPlace == null ? null : workPlace.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_type
     *
     * @return the value of v_talent_inventory_roster.position_type
     *
     * @mbg.generated
     */
    public String getPositionType() {
        return positionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_type
     *
     * @param positionType the value for v_talent_inventory_roster.position_type
     *
     * @mbg.generated
     */
    public void setPositionType(String positionType) {
        this.positionType = positionType == null ? null : positionType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_type_trad
     *
     * @return the value of v_talent_inventory_roster.position_type_trad
     *
     * @mbg.generated
     */
    public String getPositionTypeTrad() {
        return positionTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_type_trad
     *
     * @param positionTypeTrad the value for v_talent_inventory_roster.position_type_trad
     *
     * @mbg.generated
     */
    public void setPositionTypeTrad(String positionTypeTrad) {
        this.positionTypeTrad = positionTypeTrad == null ? null : positionTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_type_eng
     *
     * @return the value of v_talent_inventory_roster.position_type_eng
     *
     * @mbg.generated
     */
    public String getPositionTypeEng() {
        return positionTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_type_eng
     *
     * @param positionTypeEng the value for v_talent_inventory_roster.position_type_eng
     *
     * @mbg.generated
     */
    public void setPositionTypeEng(String positionTypeEng) {
        this.positionTypeEng = positionTypeEng == null ? null : positionTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.expertise
     *
     * @return the value of v_talent_inventory_roster.expertise
     *
     * @mbg.generated
     */
    public String getExpertise() {
        return expertise;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.expertise
     *
     * @param expertise the value for v_talent_inventory_roster.expertise
     *
     * @mbg.generated
     */
    public void setExpertise(String expertise) {
        this.expertise = expertise == null ? null : expertise.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_level
     *
     * @return the value of v_talent_inventory_roster.position_level
     *
     * @mbg.generated
     */
    public String getPositionLevel() {
        return positionLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_level
     *
     * @param positionLevel the value for v_talent_inventory_roster.position_level
     *
     * @mbg.generated
     */
    public void setPositionLevel(String positionLevel) {
        this.positionLevel = positionLevel == null ? null : positionLevel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_grade
     *
     * @return the value of v_talent_inventory_roster.job_grade
     *
     * @mbg.generated
     */
    public String getJobGrade() {
        return jobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_grade
     *
     * @param jobGrade the value for v_talent_inventory_roster.job_grade
     *
     * @mbg.generated
     */
    public void setJobGrade(String jobGrade) {
        this.jobGrade = jobGrade == null ? null : jobGrade.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hk_job_grade
     *
     * @return the value of v_talent_inventory_roster.hk_job_grade
     *
     * @mbg.generated
     */
    public String getHkJobGrade() {
        return hkJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hk_job_grade
     *
     * @param hkJobGrade the value for v_talent_inventory_roster.hk_job_grade
     *
     * @mbg.generated
     */
    public void setHkJobGrade(String hkJobGrade) {
        this.hkJobGrade = hkJobGrade == null ? null : hkJobGrade.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.gender
     *
     * @return the value of v_talent_inventory_roster.gender
     *
     * @mbg.generated
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.gender
     *
     * @param gender the value for v_talent_inventory_roster.gender
     *
     * @mbg.generated
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.start_work_time
     *
     * @return the value of v_talent_inventory_roster.start_work_time
     *
     * @mbg.generated
     */
    public LocalDate getStartWorkTime() {
        return startWorkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.start_work_time
     *
     * @param startWorkTime the value for v_talent_inventory_roster.start_work_time
     *
     * @mbg.generated
     */
    public void setStartWorkTime(LocalDate startWorkTime) {
        this.startWorkTime = startWorkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.join_cohl_time
     *
     * @return the value of v_talent_inventory_roster.join_cohl_time
     *
     * @mbg.generated
     */
    public LocalDate getJoinCohlTime() {
        return joinCohlTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.join_cohl_time
     *
     * @param joinCohlTime the value for v_talent_inventory_roster.join_cohl_time
     *
     * @mbg.generated
     */
    public void setJoinCohlTime(LocalDate joinCohlTime) {
        this.joinCohlTime = joinCohlTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.join_3311_time
     *
     * @return the value of v_talent_inventory_roster.join_3311_time
     *
     * @mbg.generated
     */
    public LocalDate getJoin3311Time() {
        return join3311Time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.join_3311_time
     *
     * @param join3311Time the value for v_talent_inventory_roster.join_3311_time
     *
     * @mbg.generated
     */
    public void setJoin3311Time(LocalDate join3311Time) {
        this.join3311Time = join3311Time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.start_overseas_time
     *
     * @return the value of v_talent_inventory_roster.start_overseas_time
     *
     * @mbg.generated
     */
    public LocalDate getStartOverseasTime() {
        return startOverseasTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.start_overseas_time
     *
     * @param startOverseasTime the value for v_talent_inventory_roster.start_overseas_time
     *
     * @mbg.generated
     */
    public void setStartOverseasTime(LocalDate startOverseasTime) {
        this.startOverseasTime = startOverseasTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.duration_current_pos
     *
     * @return the value of v_talent_inventory_roster.duration_current_pos
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurrentPos() {
        return durationCurrentPos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.duration_current_pos
     *
     * @param durationCurrentPos the value for v_talent_inventory_roster.duration_current_pos
     *
     * @mbg.generated
     */
    public void setDurationCurrentPos(LocalDate durationCurrentPos) {
        this.durationCurrentPos = durationCurrentPos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.duration_current_level
     *
     * @return the value of v_talent_inventory_roster.duration_current_level
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurrentLevel() {
        return durationCurrentLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.duration_current_level
     *
     * @param durationCurrentLevel the value for v_talent_inventory_roster.duration_current_level
     *
     * @mbg.generated
     */
    public void setDurationCurrentLevel(LocalDate durationCurrentLevel) {
        this.durationCurrentLevel = durationCurrentLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.duration_cur_job_grade
     *
     * @return the value of v_talent_inventory_roster.duration_cur_job_grade
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurJobGrade() {
        return durationCurJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.duration_cur_job_grade
     *
     * @param durationCurJobGrade the value for v_talent_inventory_roster.duration_cur_job_grade
     *
     * @mbg.generated
     */
    public void setDurationCurJobGrade(LocalDate durationCurJobGrade) {
        this.durationCurJobGrade = durationCurJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.birthdate
     *
     * @return the value of v_talent_inventory_roster.birthdate
     *
     * @mbg.generated
     */
    public LocalDate getBirthdate() {
        return birthdate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.birthdate
     *
     * @param birthdate the value for v_talent_inventory_roster.birthdate
     *
     * @mbg.generated
     */
    public void setBirthdate(LocalDate birthdate) {
        this.birthdate = birthdate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.age
     *
     * @return the value of v_talent_inventory_roster.age
     *
     * @mbg.generated
     */
    public Integer getAge() {
        return age;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.age
     *
     * @param age the value for v_talent_inventory_roster.age
     *
     * @mbg.generated
     */
    public void setAge(Integer age) {
        this.age = age;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.ethnicity
     *
     * @return the value of v_talent_inventory_roster.ethnicity
     *
     * @mbg.generated
     */
    public String getEthnicity() {
        return ethnicity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.ethnicity
     *
     * @param ethnicity the value for v_talent_inventory_roster.ethnicity
     *
     * @mbg.generated
     */
    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity == null ? null : ethnicity.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.ethnicity_trad
     *
     * @return the value of v_talent_inventory_roster.ethnicity_trad
     *
     * @mbg.generated
     */
    public String getEthnicityTrad() {
        return ethnicityTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.ethnicity_trad
     *
     * @param ethnicityTrad the value for v_talent_inventory_roster.ethnicity_trad
     *
     * @mbg.generated
     */
    public void setEthnicityTrad(String ethnicityTrad) {
        this.ethnicityTrad = ethnicityTrad == null ? null : ethnicityTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.ethnicity_eng
     *
     * @return the value of v_talent_inventory_roster.ethnicity_eng
     *
     * @mbg.generated
     */
    public String getEthnicityEng() {
        return ethnicityEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.ethnicity_eng
     *
     * @param ethnicityEng the value for v_talent_inventory_roster.ethnicity_eng
     *
     * @mbg.generated
     */
    public void setEthnicityEng(String ethnicityEng) {
        this.ethnicityEng = ethnicityEng == null ? null : ethnicityEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hometown
     *
     * @return the value of v_talent_inventory_roster.hometown
     *
     * @mbg.generated
     */
    public String getHometown() {
        return hometown;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hometown
     *
     * @param hometown the value for v_talent_inventory_roster.hometown
     *
     * @mbg.generated
     */
    public void setHometown(String hometown) {
        this.hometown = hometown == null ? null : hometown.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.birth_place
     *
     * @return the value of v_talent_inventory_roster.birth_place
     *
     * @mbg.generated
     */
    public String getBirthPlace() {
        return birthPlace;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.birth_place
     *
     * @param birthPlace the value for v_talent_inventory_roster.birth_place
     *
     * @mbg.generated
     */
    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace == null ? null : birthPlace.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.residence
     *
     * @return the value of v_talent_inventory_roster.residence
     *
     * @mbg.generated
     */
    public String getResidence() {
        return residence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.residence
     *
     * @param residence the value for v_talent_inventory_roster.residence
     *
     * @mbg.generated
     */
    public void setResidence(String residence) {
        this.residence = residence == null ? null : residence.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.marital_status
     *
     * @return the value of v_talent_inventory_roster.marital_status
     *
     * @mbg.generated
     */
    public String getMaritalStatus() {
        return maritalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.marital_status
     *
     * @param maritalStatus the value for v_talent_inventory_roster.marital_status
     *
     * @mbg.generated
     */
    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus == null ? null : maritalStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.child_status
     *
     * @return the value of v_talent_inventory_roster.child_status
     *
     * @mbg.generated
     */
    public String getChildStatus() {
        return childStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.child_status
     *
     * @param childStatus the value for v_talent_inventory_roster.child_status
     *
     * @mbg.generated
     */
    public void setChildStatus(String childStatus) {
        this.childStatus = childStatus == null ? null : childStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.education
     *
     * @return the value of v_talent_inventory_roster.education
     *
     * @mbg.generated
     */
    public String getEducation() {
        return education;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.education
     *
     * @param education the value for v_talent_inventory_roster.education
     *
     * @mbg.generated
     */
    public void setEducation(String education) {
        this.education = education == null ? null : education.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.major
     *
     * @return the value of v_talent_inventory_roster.major
     *
     * @mbg.generated
     */
    public String getMajor() {
        return major;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.major
     *
     * @param major the value for v_talent_inventory_roster.major
     *
     * @mbg.generated
     */
    public void setMajor(String major) {
        this.major = major == null ? null : major.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_title
     *
     * @return the value of v_talent_inventory_roster.job_title
     *
     * @mbg.generated
     */
    public String getJobTitle() {
        return jobTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_title
     *
     * @param jobTitle the value for v_talent_inventory_roster.job_title
     *
     * @mbg.generated
     */
    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle == null ? null : jobTitle.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.credentials
     *
     * @return the value of v_talent_inventory_roster.credentials
     *
     * @mbg.generated
     */
    public String getCredentials() {
        return credentials;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.credentials
     *
     * @param credentials the value for v_talent_inventory_roster.credentials
     *
     * @mbg.generated
     */
    public void setCredentials(String credentials) {
        this.credentials = credentials == null ? null : credentials.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.public_office
     *
     * @return the value of v_talent_inventory_roster.public_office
     *
     * @mbg.generated
     */
    public String getPublicOffice() {
        return publicOffice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.public_office
     *
     * @param publicOffice the value for v_talent_inventory_roster.public_office
     *
     * @mbg.generated
     */
    public void setPublicOffice(String publicOffice) {
        this.publicOffice = publicOffice == null ? null : publicOffice.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.source
     *
     * @return the value of v_talent_inventory_roster.source
     *
     * @mbg.generated
     */
    public String getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.source
     *
     * @param source the value for v_talent_inventory_roster.source
     *
     * @mbg.generated
     */
    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.mobile
     *
     * @return the value of v_talent_inventory_roster.mobile
     *
     * @mbg.generated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.mobile
     *
     * @param mobile the value for v_talent_inventory_roster.mobile
     *
     * @mbg.generated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.email
     *
     * @return the value of v_talent_inventory_roster.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.email
     *
     * @param email the value for v_talent_inventory_roster.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.remark
     *
     * @return the value of v_talent_inventory_roster.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.remark
     *
     * @param remark the value for v_talent_inventory_roster.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.remark_trad
     *
     * @return the value of v_talent_inventory_roster.remark_trad
     *
     * @mbg.generated
     */
    public String getRemarkTrad() {
        return remarkTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.remark_trad
     *
     * @param remarkTrad the value for v_talent_inventory_roster.remark_trad
     *
     * @mbg.generated
     */
    public void setRemarkTrad(String remarkTrad) {
        this.remarkTrad = remarkTrad == null ? null : remarkTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.remark_eng
     *
     * @return the value of v_talent_inventory_roster.remark_eng
     *
     * @mbg.generated
     */
    public String getRemarkEng() {
        return remarkEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.remark_eng
     *
     * @param remarkEng the value for v_talent_inventory_roster.remark_eng
     *
     * @mbg.generated
     */
    public void setRemarkEng(String remarkEng) {
        this.remarkEng = remarkEng == null ? null : remarkEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.organization_id
     *
     * @return the value of v_talent_inventory_roster.organization_id
     *
     * @mbg.generated
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.organization_id
     *
     * @param organizationId the value for v_talent_inventory_roster.organization_id
     *
     * @mbg.generated
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.company_code
     *
     * @return the value of v_talent_inventory_roster.company_code
     *
     * @mbg.generated
     */
    public String getCompanyCode() {
        return companyCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.company_code
     *
     * @param companyCode the value for v_talent_inventory_roster.company_code
     *
     * @mbg.generated
     */
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.salary_code
     *
     * @return the value of v_talent_inventory_roster.salary_code
     *
     * @mbg.generated
     */
    public String getSalaryCode() {
        return salaryCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.salary_code
     *
     * @param salaryCode the value for v_talent_inventory_roster.salary_code
     *
     * @mbg.generated
     */
    public void setSalaryCode(String salaryCode) {
        this.salaryCode = salaryCode == null ? null : salaryCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.personnel_scope
     *
     * @return the value of v_talent_inventory_roster.personnel_scope
     *
     * @mbg.generated
     */
    public String getPersonnelScope() {
        return personnelScope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.personnel_scope
     *
     * @param personnelScope the value for v_talent_inventory_roster.personnel_scope
     *
     * @mbg.generated
     */
    public void setPersonnelScope(String personnelScope) {
        this.personnelScope = personnelScope == null ? null : personnelScope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.personnel_subscope
     *
     * @return the value of v_talent_inventory_roster.personnel_subscope
     *
     * @mbg.generated
     */
    public String getPersonnelSubscope() {
        return personnelSubscope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.personnel_subscope
     *
     * @param personnelSubscope the value for v_talent_inventory_roster.personnel_subscope
     *
     * @mbg.generated
     */
    public void setPersonnelSubscope(String personnelSubscope) {
        this.personnelSubscope = personnelSubscope == null ? null : personnelSubscope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_group
     *
     * @return the value of v_talent_inventory_roster.employee_group
     *
     * @mbg.generated
     */
    public String getEmployeeGroup() {
        return employeeGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_group
     *
     * @param employeeGroup the value for v_talent_inventory_roster.employee_group
     *
     * @mbg.generated
     */
    public void setEmployeeGroup(String employeeGroup) {
        this.employeeGroup = employeeGroup == null ? null : employeeGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_group_text
     *
     * @return the value of v_talent_inventory_roster.employee_group_text
     *
     * @mbg.generated
     */
    public String getEmployeeGroupText() {
        return employeeGroupText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_group_text
     *
     * @param employeeGroupText the value for v_talent_inventory_roster.employee_group_text
     *
     * @mbg.generated
     */
    public void setEmployeeGroupText(String employeeGroupText) {
        this.employeeGroupText = employeeGroupText == null ? null : employeeGroupText.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_group_text_trad
     *
     * @return the value of v_talent_inventory_roster.employee_group_text_trad
     *
     * @mbg.generated
     */
    public String getEmployeeGroupTextTrad() {
        return employeeGroupTextTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_group_text_trad
     *
     * @param employeeGroupTextTrad the value for v_talent_inventory_roster.employee_group_text_trad
     *
     * @mbg.generated
     */
    public void setEmployeeGroupTextTrad(String employeeGroupTextTrad) {
        this.employeeGroupTextTrad = employeeGroupTextTrad == null ? null : employeeGroupTextTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_group_text_eng
     *
     * @return the value of v_talent_inventory_roster.employee_group_text_eng
     *
     * @mbg.generated
     */
    public String getEmployeeGroupTextEng() {
        return employeeGroupTextEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_group_text_eng
     *
     * @param employeeGroupTextEng the value for v_talent_inventory_roster.employee_group_text_eng
     *
     * @mbg.generated
     */
    public void setEmployeeGroupTextEng(String employeeGroupTextEng) {
        this.employeeGroupTextEng = employeeGroupTextEng == null ? null : employeeGroupTextEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_subgroup
     *
     * @return the value of v_talent_inventory_roster.employee_subgroup
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroup() {
        return employeeSubgroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_subgroup
     *
     * @param employeeSubgroup the value for v_talent_inventory_roster.employee_subgroup
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroup(String employeeSubgroup) {
        this.employeeSubgroup = employeeSubgroup == null ? null : employeeSubgroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_subgroup_text
     *
     * @return the value of v_talent_inventory_roster.employee_subgroup_text
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupText() {
        return employeeSubgroupText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_subgroup_text
     *
     * @param employeeSubgroupText the value for v_talent_inventory_roster.employee_subgroup_text
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupText(String employeeSubgroupText) {
        this.employeeSubgroupText = employeeSubgroupText == null ? null : employeeSubgroupText.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_subgroup_text_trad
     *
     * @return the value of v_talent_inventory_roster.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupTextTrad() {
        return employeeSubgroupTextTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_subgroup_text_trad
     *
     * @param employeeSubgroupTextTrad the value for v_talent_inventory_roster.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupTextTrad(String employeeSubgroupTextTrad) {
        this.employeeSubgroupTextTrad = employeeSubgroupTextTrad == null ? null : employeeSubgroupTextTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employee_subgroup_text_eng
     *
     * @return the value of v_talent_inventory_roster.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupTextEng() {
        return employeeSubgroupTextEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employee_subgroup_text_eng
     *
     * @param employeeSubgroupTextEng the value for v_talent_inventory_roster.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupTextEng(String employeeSubgroupTextEng) {
        this.employeeSubgroupTextEng = employeeSubgroupTextEng == null ? null : employeeSubgroupTextEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.mainland_id_card
     *
     * @return the value of v_talent_inventory_roster.mainland_id_card
     *
     * @mbg.generated
     */
    public String getMainlandIdCard() {
        return mainlandIdCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.mainland_id_card
     *
     * @param mainlandIdCard the value for v_talent_inventory_roster.mainland_id_card
     *
     * @mbg.generated
     */
    public void setMainlandIdCard(String mainlandIdCard) {
        this.mainlandIdCard = mainlandIdCard == null ? null : mainlandIdCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.foreign_id_card
     *
     * @return the value of v_talent_inventory_roster.foreign_id_card
     *
     * @mbg.generated
     */
    public String getForeignIdCard() {
        return foreignIdCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.foreign_id_card
     *
     * @param foreignIdCard the value for v_talent_inventory_roster.foreign_id_card
     *
     * @mbg.generated
     */
    public void setForeignIdCard(String foreignIdCard) {
        this.foreignIdCard = foreignIdCard == null ? null : foreignIdCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hk_mo_passport
     *
     * @return the value of v_talent_inventory_roster.hk_mo_passport
     *
     * @mbg.generated
     */
    public String getHkMoPassport() {
        return hkMoPassport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hk_mo_passport
     *
     * @param hkMoPassport the value for v_talent_inventory_roster.hk_mo_passport
     *
     * @mbg.generated
     */
    public void setHkMoPassport(String hkMoPassport) {
        this.hkMoPassport = hkMoPassport == null ? null : hkMoPassport.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hk_mo_end_expiry
     *
     * @return the value of v_talent_inventory_roster.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    public LocalDate getHkMoEndExpiry() {
        return hkMoEndExpiry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hk_mo_end_expiry
     *
     * @param hkMoEndExpiry the value for v_talent_inventory_roster.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    public void setHkMoEndExpiry(LocalDate hkMoEndExpiry) {
        this.hkMoEndExpiry = hkMoEndExpiry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employment_mode
     *
     * @return the value of v_talent_inventory_roster.employment_mode
     *
     * @mbg.generated
     */
    public String getEmploymentMode() {
        return employmentMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employment_mode
     *
     * @param employmentMode the value for v_talent_inventory_roster.employment_mode
     *
     * @mbg.generated
     */
    public void setEmploymentMode(String employmentMode) {
        this.employmentMode = employmentMode == null ? null : employmentMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employment_mode_trad
     *
     * @return the value of v_talent_inventory_roster.employment_mode_trad
     *
     * @mbg.generated
     */
    public String getEmploymentModeTrad() {
        return employmentModeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employment_mode_trad
     *
     * @param employmentModeTrad the value for v_talent_inventory_roster.employment_mode_trad
     *
     * @mbg.generated
     */
    public void setEmploymentModeTrad(String employmentModeTrad) {
        this.employmentModeTrad = employmentModeTrad == null ? null : employmentModeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.employment_mode_eng
     *
     * @return the value of v_talent_inventory_roster.employment_mode_eng
     *
     * @mbg.generated
     */
    public String getEmploymentModeEng() {
        return employmentModeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.employment_mode_eng
     *
     * @param employmentModeEng the value for v_talent_inventory_roster.employment_mode_eng
     *
     * @mbg.generated
     */
    public void setEmploymentModeEng(String employmentModeEng) {
        this.employmentModeEng = employmentModeEng == null ? null : employmentModeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hzz_generation
     *
     * @return the value of v_talent_inventory_roster.hzz_generation
     *
     * @mbg.generated
     */
    public String getHzzGeneration() {
        return hzzGeneration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hzz_generation
     *
     * @param hzzGeneration the value for v_talent_inventory_roster.hzz_generation
     *
     * @mbg.generated
     */
    public void setHzzGeneration(String hzzGeneration) {
        this.hzzGeneration = hzzGeneration == null ? null : hzzGeneration.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hzz_sequence
     *
     * @return the value of v_talent_inventory_roster.hzz_sequence
     *
     * @mbg.generated
     */
    public String getHzzSequence() {
        return hzzSequence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hzz_sequence
     *
     * @param hzzSequence the value for v_talent_inventory_roster.hzz_sequence
     *
     * @mbg.generated
     */
    public void setHzzSequence(String hzzSequence) {
        this.hzzSequence = hzzSequence == null ? null : hzzSequence.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.political_status
     *
     * @return the value of v_talent_inventory_roster.political_status
     *
     * @mbg.generated
     */
    public String getPoliticalStatus() {
        return politicalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.political_status
     *
     * @param politicalStatus the value for v_talent_inventory_roster.political_status
     *
     * @mbg.generated
     */
    public void setPoliticalStatus(String politicalStatus) {
        this.politicalStatus = politicalStatus == null ? null : politicalStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.political_status_trad
     *
     * @return the value of v_talent_inventory_roster.political_status_trad
     *
     * @mbg.generated
     */
    public String getPoliticalStatusTrad() {
        return politicalStatusTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.political_status_trad
     *
     * @param politicalStatusTrad the value for v_talent_inventory_roster.political_status_trad
     *
     * @mbg.generated
     */
    public void setPoliticalStatusTrad(String politicalStatusTrad) {
        this.politicalStatusTrad = politicalStatusTrad == null ? null : politicalStatusTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.political_status_eng
     *
     * @return the value of v_talent_inventory_roster.political_status_eng
     *
     * @mbg.generated
     */
    public String getPoliticalStatusEng() {
        return politicalStatusEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.political_status_eng
     *
     * @param politicalStatusEng the value for v_talent_inventory_roster.political_status_eng
     *
     * @mbg.generated
     */
    public void setPoliticalStatusEng(String politicalStatusEng) {
        this.politicalStatusEng = politicalStatusEng == null ? null : politicalStatusEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.party_joining_date
     *
     * @return the value of v_talent_inventory_roster.party_joining_date
     *
     * @mbg.generated
     */
    public LocalDate getPartyJoiningDate() {
        return partyJoiningDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.party_joining_date
     *
     * @param partyJoiningDate the value for v_talent_inventory_roster.party_joining_date
     *
     * @mbg.generated
     */
    public void setPartyJoiningDate(LocalDate partyJoiningDate) {
        this.partyJoiningDate = partyJoiningDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.specialty
     *
     * @return the value of v_talent_inventory_roster.specialty
     *
     * @mbg.generated
     */
    public String getSpecialty() {
        return specialty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.specialty
     *
     * @param specialty the value for v_talent_inventory_roster.specialty
     *
     * @mbg.generated
     */
    public void setSpecialty(String specialty) {
        this.specialty = specialty == null ? null : specialty.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.spouse_location
     *
     * @return the value of v_talent_inventory_roster.spouse_location
     *
     * @mbg.generated
     */
    public String getSpouseLocation() {
        return spouseLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.spouse_location
     *
     * @param spouseLocation the value for v_talent_inventory_roster.spouse_location
     *
     * @mbg.generated
     */
    public void setSpouseLocation(String spouseLocation) {
        this.spouseLocation = spouseLocation == null ? null : spouseLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.parents_location
     *
     * @return the value of v_talent_inventory_roster.parents_location
     *
     * @mbg.generated
     */
    public String getParentsLocation() {
        return parentsLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.parents_location
     *
     * @param parentsLocation the value for v_talent_inventory_roster.parents_location
     *
     * @mbg.generated
     */
    public void setParentsLocation(String parentsLocation) {
        this.parentsLocation = parentsLocation == null ? null : parentsLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.blood_type
     *
     * @return the value of v_talent_inventory_roster.blood_type
     *
     * @mbg.generated
     */
    public String getBloodType() {
        return bloodType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.blood_type
     *
     * @param bloodType the value for v_talent_inventory_roster.blood_type
     *
     * @mbg.generated
     */
    public void setBloodType(String bloodType) {
        this.bloodType = bloodType == null ? null : bloodType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.blood_type_trad
     *
     * @return the value of v_talent_inventory_roster.blood_type_trad
     *
     * @mbg.generated
     */
    public String getBloodTypeTrad() {
        return bloodTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.blood_type_trad
     *
     * @param bloodTypeTrad the value for v_talent_inventory_roster.blood_type_trad
     *
     * @mbg.generated
     */
    public void setBloodTypeTrad(String bloodTypeTrad) {
        this.bloodTypeTrad = bloodTypeTrad == null ? null : bloodTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.blood_type_eng
     *
     * @return the value of v_talent_inventory_roster.blood_type_eng
     *
     * @mbg.generated
     */
    public String getBloodTypeEng() {
        return bloodTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.blood_type_eng
     *
     * @param bloodTypeEng the value for v_talent_inventory_roster.blood_type_eng
     *
     * @mbg.generated
     */
    public void setBloodTypeEng(String bloodTypeEng) {
        this.bloodTypeEng = bloodTypeEng == null ? null : bloodTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.health_condition
     *
     * @return the value of v_talent_inventory_roster.health_condition
     *
     * @mbg.generated
     */
    public String getHealthCondition() {
        return healthCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.health_condition
     *
     * @param healthCondition the value for v_talent_inventory_roster.health_condition
     *
     * @mbg.generated
     */
    public void setHealthCondition(String healthCondition) {
        this.healthCondition = healthCondition == null ? null : healthCondition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.health_condition_trad
     *
     * @return the value of v_talent_inventory_roster.health_condition_trad
     *
     * @mbg.generated
     */
    public String getHealthConditionTrad() {
        return healthConditionTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.health_condition_trad
     *
     * @param healthConditionTrad the value for v_talent_inventory_roster.health_condition_trad
     *
     * @mbg.generated
     */
    public void setHealthConditionTrad(String healthConditionTrad) {
        this.healthConditionTrad = healthConditionTrad == null ? null : healthConditionTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.health_condition_eng
     *
     * @return the value of v_talent_inventory_roster.health_condition_eng
     *
     * @mbg.generated
     */
    public String getHealthConditionEng() {
        return healthConditionEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.health_condition_eng
     *
     * @param healthConditionEng the value for v_talent_inventory_roster.health_condition_eng
     *
     * @mbg.generated
     */
    public void setHealthConditionEng(String healthConditionEng) {
        this.healthConditionEng = healthConditionEng == null ? null : healthConditionEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.interests_hobbies
     *
     * @return the value of v_talent_inventory_roster.interests_hobbies
     *
     * @mbg.generated
     */
    public String getInterestsHobbies() {
        return interestsHobbies;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.interests_hobbies
     *
     * @param interestsHobbies the value for v_talent_inventory_roster.interests_hobbies
     *
     * @mbg.generated
     */
    public void setInterestsHobbies(String interestsHobbies) {
        this.interestsHobbies = interestsHobbies == null ? null : interestsHobbies.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.archive_company
     *
     * @return the value of v_talent_inventory_roster.archive_company
     *
     * @mbg.generated
     */
    public String getArchiveCompany() {
        return archiveCompany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.archive_company
     *
     * @param archiveCompany the value for v_talent_inventory_roster.archive_company
     *
     * @mbg.generated
     */
    public void setArchiveCompany(String archiveCompany) {
        this.archiveCompany = archiveCompany == null ? null : archiveCompany.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.res_loc
     *
     * @return the value of v_talent_inventory_roster.res_loc
     *
     * @mbg.generated
     */
    public String getResLoc() {
        return resLoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.res_loc
     *
     * @param resLoc the value for v_talent_inventory_roster.res_loc
     *
     * @mbg.generated
     */
    public void setResLoc(String resLoc) {
        this.resLoc = resLoc == null ? null : resLoc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.res_type
     *
     * @return the value of v_talent_inventory_roster.res_type
     *
     * @mbg.generated
     */
    public String getResType() {
        return resType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.res_type
     *
     * @param resType the value for v_talent_inventory_roster.res_type
     *
     * @mbg.generated
     */
    public void setResType(String resType) {
        this.resType = resType == null ? null : resType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.res_type_trad
     *
     * @return the value of v_talent_inventory_roster.res_type_trad
     *
     * @mbg.generated
     */
    public String getResTypeTrad() {
        return resTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.res_type_trad
     *
     * @param resTypeTrad the value for v_talent_inventory_roster.res_type_trad
     *
     * @mbg.generated
     */
    public void setResTypeTrad(String resTypeTrad) {
        this.resTypeTrad = resTypeTrad == null ? null : resTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.res_type_eng
     *
     * @return the value of v_talent_inventory_roster.res_type_eng
     *
     * @mbg.generated
     */
    public String getResTypeEng() {
        return resTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.res_type_eng
     *
     * @param resTypeEng the value for v_talent_inventory_roster.res_type_eng
     *
     * @mbg.generated
     */
    public void setResTypeEng(String resTypeEng) {
        this.resTypeEng = resTypeEng == null ? null : resTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.seq
     *
     * @return the value of v_talent_inventory_roster.seq
     *
     * @mbg.generated
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.seq
     *
     * @param seq the value for v_talent_inventory_roster.seq
     *
     * @mbg.generated
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.is_deleted
     *
     * @return the value of v_talent_inventory_roster.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.is_deleted
     *
     * @param isDeleted the value for v_talent_inventory_roster.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.is_sync
     *
     * @return the value of v_talent_inventory_roster.is_sync
     *
     * @mbg.generated
     */
    public Boolean getIsSync() {
        return isSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.is_sync
     *
     * @param isSync the value for v_talent_inventory_roster.is_sync
     *
     * @mbg.generated
     */
    public void setIsSync(Boolean isSync) {
        this.isSync = isSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.years_in_current_position
     *
     * @return the value of v_talent_inventory_roster.years_in_current_position
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentPosition() {
        return yearsInCurrentPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.years_in_current_position
     *
     * @param yearsInCurrentPosition the value for v_talent_inventory_roster.years_in_current_position
     *
     * @mbg.generated
     */
    public void setYearsInCurrentPosition(BigDecimal yearsInCurrentPosition) {
        this.yearsInCurrentPosition = yearsInCurrentPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.years_in_current_job_level
     *
     * @return the value of v_talent_inventory_roster.years_in_current_job_level
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentJobLevel() {
        return yearsInCurrentJobLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.years_in_current_job_level
     *
     * @param yearsInCurrentJobLevel the value for v_talent_inventory_roster.years_in_current_job_level
     *
     * @mbg.generated
     */
    public void setYearsInCurrentJobLevel(BigDecimal yearsInCurrentJobLevel) {
        this.yearsInCurrentJobLevel = yearsInCurrentJobLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.years_in_current_rank
     *
     * @return the value of v_talent_inventory_roster.years_in_current_rank
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentRank() {
        return yearsInCurrentRank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.years_in_current_rank
     *
     * @param yearsInCurrentRank the value for v_talent_inventory_roster.years_in_current_rank
     *
     * @mbg.generated
     */
    public void setYearsInCurrentRank(BigDecimal yearsInCurrentRank) {
        this.yearsInCurrentRank = yearsInCurrentRank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.talent_inventory_placement
     *
     * @return the value of v_talent_inventory_roster.talent_inventory_placement
     *
     * @mbg.generated
     */
    public String getTalentInventoryPlacement() {
        return talentInventoryPlacement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.talent_inventory_placement
     *
     * @param talentInventoryPlacement the value for v_talent_inventory_roster.talent_inventory_placement
     *
     * @mbg.generated
     */
    public void setTalentInventoryPlacement(String talentInventoryPlacement) {
        this.talentInventoryPlacement = talentInventoryPlacement == null ? null : talentInventoryPlacement.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.appraisal_result_year1
     *
     * @return the value of v_talent_inventory_roster.appraisal_result_year1
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear1() {
        return appraisalResultYear1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.appraisal_result_year1
     *
     * @param appraisalResultYear1 the value for v_talent_inventory_roster.appraisal_result_year1
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear1(String appraisalResultYear1) {
        this.appraisalResultYear1 = appraisalResultYear1 == null ? null : appraisalResultYear1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.appraisal_result_year2
     *
     * @return the value of v_talent_inventory_roster.appraisal_result_year2
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear2() {
        return appraisalResultYear2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.appraisal_result_year2
     *
     * @param appraisalResultYear2 the value for v_talent_inventory_roster.appraisal_result_year2
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear2(String appraisalResultYear2) {
        this.appraisalResultYear2 = appraisalResultYear2 == null ? null : appraisalResultYear2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.appraisal_result_year3
     *
     * @return the value of v_talent_inventory_roster.appraisal_result_year3
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear3() {
        return appraisalResultYear3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.appraisal_result_year3
     *
     * @param appraisalResultYear3 the value for v_talent_inventory_roster.appraisal_result_year3
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear3(String appraisalResultYear3) {
        this.appraisalResultYear3 = appraisalResultYear3 == null ? null : appraisalResultYear3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.photo
     *
     * @return the value of v_talent_inventory_roster.photo
     *
     * @mbg.generated
     */
    public String getPhoto() {
        return photo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.photo
     *
     * @param photo the value for v_talent_inventory_roster.photo
     *
     * @mbg.generated
     */
    public void setPhoto(String photo) {
        this.photo = photo == null ? null : photo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.creation_time
     *
     * @return the value of v_talent_inventory_roster.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.creation_time
     *
     * @param creationTime the value for v_talent_inventory_roster.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.create_username
     *
     * @return the value of v_talent_inventory_roster.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.create_username
     *
     * @param createUsername the value for v_talent_inventory_roster.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.create_user_id
     *
     * @return the value of v_talent_inventory_roster.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.create_user_id
     *
     * @param createUserId the value for v_talent_inventory_roster.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.last_update_time
     *
     * @return the value of v_talent_inventory_roster.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.last_update_time
     *
     * @param lastUpdateTime the value for v_talent_inventory_roster.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.last_update_username
     *
     * @return the value of v_talent_inventory_roster.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.last_update_username
     *
     * @param lastUpdateUsername the value for v_talent_inventory_roster.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.last_update_user_id
     *
     * @return the value of v_talent_inventory_roster.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.last_update_user_id
     *
     * @param lastUpdateUserId the value for v_talent_inventory_roster.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.last_update_version
     *
     * @return the value of v_talent_inventory_roster.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.last_update_version
     *
     * @param lastUpdateVersion the value for v_talent_inventory_roster.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.has_overseas_work_exp
     *
     * @return the value of v_talent_inventory_roster.has_overseas_work_exp
     *
     * @mbg.generated
     */
    public Boolean getHasOverseasWorkExp() {
        return hasOverseasWorkExp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.has_overseas_work_exp
     *
     * @param hasOverseasWorkExp the value for v_talent_inventory_roster.has_overseas_work_exp
     *
     * @mbg.generated
     */
    public void setHasOverseasWorkExp(Boolean hasOverseasWorkExp) {
        this.hasOverseasWorkExp = hasOverseasWorkExp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_level_code
     *
     * @return the value of v_talent_inventory_roster.position_level_code
     *
     * @mbg.generated
     */
    public String getPositionLevelCode() {
        return positionLevelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_level_code
     *
     * @param positionLevelCode the value for v_talent_inventory_roster.position_level_code
     *
     * @mbg.generated
     */
    public void setPositionLevelCode(String positionLevelCode) {
        this.positionLevelCode = positionLevelCode == null ? null : positionLevelCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.social_service
     *
     * @return the value of v_talent_inventory_roster.social_service
     *
     * @mbg.generated
     */
    public String getSocialService() {
        return socialService;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.social_service
     *
     * @param socialService the value for v_talent_inventory_roster.social_service
     *
     * @mbg.generated
     */
    public void setSocialService(String socialService) {
        this.socialService = socialService == null ? null : socialService.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.province
     *
     * @return the value of v_talent_inventory_roster.province
     *
     * @mbg.generated
     */
    public String getProvince() {
        return province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.province
     *
     * @param province the value for v_talent_inventory_roster.province
     *
     * @mbg.generated
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.is_on_job
     *
     * @return the value of v_talent_inventory_roster.is_on_job
     *
     * @mbg.generated
     */
    public Byte getIsOnJob() {
        return isOnJob;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.is_on_job
     *
     * @param isOnJob the value for v_talent_inventory_roster.is_on_job
     *
     * @mbg.generated
     */
    public void setIsOnJob(Byte isOnJob) {
        this.isOnJob = isOnJob;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.work_seniority
     *
     * @return the value of v_talent_inventory_roster.work_seniority
     *
     * @mbg.generated
     */
    public BigDecimal getWorkSeniority() {
        return workSeniority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.work_seniority
     *
     * @param workSeniority the value for v_talent_inventory_roster.work_seniority
     *
     * @mbg.generated
     */
    public void setWorkSeniority(BigDecimal workSeniority) {
        this.workSeniority = workSeniority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.cohl_seniority
     *
     * @return the value of v_talent_inventory_roster.cohl_seniority
     *
     * @mbg.generated
     */
    public BigDecimal getCohlSeniority() {
        return cohlSeniority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.cohl_seniority
     *
     * @param cohlSeniority the value for v_talent_inventory_roster.cohl_seniority
     *
     * @mbg.generated
     */
    public void setCohlSeniority(BigDecimal cohlSeniority) {
        this.cohlSeniority = cohlSeniority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.organization_code
     *
     * @return the value of v_talent_inventory_roster.organization_code
     *
     * @mbg.generated
     */
    public String getOrganizationCode() {
        return organizationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.organization_code
     *
     * @param organizationCode the value for v_talent_inventory_roster.organization_code
     *
     * @mbg.generated
     */
    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode == null ? null : organizationCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.platform_company_code
     *
     * @return the value of v_talent_inventory_roster.platform_company_code
     *
     * @mbg.generated
     */
    public String getPlatformCompanyCode() {
        return platformCompanyCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.platform_company_code
     *
     * @param platformCompanyCode the value for v_talent_inventory_roster.platform_company_code
     *
     * @mbg.generated
     */
    public void setPlatformCompanyCode(String platformCompanyCode) {
        this.platformCompanyCode = platformCompanyCode == null ? null : platformCompanyCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hire_type
     *
     * @return the value of v_talent_inventory_roster.hire_type
     *
     * @mbg.generated
     */
    public String getHireType() {
        return hireType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hire_type
     *
     * @param hireType the value for v_talent_inventory_roster.hire_type
     *
     * @mbg.generated
     */
    public void setHireType(String hireType) {
        this.hireType = hireType == null ? null : hireType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.manage_type
     *
     * @return the value of v_talent_inventory_roster.manage_type
     *
     * @mbg.generated
     */
    public String getManageType() {
        return manageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.manage_type
     *
     * @param manageType the value for v_talent_inventory_roster.manage_type
     *
     * @mbg.generated
     */
    public void setManageType(String manageType) {
        this.manageType = manageType == null ? null : manageType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.is_cadre
     *
     * @return the value of v_talent_inventory_roster.is_cadre
     *
     * @mbg.generated
     */
    public Boolean getIsCadre() {
        return isCadre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.is_cadre
     *
     * @param isCadre the value for v_talent_inventory_roster.is_cadre
     *
     * @mbg.generated
     */
    public void setIsCadre(Boolean isCadre) {
        this.isCadre = isCadre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.is_hzz
     *
     * @return the value of v_talent_inventory_roster.is_hzz
     *
     * @mbg.generated
     */
    public Boolean getIsHzz() {
        return isHzz;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.is_hzz
     *
     * @param isHzz the value for v_talent_inventory_roster.is_hzz
     *
     * @mbg.generated
     */
    public void setIsHzz(Boolean isHzz) {
        this.isHzz = isHzz;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.graduation_date
     *
     * @return the value of v_talent_inventory_roster.graduation_date
     *
     * @mbg.generated
     */
    public String getGraduationDate() {
        return graduationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.graduation_date
     *
     * @param graduationDate the value for v_talent_inventory_roster.graduation_date
     *
     * @mbg.generated
     */
    public void setGraduationDate(String graduationDate) {
        this.graduationDate = graduationDate == null ? null : graduationDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.special_line
     *
     * @return the value of v_talent_inventory_roster.special_line
     *
     * @mbg.generated
     */
    public String getSpecialLine() {
        return specialLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.special_line
     *
     * @param specialLine the value for v_talent_inventory_roster.special_line
     *
     * @mbg.generated
     */
    public void setSpecialLine(String specialLine) {
        this.specialLine = specialLine == null ? null : specialLine.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_level_code_hk
     *
     * @return the value of v_talent_inventory_roster.position_level_code_hk
     *
     * @mbg.generated
     */
    public String getPositionLevelCodeHk() {
        return positionLevelCodeHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_level_code_hk
     *
     * @param positionLevelCodeHk the value for v_talent_inventory_roster.position_level_code_hk
     *
     * @mbg.generated
     */
    public void setPositionLevelCodeHk(String positionLevelCodeHk) {
        this.positionLevelCodeHk = positionLevelCodeHk == null ? null : positionLevelCodeHk.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.birthdate_month
     *
     * @return the value of v_talent_inventory_roster.birthdate_month
     *
     * @mbg.generated
     */
    public Integer getBirthdateMonth() {
        return birthdateMonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.birthdate_month
     *
     * @param birthdateMonth the value for v_talent_inventory_roster.birthdate_month
     *
     * @mbg.generated
     */
    public void setBirthdateMonth(Integer birthdateMonth) {
        this.birthdateMonth = birthdateMonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.birthdate_day
     *
     * @return the value of v_talent_inventory_roster.birthdate_day
     *
     * @mbg.generated
     */
    public Integer getBirthdateDay() {
        return birthdateDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.birthdate_day
     *
     * @param birthdateDay the value for v_talent_inventory_roster.birthdate_day
     *
     * @mbg.generated
     */
    public void setBirthdateDay(Integer birthdateDay) {
        this.birthdateDay = birthdateDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.position_level_name
     *
     * @return the value of v_talent_inventory_roster.position_level_name
     *
     * @mbg.generated
     */
    public String getPositionLevelName() {
        return positionLevelName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.position_level_name
     *
     * @param positionLevelName the value for v_talent_inventory_roster.position_level_name
     *
     * @mbg.generated
     */
    public void setPositionLevelName(String positionLevelName) {
        this.positionLevelName = positionLevelName == null ? null : positionLevelName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.phone_overseas
     *
     * @return the value of v_talent_inventory_roster.phone_overseas
     *
     * @mbg.generated
     */
    public String getPhoneOverseas() {
        return phoneOverseas;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.phone_overseas
     *
     * @param phoneOverseas the value for v_talent_inventory_roster.phone_overseas
     *
     * @mbg.generated
     */
    public void setPhoneOverseas(String phoneOverseas) {
        this.phoneOverseas = phoneOverseas == null ? null : phoneOverseas.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.edu_all
     *
     * @return the value of v_talent_inventory_roster.edu_all
     *
     * @mbg.generated
     */
    public String getEduAll() {
        return eduAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.edu_all
     *
     * @param eduAll the value for v_talent_inventory_roster.edu_all
     *
     * @mbg.generated
     */
    public void setEduAll(String eduAll) {
        this.eduAll = eduAll == null ? null : eduAll.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.school_all
     *
     * @return the value of v_talent_inventory_roster.school_all
     *
     * @mbg.generated
     */
    public String getSchoolAll() {
        return schoolAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.school_all
     *
     * @param schoolAll the value for v_talent_inventory_roster.school_all
     *
     * @mbg.generated
     */
    public void setSchoolAll(String schoolAll) {
        this.schoolAll = schoolAll == null ? null : schoolAll.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.major_all
     *
     * @return the value of v_talent_inventory_roster.major_all
     *
     * @mbg.generated
     */
    public String getMajorAll() {
        return majorAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.major_all
     *
     * @param majorAll the value for v_talent_inventory_roster.major_all
     *
     * @mbg.generated
     */
    public void setMajorAll(String majorAll) {
        this.majorAll = majorAll == null ? null : majorAll.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.sex
     *
     * @return the value of v_talent_inventory_roster.sex
     *
     * @mbg.generated
     */
    public String getSex() {
        return sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.sex
     *
     * @param sex the value for v_talent_inventory_roster.sex
     *
     * @mbg.generated
     */
    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.staff_class3311
     *
     * @return the value of v_talent_inventory_roster.staff_class3311
     *
     * @mbg.generated
     */
    public String getStaffClass3311() {
        return staffClass3311;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.staff_class3311
     *
     * @param staffClass3311 the value for v_talent_inventory_roster.staff_class3311
     *
     * @mbg.generated
     */
    public void setStaffClass3311(String staffClass3311) {
        this.staffClass3311 = staffClass3311 == null ? null : staffClass3311.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.staff_class
     *
     * @return the value of v_talent_inventory_roster.staff_class
     *
     * @mbg.generated
     */
    public String getStaffClass() {
        return staffClass;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.staff_class
     *
     * @param staffClass the value for v_talent_inventory_roster.staff_class
     *
     * @mbg.generated
     */
    public void setStaffClass(String staffClass) {
        this.staffClass = staffClass == null ? null : staffClass.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.dimission_date
     *
     * @return the value of v_talent_inventory_roster.dimission_date
     *
     * @mbg.generated
     */
    public LocalDate getDimissionDate() {
        return dimissionDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.dimission_date
     *
     * @param dimissionDate the value for v_talent_inventory_roster.dimission_date
     *
     * @mbg.generated
     */
    public void setDimissionDate(LocalDate dimissionDate) {
        this.dimissionDate = dimissionDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.primary_position_pinyin
     *
     * @return the value of v_talent_inventory_roster.primary_position_pinyin
     *
     * @mbg.generated
     */
    public String getPrimaryPositionPinyin() {
        return primaryPositionPinyin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.primary_position_pinyin
     *
     * @param primaryPositionPinyin the value for v_talent_inventory_roster.primary_position_pinyin
     *
     * @mbg.generated
     */
    public void setPrimaryPositionPinyin(String primaryPositionPinyin) {
        this.primaryPositionPinyin = primaryPositionPinyin == null ? null : primaryPositionPinyin.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hire_type_query
     *
     * @return the value of v_talent_inventory_roster.hire_type_query
     *
     * @mbg.generated
     */
    public String getHireTypeQuery() {
        return hireTypeQuery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hire_type_query
     *
     * @param hireTypeQuery the value for v_talent_inventory_roster.hire_type_query
     *
     * @mbg.generated
     */
    public void setHireTypeQuery(String hireTypeQuery) {
        this.hireTypeQuery = hireTypeQuery == null ? null : hireTypeQuery.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.adname
     *
     * @return the value of v_talent_inventory_roster.adname
     *
     * @mbg.generated
     */
    public String getAdname() {
        return adname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.adname
     *
     * @param adname the value for v_talent_inventory_roster.adname
     *
     * @mbg.generated
     */
    public void setAdname(String adname) {
        this.adname = adname == null ? null : adname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.domain
     *
     * @return the value of v_talent_inventory_roster.domain
     *
     * @mbg.generated
     */
    public String getDomain() {
        return domain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.domain
     *
     * @param domain the value for v_talent_inventory_roster.domain
     *
     * @mbg.generated
     */
    public void setDomain(String domain) {
        this.domain = domain == null ? null : domain.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.username
     *
     * @return the value of v_talent_inventory_roster.username
     *
     * @mbg.generated
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.username
     *
     * @param username the value for v_talent_inventory_roster.username
     *
     * @mbg.generated
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.staff_class_macau
     *
     * @return the value of v_talent_inventory_roster.staff_class_macau
     *
     * @mbg.generated
     */
    public String getStaffClassMacau() {
        return staffClassMacau;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.staff_class_macau
     *
     * @param staffClassMacau the value for v_talent_inventory_roster.staff_class_macau
     *
     * @mbg.generated
     */
    public void setStaffClassMacau(String staffClassMacau) {
        this.staffClassMacau = staffClassMacau == null ? null : staffClassMacau.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.nationality
     *
     * @return the value of v_talent_inventory_roster.nationality
     *
     * @mbg.generated
     */
    public String getNationality() {
        return nationality;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.nationality
     *
     * @param nationality the value for v_talent_inventory_roster.nationality
     *
     * @mbg.generated
     */
    public void setNationality(String nationality) {
        this.nationality = nationality == null ? null : nationality.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_level_name
     *
     * @return the value of v_talent_inventory_roster.job_level_name
     *
     * @mbg.generated
     */
    public String getJobLevelName() {
        return jobLevelName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_level_name
     *
     * @param jobLevelName the value for v_talent_inventory_roster.job_level_name
     *
     * @mbg.generated
     */
    public void setJobLevelName(String jobLevelName) {
        this.jobLevelName = jobLevelName == null ? null : jobLevelName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_level_pilot
     *
     * @return the value of v_talent_inventory_roster.job_level_pilot
     *
     * @mbg.generated
     */
    public String getJobLevelPilot() {
        return jobLevelPilot;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_level_pilot
     *
     * @param jobLevelPilot the value for v_talent_inventory_roster.job_level_pilot
     *
     * @mbg.generated
     */
    public void setJobLevelPilot(String jobLevelPilot) {
        this.jobLevelPilot = jobLevelPilot == null ? null : jobLevelPilot.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.name_simplified
     *
     * @return the value of v_talent_inventory_roster.name_simplified
     *
     * @mbg.generated
     */
    public String getNameSimplified() {
        return nameSimplified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.name_simplified
     *
     * @param nameSimplified the value for v_talent_inventory_roster.name_simplified
     *
     * @mbg.generated
     */
    public void setNameSimplified(String nameSimplified) {
        this.nameSimplified = nameSimplified == null ? null : nameSimplified.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.school_type
     *
     * @return the value of v_talent_inventory_roster.school_type
     *
     * @mbg.generated
     */
    public String getSchoolType() {
        return schoolType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.school_type
     *
     * @param schoolType the value for v_talent_inventory_roster.school_type
     *
     * @mbg.generated
     */
    public void setSchoolType(String schoolType) {
        this.schoolType = schoolType == null ? null : schoolType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.personal_email
     *
     * @return the value of v_talent_inventory_roster.personal_email
     *
     * @mbg.generated
     */
    public String getPersonalEmail() {
        return personalEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.personal_email
     *
     * @param personalEmail the value for v_talent_inventory_roster.personal_email
     *
     * @mbg.generated
     */
    public void setPersonalEmail(String personalEmail) {
        this.personalEmail = personalEmail == null ? null : personalEmail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.hk_mo_cert_expiry_date
     *
     * @return the value of v_talent_inventory_roster.hk_mo_cert_expiry_date
     *
     * @mbg.generated
     */
    public LocalDate getHkMoCertExpiryDate() {
        return hkMoCertExpiryDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.hk_mo_cert_expiry_date
     *
     * @param hkMoCertExpiryDate the value for v_talent_inventory_roster.hk_mo_cert_expiry_date
     *
     * @mbg.generated
     */
    public void setHkMoCertExpiryDate(LocalDate hkMoCertExpiryDate) {
        this.hkMoCertExpiryDate = hkMoCertExpiryDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.height
     *
     * @return the value of v_talent_inventory_roster.height
     *
     * @mbg.generated
     */
    public BigDecimal getHeight() {
        return height;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.height
     *
     * @param height the value for v_talent_inventory_roster.height
     *
     * @mbg.generated
     */
    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_type_hk
     *
     * @return the value of v_talent_inventory_roster.job_type_hk
     *
     * @mbg.generated
     */
    public String getJobTypeHk() {
        return jobTypeHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_type_hk
     *
     * @param jobTypeHk the value for v_talent_inventory_roster.job_type_hk
     *
     * @mbg.generated
     */
    public void setJobTypeHk(String jobTypeHk) {
        this.jobTypeHk = jobTypeHk == null ? null : jobTypeHk.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.bank_account
     *
     * @return the value of v_talent_inventory_roster.bank_account
     *
     * @mbg.generated
     */
    public String getBankAccount() {
        return bankAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.bank_account
     *
     * @param bankAccount the value for v_talent_inventory_roster.bank_account
     *
     * @mbg.generated
     */
    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount == null ? null : bankAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.party_regular_date
     *
     * @return the value of v_talent_inventory_roster.party_regular_date
     *
     * @mbg.generated
     */
    public LocalDate getPartyRegularDate() {
        return partyRegularDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.party_regular_date
     *
     * @param partyRegularDate the value for v_talent_inventory_roster.party_regular_date
     *
     * @mbg.generated
     */
    public void setPartyRegularDate(LocalDate partyRegularDate) {
        this.partyRegularDate = partyRegularDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.eng_name_origin
     *
     * @return the value of v_talent_inventory_roster.eng_name_origin
     *
     * @mbg.generated
     */
    public String getEngNameOrigin() {
        return engNameOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.eng_name_origin
     *
     * @param engNameOrigin the value for v_talent_inventory_roster.eng_name_origin
     *
     * @mbg.generated
     */
    public void setEngNameOrigin(String engNameOrigin) {
        this.engNameOrigin = engNameOrigin == null ? null : engNameOrigin.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.source_tag
     *
     * @return the value of v_talent_inventory_roster.source_tag
     *
     * @mbg.generated
     */
    public String getSourceTag() {
        return sourceTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.source_tag
     *
     * @param sourceTag the value for v_talent_inventory_roster.source_tag
     *
     * @mbg.generated
     */
    public void setSourceTag(String sourceTag) {
        this.sourceTag = sourceTag == null ? null : sourceTag.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v_talent_inventory_roster.job_type_nd
     *
     * @return the value of v_talent_inventory_roster.job_type_nd
     *
     * @mbg.generated
     */
    public String getJobTypeNd() {
        return jobTypeNd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v_talent_inventory_roster.job_type_nd
     *
     * @param jobTypeNd the value for v_talent_inventory_roster.job_type_nd
     *
     * @mbg.generated
     */
    public void setJobTypeNd(String jobTypeNd) {
        this.jobTypeNd = jobTypeNd == null ? null : jobTypeNd.trim();
    }
}