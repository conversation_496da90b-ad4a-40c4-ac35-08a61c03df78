package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_person_info
 */
@TableName("t_person_info")
public class PersonInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.id
     *
     * @mbg.generated
     */
    @TableField("id")
    @TableId
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.head_id
     *
     * @mbg.generated
     */
    @TableField("head_id")
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.pernr
     *
     * @mbg.generated
     */
    @TableField("pernr")
    private String pernr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.name
     *
     * @mbg.generated
     */
    @TableField("name")
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.pinyin_name
     *
     * @mbg.generated
     */
    @TableField("pinyin_name")
    private String pinyinName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.eng_name
     *
     * @mbg.generated
     */
    @TableField("eng_name")
    private String engName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.eng_name_display
     *
     * @mbg.generated
     */
    @TableField("eng_name_display")
    private String engNameDisplay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.platform
     *
     * @mbg.generated
     */
    @TableField("platform")
    private String platform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.platform_trad
     *
     * @mbg.generated
     */
    @TableField("platform_trad")
    private String platformTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.platform_eng
     *
     * @mbg.generated
     */
    @TableField("platform_eng")
    private String platformEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.subsidiary
     *
     * @mbg.generated
     */
    @TableField("subsidiary")
    private String subsidiary;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.subsidiary_trad
     *
     * @mbg.generated
     */
    @TableField("subsidiary_trad")
    private String subsidiaryTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.subsidiary_eng
     *
     * @mbg.generated
     */
    @TableField("subsidiary_eng")
    private String subsidiaryEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.sub_project
     *
     * @mbg.generated
     */
    @TableField("sub_project")
    private String subProject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.sub_project_trad
     *
     * @mbg.generated
     */
    @TableField("sub_project_trad")
    private String subProjectTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.sub_project_eng
     *
     * @mbg.generated
     */
    @TableField("sub_project_eng")
    private String subProjectEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.primary_position
     *
     * @mbg.generated
     */
    @TableField("primary_position")
    private String primaryPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.conc_position
     *
     * @mbg.generated
     */
    @TableField("conc_position")
    private String concPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.person_in_charge
     *
     * @mbg.generated
     */
    @TableField("person_in_charge")
    private String personInCharge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.person_in_charge_trad
     *
     * @mbg.generated
     */
    @TableField("person_in_charge_trad")
    private String personInChargeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.person_in_charge_eng
     *
     * @mbg.generated
     */
    @TableField("person_in_charge_eng")
    private String personInChargeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_category
     *
     * @mbg.generated
     */
    @TableField("employee_category")
    private String employeeCategory;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_category_name
     *
     * @mbg.generated
     */
    @TableField("employee_category_name")
    private String employeeCategoryName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.work_place
     *
     * @mbg.generated
     */
    @TableField("work_place")
    private String workPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.position_type
     *
     * @mbg.generated
     */
    @TableField("position_type")
    private String positionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.position_type_trad
     *
     * @mbg.generated
     */
    @TableField("position_type_trad")
    private String positionTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.position_type_eng
     *
     * @mbg.generated
     */
    @TableField("position_type_eng")
    private String positionTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.expertise
     *
     * @mbg.generated
     */
    @TableField("expertise")
    private String expertise;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.position_level
     *
     * @mbg.generated
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.job_grade
     *
     * @mbg.generated
     */
    @TableField("job_grade")
    private String jobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hk_job_grade
     *
     * @mbg.generated
     */
    @TableField("hk_job_grade")
    private String hkJobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.gender
     *
     * @mbg.generated
     */
    @TableField("gender")
    private Integer gender;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.start_work_time
     *
     * @mbg.generated
     */
    @TableField("start_work_time")
    private LocalDate startWorkTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.join_cohl_time
     *
     * @mbg.generated
     */
    @TableField("join_cohl_time")
    private LocalDate joinCohlTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.join_3311_time
     *
     * @mbg.generated
     */
    @TableField("join_3311_time")
    private LocalDate join3311Time;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.start_overseas_time
     *
     * @mbg.generated
     */
    @TableField("start_overseas_time")
    private LocalDate startOverseasTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.duration_current_pos
     *
     * @mbg.generated
     */
    @TableField("duration_current_pos")
    private LocalDate durationCurrentPos;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.duration_current_level
     *
     * @mbg.generated
     */
    @TableField("duration_current_level")
    private LocalDate durationCurrentLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.duration_cur_job_grade
     *
     * @mbg.generated
     */
    @TableField("duration_cur_job_grade")
    private LocalDate durationCurJobGrade;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.birthdate
     *
     * @mbg.generated
     */
    @TableField("birthdate")
    private LocalDate birthdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.age
     *
     * @mbg.generated
     */
    @TableField("age")
    private Integer age;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.ethnicity
     *
     * @mbg.generated
     */
    @TableField("ethnicity")
    private String ethnicity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.ethnicity_trad
     *
     * @mbg.generated
     */
    @TableField("ethnicity_trad")
    private String ethnicityTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.ethnicity_eng
     *
     * @mbg.generated
     */
    @TableField("ethnicity_eng")
    private String ethnicityEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hometown
     *
     * @mbg.generated
     */
    @TableField("hometown")
    private String hometown;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.birth_place
     *
     * @mbg.generated
     */
    @TableField("birth_place")
    private String birthPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.residence
     *
     * @mbg.generated
     */
    @TableField("residence")
    private String residence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.marital_status
     *
     * @mbg.generated
     */
    @TableField("marital_status")
    private String maritalStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.child_status
     *
     * @mbg.generated
     */
    @TableField("child_status")
    private String childStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.education
     *
     * @mbg.generated
     */
    @TableField("education")
    private String education;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.major
     *
     * @mbg.generated
     */
    @TableField("major")
    private String major;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.job_title
     *
     * @mbg.generated
     */
    @TableField("job_title")
    private String jobTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.credentials
     *
     * @mbg.generated
     */
    @TableField("credentials")
    private String credentials;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.public_office
     *
     * @mbg.generated
     */
    @TableField("public_office")
    private String publicOffice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.source
     *
     * @mbg.generated
     */
    @TableField("source")
    private String source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.mobile
     *
     * @mbg.generated
     */
    @TableField("mobile")
    private String mobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.email
     *
     * @mbg.generated
     */
    @TableField("email")
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.remark
     *
     * @mbg.generated
     */
    @TableField("remark")
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.remark_trad
     *
     * @mbg.generated
     */
    @TableField("remark_trad")
    private String remarkTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.remark_eng
     *
     * @mbg.generated
     */
    @TableField("remark_eng")
    private String remarkEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.organization_id
     *
     * @mbg.generated
     */
    @TableField("organization_id")
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.company_code
     *
     * @mbg.generated
     */
    @TableField("company_code")
    private String companyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.salary_code
     *
     * @mbg.generated
     */
    @TableField("salary_code")
    private String salaryCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.personnel_scope
     *
     * @mbg.generated
     */
    @TableField("personnel_scope")
    private String personnelScope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.personnel_subscope
     *
     * @mbg.generated
     */
    @TableField("personnel_subscope")
    private String personnelSubscope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_group
     *
     * @mbg.generated
     */
    @TableField("employee_group")
    private String employeeGroup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_group_text
     *
     * @mbg.generated
     */
    @TableField("employee_group_text")
    private String employeeGroupText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_group_text_trad
     *
     * @mbg.generated
     */
    @TableField("employee_group_text_trad")
    private String employeeGroupTextTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_group_text_eng
     *
     * @mbg.generated
     */
    @TableField("employee_group_text_eng")
    private String employeeGroupTextEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_subgroup
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup")
    private String employeeSubgroup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_subgroup_text
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text")
    private String employeeSubgroupText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text_trad")
    private String employeeSubgroupTextTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    @TableField("employee_subgroup_text_eng")
    private String employeeSubgroupTextEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.mainland_id_card
     *
     * @mbg.generated
     */
    @TableField("mainland_id_card")
    private String mainlandIdCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.foreign_id_card
     *
     * @mbg.generated
     */
    @TableField("foreign_id_card")
    private String foreignIdCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hk_mo_passport
     *
     * @mbg.generated
     */
    @TableField("hk_mo_passport")
    private String hkMoPassport;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    @TableField("hk_mo_end_expiry")
    private String hkMoEndExpiry;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employment_mode
     *
     * @mbg.generated
     */
    @TableField("employment_mode")
    private String employmentMode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employment_mode_trad
     *
     * @mbg.generated
     */
    @TableField("employment_mode_trad")
    private String employmentModeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.employment_mode_eng
     *
     * @mbg.generated
     */
    @TableField("employment_mode_eng")
    private String employmentModeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hzz_generation
     *
     * @mbg.generated
     */
    @TableField("hzz_generation")
    private String hzzGeneration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.hzz_sequence
     *
     * @mbg.generated
     */
    @TableField("hzz_sequence")
    private String hzzSequence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.political_status
     *
     * @mbg.generated
     */
    @TableField("political_status")
    private String politicalStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.political_status_trad
     *
     * @mbg.generated
     */
    @TableField("political_status_trad")
    private String politicalStatusTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.political_status_eng
     *
     * @mbg.generated
     */
    @TableField("political_status_eng")
    private String politicalStatusEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.party_joining_date
     *
     * @mbg.generated
     */
    @TableField("party_joining_date")
    private LocalDate partyJoiningDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.specialty
     *
     * @mbg.generated
     */
    @TableField("specialty")
    private String specialty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.spouse_location
     *
     * @mbg.generated
     */
    @TableField("spouse_location")
    private String spouseLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.parents_location
     *
     * @mbg.generated
     */
    @TableField("parents_location")
    private String parentsLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.blood_type
     *
     * @mbg.generated
     */
    @TableField("blood_type")
    private String bloodType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.blood_type_trad
     *
     * @mbg.generated
     */
    @TableField("blood_type_trad")
    private String bloodTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.blood_type_eng
     *
     * @mbg.generated
     */
    @TableField("blood_type_eng")
    private String bloodTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.health_condition
     *
     * @mbg.generated
     */
    @TableField("health_condition")
    private String healthCondition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.health_condition_trad
     *
     * @mbg.generated
     */
    @TableField("health_condition_trad")
    private String healthConditionTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.health_condition_eng
     *
     * @mbg.generated
     */
    @TableField("health_condition_eng")
    private String healthConditionEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.interests_hobbies
     *
     * @mbg.generated
     */
    @TableField("interests_hobbies")
    private String interestsHobbies;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.archive_company
     *
     * @mbg.generated
     */
    @TableField("archive_company")
    private String archiveCompany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.res_loc
     *
     * @mbg.generated
     */
    @TableField("res_loc")
    private String resLoc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.res_type
     *
     * @mbg.generated
     */
    @TableField("res_type")
    private String resType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.res_type_trad
     *
     * @mbg.generated
     */
    @TableField("res_type_trad")
    private String resTypeTrad;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.res_type_eng
     *
     * @mbg.generated
     */
    @TableField("res_type_eng")
    private String resTypeEng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.seq
     *
     * @mbg.generated
     */
    @TableField("seq")
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.is_deleted
     *
     * @mbg.generated
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.is_sync
     *
     * @mbg.generated
     */
    @TableField("is_sync")
    private Boolean isSync;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.years_in_current_position
     *
     * @mbg.generated
     */
    @TableField("years_in_current_position")
    private BigDecimal yearsInCurrentPosition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.years_in_current_job_level
     *
     * @mbg.generated
     */
    @TableField("years_in_current_job_level")
    private BigDecimal yearsInCurrentJobLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.years_in_current_rank
     *
     * @mbg.generated
     */
    @TableField("years_in_current_rank")
    private BigDecimal yearsInCurrentRank;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.talent_inventory_placement
     *
     * @mbg.generated
     */
    @TableField("talent_inventory_placement")
    private String talentInventoryPlacement;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.appraisal_result_year1
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year1")
    private String appraisalResultYear1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.appraisal_result_year2
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year2")
    private String appraisalResultYear2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.appraisal_result_year3
     *
     * @mbg.generated
     */
    @TableField("appraisal_result_year3")
    private String appraisalResultYear3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.photo
     *
     * @mbg.generated
     */
    @TableField("photo")
    private String photo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.creation_time
     *
     * @mbg.generated
     */
    @TableField("creation_time")
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.create_username
     *
     * @mbg.generated
     */
    @TableField("create_username")
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.create_user_id
     *
     * @mbg.generated
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.last_update_time
     *
     * @mbg.generated
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.last_update_username
     *
     * @mbg.generated
     */
    @TableField("last_update_username")
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.last_update_user_id
     *
     * @mbg.generated
     */
    @TableField("last_update_user_id")
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.last_update_version
     *
     * @mbg.generated
     */
    @TableField("last_update_version")
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_person_info.organization_code
     *
     * @mbg.generated
     */
    @TableField("organization_code")
    private String organizationCode;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.id
     *
     * @return the value of t_person_info.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.id
     *
     * @param id the value for t_person_info.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.head_id
     *
     * @return the value of t_person_info.head_id
     *
     * @mbg.generated
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.head_id
     *
     * @param headId the value for t_person_info.head_id
     *
     * @mbg.generated
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.pernr
     *
     * @return the value of t_person_info.pernr
     *
     * @mbg.generated
     */
    public String getPernr() {
        return pernr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.pernr
     *
     * @param pernr the value for t_person_info.pernr
     *
     * @mbg.generated
     */
    public void setPernr(String pernr) {
        this.pernr = pernr == null ? null : pernr.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.name
     *
     * @return the value of t_person_info.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.name
     *
     * @param name the value for t_person_info.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.pinyin_name
     *
     * @return the value of t_person_info.pinyin_name
     *
     * @mbg.generated
     */
    public String getPinyinName() {
        return pinyinName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.pinyin_name
     *
     * @param pinyinName the value for t_person_info.pinyin_name
     *
     * @mbg.generated
     */
    public void setPinyinName(String pinyinName) {
        this.pinyinName = pinyinName == null ? null : pinyinName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.eng_name
     *
     * @return the value of t_person_info.eng_name
     *
     * @mbg.generated
     */
    public String getEngName() {
        return engName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.eng_name
     *
     * @param engName the value for t_person_info.eng_name
     *
     * @mbg.generated
     */
    public void setEngName(String engName) {
        this.engName = engName == null ? null : engName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.eng_name_display
     *
     * @return the value of t_person_info.eng_name_display
     *
     * @mbg.generated
     */
    public String getEngNameDisplay() {
        return engNameDisplay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.eng_name_display
     *
     * @param engNameDisplay the value for t_person_info.eng_name_display
     *
     * @mbg.generated
     */
    public void setEngNameDisplay(String engNameDisplay) {
        this.engNameDisplay = engNameDisplay == null ? null : engNameDisplay.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.platform
     *
     * @return the value of t_person_info.platform
     *
     * @mbg.generated
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.platform
     *
     * @param platform the value for t_person_info.platform
     *
     * @mbg.generated
     */
    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.platform_trad
     *
     * @return the value of t_person_info.platform_trad
     *
     * @mbg.generated
     */
    public String getPlatformTrad() {
        return platformTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.platform_trad
     *
     * @param platformTrad the value for t_person_info.platform_trad
     *
     * @mbg.generated
     */
    public void setPlatformTrad(String platformTrad) {
        this.platformTrad = platformTrad == null ? null : platformTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.platform_eng
     *
     * @return the value of t_person_info.platform_eng
     *
     * @mbg.generated
     */
    public String getPlatformEng() {
        return platformEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.platform_eng
     *
     * @param platformEng the value for t_person_info.platform_eng
     *
     * @mbg.generated
     */
    public void setPlatformEng(String platformEng) {
        this.platformEng = platformEng == null ? null : platformEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.subsidiary
     *
     * @return the value of t_person_info.subsidiary
     *
     * @mbg.generated
     */
    public String getSubsidiary() {
        return subsidiary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.subsidiary
     *
     * @param subsidiary the value for t_person_info.subsidiary
     *
     * @mbg.generated
     */
    public void setSubsidiary(String subsidiary) {
        this.subsidiary = subsidiary == null ? null : subsidiary.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.subsidiary_trad
     *
     * @return the value of t_person_info.subsidiary_trad
     *
     * @mbg.generated
     */
    public String getSubsidiaryTrad() {
        return subsidiaryTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.subsidiary_trad
     *
     * @param subsidiaryTrad the value for t_person_info.subsidiary_trad
     *
     * @mbg.generated
     */
    public void setSubsidiaryTrad(String subsidiaryTrad) {
        this.subsidiaryTrad = subsidiaryTrad == null ? null : subsidiaryTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.subsidiary_eng
     *
     * @return the value of t_person_info.subsidiary_eng
     *
     * @mbg.generated
     */
    public String getSubsidiaryEng() {
        return subsidiaryEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.subsidiary_eng
     *
     * @param subsidiaryEng the value for t_person_info.subsidiary_eng
     *
     * @mbg.generated
     */
    public void setSubsidiaryEng(String subsidiaryEng) {
        this.subsidiaryEng = subsidiaryEng == null ? null : subsidiaryEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.sub_project
     *
     * @return the value of t_person_info.sub_project
     *
     * @mbg.generated
     */
    public String getSubProject() {
        return subProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.sub_project
     *
     * @param subProject the value for t_person_info.sub_project
     *
     * @mbg.generated
     */
    public void setSubProject(String subProject) {
        this.subProject = subProject == null ? null : subProject.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.sub_project_trad
     *
     * @return the value of t_person_info.sub_project_trad
     *
     * @mbg.generated
     */
    public String getSubProjectTrad() {
        return subProjectTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.sub_project_trad
     *
     * @param subProjectTrad the value for t_person_info.sub_project_trad
     *
     * @mbg.generated
     */
    public void setSubProjectTrad(String subProjectTrad) {
        this.subProjectTrad = subProjectTrad == null ? null : subProjectTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.sub_project_eng
     *
     * @return the value of t_person_info.sub_project_eng
     *
     * @mbg.generated
     */
    public String getSubProjectEng() {
        return subProjectEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.sub_project_eng
     *
     * @param subProjectEng the value for t_person_info.sub_project_eng
     *
     * @mbg.generated
     */
    public void setSubProjectEng(String subProjectEng) {
        this.subProjectEng = subProjectEng == null ? null : subProjectEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.primary_position
     *
     * @return the value of t_person_info.primary_position
     *
     * @mbg.generated
     */
    public String getPrimaryPosition() {
        return primaryPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.primary_position
     *
     * @param primaryPosition the value for t_person_info.primary_position
     *
     * @mbg.generated
     */
    public void setPrimaryPosition(String primaryPosition) {
        this.primaryPosition = primaryPosition == null ? null : primaryPosition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.conc_position
     *
     * @return the value of t_person_info.conc_position
     *
     * @mbg.generated
     */
    public String getConcPosition() {
        return concPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.conc_position
     *
     * @param concPosition the value for t_person_info.conc_position
     *
     * @mbg.generated
     */
    public void setConcPosition(String concPosition) {
        this.concPosition = concPosition == null ? null : concPosition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.person_in_charge
     *
     * @return the value of t_person_info.person_in_charge
     *
     * @mbg.generated
     */
    public String getPersonInCharge() {
        return personInCharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.person_in_charge
     *
     * @param personInCharge the value for t_person_info.person_in_charge
     *
     * @mbg.generated
     */
    public void setPersonInCharge(String personInCharge) {
        this.personInCharge = personInCharge == null ? null : personInCharge.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.person_in_charge_trad
     *
     * @return the value of t_person_info.person_in_charge_trad
     *
     * @mbg.generated
     */
    public String getPersonInChargeTrad() {
        return personInChargeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.person_in_charge_trad
     *
     * @param personInChargeTrad the value for t_person_info.person_in_charge_trad
     *
     * @mbg.generated
     */
    public void setPersonInChargeTrad(String personInChargeTrad) {
        this.personInChargeTrad = personInChargeTrad == null ? null : personInChargeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.person_in_charge_eng
     *
     * @return the value of t_person_info.person_in_charge_eng
     *
     * @mbg.generated
     */
    public String getPersonInChargeEng() {
        return personInChargeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.person_in_charge_eng
     *
     * @param personInChargeEng the value for t_person_info.person_in_charge_eng
     *
     * @mbg.generated
     */
    public void setPersonInChargeEng(String personInChargeEng) {
        this.personInChargeEng = personInChargeEng == null ? null : personInChargeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_category
     *
     * @return the value of t_person_info.employee_category
     *
     * @mbg.generated
     */
    public String getEmployeeCategory() {
        return employeeCategory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_category
     *
     * @param employeeCategory the value for t_person_info.employee_category
     *
     * @mbg.generated
     */
    public void setEmployeeCategory(String employeeCategory) {
        this.employeeCategory = employeeCategory == null ? null : employeeCategory.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_category_name
     *
     * @return the value of t_person_info.employee_category_name
     *
     * @mbg.generated
     */
    public String getEmployeeCategoryName() {
        return employeeCategoryName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_category_name
     *
     * @param employeeCategoryName the value for t_person_info.employee_category_name
     *
     * @mbg.generated
     */
    public void setEmployeeCategoryName(String employeeCategoryName) {
        this.employeeCategoryName = employeeCategoryName == null ? null : employeeCategoryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.work_place
     *
     * @return the value of t_person_info.work_place
     *
     * @mbg.generated
     */
    public String getWorkPlace() {
        return workPlace;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.work_place
     *
     * @param workPlace the value for t_person_info.work_place
     *
     * @mbg.generated
     */
    public void setWorkPlace(String workPlace) {
        this.workPlace = workPlace == null ? null : workPlace.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.position_type
     *
     * @return the value of t_person_info.position_type
     *
     * @mbg.generated
     */
    public String getPositionType() {
        return positionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.position_type
     *
     * @param positionType the value for t_person_info.position_type
     *
     * @mbg.generated
     */
    public void setPositionType(String positionType) {
        this.positionType = positionType == null ? null : positionType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.position_type_trad
     *
     * @return the value of t_person_info.position_type_trad
     *
     * @mbg.generated
     */
    public String getPositionTypeTrad() {
        return positionTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.position_type_trad
     *
     * @param positionTypeTrad the value for t_person_info.position_type_trad
     *
     * @mbg.generated
     */
    public void setPositionTypeTrad(String positionTypeTrad) {
        this.positionTypeTrad = positionTypeTrad == null ? null : positionTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.position_type_eng
     *
     * @return the value of t_person_info.position_type_eng
     *
     * @mbg.generated
     */
    public String getPositionTypeEng() {
        return positionTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.position_type_eng
     *
     * @param positionTypeEng the value for t_person_info.position_type_eng
     *
     * @mbg.generated
     */
    public void setPositionTypeEng(String positionTypeEng) {
        this.positionTypeEng = positionTypeEng == null ? null : positionTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.expertise
     *
     * @return the value of t_person_info.expertise
     *
     * @mbg.generated
     */
    public String getExpertise() {
        return expertise;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.expertise
     *
     * @param expertise the value for t_person_info.expertise
     *
     * @mbg.generated
     */
    public void setExpertise(String expertise) {
        this.expertise = expertise == null ? null : expertise.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.position_level
     *
     * @return the value of t_person_info.position_level
     *
     * @mbg.generated
     */
    public String getPositionLevel() {
        return positionLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.position_level
     *
     * @param positionLevel the value for t_person_info.position_level
     *
     * @mbg.generated
     */
    public void setPositionLevel(String positionLevel) {
        this.positionLevel = positionLevel == null ? null : positionLevel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.job_grade
     *
     * @return the value of t_person_info.job_grade
     *
     * @mbg.generated
     */
    public String getJobGrade() {
        return jobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.job_grade
     *
     * @param jobGrade the value for t_person_info.job_grade
     *
     * @mbg.generated
     */
    public void setJobGrade(String jobGrade) {
        this.jobGrade = jobGrade == null ? null : jobGrade.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hk_job_grade
     *
     * @return the value of t_person_info.hk_job_grade
     *
     * @mbg.generated
     */
    public String getHkJobGrade() {
        return hkJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hk_job_grade
     *
     * @param hkJobGrade the value for t_person_info.hk_job_grade
     *
     * @mbg.generated
     */
    public void setHkJobGrade(String hkJobGrade) {
        this.hkJobGrade = hkJobGrade == null ? null : hkJobGrade.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.gender
     *
     * @return the value of t_person_info.gender
     *
     * @mbg.generated
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.gender
     *
     * @param gender the value for t_person_info.gender
     *
     * @mbg.generated
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.start_work_time
     *
     * @return the value of t_person_info.start_work_time
     *
     * @mbg.generated
     */
    public LocalDate getStartWorkTime() {
        return startWorkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.start_work_time
     *
     * @param startWorkTime the value for t_person_info.start_work_time
     *
     * @mbg.generated
     */
    public void setStartWorkTime(LocalDate startWorkTime) {
        this.startWorkTime = startWorkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.join_cohl_time
     *
     * @return the value of t_person_info.join_cohl_time
     *
     * @mbg.generated
     */
    public LocalDate getJoinCohlTime() {
        return joinCohlTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.join_cohl_time
     *
     * @param joinCohlTime the value for t_person_info.join_cohl_time
     *
     * @mbg.generated
     */
    public void setJoinCohlTime(LocalDate joinCohlTime) {
        this.joinCohlTime = joinCohlTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.join_3311_time
     *
     * @return the value of t_person_info.join_3311_time
     *
     * @mbg.generated
     */
    public LocalDate getJoin3311Time() {
        return join3311Time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.join_3311_time
     *
     * @param join3311Time the value for t_person_info.join_3311_time
     *
     * @mbg.generated
     */
    public void setJoin3311Time(LocalDate join3311Time) {
        this.join3311Time = join3311Time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.start_overseas_time
     *
     * @return the value of t_person_info.start_overseas_time
     *
     * @mbg.generated
     */
    public LocalDate getStartOverseasTime() {
        return startOverseasTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.start_overseas_time
     *
     * @param startOverseasTime the value for t_person_info.start_overseas_time
     *
     * @mbg.generated
     */
    public void setStartOverseasTime(LocalDate startOverseasTime) {
        this.startOverseasTime = startOverseasTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.duration_current_pos
     *
     * @return the value of t_person_info.duration_current_pos
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurrentPos() {
        return durationCurrentPos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.duration_current_pos
     *
     * @param durationCurrentPos the value for t_person_info.duration_current_pos
     *
     * @mbg.generated
     */
    public void setDurationCurrentPos(LocalDate durationCurrentPos) {
        this.durationCurrentPos = durationCurrentPos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.duration_current_level
     *
     * @return the value of t_person_info.duration_current_level
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurrentLevel() {
        return durationCurrentLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.duration_current_level
     *
     * @param durationCurrentLevel the value for t_person_info.duration_current_level
     *
     * @mbg.generated
     */
    public void setDurationCurrentLevel(LocalDate durationCurrentLevel) {
        this.durationCurrentLevel = durationCurrentLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.duration_cur_job_grade
     *
     * @return the value of t_person_info.duration_cur_job_grade
     *
     * @mbg.generated
     */
    public LocalDate getDurationCurJobGrade() {
        return durationCurJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.duration_cur_job_grade
     *
     * @param durationCurJobGrade the value for t_person_info.duration_cur_job_grade
     *
     * @mbg.generated
     */
    public void setDurationCurJobGrade(LocalDate durationCurJobGrade) {
        this.durationCurJobGrade = durationCurJobGrade;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.birthdate
     *
     * @return the value of t_person_info.birthdate
     *
     * @mbg.generated
     */
    public LocalDate getBirthdate() {
        return birthdate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.birthdate
     *
     * @param birthdate the value for t_person_info.birthdate
     *
     * @mbg.generated
     */
    public void setBirthdate(LocalDate birthdate) {
        this.birthdate = birthdate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.age
     *
     * @return the value of t_person_info.age
     *
     * @mbg.generated
     */
    public Integer getAge() {
        return age;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.age
     *
     * @param age the value for t_person_info.age
     *
     * @mbg.generated
     */
    public void setAge(Integer age) {
        this.age = age;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.ethnicity
     *
     * @return the value of t_person_info.ethnicity
     *
     * @mbg.generated
     */
    public String getEthnicity() {
        return ethnicity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.ethnicity
     *
     * @param ethnicity the value for t_person_info.ethnicity
     *
     * @mbg.generated
     */
    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity == null ? null : ethnicity.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.ethnicity_trad
     *
     * @return the value of t_person_info.ethnicity_trad
     *
     * @mbg.generated
     */
    public String getEthnicityTrad() {
        return ethnicityTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.ethnicity_trad
     *
     * @param ethnicityTrad the value for t_person_info.ethnicity_trad
     *
     * @mbg.generated
     */
    public void setEthnicityTrad(String ethnicityTrad) {
        this.ethnicityTrad = ethnicityTrad == null ? null : ethnicityTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.ethnicity_eng
     *
     * @return the value of t_person_info.ethnicity_eng
     *
     * @mbg.generated
     */
    public String getEthnicityEng() {
        return ethnicityEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.ethnicity_eng
     *
     * @param ethnicityEng the value for t_person_info.ethnicity_eng
     *
     * @mbg.generated
     */
    public void setEthnicityEng(String ethnicityEng) {
        this.ethnicityEng = ethnicityEng == null ? null : ethnicityEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hometown
     *
     * @return the value of t_person_info.hometown
     *
     * @mbg.generated
     */
    public String getHometown() {
        return hometown;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hometown
     *
     * @param hometown the value for t_person_info.hometown
     *
     * @mbg.generated
     */
    public void setHometown(String hometown) {
        this.hometown = hometown == null ? null : hometown.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.birth_place
     *
     * @return the value of t_person_info.birth_place
     *
     * @mbg.generated
     */
    public String getBirthPlace() {
        return birthPlace;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.birth_place
     *
     * @param birthPlace the value for t_person_info.birth_place
     *
     * @mbg.generated
     */
    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace == null ? null : birthPlace.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.residence
     *
     * @return the value of t_person_info.residence
     *
     * @mbg.generated
     */
    public String getResidence() {
        return residence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.residence
     *
     * @param residence the value for t_person_info.residence
     *
     * @mbg.generated
     */
    public void setResidence(String residence) {
        this.residence = residence == null ? null : residence.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.marital_status
     *
     * @return the value of t_person_info.marital_status
     *
     * @mbg.generated
     */
    public String getMaritalStatus() {
        return maritalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.marital_status
     *
     * @param maritalStatus the value for t_person_info.marital_status
     *
     * @mbg.generated
     */
    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus == null ? null : maritalStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.child_status
     *
     * @return the value of t_person_info.child_status
     *
     * @mbg.generated
     */
    public String getChildStatus() {
        return childStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.child_status
     *
     * @param childStatus the value for t_person_info.child_status
     *
     * @mbg.generated
     */
    public void setChildStatus(String childStatus) {
        this.childStatus = childStatus == null ? null : childStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.education
     *
     * @return the value of t_person_info.education
     *
     * @mbg.generated
     */
    public String getEducation() {
        return education;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.education
     *
     * @param education the value for t_person_info.education
     *
     * @mbg.generated
     */
    public void setEducation(String education) {
        this.education = education == null ? null : education.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.major
     *
     * @return the value of t_person_info.major
     *
     * @mbg.generated
     */
    public String getMajor() {
        return major;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.major
     *
     * @param major the value for t_person_info.major
     *
     * @mbg.generated
     */
    public void setMajor(String major) {
        this.major = major == null ? null : major.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.job_title
     *
     * @return the value of t_person_info.job_title
     *
     * @mbg.generated
     */
    public String getJobTitle() {
        return jobTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.job_title
     *
     * @param jobTitle the value for t_person_info.job_title
     *
     * @mbg.generated
     */
    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle == null ? null : jobTitle.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.credentials
     *
     * @return the value of t_person_info.credentials
     *
     * @mbg.generated
     */
    public String getCredentials() {
        return credentials;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.credentials
     *
     * @param credentials the value for t_person_info.credentials
     *
     * @mbg.generated
     */
    public void setCredentials(String credentials) {
        this.credentials = credentials == null ? null : credentials.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.public_office
     *
     * @return the value of t_person_info.public_office
     *
     * @mbg.generated
     */
    public String getPublicOffice() {
        return publicOffice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.public_office
     *
     * @param publicOffice the value for t_person_info.public_office
     *
     * @mbg.generated
     */
    public void setPublicOffice(String publicOffice) {
        this.publicOffice = publicOffice == null ? null : publicOffice.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.source
     *
     * @return the value of t_person_info.source
     *
     * @mbg.generated
     */
    public String getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.source
     *
     * @param source the value for t_person_info.source
     *
     * @mbg.generated
     */
    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.mobile
     *
     * @return the value of t_person_info.mobile
     *
     * @mbg.generated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.mobile
     *
     * @param mobile the value for t_person_info.mobile
     *
     * @mbg.generated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.email
     *
     * @return the value of t_person_info.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.email
     *
     * @param email the value for t_person_info.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.remark
     *
     * @return the value of t_person_info.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.remark
     *
     * @param remark the value for t_person_info.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.remark_trad
     *
     * @return the value of t_person_info.remark_trad
     *
     * @mbg.generated
     */
    public String getRemarkTrad() {
        return remarkTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.remark_trad
     *
     * @param remarkTrad the value for t_person_info.remark_trad
     *
     * @mbg.generated
     */
    public void setRemarkTrad(String remarkTrad) {
        this.remarkTrad = remarkTrad == null ? null : remarkTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.remark_eng
     *
     * @return the value of t_person_info.remark_eng
     *
     * @mbg.generated
     */
    public String getRemarkEng() {
        return remarkEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.remark_eng
     *
     * @param remarkEng the value for t_person_info.remark_eng
     *
     * @mbg.generated
     */
    public void setRemarkEng(String remarkEng) {
        this.remarkEng = remarkEng == null ? null : remarkEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.organization_id
     *
     * @return the value of t_person_info.organization_id
     *
     * @mbg.generated
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.organization_id
     *
     * @param organizationId the value for t_person_info.organization_id
     *
     * @mbg.generated
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.company_code
     *
     * @return the value of t_person_info.company_code
     *
     * @mbg.generated
     */
    public String getCompanyCode() {
        return companyCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.company_code
     *
     * @param companyCode the value for t_person_info.company_code
     *
     * @mbg.generated
     */
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.salary_code
     *
     * @return the value of t_person_info.salary_code
     *
     * @mbg.generated
     */
    public String getSalaryCode() {
        return salaryCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.salary_code
     *
     * @param salaryCode the value for t_person_info.salary_code
     *
     * @mbg.generated
     */
    public void setSalaryCode(String salaryCode) {
        this.salaryCode = salaryCode == null ? null : salaryCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.personnel_scope
     *
     * @return the value of t_person_info.personnel_scope
     *
     * @mbg.generated
     */
    public String getPersonnelScope() {
        return personnelScope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.personnel_scope
     *
     * @param personnelScope the value for t_person_info.personnel_scope
     *
     * @mbg.generated
     */
    public void setPersonnelScope(String personnelScope) {
        this.personnelScope = personnelScope == null ? null : personnelScope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.personnel_subscope
     *
     * @return the value of t_person_info.personnel_subscope
     *
     * @mbg.generated
     */
    public String getPersonnelSubscope() {
        return personnelSubscope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.personnel_subscope
     *
     * @param personnelSubscope the value for t_person_info.personnel_subscope
     *
     * @mbg.generated
     */
    public void setPersonnelSubscope(String personnelSubscope) {
        this.personnelSubscope = personnelSubscope == null ? null : personnelSubscope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_group
     *
     * @return the value of t_person_info.employee_group
     *
     * @mbg.generated
     */
    public String getEmployeeGroup() {
        return employeeGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_group
     *
     * @param employeeGroup the value for t_person_info.employee_group
     *
     * @mbg.generated
     */
    public void setEmployeeGroup(String employeeGroup) {
        this.employeeGroup = employeeGroup == null ? null : employeeGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_group_text
     *
     * @return the value of t_person_info.employee_group_text
     *
     * @mbg.generated
     */
    public String getEmployeeGroupText() {
        return employeeGroupText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_group_text
     *
     * @param employeeGroupText the value for t_person_info.employee_group_text
     *
     * @mbg.generated
     */
    public void setEmployeeGroupText(String employeeGroupText) {
        this.employeeGroupText = employeeGroupText == null ? null : employeeGroupText.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_group_text_trad
     *
     * @return the value of t_person_info.employee_group_text_trad
     *
     * @mbg.generated
     */
    public String getEmployeeGroupTextTrad() {
        return employeeGroupTextTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_group_text_trad
     *
     * @param employeeGroupTextTrad the value for t_person_info.employee_group_text_trad
     *
     * @mbg.generated
     */
    public void setEmployeeGroupTextTrad(String employeeGroupTextTrad) {
        this.employeeGroupTextTrad = employeeGroupTextTrad == null ? null : employeeGroupTextTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_group_text_eng
     *
     * @return the value of t_person_info.employee_group_text_eng
     *
     * @mbg.generated
     */
    public String getEmployeeGroupTextEng() {
        return employeeGroupTextEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_group_text_eng
     *
     * @param employeeGroupTextEng the value for t_person_info.employee_group_text_eng
     *
     * @mbg.generated
     */
    public void setEmployeeGroupTextEng(String employeeGroupTextEng) {
        this.employeeGroupTextEng = employeeGroupTextEng == null ? null : employeeGroupTextEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_subgroup
     *
     * @return the value of t_person_info.employee_subgroup
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroup() {
        return employeeSubgroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_subgroup
     *
     * @param employeeSubgroup the value for t_person_info.employee_subgroup
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroup(String employeeSubgroup) {
        this.employeeSubgroup = employeeSubgroup == null ? null : employeeSubgroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_subgroup_text
     *
     * @return the value of t_person_info.employee_subgroup_text
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupText() {
        return employeeSubgroupText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_subgroup_text
     *
     * @param employeeSubgroupText the value for t_person_info.employee_subgroup_text
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupText(String employeeSubgroupText) {
        this.employeeSubgroupText = employeeSubgroupText == null ? null : employeeSubgroupText.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_subgroup_text_trad
     *
     * @return the value of t_person_info.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupTextTrad() {
        return employeeSubgroupTextTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_subgroup_text_trad
     *
     * @param employeeSubgroupTextTrad the value for t_person_info.employee_subgroup_text_trad
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupTextTrad(String employeeSubgroupTextTrad) {
        this.employeeSubgroupTextTrad = employeeSubgroupTextTrad == null ? null : employeeSubgroupTextTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employee_subgroup_text_eng
     *
     * @return the value of t_person_info.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    public String getEmployeeSubgroupTextEng() {
        return employeeSubgroupTextEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employee_subgroup_text_eng
     *
     * @param employeeSubgroupTextEng the value for t_person_info.employee_subgroup_text_eng
     *
     * @mbg.generated
     */
    public void setEmployeeSubgroupTextEng(String employeeSubgroupTextEng) {
        this.employeeSubgroupTextEng = employeeSubgroupTextEng == null ? null : employeeSubgroupTextEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.mainland_id_card
     *
     * @return the value of t_person_info.mainland_id_card
     *
     * @mbg.generated
     */
    public String getMainlandIdCard() {
        return mainlandIdCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.mainland_id_card
     *
     * @param mainlandIdCard the value for t_person_info.mainland_id_card
     *
     * @mbg.generated
     */
    public void setMainlandIdCard(String mainlandIdCard) {
        this.mainlandIdCard = mainlandIdCard == null ? null : mainlandIdCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.foreign_id_card
     *
     * @return the value of t_person_info.foreign_id_card
     *
     * @mbg.generated
     */
    public String getForeignIdCard() {
        return foreignIdCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.foreign_id_card
     *
     * @param foreignIdCard the value for t_person_info.foreign_id_card
     *
     * @mbg.generated
     */
    public void setForeignIdCard(String foreignIdCard) {
        this.foreignIdCard = foreignIdCard == null ? null : foreignIdCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hk_mo_passport
     *
     * @return the value of t_person_info.hk_mo_passport
     *
     * @mbg.generated
     */
    public String getHkMoPassport() {
        return hkMoPassport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hk_mo_passport
     *
     * @param hkMoPassport the value for t_person_info.hk_mo_passport
     *
     * @mbg.generated
     */
    public void setHkMoPassport(String hkMoPassport) {
        this.hkMoPassport = hkMoPassport == null ? null : hkMoPassport.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hk_mo_end_expiry
     *
     * @return the value of t_person_info.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    public String getHkMoEndExpiry() {
        return hkMoEndExpiry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hk_mo_end_expiry
     *
     * @param hkMoEndExpiry the value for t_person_info.hk_mo_end_expiry
     *
     * @mbg.generated
     */
    public void setHkMoEndExpiry(String hkMoEndExpiry) {
        this.hkMoEndExpiry = hkMoEndExpiry == null ? null : hkMoEndExpiry.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employment_mode
     *
     * @return the value of t_person_info.employment_mode
     *
     * @mbg.generated
     */
    public String getEmploymentMode() {
        return employmentMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employment_mode
     *
     * @param employmentMode the value for t_person_info.employment_mode
     *
     * @mbg.generated
     */
    public void setEmploymentMode(String employmentMode) {
        this.employmentMode = employmentMode == null ? null : employmentMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employment_mode_trad
     *
     * @return the value of t_person_info.employment_mode_trad
     *
     * @mbg.generated
     */
    public String getEmploymentModeTrad() {
        return employmentModeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employment_mode_trad
     *
     * @param employmentModeTrad the value for t_person_info.employment_mode_trad
     *
     * @mbg.generated
     */
    public void setEmploymentModeTrad(String employmentModeTrad) {
        this.employmentModeTrad = employmentModeTrad == null ? null : employmentModeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.employment_mode_eng
     *
     * @return the value of t_person_info.employment_mode_eng
     *
     * @mbg.generated
     */
    public String getEmploymentModeEng() {
        return employmentModeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.employment_mode_eng
     *
     * @param employmentModeEng the value for t_person_info.employment_mode_eng
     *
     * @mbg.generated
     */
    public void setEmploymentModeEng(String employmentModeEng) {
        this.employmentModeEng = employmentModeEng == null ? null : employmentModeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hzz_generation
     *
     * @return the value of t_person_info.hzz_generation
     *
     * @mbg.generated
     */
    public String getHzzGeneration() {
        return hzzGeneration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hzz_generation
     *
     * @param hzzGeneration the value for t_person_info.hzz_generation
     *
     * @mbg.generated
     */
    public void setHzzGeneration(String hzzGeneration) {
        this.hzzGeneration = hzzGeneration == null ? null : hzzGeneration.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.hzz_sequence
     *
     * @return the value of t_person_info.hzz_sequence
     *
     * @mbg.generated
     */
    public String getHzzSequence() {
        return hzzSequence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.hzz_sequence
     *
     * @param hzzSequence the value for t_person_info.hzz_sequence
     *
     * @mbg.generated
     */
    public void setHzzSequence(String hzzSequence) {
        this.hzzSequence = hzzSequence == null ? null : hzzSequence.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.political_status
     *
     * @return the value of t_person_info.political_status
     *
     * @mbg.generated
     */
    public String getPoliticalStatus() {
        return politicalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.political_status
     *
     * @param politicalStatus the value for t_person_info.political_status
     *
     * @mbg.generated
     */
    public void setPoliticalStatus(String politicalStatus) {
        this.politicalStatus = politicalStatus == null ? null : politicalStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.political_status_trad
     *
     * @return the value of t_person_info.political_status_trad
     *
     * @mbg.generated
     */
    public String getPoliticalStatusTrad() {
        return politicalStatusTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.political_status_trad
     *
     * @param politicalStatusTrad the value for t_person_info.political_status_trad
     *
     * @mbg.generated
     */
    public void setPoliticalStatusTrad(String politicalStatusTrad) {
        this.politicalStatusTrad = politicalStatusTrad == null ? null : politicalStatusTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.political_status_eng
     *
     * @return the value of t_person_info.political_status_eng
     *
     * @mbg.generated
     */
    public String getPoliticalStatusEng() {
        return politicalStatusEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.political_status_eng
     *
     * @param politicalStatusEng the value for t_person_info.political_status_eng
     *
     * @mbg.generated
     */
    public void setPoliticalStatusEng(String politicalStatusEng) {
        this.politicalStatusEng = politicalStatusEng == null ? null : politicalStatusEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.party_joining_date
     *
     * @return the value of t_person_info.party_joining_date
     *
     * @mbg.generated
     */
    public LocalDate getPartyJoiningDate() {
        return partyJoiningDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.party_joining_date
     *
     * @param partyJoiningDate the value for t_person_info.party_joining_date
     *
     * @mbg.generated
     */
    public void setPartyJoiningDate(LocalDate partyJoiningDate) {
        this.partyJoiningDate = partyJoiningDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.specialty
     *
     * @return the value of t_person_info.specialty
     *
     * @mbg.generated
     */
    public String getSpecialty() {
        return specialty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.specialty
     *
     * @param specialty the value for t_person_info.specialty
     *
     * @mbg.generated
     */
    public void setSpecialty(String specialty) {
        this.specialty = specialty == null ? null : specialty.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.spouse_location
     *
     * @return the value of t_person_info.spouse_location
     *
     * @mbg.generated
     */
    public String getSpouseLocation() {
        return spouseLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.spouse_location
     *
     * @param spouseLocation the value for t_person_info.spouse_location
     *
     * @mbg.generated
     */
    public void setSpouseLocation(String spouseLocation) {
        this.spouseLocation = spouseLocation == null ? null : spouseLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.parents_location
     *
     * @return the value of t_person_info.parents_location
     *
     * @mbg.generated
     */
    public String getParentsLocation() {
        return parentsLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.parents_location
     *
     * @param parentsLocation the value for t_person_info.parents_location
     *
     * @mbg.generated
     */
    public void setParentsLocation(String parentsLocation) {
        this.parentsLocation = parentsLocation == null ? null : parentsLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.blood_type
     *
     * @return the value of t_person_info.blood_type
     *
     * @mbg.generated
     */
    public String getBloodType() {
        return bloodType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.blood_type
     *
     * @param bloodType the value for t_person_info.blood_type
     *
     * @mbg.generated
     */
    public void setBloodType(String bloodType) {
        this.bloodType = bloodType == null ? null : bloodType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.blood_type_trad
     *
     * @return the value of t_person_info.blood_type_trad
     *
     * @mbg.generated
     */
    public String getBloodTypeTrad() {
        return bloodTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.blood_type_trad
     *
     * @param bloodTypeTrad the value for t_person_info.blood_type_trad
     *
     * @mbg.generated
     */
    public void setBloodTypeTrad(String bloodTypeTrad) {
        this.bloodTypeTrad = bloodTypeTrad == null ? null : bloodTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.blood_type_eng
     *
     * @return the value of t_person_info.blood_type_eng
     *
     * @mbg.generated
     */
    public String getBloodTypeEng() {
        return bloodTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.blood_type_eng
     *
     * @param bloodTypeEng the value for t_person_info.blood_type_eng
     *
     * @mbg.generated
     */
    public void setBloodTypeEng(String bloodTypeEng) {
        this.bloodTypeEng = bloodTypeEng == null ? null : bloodTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.health_condition
     *
     * @return the value of t_person_info.health_condition
     *
     * @mbg.generated
     */
    public String getHealthCondition() {
        return healthCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.health_condition
     *
     * @param healthCondition the value for t_person_info.health_condition
     *
     * @mbg.generated
     */
    public void setHealthCondition(String healthCondition) {
        this.healthCondition = healthCondition == null ? null : healthCondition.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.health_condition_trad
     *
     * @return the value of t_person_info.health_condition_trad
     *
     * @mbg.generated
     */
    public String getHealthConditionTrad() {
        return healthConditionTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.health_condition_trad
     *
     * @param healthConditionTrad the value for t_person_info.health_condition_trad
     *
     * @mbg.generated
     */
    public void setHealthConditionTrad(String healthConditionTrad) {
        this.healthConditionTrad = healthConditionTrad == null ? null : healthConditionTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.health_condition_eng
     *
     * @return the value of t_person_info.health_condition_eng
     *
     * @mbg.generated
     */
    public String getHealthConditionEng() {
        return healthConditionEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.health_condition_eng
     *
     * @param healthConditionEng the value for t_person_info.health_condition_eng
     *
     * @mbg.generated
     */
    public void setHealthConditionEng(String healthConditionEng) {
        this.healthConditionEng = healthConditionEng == null ? null : healthConditionEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.interests_hobbies
     *
     * @return the value of t_person_info.interests_hobbies
     *
     * @mbg.generated
     */
    public String getInterestsHobbies() {
        return interestsHobbies;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.interests_hobbies
     *
     * @param interestsHobbies the value for t_person_info.interests_hobbies
     *
     * @mbg.generated
     */
    public void setInterestsHobbies(String interestsHobbies) {
        this.interestsHobbies = interestsHobbies == null ? null : interestsHobbies.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.archive_company
     *
     * @return the value of t_person_info.archive_company
     *
     * @mbg.generated
     */
    public String getArchiveCompany() {
        return archiveCompany;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.archive_company
     *
     * @param archiveCompany the value for t_person_info.archive_company
     *
     * @mbg.generated
     */
    public void setArchiveCompany(String archiveCompany) {
        this.archiveCompany = archiveCompany == null ? null : archiveCompany.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.res_loc
     *
     * @return the value of t_person_info.res_loc
     *
     * @mbg.generated
     */
    public String getResLoc() {
        return resLoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.res_loc
     *
     * @param resLoc the value for t_person_info.res_loc
     *
     * @mbg.generated
     */
    public void setResLoc(String resLoc) {
        this.resLoc = resLoc == null ? null : resLoc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.res_type
     *
     * @return the value of t_person_info.res_type
     *
     * @mbg.generated
     */
    public String getResType() {
        return resType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.res_type
     *
     * @param resType the value for t_person_info.res_type
     *
     * @mbg.generated
     */
    public void setResType(String resType) {
        this.resType = resType == null ? null : resType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.res_type_trad
     *
     * @return the value of t_person_info.res_type_trad
     *
     * @mbg.generated
     */
    public String getResTypeTrad() {
        return resTypeTrad;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.res_type_trad
     *
     * @param resTypeTrad the value for t_person_info.res_type_trad
     *
     * @mbg.generated
     */
    public void setResTypeTrad(String resTypeTrad) {
        this.resTypeTrad = resTypeTrad == null ? null : resTypeTrad.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.res_type_eng
     *
     * @return the value of t_person_info.res_type_eng
     *
     * @mbg.generated
     */
    public String getResTypeEng() {
        return resTypeEng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.res_type_eng
     *
     * @param resTypeEng the value for t_person_info.res_type_eng
     *
     * @mbg.generated
     */
    public void setResTypeEng(String resTypeEng) {
        this.resTypeEng = resTypeEng == null ? null : resTypeEng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.seq
     *
     * @return the value of t_person_info.seq
     *
     * @mbg.generated
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.seq
     *
     * @param seq the value for t_person_info.seq
     *
     * @mbg.generated
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.is_deleted
     *
     * @return the value of t_person_info.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.is_deleted
     *
     * @param isDeleted the value for t_person_info.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.is_sync
     *
     * @return the value of t_person_info.is_sync
     *
     * @mbg.generated
     */
    public Boolean getIsSync() {
        return isSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.is_sync
     *
     * @param isSync the value for t_person_info.is_sync
     *
     * @mbg.generated
     */
    public void setIsSync(Boolean isSync) {
        this.isSync = isSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.years_in_current_position
     *
     * @return the value of t_person_info.years_in_current_position
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentPosition() {
        return yearsInCurrentPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.years_in_current_position
     *
     * @param yearsInCurrentPosition the value for t_person_info.years_in_current_position
     *
     * @mbg.generated
     */
    public void setYearsInCurrentPosition(BigDecimal yearsInCurrentPosition) {
        this.yearsInCurrentPosition = yearsInCurrentPosition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.years_in_current_job_level
     *
     * @return the value of t_person_info.years_in_current_job_level
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentJobLevel() {
        return yearsInCurrentJobLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.years_in_current_job_level
     *
     * @param yearsInCurrentJobLevel the value for t_person_info.years_in_current_job_level
     *
     * @mbg.generated
     */
    public void setYearsInCurrentJobLevel(BigDecimal yearsInCurrentJobLevel) {
        this.yearsInCurrentJobLevel = yearsInCurrentJobLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.years_in_current_rank
     *
     * @return the value of t_person_info.years_in_current_rank
     *
     * @mbg.generated
     */
    public BigDecimal getYearsInCurrentRank() {
        return yearsInCurrentRank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.years_in_current_rank
     *
     * @param yearsInCurrentRank the value for t_person_info.years_in_current_rank
     *
     * @mbg.generated
     */
    public void setYearsInCurrentRank(BigDecimal yearsInCurrentRank) {
        this.yearsInCurrentRank = yearsInCurrentRank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.talent_inventory_placement
     *
     * @return the value of t_person_info.talent_inventory_placement
     *
     * @mbg.generated
     */
    public String getTalentInventoryPlacement() {
        return talentInventoryPlacement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.talent_inventory_placement
     *
     * @param talentInventoryPlacement the value for t_person_info.talent_inventory_placement
     *
     * @mbg.generated
     */
    public void setTalentInventoryPlacement(String talentInventoryPlacement) {
        this.talentInventoryPlacement = talentInventoryPlacement == null ? null : talentInventoryPlacement.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.appraisal_result_year1
     *
     * @return the value of t_person_info.appraisal_result_year1
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear1() {
        return appraisalResultYear1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.appraisal_result_year1
     *
     * @param appraisalResultYear1 the value for t_person_info.appraisal_result_year1
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear1(String appraisalResultYear1) {
        this.appraisalResultYear1 = appraisalResultYear1 == null ? null : appraisalResultYear1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.appraisal_result_year2
     *
     * @return the value of t_person_info.appraisal_result_year2
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear2() {
        return appraisalResultYear2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.appraisal_result_year2
     *
     * @param appraisalResultYear2 the value for t_person_info.appraisal_result_year2
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear2(String appraisalResultYear2) {
        this.appraisalResultYear2 = appraisalResultYear2 == null ? null : appraisalResultYear2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.appraisal_result_year3
     *
     * @return the value of t_person_info.appraisal_result_year3
     *
     * @mbg.generated
     */
    public String getAppraisalResultYear3() {
        return appraisalResultYear3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.appraisal_result_year3
     *
     * @param appraisalResultYear3 the value for t_person_info.appraisal_result_year3
     *
     * @mbg.generated
     */
    public void setAppraisalResultYear3(String appraisalResultYear3) {
        this.appraisalResultYear3 = appraisalResultYear3 == null ? null : appraisalResultYear3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.photo
     *
     * @return the value of t_person_info.photo
     *
     * @mbg.generated
     */
    public String getPhoto() {
        return photo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.photo
     *
     * @param photo the value for t_person_info.photo
     *
     * @mbg.generated
     */
    public void setPhoto(String photo) {
        this.photo = photo == null ? null : photo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.creation_time
     *
     * @return the value of t_person_info.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.creation_time
     *
     * @param creationTime the value for t_person_info.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.create_username
     *
     * @return the value of t_person_info.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.create_username
     *
     * @param createUsername the value for t_person_info.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.create_user_id
     *
     * @return the value of t_person_info.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.create_user_id
     *
     * @param createUserId the value for t_person_info.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.last_update_time
     *
     * @return the value of t_person_info.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.last_update_time
     *
     * @param lastUpdateTime the value for t_person_info.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.last_update_username
     *
     * @return the value of t_person_info.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.last_update_username
     *
     * @param lastUpdateUsername the value for t_person_info.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.last_update_user_id
     *
     * @return the value of t_person_info.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_person_info.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.last_update_version
     *
     * @return the value of t_person_info.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.last_update_version
     *
     * @param lastUpdateVersion the value for t_person_info.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_person_info.organization_code
     *
     * @return the value of t_person_info.organization_code
     *
     * @mbg.generated
     */
    public String getOrganizationCode() {
        return organizationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_person_info.organization_code
     *
     * @param organizationCode the value for t_person_info.organization_code
     *
     * @mbg.generated
     */
    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode == null ? null : organizationCode.trim();
    }
}