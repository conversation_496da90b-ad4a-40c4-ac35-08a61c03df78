package com.csci.hrrs.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AmbientDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public AmbientDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(String value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(String value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(String value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(String value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(String value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLike(String value) {
            addCriterion("head_id like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotLike(String value) {
            addCriterion("head_id not like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<String> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<String> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(String value1, String value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(String value1, String value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestIsNull() {
            addCriterion("category_digest is null");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestIsNotNull() {
            addCriterion("category_digest is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestEqualTo(String value) {
            addCriterion("category_digest =", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestNotEqualTo(String value) {
            addCriterion("category_digest <>", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestGreaterThan(String value) {
            addCriterion("category_digest >", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestGreaterThanOrEqualTo(String value) {
            addCriterion("category_digest >=", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestLessThan(String value) {
            addCriterion("category_digest <", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestLessThanOrEqualTo(String value) {
            addCriterion("category_digest <=", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestLike(String value) {
            addCriterion("category_digest like", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestNotLike(String value) {
            addCriterion("category_digest not like", value, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestIn(List<String> values) {
            addCriterion("category_digest in", values, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestNotIn(List<String> values) {
            addCriterion("category_digest not in", values, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestBetween(String value1, String value2) {
            addCriterion("category_digest between", value1, value2, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andCategoryDigestNotBetween(String value1, String value2) {
            addCriterion("category_digest not between", value1, value2, "categoryDigest");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andType2IsNull() {
            addCriterion("type2 is null");
            return (Criteria) this;
        }

        public Criteria andType2IsNotNull() {
            addCriterion("type2 is not null");
            return (Criteria) this;
        }

        public Criteria andType2EqualTo(String value) {
            addCriterion("type2 =", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2NotEqualTo(String value) {
            addCriterion("type2 <>", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2GreaterThan(String value) {
            addCriterion("type2 >", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2GreaterThanOrEqualTo(String value) {
            addCriterion("type2 >=", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2LessThan(String value) {
            addCriterion("type2 <", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2LessThanOrEqualTo(String value) {
            addCriterion("type2 <=", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2Like(String value) {
            addCriterion("type2 like", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2NotLike(String value) {
            addCriterion("type2 not like", value, "type2");
            return (Criteria) this;
        }

        public Criteria andType2In(List<String> values) {
            addCriterion("type2 in", values, "type2");
            return (Criteria) this;
        }

        public Criteria andType2NotIn(List<String> values) {
            addCriterion("type2 not in", values, "type2");
            return (Criteria) this;
        }

        public Criteria andType2Between(String value1, String value2) {
            addCriterion("type2 between", value1, value2, "type2");
            return (Criteria) this;
        }

        public Criteria andType2NotBetween(String value1, String value2) {
            addCriterion("type2 not between", value1, value2, "type2");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNull() {
            addCriterion("unit_code is null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNotNull() {
            addCriterion("unit_code is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeEqualTo(String value) {
            addCriterion("unit_code =", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotEqualTo(String value) {
            addCriterion("unit_code <>", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThan(String value) {
            addCriterion("unit_code >", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("unit_code >=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThan(String value) {
            addCriterion("unit_code <", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("unit_code <=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLike(String value) {
            addCriterion("unit_code like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotLike(String value) {
            addCriterion("unit_code not like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIn(List<String> values) {
            addCriterion("unit_code in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotIn(List<String> values) {
            addCriterion("unit_code not in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeBetween(String value1, String value2) {
            addCriterion("unit_code between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotBetween(String value1, String value2) {
            addCriterion("unit_code not between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andMonthValue1IsNull() {
            addCriterion("month_value_1 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue1IsNotNull() {
            addCriterion("month_value_1 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue1EqualTo(String value) {
            addCriterion("month_value_1 =", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1NotEqualTo(String value) {
            addCriterion("month_value_1 <>", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1GreaterThan(String value) {
            addCriterion("month_value_1 >", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_1 >=", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1LessThan(String value) {
            addCriterion("month_value_1 <", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1LessThanOrEqualTo(String value) {
            addCriterion("month_value_1 <=", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1Like(String value) {
            addCriterion("month_value_1 like", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1NotLike(String value) {
            addCriterion("month_value_1 not like", value, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1In(List<String> values) {
            addCriterion("month_value_1 in", values, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1NotIn(List<String> values) {
            addCriterion("month_value_1 not in", values, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1Between(String value1, String value2) {
            addCriterion("month_value_1 between", value1, value2, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue1NotBetween(String value1, String value2) {
            addCriterion("month_value_1 not between", value1, value2, "monthValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue2IsNull() {
            addCriterion("month_value_2 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue2IsNotNull() {
            addCriterion("month_value_2 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue2EqualTo(String value) {
            addCriterion("month_value_2 =", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2NotEqualTo(String value) {
            addCriterion("month_value_2 <>", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2GreaterThan(String value) {
            addCriterion("month_value_2 >", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_2 >=", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2LessThan(String value) {
            addCriterion("month_value_2 <", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2LessThanOrEqualTo(String value) {
            addCriterion("month_value_2 <=", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2Like(String value) {
            addCriterion("month_value_2 like", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2NotLike(String value) {
            addCriterion("month_value_2 not like", value, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2In(List<String> values) {
            addCriterion("month_value_2 in", values, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2NotIn(List<String> values) {
            addCriterion("month_value_2 not in", values, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2Between(String value1, String value2) {
            addCriterion("month_value_2 between", value1, value2, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue2NotBetween(String value1, String value2) {
            addCriterion("month_value_2 not between", value1, value2, "monthValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue3IsNull() {
            addCriterion("month_value_3 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue3IsNotNull() {
            addCriterion("month_value_3 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue3EqualTo(String value) {
            addCriterion("month_value_3 =", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3NotEqualTo(String value) {
            addCriterion("month_value_3 <>", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3GreaterThan(String value) {
            addCriterion("month_value_3 >", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_3 >=", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3LessThan(String value) {
            addCriterion("month_value_3 <", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3LessThanOrEqualTo(String value) {
            addCriterion("month_value_3 <=", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3Like(String value) {
            addCriterion("month_value_3 like", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3NotLike(String value) {
            addCriterion("month_value_3 not like", value, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3In(List<String> values) {
            addCriterion("month_value_3 in", values, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3NotIn(List<String> values) {
            addCriterion("month_value_3 not in", values, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3Between(String value1, String value2) {
            addCriterion("month_value_3 between", value1, value2, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue3NotBetween(String value1, String value2) {
            addCriterion("month_value_3 not between", value1, value2, "monthValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1IsNull() {
            addCriterion("season_value_1 is null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1IsNotNull() {
            addCriterion("season_value_1 is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1EqualTo(String value) {
            addCriterion("season_value_1 =", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1NotEqualTo(String value) {
            addCriterion("season_value_1 <>", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1GreaterThan(String value) {
            addCriterion("season_value_1 >", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1GreaterThanOrEqualTo(String value) {
            addCriterion("season_value_1 >=", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1LessThan(String value) {
            addCriterion("season_value_1 <", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1LessThanOrEqualTo(String value) {
            addCriterion("season_value_1 <=", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1Like(String value) {
            addCriterion("season_value_1 like", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1NotLike(String value) {
            addCriterion("season_value_1 not like", value, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1In(List<String> values) {
            addCriterion("season_value_1 in", values, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1NotIn(List<String> values) {
            addCriterion("season_value_1 not in", values, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1Between(String value1, String value2) {
            addCriterion("season_value_1 between", value1, value2, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andSeasonValue1NotBetween(String value1, String value2) {
            addCriterion("season_value_1 not between", value1, value2, "seasonValue1");
            return (Criteria) this;
        }

        public Criteria andMonthValue4IsNull() {
            addCriterion("month_value_4 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue4IsNotNull() {
            addCriterion("month_value_4 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue4EqualTo(String value) {
            addCriterion("month_value_4 =", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4NotEqualTo(String value) {
            addCriterion("month_value_4 <>", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4GreaterThan(String value) {
            addCriterion("month_value_4 >", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_4 >=", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4LessThan(String value) {
            addCriterion("month_value_4 <", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4LessThanOrEqualTo(String value) {
            addCriterion("month_value_4 <=", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4Like(String value) {
            addCriterion("month_value_4 like", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4NotLike(String value) {
            addCriterion("month_value_4 not like", value, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4In(List<String> values) {
            addCriterion("month_value_4 in", values, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4NotIn(List<String> values) {
            addCriterion("month_value_4 not in", values, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4Between(String value1, String value2) {
            addCriterion("month_value_4 between", value1, value2, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue4NotBetween(String value1, String value2) {
            addCriterion("month_value_4 not between", value1, value2, "monthValue4");
            return (Criteria) this;
        }

        public Criteria andMonthValue5IsNull() {
            addCriterion("month_value_5 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue5IsNotNull() {
            addCriterion("month_value_5 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue5EqualTo(String value) {
            addCriterion("month_value_5 =", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5NotEqualTo(String value) {
            addCriterion("month_value_5 <>", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5GreaterThan(String value) {
            addCriterion("month_value_5 >", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_5 >=", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5LessThan(String value) {
            addCriterion("month_value_5 <", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5LessThanOrEqualTo(String value) {
            addCriterion("month_value_5 <=", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5Like(String value) {
            addCriterion("month_value_5 like", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5NotLike(String value) {
            addCriterion("month_value_5 not like", value, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5In(List<String> values) {
            addCriterion("month_value_5 in", values, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5NotIn(List<String> values) {
            addCriterion("month_value_5 not in", values, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5Between(String value1, String value2) {
            addCriterion("month_value_5 between", value1, value2, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue5NotBetween(String value1, String value2) {
            addCriterion("month_value_5 not between", value1, value2, "monthValue5");
            return (Criteria) this;
        }

        public Criteria andMonthValue6IsNull() {
            addCriterion("month_value_6 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue6IsNotNull() {
            addCriterion("month_value_6 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue6EqualTo(String value) {
            addCriterion("month_value_6 =", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6NotEqualTo(String value) {
            addCriterion("month_value_6 <>", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6GreaterThan(String value) {
            addCriterion("month_value_6 >", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_6 >=", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6LessThan(String value) {
            addCriterion("month_value_6 <", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6LessThanOrEqualTo(String value) {
            addCriterion("month_value_6 <=", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6Like(String value) {
            addCriterion("month_value_6 like", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6NotLike(String value) {
            addCriterion("month_value_6 not like", value, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6In(List<String> values) {
            addCriterion("month_value_6 in", values, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6NotIn(List<String> values) {
            addCriterion("month_value_6 not in", values, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6Between(String value1, String value2) {
            addCriterion("month_value_6 between", value1, value2, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andMonthValue6NotBetween(String value1, String value2) {
            addCriterion("month_value_6 not between", value1, value2, "monthValue6");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2IsNull() {
            addCriterion("season_value_2 is null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2IsNotNull() {
            addCriterion("season_value_2 is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2EqualTo(String value) {
            addCriterion("season_value_2 =", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2NotEqualTo(String value) {
            addCriterion("season_value_2 <>", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2GreaterThan(String value) {
            addCriterion("season_value_2 >", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2GreaterThanOrEqualTo(String value) {
            addCriterion("season_value_2 >=", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2LessThan(String value) {
            addCriterion("season_value_2 <", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2LessThanOrEqualTo(String value) {
            addCriterion("season_value_2 <=", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2Like(String value) {
            addCriterion("season_value_2 like", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2NotLike(String value) {
            addCriterion("season_value_2 not like", value, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2In(List<String> values) {
            addCriterion("season_value_2 in", values, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2NotIn(List<String> values) {
            addCriterion("season_value_2 not in", values, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2Between(String value1, String value2) {
            addCriterion("season_value_2 between", value1, value2, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andSeasonValue2NotBetween(String value1, String value2) {
            addCriterion("season_value_2 not between", value1, value2, "seasonValue2");
            return (Criteria) this;
        }

        public Criteria andMonthValue7IsNull() {
            addCriterion("month_value_7 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue7IsNotNull() {
            addCriterion("month_value_7 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue7EqualTo(String value) {
            addCriterion("month_value_7 =", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7NotEqualTo(String value) {
            addCriterion("month_value_7 <>", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7GreaterThan(String value) {
            addCriterion("month_value_7 >", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_7 >=", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7LessThan(String value) {
            addCriterion("month_value_7 <", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7LessThanOrEqualTo(String value) {
            addCriterion("month_value_7 <=", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7Like(String value) {
            addCriterion("month_value_7 like", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7NotLike(String value) {
            addCriterion("month_value_7 not like", value, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7In(List<String> values) {
            addCriterion("month_value_7 in", values, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7NotIn(List<String> values) {
            addCriterion("month_value_7 not in", values, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7Between(String value1, String value2) {
            addCriterion("month_value_7 between", value1, value2, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue7NotBetween(String value1, String value2) {
            addCriterion("month_value_7 not between", value1, value2, "monthValue7");
            return (Criteria) this;
        }

        public Criteria andMonthValue8IsNull() {
            addCriterion("month_value_8 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue8IsNotNull() {
            addCriterion("month_value_8 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue8EqualTo(String value) {
            addCriterion("month_value_8 =", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8NotEqualTo(String value) {
            addCriterion("month_value_8 <>", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8GreaterThan(String value) {
            addCriterion("month_value_8 >", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_8 >=", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8LessThan(String value) {
            addCriterion("month_value_8 <", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8LessThanOrEqualTo(String value) {
            addCriterion("month_value_8 <=", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8Like(String value) {
            addCriterion("month_value_8 like", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8NotLike(String value) {
            addCriterion("month_value_8 not like", value, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8In(List<String> values) {
            addCriterion("month_value_8 in", values, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8NotIn(List<String> values) {
            addCriterion("month_value_8 not in", values, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8Between(String value1, String value2) {
            addCriterion("month_value_8 between", value1, value2, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue8NotBetween(String value1, String value2) {
            addCriterion("month_value_8 not between", value1, value2, "monthValue8");
            return (Criteria) this;
        }

        public Criteria andMonthValue9IsNull() {
            addCriterion("month_value_9 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue9IsNotNull() {
            addCriterion("month_value_9 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue9EqualTo(String value) {
            addCriterion("month_value_9 =", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9NotEqualTo(String value) {
            addCriterion("month_value_9 <>", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9GreaterThan(String value) {
            addCriterion("month_value_9 >", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_9 >=", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9LessThan(String value) {
            addCriterion("month_value_9 <", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9LessThanOrEqualTo(String value) {
            addCriterion("month_value_9 <=", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9Like(String value) {
            addCriterion("month_value_9 like", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9NotLike(String value) {
            addCriterion("month_value_9 not like", value, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9In(List<String> values) {
            addCriterion("month_value_9 in", values, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9NotIn(List<String> values) {
            addCriterion("month_value_9 not in", values, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9Between(String value1, String value2) {
            addCriterion("month_value_9 between", value1, value2, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andMonthValue9NotBetween(String value1, String value2) {
            addCriterion("month_value_9 not between", value1, value2, "monthValue9");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3IsNull() {
            addCriterion("season_value_3 is null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3IsNotNull() {
            addCriterion("season_value_3 is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3EqualTo(String value) {
            addCriterion("season_value_3 =", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3NotEqualTo(String value) {
            addCriterion("season_value_3 <>", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3GreaterThan(String value) {
            addCriterion("season_value_3 >", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3GreaterThanOrEqualTo(String value) {
            addCriterion("season_value_3 >=", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3LessThan(String value) {
            addCriterion("season_value_3 <", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3LessThanOrEqualTo(String value) {
            addCriterion("season_value_3 <=", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3Like(String value) {
            addCriterion("season_value_3 like", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3NotLike(String value) {
            addCriterion("season_value_3 not like", value, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3In(List<String> values) {
            addCriterion("season_value_3 in", values, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3NotIn(List<String> values) {
            addCriterion("season_value_3 not in", values, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3Between(String value1, String value2) {
            addCriterion("season_value_3 between", value1, value2, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andSeasonValue3NotBetween(String value1, String value2) {
            addCriterion("season_value_3 not between", value1, value2, "seasonValue3");
            return (Criteria) this;
        }

        public Criteria andMonthValue10IsNull() {
            addCriterion("month_value_10 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue10IsNotNull() {
            addCriterion("month_value_10 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue10EqualTo(String value) {
            addCriterion("month_value_10 =", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10NotEqualTo(String value) {
            addCriterion("month_value_10 <>", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10GreaterThan(String value) {
            addCriterion("month_value_10 >", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_10 >=", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10LessThan(String value) {
            addCriterion("month_value_10 <", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10LessThanOrEqualTo(String value) {
            addCriterion("month_value_10 <=", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10Like(String value) {
            addCriterion("month_value_10 like", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10NotLike(String value) {
            addCriterion("month_value_10 not like", value, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10In(List<String> values) {
            addCriterion("month_value_10 in", values, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10NotIn(List<String> values) {
            addCriterion("month_value_10 not in", values, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10Between(String value1, String value2) {
            addCriterion("month_value_10 between", value1, value2, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue10NotBetween(String value1, String value2) {
            addCriterion("month_value_10 not between", value1, value2, "monthValue10");
            return (Criteria) this;
        }

        public Criteria andMonthValue11IsNull() {
            addCriterion("month_value_11 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue11IsNotNull() {
            addCriterion("month_value_11 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue11EqualTo(String value) {
            addCriterion("month_value_11 =", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11NotEqualTo(String value) {
            addCriterion("month_value_11 <>", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11GreaterThan(String value) {
            addCriterion("month_value_11 >", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_11 >=", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11LessThan(String value) {
            addCriterion("month_value_11 <", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11LessThanOrEqualTo(String value) {
            addCriterion("month_value_11 <=", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11Like(String value) {
            addCriterion("month_value_11 like", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11NotLike(String value) {
            addCriterion("month_value_11 not like", value, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11In(List<String> values) {
            addCriterion("month_value_11 in", values, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11NotIn(List<String> values) {
            addCriterion("month_value_11 not in", values, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11Between(String value1, String value2) {
            addCriterion("month_value_11 between", value1, value2, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue11NotBetween(String value1, String value2) {
            addCriterion("month_value_11 not between", value1, value2, "monthValue11");
            return (Criteria) this;
        }

        public Criteria andMonthValue12IsNull() {
            addCriterion("month_value_12 is null");
            return (Criteria) this;
        }

        public Criteria andMonthValue12IsNotNull() {
            addCriterion("month_value_12 is not null");
            return (Criteria) this;
        }

        public Criteria andMonthValue12EqualTo(String value) {
            addCriterion("month_value_12 =", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12NotEqualTo(String value) {
            addCriterion("month_value_12 <>", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12GreaterThan(String value) {
            addCriterion("month_value_12 >", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12GreaterThanOrEqualTo(String value) {
            addCriterion("month_value_12 >=", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12LessThan(String value) {
            addCriterion("month_value_12 <", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12LessThanOrEqualTo(String value) {
            addCriterion("month_value_12 <=", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12Like(String value) {
            addCriterion("month_value_12 like", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12NotLike(String value) {
            addCriterion("month_value_12 not like", value, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12In(List<String> values) {
            addCriterion("month_value_12 in", values, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12NotIn(List<String> values) {
            addCriterion("month_value_12 not in", values, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12Between(String value1, String value2) {
            addCriterion("month_value_12 between", value1, value2, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andMonthValue12NotBetween(String value1, String value2) {
            addCriterion("month_value_12 not between", value1, value2, "monthValue12");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4IsNull() {
            addCriterion("season_value_4 is null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4IsNotNull() {
            addCriterion("season_value_4 is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4EqualTo(String value) {
            addCriterion("season_value_4 =", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4NotEqualTo(String value) {
            addCriterion("season_value_4 <>", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4GreaterThan(String value) {
            addCriterion("season_value_4 >", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4GreaterThanOrEqualTo(String value) {
            addCriterion("season_value_4 >=", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4LessThan(String value) {
            addCriterion("season_value_4 <", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4LessThanOrEqualTo(String value) {
            addCriterion("season_value_4 <=", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4Like(String value) {
            addCriterion("season_value_4 like", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4NotLike(String value) {
            addCriterion("season_value_4 not like", value, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4In(List<String> values) {
            addCriterion("season_value_4 in", values, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4NotIn(List<String> values) {
            addCriterion("season_value_4 not in", values, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4Between(String value1, String value2) {
            addCriterion("season_value_4 between", value1, value2, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andSeasonValue4NotBetween(String value1, String value2) {
            addCriterion("season_value_4 not between", value1, value2, "seasonValue4");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueIsNull() {
            addCriterion("year_total_value is null");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueIsNotNull() {
            addCriterion("year_total_value is not null");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueEqualTo(String value) {
            addCriterion("year_total_value =", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueNotEqualTo(String value) {
            addCriterion("year_total_value <>", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueGreaterThan(String value) {
            addCriterion("year_total_value >", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueGreaterThanOrEqualTo(String value) {
            addCriterion("year_total_value >=", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueLessThan(String value) {
            addCriterion("year_total_value <", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueLessThanOrEqualTo(String value) {
            addCriterion("year_total_value <=", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueLike(String value) {
            addCriterion("year_total_value like", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueNotLike(String value) {
            addCriterion("year_total_value not like", value, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueIn(List<String> values) {
            addCriterion("year_total_value in", values, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueNotIn(List<String> values) {
            addCriterion("year_total_value not in", values, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueBetween(String value1, String value2) {
            addCriterion("year_total_value between", value1, value2, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andYearTotalValueNotBetween(String value1, String value2) {
            addCriterion("year_total_value not between", value1, value2, "yearTotalValue");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ambient_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}