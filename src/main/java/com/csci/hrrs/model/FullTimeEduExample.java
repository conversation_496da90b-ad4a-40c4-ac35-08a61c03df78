package com.csci.hrrs.model;

import java.util.ArrayList;
import java.util.List;

public class FullTimeEduExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public FullTimeEduExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPernrIsNull() {
            addCriterion("PERNR is null");
            return (Criteria) this;
        }

        public Criteria andPernrIsNotNull() {
            addCriterion("PERNR is not null");
            return (Criteria) this;
        }

        public Criteria andPernrEqualTo(String value) {
            addCriterion("PERNR =", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotEqualTo(String value) {
            addCriterion("PERNR <>", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThan(String value) {
            addCriterion("PERNR >", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThanOrEqualTo(String value) {
            addCriterion("PERNR >=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThan(String value) {
            addCriterion("PERNR <", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThanOrEqualTo(String value) {
            addCriterion("PERNR <=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLike(String value) {
            addCriterion("PERNR like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotLike(String value) {
            addCriterion("PERNR not like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrIn(List<String> values) {
            addCriterion("PERNR in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotIn(List<String> values) {
            addCriterion("PERNR not in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrBetween(String value1, String value2) {
            addCriterion("PERNR between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotBetween(String value1, String value2) {
            addCriterion("PERNR not between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andBegdaIsNull() {
            addCriterion("BEGDA is null");
            return (Criteria) this;
        }

        public Criteria andBegdaIsNotNull() {
            addCriterion("BEGDA is not null");
            return (Criteria) this;
        }

        public Criteria andBegdaEqualTo(String value) {
            addCriterion("BEGDA =", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotEqualTo(String value) {
            addCriterion("BEGDA <>", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaGreaterThan(String value) {
            addCriterion("BEGDA >", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaGreaterThanOrEqualTo(String value) {
            addCriterion("BEGDA >=", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLessThan(String value) {
            addCriterion("BEGDA <", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLessThanOrEqualTo(String value) {
            addCriterion("BEGDA <=", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaLike(String value) {
            addCriterion("BEGDA like", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotLike(String value) {
            addCriterion("BEGDA not like", value, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaIn(List<String> values) {
            addCriterion("BEGDA in", values, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotIn(List<String> values) {
            addCriterion("BEGDA not in", values, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaBetween(String value1, String value2) {
            addCriterion("BEGDA between", value1, value2, "begda");
            return (Criteria) this;
        }

        public Criteria andBegdaNotBetween(String value1, String value2) {
            addCriterion("BEGDA not between", value1, value2, "begda");
            return (Criteria) this;
        }

        public Criteria andEnddaIsNull() {
            addCriterion("ENDDA is null");
            return (Criteria) this;
        }

        public Criteria andEnddaIsNotNull() {
            addCriterion("ENDDA is not null");
            return (Criteria) this;
        }

        public Criteria andEnddaEqualTo(String value) {
            addCriterion("ENDDA =", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotEqualTo(String value) {
            addCriterion("ENDDA <>", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaGreaterThan(String value) {
            addCriterion("ENDDA >", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaGreaterThanOrEqualTo(String value) {
            addCriterion("ENDDA >=", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLessThan(String value) {
            addCriterion("ENDDA <", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLessThanOrEqualTo(String value) {
            addCriterion("ENDDA <=", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaLike(String value) {
            addCriterion("ENDDA like", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotLike(String value) {
            addCriterion("ENDDA not like", value, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaIn(List<String> values) {
            addCriterion("ENDDA in", values, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotIn(List<String> values) {
            addCriterion("ENDDA not in", values, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaBetween(String value1, String value2) {
            addCriterion("ENDDA between", value1, value2, "endda");
            return (Criteria) this;
        }

        public Criteria andEnddaNotBetween(String value1, String value2) {
            addCriterion("ENDDA not between", value1, value2, "endda");
            return (Criteria) this;
        }

        public Criteria andSchoolIsNull() {
            addCriterion("School is null");
            return (Criteria) this;
        }

        public Criteria andSchoolIsNotNull() {
            addCriterion("School is not null");
            return (Criteria) this;
        }

        public Criteria andSchoolEqualTo(String value) {
            addCriterion("School =", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolNotEqualTo(String value) {
            addCriterion("School <>", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolGreaterThan(String value) {
            addCriterion("School >", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolGreaterThanOrEqualTo(String value) {
            addCriterion("School >=", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolLessThan(String value) {
            addCriterion("School <", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolLessThanOrEqualTo(String value) {
            addCriterion("School <=", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolLike(String value) {
            addCriterion("School like", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolNotLike(String value) {
            addCriterion("School not like", value, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolIn(List<String> values) {
            addCriterion("School in", values, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolNotIn(List<String> values) {
            addCriterion("School not in", values, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolBetween(String value1, String value2) {
            addCriterion("School between", value1, value2, "school");
            return (Criteria) this;
        }

        public Criteria andSchoolNotBetween(String value1, String value2) {
            addCriterion("School not between", value1, value2, "school");
            return (Criteria) this;
        }

        public Criteria andMajorIsNull() {
            addCriterion("Major is null");
            return (Criteria) this;
        }

        public Criteria andMajorIsNotNull() {
            addCriterion("Major is not null");
            return (Criteria) this;
        }

        public Criteria andMajorEqualTo(String value) {
            addCriterion("Major =", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotEqualTo(String value) {
            addCriterion("Major <>", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorGreaterThan(String value) {
            addCriterion("Major >", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorGreaterThanOrEqualTo(String value) {
            addCriterion("Major >=", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLessThan(String value) {
            addCriterion("Major <", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLessThanOrEqualTo(String value) {
            addCriterion("Major <=", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLike(String value) {
            addCriterion("Major like", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotLike(String value) {
            addCriterion("Major not like", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorIn(List<String> values) {
            addCriterion("Major in", values, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotIn(List<String> values) {
            addCriterion("Major not in", values, "major");
            return (Criteria) this;
        }

        public Criteria andMajorBetween(String value1, String value2) {
            addCriterion("Major between", value1, value2, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotBetween(String value1, String value2) {
            addCriterion("Major not between", value1, value2, "major");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorIsNull() {
            addCriterion("SchoolMajor is null");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorIsNotNull() {
            addCriterion("SchoolMajor is not null");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEqualTo(String value) {
            addCriterion("SchoolMajor =", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorNotEqualTo(String value) {
            addCriterion("SchoolMajor <>", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorGreaterThan(String value) {
            addCriterion("SchoolMajor >", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorGreaterThanOrEqualTo(String value) {
            addCriterion("SchoolMajor >=", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorLessThan(String value) {
            addCriterion("SchoolMajor <", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorLessThanOrEqualTo(String value) {
            addCriterion("SchoolMajor <=", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorLike(String value) {
            addCriterion("SchoolMajor like", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorNotLike(String value) {
            addCriterion("SchoolMajor not like", value, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorIn(List<String> values) {
            addCriterion("SchoolMajor in", values, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorNotIn(List<String> values) {
            addCriterion("SchoolMajor not in", values, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorBetween(String value1, String value2) {
            addCriterion("SchoolMajor between", value1, value2, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorNotBetween(String value1, String value2) {
            addCriterion("SchoolMajor not between", value1, value2, "schoolmajor");
            return (Criteria) this;
        }

        public Criteria andEduIsNull() {
            addCriterion("edu is null");
            return (Criteria) this;
        }

        public Criteria andEduIsNotNull() {
            addCriterion("edu is not null");
            return (Criteria) this;
        }

        public Criteria andEduEqualTo(String value) {
            addCriterion("edu =", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotEqualTo(String value) {
            addCriterion("edu <>", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduGreaterThan(String value) {
            addCriterion("edu >", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduGreaterThanOrEqualTo(String value) {
            addCriterion("edu >=", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLessThan(String value) {
            addCriterion("edu <", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLessThanOrEqualTo(String value) {
            addCriterion("edu <=", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduLike(String value) {
            addCriterion("edu like", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotLike(String value) {
            addCriterion("edu not like", value, "edu");
            return (Criteria) this;
        }

        public Criteria andEduIn(List<String> values) {
            addCriterion("edu in", values, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotIn(List<String> values) {
            addCriterion("edu not in", values, "edu");
            return (Criteria) this;
        }

        public Criteria andEduBetween(String value1, String value2) {
            addCriterion("edu between", value1, value2, "edu");
            return (Criteria) this;
        }

        public Criteria andEduNotBetween(String value1, String value2) {
            addCriterion("edu not between", value1, value2, "edu");
            return (Criteria) this;
        }

        public Criteria andDegreeIsNull() {
            addCriterion("degree is null");
            return (Criteria) this;
        }

        public Criteria andDegreeIsNotNull() {
            addCriterion("degree is not null");
            return (Criteria) this;
        }

        public Criteria andDegreeEqualTo(String value) {
            addCriterion("degree =", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeNotEqualTo(String value) {
            addCriterion("degree <>", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeGreaterThan(String value) {
            addCriterion("degree >", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeGreaterThanOrEqualTo(String value) {
            addCriterion("degree >=", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeLessThan(String value) {
            addCriterion("degree <", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeLessThanOrEqualTo(String value) {
            addCriterion("degree <=", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeLike(String value) {
            addCriterion("degree like", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeNotLike(String value) {
            addCriterion("degree not like", value, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeIn(List<String> values) {
            addCriterion("degree in", values, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeNotIn(List<String> values) {
            addCriterion("degree not in", values, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeBetween(String value1, String value2) {
            addCriterion("degree between", value1, value2, "degree");
            return (Criteria) this;
        }

        public Criteria andDegreeNotBetween(String value1, String value2) {
            addCriterion("degree not between", value1, value2, "degree");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduIsNull() {
            addCriterion("is_highest_edu is null");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduIsNotNull() {
            addCriterion("is_highest_edu is not null");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduEqualTo(String value) {
            addCriterion("is_highest_edu =", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduNotEqualTo(String value) {
            addCriterion("is_highest_edu <>", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduGreaterThan(String value) {
            addCriterion("is_highest_edu >", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduGreaterThanOrEqualTo(String value) {
            addCriterion("is_highest_edu >=", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduLessThan(String value) {
            addCriterion("is_highest_edu <", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduLessThanOrEqualTo(String value) {
            addCriterion("is_highest_edu <=", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduLike(String value) {
            addCriterion("is_highest_edu like", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduNotLike(String value) {
            addCriterion("is_highest_edu not like", value, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduIn(List<String> values) {
            addCriterion("is_highest_edu in", values, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduNotIn(List<String> values) {
            addCriterion("is_highest_edu not in", values, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduBetween(String value1, String value2) {
            addCriterion("is_highest_edu between", value1, value2, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andIsHighestEduNotBetween(String value1, String value2) {
            addCriterion("is_highest_edu not between", value1, value2, "isHighestEdu");
            return (Criteria) this;
        }

        public Criteria andEduTypeIsNull() {
            addCriterion("edu_type is null");
            return (Criteria) this;
        }

        public Criteria andEduTypeIsNotNull() {
            addCriterion("edu_type is not null");
            return (Criteria) this;
        }

        public Criteria andEduTypeEqualTo(String value) {
            addCriterion("edu_type =", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeNotEqualTo(String value) {
            addCriterion("edu_type <>", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeGreaterThan(String value) {
            addCriterion("edu_type >", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeGreaterThanOrEqualTo(String value) {
            addCriterion("edu_type >=", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeLessThan(String value) {
            addCriterion("edu_type <", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeLessThanOrEqualTo(String value) {
            addCriterion("edu_type <=", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeLike(String value) {
            addCriterion("edu_type like", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeNotLike(String value) {
            addCriterion("edu_type not like", value, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeIn(List<String> values) {
            addCriterion("edu_type in", values, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeNotIn(List<String> values) {
            addCriterion("edu_type not in", values, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeBetween(String value1, String value2) {
            addCriterion("edu_type between", value1, value2, "eduType");
            return (Criteria) this;
        }

        public Criteria andEduTypeNotBetween(String value1, String value2) {
            addCriterion("edu_type not between", value1, value2, "eduType");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduIsNull() {
            addCriterion("SchoolMajor_edu is null");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduIsNotNull() {
            addCriterion("SchoolMajor_edu is not null");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduEqualTo(String value) {
            addCriterion("SchoolMajor_edu =", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduNotEqualTo(String value) {
            addCriterion("SchoolMajor_edu <>", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduGreaterThan(String value) {
            addCriterion("SchoolMajor_edu >", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduGreaterThanOrEqualTo(String value) {
            addCriterion("SchoolMajor_edu >=", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduLessThan(String value) {
            addCriterion("SchoolMajor_edu <", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduLessThanOrEqualTo(String value) {
            addCriterion("SchoolMajor_edu <=", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduLike(String value) {
            addCriterion("SchoolMajor_edu like", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduNotLike(String value) {
            addCriterion("SchoolMajor_edu not like", value, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduIn(List<String> values) {
            addCriterion("SchoolMajor_edu in", values, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduNotIn(List<String> values) {
            addCriterion("SchoolMajor_edu not in", values, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduBetween(String value1, String value2) {
            addCriterion("SchoolMajor_edu between", value1, value2, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andSchoolmajorEduNotBetween(String value1, String value2) {
            addCriterion("SchoolMajor_edu not between", value1, value2, "schoolmajorEdu");
            return (Criteria) this;
        }

        public Criteria andZzZz1IsNull() {
            addCriterion("ZZ_ZZ1 is null");
            return (Criteria) this;
        }

        public Criteria andZzZz1IsNotNull() {
            addCriterion("ZZ_ZZ1 is not null");
            return (Criteria) this;
        }

        public Criteria andZzZz1EqualTo(String value) {
            addCriterion("ZZ_ZZ1 =", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1NotEqualTo(String value) {
            addCriterion("ZZ_ZZ1 <>", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1GreaterThan(String value) {
            addCriterion("ZZ_ZZ1 >", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1GreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_ZZ1 >=", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1LessThan(String value) {
            addCriterion("ZZ_ZZ1 <", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1LessThanOrEqualTo(String value) {
            addCriterion("ZZ_ZZ1 <=", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1Like(String value) {
            addCriterion("ZZ_ZZ1 like", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1NotLike(String value) {
            addCriterion("ZZ_ZZ1 not like", value, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1In(List<String> values) {
            addCriterion("ZZ_ZZ1 in", values, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1NotIn(List<String> values) {
            addCriterion("ZZ_ZZ1 not in", values, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1Between(String value1, String value2) {
            addCriterion("ZZ_ZZ1 between", value1, value2, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz1NotBetween(String value1, String value2) {
            addCriterion("ZZ_ZZ1 not between", value1, value2, "zzZz1");
            return (Criteria) this;
        }

        public Criteria andZzZz2IsNull() {
            addCriterion("ZZ_ZZ2 is null");
            return (Criteria) this;
        }

        public Criteria andZzZz2IsNotNull() {
            addCriterion("ZZ_ZZ2 is not null");
            return (Criteria) this;
        }

        public Criteria andZzZz2EqualTo(String value) {
            addCriterion("ZZ_ZZ2 =", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2NotEqualTo(String value) {
            addCriterion("ZZ_ZZ2 <>", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2GreaterThan(String value) {
            addCriterion("ZZ_ZZ2 >", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2GreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_ZZ2 >=", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2LessThan(String value) {
            addCriterion("ZZ_ZZ2 <", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2LessThanOrEqualTo(String value) {
            addCriterion("ZZ_ZZ2 <=", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2Like(String value) {
            addCriterion("ZZ_ZZ2 like", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2NotLike(String value) {
            addCriterion("ZZ_ZZ2 not like", value, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2In(List<String> values) {
            addCriterion("ZZ_ZZ2 in", values, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2NotIn(List<String> values) {
            addCriterion("ZZ_ZZ2 not in", values, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2Between(String value1, String value2) {
            addCriterion("ZZ_ZZ2 between", value1, value2, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzZz2NotBetween(String value1, String value2) {
            addCriterion("ZZ_ZZ2 not between", value1, value2, "zzZz2");
            return (Criteria) this;
        }

        public Criteria andZzDyxlIsNull() {
            addCriterion("ZZ_DYXL is null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlIsNotNull() {
            addCriterion("ZZ_DYXL is not null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlEqualTo(String value) {
            addCriterion("ZZ_DYXL =", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlNotEqualTo(String value) {
            addCriterion("ZZ_DYXL <>", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlGreaterThan(String value) {
            addCriterion("ZZ_DYXL >", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXL >=", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlLessThan(String value) {
            addCriterion("ZZ_DYXL <", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlLessThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXL <=", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlLike(String value) {
            addCriterion("ZZ_DYXL like", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlNotLike(String value) {
            addCriterion("ZZ_DYXL not like", value, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlIn(List<String> values) {
            addCriterion("ZZ_DYXL in", values, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlNotIn(List<String> values) {
            addCriterion("ZZ_DYXL not in", values, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlBetween(String value1, String value2) {
            addCriterion("ZZ_DYXL between", value1, value2, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlNotBetween(String value1, String value2) {
            addCriterion("ZZ_DYXL not between", value1, value2, "zzDyxl");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxIsNull() {
            addCriterion("ZZ_DYXLBYYX is null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxIsNotNull() {
            addCriterion("ZZ_DYXLBYYX is not null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxEqualTo(String value) {
            addCriterion("ZZ_DYXLBYYX =", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxNotEqualTo(String value) {
            addCriterion("ZZ_DYXLBYYX <>", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxGreaterThan(String value) {
            addCriterion("ZZ_DYXLBYYX >", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXLBYYX >=", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxLessThan(String value) {
            addCriterion("ZZ_DYXLBYYX <", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxLessThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXLBYYX <=", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxLike(String value) {
            addCriterion("ZZ_DYXLBYYX like", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxNotLike(String value) {
            addCriterion("ZZ_DYXLBYYX not like", value, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxIn(List<String> values) {
            addCriterion("ZZ_DYXLBYYX in", values, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxNotIn(List<String> values) {
            addCriterion("ZZ_DYXLBYYX not in", values, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxBetween(String value1, String value2) {
            addCriterion("ZZ_DYXLBYYX between", value1, value2, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlbyyxNotBetween(String value1, String value2) {
            addCriterion("ZZ_DYXLBYYX not between", value1, value2, "zzDyxlbyyx");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyIsNull() {
            addCriterion("ZZ_DYXLZY is null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyIsNotNull() {
            addCriterion("ZZ_DYXLZY is not null");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyEqualTo(String value) {
            addCriterion("ZZ_DYXLZY =", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyNotEqualTo(String value) {
            addCriterion("ZZ_DYXLZY <>", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyGreaterThan(String value) {
            addCriterion("ZZ_DYXLZY >", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyGreaterThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXLZY >=", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyLessThan(String value) {
            addCriterion("ZZ_DYXLZY <", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyLessThanOrEqualTo(String value) {
            addCriterion("ZZ_DYXLZY <=", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyLike(String value) {
            addCriterion("ZZ_DYXLZY like", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyNotLike(String value) {
            addCriterion("ZZ_DYXLZY not like", value, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyIn(List<String> values) {
            addCriterion("ZZ_DYXLZY in", values, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyNotIn(List<String> values) {
            addCriterion("ZZ_DYXLZY not in", values, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyBetween(String value1, String value2) {
            addCriterion("ZZ_DYXLZY between", value1, value2, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andZzDyxlzyNotBetween(String value1, String value2) {
            addCriterion("ZZ_DYXLZY not between", value1, value2, "zzDyxlzy");
            return (Criteria) this;
        }

        public Criteria andSchooltypeIsNull() {
            addCriterion("SchoolType is null");
            return (Criteria) this;
        }

        public Criteria andSchooltypeIsNotNull() {
            addCriterion("SchoolType is not null");
            return (Criteria) this;
        }

        public Criteria andSchooltypeEqualTo(String value) {
            addCriterion("SchoolType =", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeNotEqualTo(String value) {
            addCriterion("SchoolType <>", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeGreaterThan(String value) {
            addCriterion("SchoolType >", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeGreaterThanOrEqualTo(String value) {
            addCriterion("SchoolType >=", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeLessThan(String value) {
            addCriterion("SchoolType <", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeLessThanOrEqualTo(String value) {
            addCriterion("SchoolType <=", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeLike(String value) {
            addCriterion("SchoolType like", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeNotLike(String value) {
            addCriterion("SchoolType not like", value, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeIn(List<String> values) {
            addCriterion("SchoolType in", values, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeNotIn(List<String> values) {
            addCriterion("SchoolType not in", values, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeBetween(String value1, String value2) {
            addCriterion("SchoolType between", value1, value2, "schooltype");
            return (Criteria) this;
        }

        public Criteria andSchooltypeNotBetween(String value1, String value2) {
            addCriterion("SchoolType not between", value1, value2, "schooltype");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FULLTIME_EDU
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}