package com.csci.hrrs.model;

import java.util.ArrayList;
import java.util.List;

public class FamilyMemberExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public FamilyMemberExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPernrIsNull() {
            addCriterion("PERNR is null");
            return (Criteria) this;
        }

        public Criteria andPernrIsNotNull() {
            addCriterion("PERNR is not null");
            return (Criteria) this;
        }

        public Criteria andPernrEqualTo(String value) {
            addCriterion("PERNR =", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotEqualTo(String value) {
            addCriterion("PERNR <>", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThan(String value) {
            addCriterion("PERNR >", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThanOrEqualTo(String value) {
            addCriterion("PERNR >=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThan(String value) {
            addCriterion("PERNR <", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThanOrEqualTo(String value) {
            addCriterion("PERNR <=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLike(String value) {
            addCriterion("PERNR like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotLike(String value) {
            addCriterion("PERNR not like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrIn(List<String> values) {
            addCriterion("PERNR in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotIn(List<String> values) {
            addCriterion("PERNR not in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrBetween(String value1, String value2) {
            addCriterion("PERNR between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotBetween(String value1, String value2) {
            addCriterion("PERNR not between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("NAME is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("NAME is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("NAME =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("NAME <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("NAME >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("NAME >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("NAME <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("NAME <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("NAME like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("NAME not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("NAME in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("NAME not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("NAME between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("NAME not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("sex is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("sex is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(String value) {
            addCriterion("sex =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(String value) {
            addCriterion("sex <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(String value) {
            addCriterion("sex >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(String value) {
            addCriterion("sex >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(String value) {
            addCriterion("sex <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(String value) {
            addCriterion("sex <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLike(String value) {
            addCriterion("sex like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotLike(String value) {
            addCriterion("sex not like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<String> values) {
            addCriterion("sex in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<String> values) {
            addCriterion("sex not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(String value1, String value2) {
            addCriterion("sex between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(String value1, String value2) {
            addCriterion("sex not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNull() {
            addCriterion("birthdate is null");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNotNull() {
            addCriterion("birthdate is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdateEqualTo(String value) {
            addCriterion("birthdate =", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotEqualTo(String value) {
            addCriterion("birthdate <>", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThan(String value) {
            addCriterion("birthdate >", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThanOrEqualTo(String value) {
            addCriterion("birthdate >=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThan(String value) {
            addCriterion("birthdate <", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThanOrEqualTo(String value) {
            addCriterion("birthdate <=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLike(String value) {
            addCriterion("birthdate like", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotLike(String value) {
            addCriterion("birthdate not like", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateIn(List<String> values) {
            addCriterion("birthdate in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotIn(List<String> values) {
            addCriterion("birthdate not in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateBetween(String value1, String value2) {
            addCriterion("birthdate between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotBetween(String value1, String value2) {
            addCriterion("birthdate not between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andPoliticIsNull() {
            addCriterion("politic is null");
            return (Criteria) this;
        }

        public Criteria andPoliticIsNotNull() {
            addCriterion("politic is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticEqualTo(String value) {
            addCriterion("politic =", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotEqualTo(String value) {
            addCriterion("politic <>", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticGreaterThan(String value) {
            addCriterion("politic >", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticGreaterThanOrEqualTo(String value) {
            addCriterion("politic >=", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLessThan(String value) {
            addCriterion("politic <", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLessThanOrEqualTo(String value) {
            addCriterion("politic <=", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticLike(String value) {
            addCriterion("politic like", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotLike(String value) {
            addCriterion("politic not like", value, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticIn(List<String> values) {
            addCriterion("politic in", values, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotIn(List<String> values) {
            addCriterion("politic not in", values, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticBetween(String value1, String value2) {
            addCriterion("politic between", value1, value2, "politic");
            return (Criteria) this;
        }

        public Criteria andPoliticNotBetween(String value1, String value2) {
            addCriterion("politic not between", value1, value2, "politic");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceIsNull() {
            addCriterion("living_place is null");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceIsNotNull() {
            addCriterion("living_place is not null");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceEqualTo(String value) {
            addCriterion("living_place =", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceNotEqualTo(String value) {
            addCriterion("living_place <>", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceGreaterThan(String value) {
            addCriterion("living_place >", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("living_place >=", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceLessThan(String value) {
            addCriterion("living_place <", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceLessThanOrEqualTo(String value) {
            addCriterion("living_place <=", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceLike(String value) {
            addCriterion("living_place like", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceNotLike(String value) {
            addCriterion("living_place not like", value, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceIn(List<String> values) {
            addCriterion("living_place in", values, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceNotIn(List<String> values) {
            addCriterion("living_place not in", values, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceBetween(String value1, String value2) {
            addCriterion("living_place between", value1, value2, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andLivingPlaceNotBetween(String value1, String value2) {
            addCriterion("living_place not between", value1, value2, "livingPlace");
            return (Criteria) this;
        }

        public Criteria andWorkUnitIsNull() {
            addCriterion("work_unit is null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitIsNotNull() {
            addCriterion("work_unit is not null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitEqualTo(String value) {
            addCriterion("work_unit =", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitNotEqualTo(String value) {
            addCriterion("work_unit <>", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitGreaterThan(String value) {
            addCriterion("work_unit >", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitGreaterThanOrEqualTo(String value) {
            addCriterion("work_unit >=", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitLessThan(String value) {
            addCriterion("work_unit <", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitLessThanOrEqualTo(String value) {
            addCriterion("work_unit <=", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitLike(String value) {
            addCriterion("work_unit like", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitNotLike(String value) {
            addCriterion("work_unit not like", value, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitIn(List<String> values) {
            addCriterion("work_unit in", values, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitNotIn(List<String> values) {
            addCriterion("work_unit not in", values, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitBetween(String value1, String value2) {
            addCriterion("work_unit between", value1, value2, "workUnit");
            return (Criteria) this;
        }

        public Criteria andWorkUnitNotBetween(String value1, String value2) {
            addCriterion("work_unit not between", value1, value2, "workUnit");
            return (Criteria) this;
        }

        public Criteria andAppellationIsNull() {
            addCriterion("appellation is null");
            return (Criteria) this;
        }

        public Criteria andAppellationIsNotNull() {
            addCriterion("appellation is not null");
            return (Criteria) this;
        }

        public Criteria andAppellationEqualTo(String value) {
            addCriterion("appellation =", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationNotEqualTo(String value) {
            addCriterion("appellation <>", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationGreaterThan(String value) {
            addCriterion("appellation >", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationGreaterThanOrEqualTo(String value) {
            addCriterion("appellation >=", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationLessThan(String value) {
            addCriterion("appellation <", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationLessThanOrEqualTo(String value) {
            addCriterion("appellation <=", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationLike(String value) {
            addCriterion("appellation like", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationNotLike(String value) {
            addCriterion("appellation not like", value, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationIn(List<String> values) {
            addCriterion("appellation in", values, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationNotIn(List<String> values) {
            addCriterion("appellation not in", values, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationBetween(String value1, String value2) {
            addCriterion("appellation between", value1, value2, "appellation");
            return (Criteria) this;
        }

        public Criteria andAppellationNotBetween(String value1, String value2) {
            addCriterion("appellation not between", value1, value2, "appellation");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsIsNull() {
            addCriterion("is_Emerg_Contacts is null");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsIsNotNull() {
            addCriterion("is_Emerg_Contacts is not null");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsEqualTo(String value) {
            addCriterion("is_Emerg_Contacts =", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsNotEqualTo(String value) {
            addCriterion("is_Emerg_Contacts <>", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsGreaterThan(String value) {
            addCriterion("is_Emerg_Contacts >", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsGreaterThanOrEqualTo(String value) {
            addCriterion("is_Emerg_Contacts >=", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsLessThan(String value) {
            addCriterion("is_Emerg_Contacts <", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsLessThanOrEqualTo(String value) {
            addCriterion("is_Emerg_Contacts <=", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsLike(String value) {
            addCriterion("is_Emerg_Contacts like", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsNotLike(String value) {
            addCriterion("is_Emerg_Contacts not like", value, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsIn(List<String> values) {
            addCriterion("is_Emerg_Contacts in", values, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsNotIn(List<String> values) {
            addCriterion("is_Emerg_Contacts not in", values, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsBetween(String value1, String value2) {
            addCriterion("is_Emerg_Contacts between", value1, value2, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andIsEmergContactsNotBetween(String value1, String value2) {
            addCriterion("is_Emerg_Contacts not between", value1, value2, "isEmergContacts");
            return (Criteria) this;
        }

        public Criteria andJobIsNull() {
            addCriterion("job is null");
            return (Criteria) this;
        }

        public Criteria andJobIsNotNull() {
            addCriterion("job is not null");
            return (Criteria) this;
        }

        public Criteria andJobEqualTo(String value) {
            addCriterion("job =", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotEqualTo(String value) {
            addCriterion("job <>", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThan(String value) {
            addCriterion("job >", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobGreaterThanOrEqualTo(String value) {
            addCriterion("job >=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThan(String value) {
            addCriterion("job <", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLessThanOrEqualTo(String value) {
            addCriterion("job <=", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobLike(String value) {
            addCriterion("job like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotLike(String value) {
            addCriterion("job not like", value, "job");
            return (Criteria) this;
        }

        public Criteria andJobIn(List<String> values) {
            addCriterion("job in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotIn(List<String> values) {
            addCriterion("job not in", values, "job");
            return (Criteria) this;
        }

        public Criteria andJobBetween(String value1, String value2) {
            addCriterion("job between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andJobNotBetween(String value1, String value2) {
            addCriterion("job not between", value1, value2, "job");
            return (Criteria) this;
        }

        public Criteria andNotesIsNull() {
            addCriterion("notes is null");
            return (Criteria) this;
        }

        public Criteria andNotesIsNotNull() {
            addCriterion("notes is not null");
            return (Criteria) this;
        }

        public Criteria andNotesEqualTo(String value) {
            addCriterion("notes =", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotEqualTo(String value) {
            addCriterion("notes <>", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThan(String value) {
            addCriterion("notes >", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThanOrEqualTo(String value) {
            addCriterion("notes >=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThan(String value) {
            addCriterion("notes <", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThanOrEqualTo(String value) {
            addCriterion("notes <=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLike(String value) {
            addCriterion("notes like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotLike(String value) {
            addCriterion("notes not like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesIn(List<String> values) {
            addCriterion("notes in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotIn(List<String> values) {
            addCriterion("notes not in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesBetween(String value1, String value2) {
            addCriterion("notes between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotBetween(String value1, String value2) {
            addCriterion("notes not between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andAssociationIsNull() {
            addCriterion("association is null");
            return (Criteria) this;
        }

        public Criteria andAssociationIsNotNull() {
            addCriterion("association is not null");
            return (Criteria) this;
        }

        public Criteria andAssociationEqualTo(String value) {
            addCriterion("association =", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationNotEqualTo(String value) {
            addCriterion("association <>", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationGreaterThan(String value) {
            addCriterion("association >", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationGreaterThanOrEqualTo(String value) {
            addCriterion("association >=", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationLessThan(String value) {
            addCriterion("association <", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationLessThanOrEqualTo(String value) {
            addCriterion("association <=", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationLike(String value) {
            addCriterion("association like", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationNotLike(String value) {
            addCriterion("association not like", value, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationIn(List<String> values) {
            addCriterion("association in", values, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationNotIn(List<String> values) {
            addCriterion("association not in", values, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationBetween(String value1, String value2) {
            addCriterion("association between", value1, value2, "association");
            return (Criteria) this;
        }

        public Criteria andAssociationNotBetween(String value1, String value2) {
            addCriterion("association not between", value1, value2, "association");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("Phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("Phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("Phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("Phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("Phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("Phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("Phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("Phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("Phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("Phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("Phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("Phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("Phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("Phone not between", value1, value2, "phone");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table FACT_FAMILY
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}