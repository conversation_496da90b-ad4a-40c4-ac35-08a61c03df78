package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;

@TableName("HRMonthReport")
public class HRMonthReport {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.sort
     *
     * @mbg.generated
     */
    @TableField("sort")
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.company
     *
     * @mbg.generated
     */
    @TableField("company")
    private String company;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.company2
     *
     * @mbg.generated
     */
    @TableField("company2")
    private String company2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastYearNum
     *
     * @mbg.generated
     */
    @TableField("LastYearNum")
    private Integer lastyearnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastMonthNum
     *
     * @mbg.generated
     */
    @TableField("LastMonthNum")
    private Integer lastmonthnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.CurrentNum
     *
     * @mbg.generated
     */
    @TableField("CurrentNum")
    private Integer currentnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddNum
     *
     * @mbg.generated
     */
    @TableField("MonthAddNum")
    private Integer monthaddnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddRate
     *
     * @mbg.generated
     */
    @TableField("MonthAddRate")
    private BigDecimal monthaddrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddNum
     *
     * @mbg.generated
     */
    @TableField("YearAddNum")
    private Integer yearaddnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddRate
     *
     * @mbg.generated
     */
    @TableField("YearAddRate")
    private BigDecimal yearaddrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum
     *
     * @mbg.generated
     */
    @TableField("ManagerNum")
    private Integer managernum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_Mailland
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_Mailland")
    private Integer managernumMailland;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_HK_NP
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_HK_NP")
    private Integer managernumHkNp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_HK_BD
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_HK_BD")
    private Integer managernumHkBd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_MC_NP
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_MC_NP")
    private Integer managernumMcNp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_MC_BD
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_MC_BD")
    private Integer managernumMcBd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_OS_NP
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_OS_NP")
    private Integer managernumOsNp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ManagerNum_OS_BD
     *
     * @mbg.generated
     */
    @TableField("ManagerNum_OS_BD")
    private Integer managernumOsBd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.N_ManagerNum
     *
     * @mbg.generated
     */
    @TableField("N_ManagerNum")
    private Integer nManagernum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.N_ManagerNum_Mailland
     *
     * @mbg.generated
     */
    @TableField("N_ManagerNum_Mailland")
    private Integer nManagernumMailland;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.N_ManagerNum_HK
     *
     * @mbg.generated
     */
    @TableField("N_ManagerNum_HK")
    private Integer nManagernumHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.N_ManagerNum_MC
     *
     * @mbg.generated
     */
    @TableField("N_ManagerNum_MC")
    private Integer nManagernumMc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.N_ManagerNum_OS
     *
     * @mbg.generated
     */
    @TableField("N_ManagerNum_OS")
    private Integer nManagernumOs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.GSHYNum
     *
     * @mbg.generated
     */
    @TableField("GSHYNum")
    private Integer gshynum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DPYXNum
     *
     * @mbg.generated
     */
    @TableField("DPYXNum")
    private Integer dpyxnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DPRXNum
     *
     * @mbg.generated
     */
    @TableField("DPRXNum")
    private Integer dprxnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.QTNum
     *
     * @mbg.generated
     */
    @TableField("QTNum")
    private Integer qtnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_BC
     *
     * @mbg.generated
     */
    @TableField("LevelNum_BC")
    private Integer levelnumBc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_D1
     *
     * @mbg.generated
     */
    @TableField("LevelNum_D1")
    private Integer levelnumD1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_D2
     *
     * @mbg.generated
     */
    @TableField("LevelNum_D2")
    private Integer levelnumD2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_D3
     *
     * @mbg.generated
     */
    @TableField("LevelNum_D3")
    private Integer levelnumD3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_D4
     *
     * @mbg.generated
     */
    @TableField("LevelNum_D4")
    private Integer levelnumD4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LevelNum_Other
     *
     * @mbg.generated
     */
    @TableField("LevelNum_Other")
    private Integer levelnumOther;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.Num_Mailland
     *
     * @mbg.generated
     */
    @TableField("Num_Mailland")
    private Integer numMailland;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.Num_HK
     *
     * @mbg.generated
     */
    @TableField("Num_HK")
    private Integer numHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.Num_MC
     *
     * @mbg.generated
     */
    @TableField("Num_MC")
    private Integer numMc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.Num_OS
     *
     * @mbg.generated
     */
    @TableField("Num_OS")
    private Integer numOs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DRNum
     *
     * @mbg.generated
     */
    @TableField("DRNum")
    private Integer drnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.SchoolNum
     *
     * @mbg.generated
     */
    @TableField("SchoolNum")
    private Integer schoolnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.SocialNum
     *
     * @mbg.generated
     */
    @TableField("SocialNum")
    private Integer socialnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DCNum
     *
     * @mbg.generated
     */
    @TableField("DCNum")
    private Integer dcnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ZhudongCZ
     *
     * @mbg.generated
     */
    @TableField("ZhudongCZ")
    private Integer zhudongcz;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.tuixiu
     *
     * @mbg.generated
     */
    @TableField("tuixiu")
    private Integer tuixiu;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.siwang
     *
     * @mbg.generated
     */
    @TableField("siwang")
    private Integer siwang;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.youhua
     *
     * @mbg.generated
     */
    @TableField("youhua")
    private Integer youhua;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearChurnrate
     *
     * @mbg.generated
     */
    @TableField("YearChurnrate")
    private BigDecimal yearchurnrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.recoverydate
     *
     * @mbg.generated
     */
    @TableField("recoverydate")
    private LocalDate recoverydate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DRNum_Manager
     *
     * @mbg.generated
     */
    @TableField("DRNum_Manager")
    private Integer drnumManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.SchoolManageNum
     *
     * @mbg.generated
     */
    @TableField("SchoolManageNum")
    private Integer schoolmanagenum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.SociaManagelNum
     *
     * @mbg.generated
     */
    @TableField("SociaManagelNum")
    private Integer sociamanagelnum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.DCNum_Manager
     *
     * @mbg.generated
     */
    @TableField("DCNum_Manager")
    private Integer dcnumManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.ZhudongCZ_Manager
     *
     * @mbg.generated
     */
    @TableField("ZhudongCZ_Manager")
    private Integer zhudongczManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.tuixiu_Manager
     *
     * @mbg.generated
     */
    @TableField("tuixiu_Manager")
    private Integer tuixiuManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.siwang_Manager
     *
     * @mbg.generated
     */
    @TableField("siwang_Manager")
    private Integer siwangManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.youhua_Manager
     *
     * @mbg.generated
     */
    @TableField("youhua_Manager")
    private Integer youhuaManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearChurnrate_Manager
     *
     * @mbg.generated
     */
    @TableField("YearChurnrate_Manager")
    private BigDecimal yearchurnrateManager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastYearNum_waibao
     *
     * @mbg.generated
     */
    @TableField("LastYearNum_waibao")
    private Integer lastyearnumWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastMonthNum_waibao
     *
     * @mbg.generated
     */
    @TableField("LastMonthNum_waibao")
    private Integer lastmonthnumWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.CurrentNum_waibao
     *
     * @mbg.generated
     */
    @TableField("CurrentNum_waibao")
    private Integer currentnumWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddNum_waibao
     *
     * @mbg.generated
     */
    @TableField("MonthAddNum_waibao")
    private Integer monthaddnumWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddRate_waibao
     *
     * @mbg.generated
     */
    @TableField("MonthAddRate_waibao")
    private BigDecimal monthaddrateWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddNum_waibao
     *
     * @mbg.generated
     */
    @TableField("YearAddNum_waibao")
    private Integer yearaddnumWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddRate_waibao
     *
     * @mbg.generated
     */
    @TableField("YearAddRate_waibao")
    private BigDecimal yearaddrateWaibao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.BUCODE
     *
     * @mbg.generated
     */
    @TableField("BUCODE")
    private String bucode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastYearManagerNum
     *
     * @mbg.generated
     */
    @TableField("LastYearManagerNum")
    private Integer lastyearmanagernum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastMonthManagerNum
     *
     * @mbg.generated
     */
    @TableField("LastMonthManagerNum")
    private Integer lastmonthmanagernum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastYearNum_wailao
     *
     * @mbg.generated
     */
    @TableField("LastYearNum_wailao")
    private Integer lastyearnumWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.LastMonthNum_wailao
     *
     * @mbg.generated
     */
    @TableField("LastMonthNum_wailao")
    private Integer lastmonthnumWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.CurrentNum_wailao
     *
     * @mbg.generated
     */
    @TableField("CurrentNum_wailao")
    private Integer currentnumWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddNum_wailao
     *
     * @mbg.generated
     */
    @TableField("MonthAddNum_wailao")
    private Integer monthaddnumWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.MonthAddRate_wailao
     *
     * @mbg.generated
     */
    @TableField("MonthAddRate_wailao")
    private BigDecimal monthaddrateWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddNum_wailao
     *
     * @mbg.generated
     */
    @TableField("YearAddNum_wailao")
    private Integer yearaddnumWailao;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column HRMonthReport.YearAddRate_wailao
     *
     * @mbg.generated
     */
    @TableField("YearAddRate_wailao")
    private BigDecimal yearaddrateWailao;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.sort
     *
     * @return the value of HRMonthReport.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.sort
     *
     * @param sort the value for HRMonthReport.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.company
     *
     * @return the value of HRMonthReport.company
     *
     * @mbg.generated
     */
    public String getCompany() {
        return company;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.company
     *
     * @param company the value for HRMonthReport.company
     *
     * @mbg.generated
     */
    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.company2
     *
     * @return the value of HRMonthReport.company2
     *
     * @mbg.generated
     */
    public String getCompany2() {
        return company2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.company2
     *
     * @param company2 the value for HRMonthReport.company2
     *
     * @mbg.generated
     */
    public void setCompany2(String company2) {
        this.company2 = company2 == null ? null : company2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastYearNum
     *
     * @return the value of HRMonthReport.LastYearNum
     *
     * @mbg.generated
     */
    public Integer getLastyearnum() {
        return lastyearnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastYearNum
     *
     * @param lastyearnum the value for HRMonthReport.LastYearNum
     *
     * @mbg.generated
     */
    public void setLastyearnum(Integer lastyearnum) {
        this.lastyearnum = lastyearnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastMonthNum
     *
     * @return the value of HRMonthReport.LastMonthNum
     *
     * @mbg.generated
     */
    public Integer getLastmonthnum() {
        return lastmonthnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastMonthNum
     *
     * @param lastmonthnum the value for HRMonthReport.LastMonthNum
     *
     * @mbg.generated
     */
    public void setLastmonthnum(Integer lastmonthnum) {
        this.lastmonthnum = lastmonthnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.CurrentNum
     *
     * @return the value of HRMonthReport.CurrentNum
     *
     * @mbg.generated
     */
    public Integer getCurrentnum() {
        return currentnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.CurrentNum
     *
     * @param currentnum the value for HRMonthReport.CurrentNum
     *
     * @mbg.generated
     */
    public void setCurrentnum(Integer currentnum) {
        this.currentnum = currentnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddNum
     *
     * @return the value of HRMonthReport.MonthAddNum
     *
     * @mbg.generated
     */
    public Integer getMonthaddnum() {
        return monthaddnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddNum
     *
     * @param monthaddnum the value for HRMonthReport.MonthAddNum
     *
     * @mbg.generated
     */
    public void setMonthaddnum(Integer monthaddnum) {
        this.monthaddnum = monthaddnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddRate
     *
     * @return the value of HRMonthReport.MonthAddRate
     *
     * @mbg.generated
     */
    public BigDecimal getMonthaddrate() {
        return monthaddrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddRate
     *
     * @param monthaddrate the value for HRMonthReport.MonthAddRate
     *
     * @mbg.generated
     */
    public void setMonthaddrate(BigDecimal monthaddrate) {
        this.monthaddrate = monthaddrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddNum
     *
     * @return the value of HRMonthReport.YearAddNum
     *
     * @mbg.generated
     */
    public Integer getYearaddnum() {
        return yearaddnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddNum
     *
     * @param yearaddnum the value for HRMonthReport.YearAddNum
     *
     * @mbg.generated
     */
    public void setYearaddnum(Integer yearaddnum) {
        this.yearaddnum = yearaddnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddRate
     *
     * @return the value of HRMonthReport.YearAddRate
     *
     * @mbg.generated
     */
    public BigDecimal getYearaddrate() {
        return yearaddrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddRate
     *
     * @param yearaddrate the value for HRMonthReport.YearAddRate
     *
     * @mbg.generated
     */
    public void setYearaddrate(BigDecimal yearaddrate) {
        this.yearaddrate = yearaddrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum
     *
     * @return the value of HRMonthReport.ManagerNum
     *
     * @mbg.generated
     */
    public Integer getManagernum() {
        return managernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum
     *
     * @param managernum the value for HRMonthReport.ManagerNum
     *
     * @mbg.generated
     */
    public void setManagernum(Integer managernum) {
        this.managernum = managernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_Mailland
     *
     * @return the value of HRMonthReport.ManagerNum_Mailland
     *
     * @mbg.generated
     */
    public Integer getManagernumMailland() {
        return managernumMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_Mailland
     *
     * @param managernumMailland the value for HRMonthReport.ManagerNum_Mailland
     *
     * @mbg.generated
     */
    public void setManagernumMailland(Integer managernumMailland) {
        this.managernumMailland = managernumMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_HK_NP
     *
     * @return the value of HRMonthReport.ManagerNum_HK_NP
     *
     * @mbg.generated
     */
    public Integer getManagernumHkNp() {
        return managernumHkNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_HK_NP
     *
     * @param managernumHkNp the value for HRMonthReport.ManagerNum_HK_NP
     *
     * @mbg.generated
     */
    public void setManagernumHkNp(Integer managernumHkNp) {
        this.managernumHkNp = managernumHkNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_HK_BD
     *
     * @return the value of HRMonthReport.ManagerNum_HK_BD
     *
     * @mbg.generated
     */
    public Integer getManagernumHkBd() {
        return managernumHkBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_HK_BD
     *
     * @param managernumHkBd the value for HRMonthReport.ManagerNum_HK_BD
     *
     * @mbg.generated
     */
    public void setManagernumHkBd(Integer managernumHkBd) {
        this.managernumHkBd = managernumHkBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_MC_NP
     *
     * @return the value of HRMonthReport.ManagerNum_MC_NP
     *
     * @mbg.generated
     */
    public Integer getManagernumMcNp() {
        return managernumMcNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_MC_NP
     *
     * @param managernumMcNp the value for HRMonthReport.ManagerNum_MC_NP
     *
     * @mbg.generated
     */
    public void setManagernumMcNp(Integer managernumMcNp) {
        this.managernumMcNp = managernumMcNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_MC_BD
     *
     * @return the value of HRMonthReport.ManagerNum_MC_BD
     *
     * @mbg.generated
     */
    public Integer getManagernumMcBd() {
        return managernumMcBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_MC_BD
     *
     * @param managernumMcBd the value for HRMonthReport.ManagerNum_MC_BD
     *
     * @mbg.generated
     */
    public void setManagernumMcBd(Integer managernumMcBd) {
        this.managernumMcBd = managernumMcBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_OS_NP
     *
     * @return the value of HRMonthReport.ManagerNum_OS_NP
     *
     * @mbg.generated
     */
    public Integer getManagernumOsNp() {
        return managernumOsNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_OS_NP
     *
     * @param managernumOsNp the value for HRMonthReport.ManagerNum_OS_NP
     *
     * @mbg.generated
     */
    public void setManagernumOsNp(Integer managernumOsNp) {
        this.managernumOsNp = managernumOsNp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ManagerNum_OS_BD
     *
     * @return the value of HRMonthReport.ManagerNum_OS_BD
     *
     * @mbg.generated
     */
    public Integer getManagernumOsBd() {
        return managernumOsBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ManagerNum_OS_BD
     *
     * @param managernumOsBd the value for HRMonthReport.ManagerNum_OS_BD
     *
     * @mbg.generated
     */
    public void setManagernumOsBd(Integer managernumOsBd) {
        this.managernumOsBd = managernumOsBd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.N_ManagerNum
     *
     * @return the value of HRMonthReport.N_ManagerNum
     *
     * @mbg.generated
     */
    public Integer getnManagernum() {
        return nManagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.N_ManagerNum
     *
     * @param nManagernum the value for HRMonthReport.N_ManagerNum
     *
     * @mbg.generated
     */
    public void setnManagernum(Integer nManagernum) {
        this.nManagernum = nManagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.N_ManagerNum_Mailland
     *
     * @return the value of HRMonthReport.N_ManagerNum_Mailland
     *
     * @mbg.generated
     */
    public Integer getnManagernumMailland() {
        return nManagernumMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.N_ManagerNum_Mailland
     *
     * @param nManagernumMailland the value for HRMonthReport.N_ManagerNum_Mailland
     *
     * @mbg.generated
     */
    public void setnManagernumMailland(Integer nManagernumMailland) {
        this.nManagernumMailland = nManagernumMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.N_ManagerNum_HK
     *
     * @return the value of HRMonthReport.N_ManagerNum_HK
     *
     * @mbg.generated
     */
    public Integer getnManagernumHk() {
        return nManagernumHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.N_ManagerNum_HK
     *
     * @param nManagernumHk the value for HRMonthReport.N_ManagerNum_HK
     *
     * @mbg.generated
     */
    public void setnManagernumHk(Integer nManagernumHk) {
        this.nManagernumHk = nManagernumHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.N_ManagerNum_MC
     *
     * @return the value of HRMonthReport.N_ManagerNum_MC
     *
     * @mbg.generated
     */
    public Integer getnManagernumMc() {
        return nManagernumMc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.N_ManagerNum_MC
     *
     * @param nManagernumMc the value for HRMonthReport.N_ManagerNum_MC
     *
     * @mbg.generated
     */
    public void setnManagernumMc(Integer nManagernumMc) {
        this.nManagernumMc = nManagernumMc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.N_ManagerNum_OS
     *
     * @return the value of HRMonthReport.N_ManagerNum_OS
     *
     * @mbg.generated
     */
    public Integer getnManagernumOs() {
        return nManagernumOs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.N_ManagerNum_OS
     *
     * @param nManagernumOs the value for HRMonthReport.N_ManagerNum_OS
     *
     * @mbg.generated
     */
    public void setnManagernumOs(Integer nManagernumOs) {
        this.nManagernumOs = nManagernumOs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.GSHYNum
     *
     * @return the value of HRMonthReport.GSHYNum
     *
     * @mbg.generated
     */
    public Integer getGshynum() {
        return gshynum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.GSHYNum
     *
     * @param gshynum the value for HRMonthReport.GSHYNum
     *
     * @mbg.generated
     */
    public void setGshynum(Integer gshynum) {
        this.gshynum = gshynum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DPYXNum
     *
     * @return the value of HRMonthReport.DPYXNum
     *
     * @mbg.generated
     */
    public Integer getDpyxnum() {
        return dpyxnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DPYXNum
     *
     * @param dpyxnum the value for HRMonthReport.DPYXNum
     *
     * @mbg.generated
     */
    public void setDpyxnum(Integer dpyxnum) {
        this.dpyxnum = dpyxnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DPRXNum
     *
     * @return the value of HRMonthReport.DPRXNum
     *
     * @mbg.generated
     */
    public Integer getDprxnum() {
        return dprxnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DPRXNum
     *
     * @param dprxnum the value for HRMonthReport.DPRXNum
     *
     * @mbg.generated
     */
    public void setDprxnum(Integer dprxnum) {
        this.dprxnum = dprxnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.QTNum
     *
     * @return the value of HRMonthReport.QTNum
     *
     * @mbg.generated
     */
    public Integer getQtnum() {
        return qtnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.QTNum
     *
     * @param qtnum the value for HRMonthReport.QTNum
     *
     * @mbg.generated
     */
    public void setQtnum(Integer qtnum) {
        this.qtnum = qtnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_BC
     *
     * @return the value of HRMonthReport.LevelNum_BC
     *
     * @mbg.generated
     */
    public Integer getLevelnumBc() {
        return levelnumBc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_BC
     *
     * @param levelnumBc the value for HRMonthReport.LevelNum_BC
     *
     * @mbg.generated
     */
    public void setLevelnumBc(Integer levelnumBc) {
        this.levelnumBc = levelnumBc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_D1
     *
     * @return the value of HRMonthReport.LevelNum_D1
     *
     * @mbg.generated
     */
    public Integer getLevelnumD1() {
        return levelnumD1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_D1
     *
     * @param levelnumD1 the value for HRMonthReport.LevelNum_D1
     *
     * @mbg.generated
     */
    public void setLevelnumD1(Integer levelnumD1) {
        this.levelnumD1 = levelnumD1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_D2
     *
     * @return the value of HRMonthReport.LevelNum_D2
     *
     * @mbg.generated
     */
    public Integer getLevelnumD2() {
        return levelnumD2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_D2
     *
     * @param levelnumD2 the value for HRMonthReport.LevelNum_D2
     *
     * @mbg.generated
     */
    public void setLevelnumD2(Integer levelnumD2) {
        this.levelnumD2 = levelnumD2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_D3
     *
     * @return the value of HRMonthReport.LevelNum_D3
     *
     * @mbg.generated
     */
    public Integer getLevelnumD3() {
        return levelnumD3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_D3
     *
     * @param levelnumD3 the value for HRMonthReport.LevelNum_D3
     *
     * @mbg.generated
     */
    public void setLevelnumD3(Integer levelnumD3) {
        this.levelnumD3 = levelnumD3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_D4
     *
     * @return the value of HRMonthReport.LevelNum_D4
     *
     * @mbg.generated
     */
    public Integer getLevelnumD4() {
        return levelnumD4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_D4
     *
     * @param levelnumD4 the value for HRMonthReport.LevelNum_D4
     *
     * @mbg.generated
     */
    public void setLevelnumD4(Integer levelnumD4) {
        this.levelnumD4 = levelnumD4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LevelNum_Other
     *
     * @return the value of HRMonthReport.LevelNum_Other
     *
     * @mbg.generated
     */
    public Integer getLevelnumOther() {
        return levelnumOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LevelNum_Other
     *
     * @param levelnumOther the value for HRMonthReport.LevelNum_Other
     *
     * @mbg.generated
     */
    public void setLevelnumOther(Integer levelnumOther) {
        this.levelnumOther = levelnumOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.Num_Mailland
     *
     * @return the value of HRMonthReport.Num_Mailland
     *
     * @mbg.generated
     */
    public Integer getNumMailland() {
        return numMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.Num_Mailland
     *
     * @param numMailland the value for HRMonthReport.Num_Mailland
     *
     * @mbg.generated
     */
    public void setNumMailland(Integer numMailland) {
        this.numMailland = numMailland;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.Num_HK
     *
     * @return the value of HRMonthReport.Num_HK
     *
     * @mbg.generated
     */
    public Integer getNumHk() {
        return numHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.Num_HK
     *
     * @param numHk the value for HRMonthReport.Num_HK
     *
     * @mbg.generated
     */
    public void setNumHk(Integer numHk) {
        this.numHk = numHk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.Num_MC
     *
     * @return the value of HRMonthReport.Num_MC
     *
     * @mbg.generated
     */
    public Integer getNumMc() {
        return numMc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.Num_MC
     *
     * @param numMc the value for HRMonthReport.Num_MC
     *
     * @mbg.generated
     */
    public void setNumMc(Integer numMc) {
        this.numMc = numMc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.Num_OS
     *
     * @return the value of HRMonthReport.Num_OS
     *
     * @mbg.generated
     */
    public Integer getNumOs() {
        return numOs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.Num_OS
     *
     * @param numOs the value for HRMonthReport.Num_OS
     *
     * @mbg.generated
     */
    public void setNumOs(Integer numOs) {
        this.numOs = numOs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DRNum
     *
     * @return the value of HRMonthReport.DRNum
     *
     * @mbg.generated
     */
    public Integer getDrnum() {
        return drnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DRNum
     *
     * @param drnum the value for HRMonthReport.DRNum
     *
     * @mbg.generated
     */
    public void setDrnum(Integer drnum) {
        this.drnum = drnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.SchoolNum
     *
     * @return the value of HRMonthReport.SchoolNum
     *
     * @mbg.generated
     */
    public Integer getSchoolnum() {
        return schoolnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.SchoolNum
     *
     * @param schoolnum the value for HRMonthReport.SchoolNum
     *
     * @mbg.generated
     */
    public void setSchoolnum(Integer schoolnum) {
        this.schoolnum = schoolnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.SocialNum
     *
     * @return the value of HRMonthReport.SocialNum
     *
     * @mbg.generated
     */
    public Integer getSocialnum() {
        return socialnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.SocialNum
     *
     * @param socialnum the value for HRMonthReport.SocialNum
     *
     * @mbg.generated
     */
    public void setSocialnum(Integer socialnum) {
        this.socialnum = socialnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DCNum
     *
     * @return the value of HRMonthReport.DCNum
     *
     * @mbg.generated
     */
    public Integer getDcnum() {
        return dcnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DCNum
     *
     * @param dcnum the value for HRMonthReport.DCNum
     *
     * @mbg.generated
     */
    public void setDcnum(Integer dcnum) {
        this.dcnum = dcnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ZhudongCZ
     *
     * @return the value of HRMonthReport.ZhudongCZ
     *
     * @mbg.generated
     */
    public Integer getZhudongcz() {
        return zhudongcz;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ZhudongCZ
     *
     * @param zhudongcz the value for HRMonthReport.ZhudongCZ
     *
     * @mbg.generated
     */
    public void setZhudongcz(Integer zhudongcz) {
        this.zhudongcz = zhudongcz;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.tuixiu
     *
     * @return the value of HRMonthReport.tuixiu
     *
     * @mbg.generated
     */
    public Integer getTuixiu() {
        return tuixiu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.tuixiu
     *
     * @param tuixiu the value for HRMonthReport.tuixiu
     *
     * @mbg.generated
     */
    public void setTuixiu(Integer tuixiu) {
        this.tuixiu = tuixiu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.siwang
     *
     * @return the value of HRMonthReport.siwang
     *
     * @mbg.generated
     */
    public Integer getSiwang() {
        return siwang;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.siwang
     *
     * @param siwang the value for HRMonthReport.siwang
     *
     * @mbg.generated
     */
    public void setSiwang(Integer siwang) {
        this.siwang = siwang;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.youhua
     *
     * @return the value of HRMonthReport.youhua
     *
     * @mbg.generated
     */
    public Integer getYouhua() {
        return youhua;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.youhua
     *
     * @param youhua the value for HRMonthReport.youhua
     *
     * @mbg.generated
     */
    public void setYouhua(Integer youhua) {
        this.youhua = youhua;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearChurnrate
     *
     * @return the value of HRMonthReport.YearChurnrate
     *
     * @mbg.generated
     */
    public BigDecimal getYearchurnrate() {
        return yearchurnrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearChurnrate
     *
     * @param yearchurnrate the value for HRMonthReport.YearChurnrate
     *
     * @mbg.generated
     */
    public void setYearchurnrate(BigDecimal yearchurnrate) {
        this.yearchurnrate = yearchurnrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.recoverydate
     *
     * @return the value of HRMonthReport.recoverydate
     *
     * @mbg.generated
     */
    public LocalDate getRecoverydate() {
        return recoverydate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.recoverydate
     *
     * @param recoverydate the value for HRMonthReport.recoverydate
     *
     * @mbg.generated
     */
    public void setRecoverydate(LocalDate recoverydate) {
        this.recoverydate = recoverydate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DRNum_Manager
     *
     * @return the value of HRMonthReport.DRNum_Manager
     *
     * @mbg.generated
     */
    public Integer getDrnumManager() {
        return drnumManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DRNum_Manager
     *
     * @param drnumManager the value for HRMonthReport.DRNum_Manager
     *
     * @mbg.generated
     */
    public void setDrnumManager(Integer drnumManager) {
        this.drnumManager = drnumManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.SchoolManageNum
     *
     * @return the value of HRMonthReport.SchoolManageNum
     *
     * @mbg.generated
     */
    public Integer getSchoolmanagenum() {
        return schoolmanagenum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.SchoolManageNum
     *
     * @param schoolmanagenum the value for HRMonthReport.SchoolManageNum
     *
     * @mbg.generated
     */
    public void setSchoolmanagenum(Integer schoolmanagenum) {
        this.schoolmanagenum = schoolmanagenum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.SociaManagelNum
     *
     * @return the value of HRMonthReport.SociaManagelNum
     *
     * @mbg.generated
     */
    public Integer getSociamanagelnum() {
        return sociamanagelnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.SociaManagelNum
     *
     * @param sociamanagelnum the value for HRMonthReport.SociaManagelNum
     *
     * @mbg.generated
     */
    public void setSociamanagelnum(Integer sociamanagelnum) {
        this.sociamanagelnum = sociamanagelnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.DCNum_Manager
     *
     * @return the value of HRMonthReport.DCNum_Manager
     *
     * @mbg.generated
     */
    public Integer getDcnumManager() {
        return dcnumManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.DCNum_Manager
     *
     * @param dcnumManager the value for HRMonthReport.DCNum_Manager
     *
     * @mbg.generated
     */
    public void setDcnumManager(Integer dcnumManager) {
        this.dcnumManager = dcnumManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.ZhudongCZ_Manager
     *
     * @return the value of HRMonthReport.ZhudongCZ_Manager
     *
     * @mbg.generated
     */
    public Integer getZhudongczManager() {
        return zhudongczManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.ZhudongCZ_Manager
     *
     * @param zhudongczManager the value for HRMonthReport.ZhudongCZ_Manager
     *
     * @mbg.generated
     */
    public void setZhudongczManager(Integer zhudongczManager) {
        this.zhudongczManager = zhudongczManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.tuixiu_Manager
     *
     * @return the value of HRMonthReport.tuixiu_Manager
     *
     * @mbg.generated
     */
    public Integer getTuixiuManager() {
        return tuixiuManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.tuixiu_Manager
     *
     * @param tuixiuManager the value for HRMonthReport.tuixiu_Manager
     *
     * @mbg.generated
     */
    public void setTuixiuManager(Integer tuixiuManager) {
        this.tuixiuManager = tuixiuManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.siwang_Manager
     *
     * @return the value of HRMonthReport.siwang_Manager
     *
     * @mbg.generated
     */
    public Integer getSiwangManager() {
        return siwangManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.siwang_Manager
     *
     * @param siwangManager the value for HRMonthReport.siwang_Manager
     *
     * @mbg.generated
     */
    public void setSiwangManager(Integer siwangManager) {
        this.siwangManager = siwangManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.youhua_Manager
     *
     * @return the value of HRMonthReport.youhua_Manager
     *
     * @mbg.generated
     */
    public Integer getYouhuaManager() {
        return youhuaManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.youhua_Manager
     *
     * @param youhuaManager the value for HRMonthReport.youhua_Manager
     *
     * @mbg.generated
     */
    public void setYouhuaManager(Integer youhuaManager) {
        this.youhuaManager = youhuaManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearChurnrate_Manager
     *
     * @return the value of HRMonthReport.YearChurnrate_Manager
     *
     * @mbg.generated
     */
    public BigDecimal getYearchurnrateManager() {
        return yearchurnrateManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearChurnrate_Manager
     *
     * @param yearchurnrateManager the value for HRMonthReport.YearChurnrate_Manager
     *
     * @mbg.generated
     */
    public void setYearchurnrateManager(BigDecimal yearchurnrateManager) {
        this.yearchurnrateManager = yearchurnrateManager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastYearNum_waibao
     *
     * @return the value of HRMonthReport.LastYearNum_waibao
     *
     * @mbg.generated
     */
    public Integer getLastyearnumWaibao() {
        return lastyearnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastYearNum_waibao
     *
     * @param lastyearnumWaibao the value for HRMonthReport.LastYearNum_waibao
     *
     * @mbg.generated
     */
    public void setLastyearnumWaibao(Integer lastyearnumWaibao) {
        this.lastyearnumWaibao = lastyearnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastMonthNum_waibao
     *
     * @return the value of HRMonthReport.LastMonthNum_waibao
     *
     * @mbg.generated
     */
    public Integer getLastmonthnumWaibao() {
        return lastmonthnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastMonthNum_waibao
     *
     * @param lastmonthnumWaibao the value for HRMonthReport.LastMonthNum_waibao
     *
     * @mbg.generated
     */
    public void setLastmonthnumWaibao(Integer lastmonthnumWaibao) {
        this.lastmonthnumWaibao = lastmonthnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.CurrentNum_waibao
     *
     * @return the value of HRMonthReport.CurrentNum_waibao
     *
     * @mbg.generated
     */
    public Integer getCurrentnumWaibao() {
        return currentnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.CurrentNum_waibao
     *
     * @param currentnumWaibao the value for HRMonthReport.CurrentNum_waibao
     *
     * @mbg.generated
     */
    public void setCurrentnumWaibao(Integer currentnumWaibao) {
        this.currentnumWaibao = currentnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddNum_waibao
     *
     * @return the value of HRMonthReport.MonthAddNum_waibao
     *
     * @mbg.generated
     */
    public Integer getMonthaddnumWaibao() {
        return monthaddnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddNum_waibao
     *
     * @param monthaddnumWaibao the value for HRMonthReport.MonthAddNum_waibao
     *
     * @mbg.generated
     */
    public void setMonthaddnumWaibao(Integer monthaddnumWaibao) {
        this.monthaddnumWaibao = monthaddnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddRate_waibao
     *
     * @return the value of HRMonthReport.MonthAddRate_waibao
     *
     * @mbg.generated
     */
    public BigDecimal getMonthaddrateWaibao() {
        return monthaddrateWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddRate_waibao
     *
     * @param monthaddrateWaibao the value for HRMonthReport.MonthAddRate_waibao
     *
     * @mbg.generated
     */
    public void setMonthaddrateWaibao(BigDecimal monthaddrateWaibao) {
        this.monthaddrateWaibao = monthaddrateWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddNum_waibao
     *
     * @return the value of HRMonthReport.YearAddNum_waibao
     *
     * @mbg.generated
     */
    public Integer getYearaddnumWaibao() {
        return yearaddnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddNum_waibao
     *
     * @param yearaddnumWaibao the value for HRMonthReport.YearAddNum_waibao
     *
     * @mbg.generated
     */
    public void setYearaddnumWaibao(Integer yearaddnumWaibao) {
        this.yearaddnumWaibao = yearaddnumWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddRate_waibao
     *
     * @return the value of HRMonthReport.YearAddRate_waibao
     *
     * @mbg.generated
     */
    public BigDecimal getYearaddrateWaibao() {
        return yearaddrateWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddRate_waibao
     *
     * @param yearaddrateWaibao the value for HRMonthReport.YearAddRate_waibao
     *
     * @mbg.generated
     */
    public void setYearaddrateWaibao(BigDecimal yearaddrateWaibao) {
        this.yearaddrateWaibao = yearaddrateWaibao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.BUCODE
     *
     * @return the value of HRMonthReport.BUCODE
     *
     * @mbg.generated
     */
    public String getBucode() {
        return bucode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.BUCODE
     *
     * @param bucode the value for HRMonthReport.BUCODE
     *
     * @mbg.generated
     */
    public void setBucode(String bucode) {
        this.bucode = bucode == null ? null : bucode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastYearManagerNum
     *
     * @return the value of HRMonthReport.LastYearManagerNum
     *
     * @mbg.generated
     */
    public Integer getLastyearmanagernum() {
        return lastyearmanagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastYearManagerNum
     *
     * @param lastyearmanagernum the value for HRMonthReport.LastYearManagerNum
     *
     * @mbg.generated
     */
    public void setLastyearmanagernum(Integer lastyearmanagernum) {
        this.lastyearmanagernum = lastyearmanagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastMonthManagerNum
     *
     * @return the value of HRMonthReport.LastMonthManagerNum
     *
     * @mbg.generated
     */
    public Integer getLastmonthmanagernum() {
        return lastmonthmanagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastMonthManagerNum
     *
     * @param lastmonthmanagernum the value for HRMonthReport.LastMonthManagerNum
     *
     * @mbg.generated
     */
    public void setLastmonthmanagernum(Integer lastmonthmanagernum) {
        this.lastmonthmanagernum = lastmonthmanagernum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastYearNum_wailao
     *
     * @return the value of HRMonthReport.LastYearNum_wailao
     *
     * @mbg.generated
     */
    public Integer getLastyearnumWailao() {
        return lastyearnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastYearNum_wailao
     *
     * @param lastyearnumWailao the value for HRMonthReport.LastYearNum_wailao
     *
     * @mbg.generated
     */
    public void setLastyearnumWailao(Integer lastyearnumWailao) {
        this.lastyearnumWailao = lastyearnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.LastMonthNum_wailao
     *
     * @return the value of HRMonthReport.LastMonthNum_wailao
     *
     * @mbg.generated
     */
    public Integer getLastmonthnumWailao() {
        return lastmonthnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.LastMonthNum_wailao
     *
     * @param lastmonthnumWailao the value for HRMonthReport.LastMonthNum_wailao
     *
     * @mbg.generated
     */
    public void setLastmonthnumWailao(Integer lastmonthnumWailao) {
        this.lastmonthnumWailao = lastmonthnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.CurrentNum_wailao
     *
     * @return the value of HRMonthReport.CurrentNum_wailao
     *
     * @mbg.generated
     */
    public Integer getCurrentnumWailao() {
        return currentnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.CurrentNum_wailao
     *
     * @param currentnumWailao the value for HRMonthReport.CurrentNum_wailao
     *
     * @mbg.generated
     */
    public void setCurrentnumWailao(Integer currentnumWailao) {
        this.currentnumWailao = currentnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddNum_wailao
     *
     * @return the value of HRMonthReport.MonthAddNum_wailao
     *
     * @mbg.generated
     */
    public Integer getMonthaddnumWailao() {
        return monthaddnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddNum_wailao
     *
     * @param monthaddnumWailao the value for HRMonthReport.MonthAddNum_wailao
     *
     * @mbg.generated
     */
    public void setMonthaddnumWailao(Integer monthaddnumWailao) {
        this.monthaddnumWailao = monthaddnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.MonthAddRate_wailao
     *
     * @return the value of HRMonthReport.MonthAddRate_wailao
     *
     * @mbg.generated
     */
    public BigDecimal getMonthaddrateWailao() {
        return monthaddrateWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.MonthAddRate_wailao
     *
     * @param monthaddrateWailao the value for HRMonthReport.MonthAddRate_wailao
     *
     * @mbg.generated
     */
    public void setMonthaddrateWailao(BigDecimal monthaddrateWailao) {
        this.monthaddrateWailao = monthaddrateWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddNum_wailao
     *
     * @return the value of HRMonthReport.YearAddNum_wailao
     *
     * @mbg.generated
     */
    public Integer getYearaddnumWailao() {
        return yearaddnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddNum_wailao
     *
     * @param yearaddnumWailao the value for HRMonthReport.YearAddNum_wailao
     *
     * @mbg.generated
     */
    public void setYearaddnumWailao(Integer yearaddnumWailao) {
        this.yearaddnumWailao = yearaddnumWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column HRMonthReport.YearAddRate_wailao
     *
     * @return the value of HRMonthReport.YearAddRate_wailao
     *
     * @mbg.generated
     */
    public BigDecimal getYearaddrateWailao() {
        return yearaddrateWailao;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column HRMonthReport.YearAddRate_wailao
     *
     * @param yearaddrateWailao the value for HRMonthReport.YearAddRate_wailao
     *
     * @mbg.generated
     */
    public void setYearaddrateWailao(BigDecimal yearaddrateWailao) {
        this.yearaddrateWailao = yearaddrateWailao;
    }
}