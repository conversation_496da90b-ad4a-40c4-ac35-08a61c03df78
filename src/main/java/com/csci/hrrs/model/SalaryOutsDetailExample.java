package com.csci.hrrs.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SalaryOutsDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public SalaryOutsDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(String value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(String value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(String value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(String value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(String value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLike(String value) {
            addCriterion("head_id like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotLike(String value) {
            addCriterion("head_id not like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<String> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<String> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(String value1, String value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(String value1, String value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("config_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("config_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(String value) {
            addCriterion("config_id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(String value) {
            addCriterion("config_id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(String value) {
            addCriterion("config_id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(String value) {
            addCriterion("config_id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(String value) {
            addCriterion("config_id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(String value) {
            addCriterion("config_id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLike(String value) {
            addCriterion("config_id like", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotLike(String value) {
            addCriterion("config_id not like", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<String> values) {
            addCriterion("config_id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<String> values) {
            addCriterion("config_id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(String value1, String value2) {
            addCriterion("config_id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(String value1, String value2) {
            addCriterion("config_id not between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongIsNull() {
            addCriterion("cost_share_belong is null");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongIsNotNull() {
            addCriterion("cost_share_belong is not null");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongEqualTo(String value) {
            addCriterion("cost_share_belong =", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongNotEqualTo(String value) {
            addCriterion("cost_share_belong <>", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongGreaterThan(String value) {
            addCriterion("cost_share_belong >", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongGreaterThanOrEqualTo(String value) {
            addCriterion("cost_share_belong >=", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongLessThan(String value) {
            addCriterion("cost_share_belong <", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongLessThanOrEqualTo(String value) {
            addCriterion("cost_share_belong <=", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongLike(String value) {
            addCriterion("cost_share_belong like", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongNotLike(String value) {
            addCriterion("cost_share_belong not like", value, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongIn(List<String> values) {
            addCriterion("cost_share_belong in", values, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongNotIn(List<String> values) {
            addCriterion("cost_share_belong not in", values, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongBetween(String value1, String value2) {
            addCriterion("cost_share_belong between", value1, value2, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareBelongNotBetween(String value1, String value2) {
            addCriterion("cost_share_belong not between", value1, value2, "costShareBelong");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgIsNull() {
            addCriterion("cost_share_org is null");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgIsNotNull() {
            addCriterion("cost_share_org is not null");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgEqualTo(String value) {
            addCriterion("cost_share_org =", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgNotEqualTo(String value) {
            addCriterion("cost_share_org <>", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgGreaterThan(String value) {
            addCriterion("cost_share_org >", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgGreaterThanOrEqualTo(String value) {
            addCriterion("cost_share_org >=", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgLessThan(String value) {
            addCriterion("cost_share_org <", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgLessThanOrEqualTo(String value) {
            addCriterion("cost_share_org <=", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgLike(String value) {
            addCriterion("cost_share_org like", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgNotLike(String value) {
            addCriterion("cost_share_org not like", value, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgIn(List<String> values) {
            addCriterion("cost_share_org in", values, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgNotIn(List<String> values) {
            addCriterion("cost_share_org not in", values, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgBetween(String value1, String value2) {
            addCriterion("cost_share_org between", value1, value2, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andCostShareOrgNotBetween(String value1, String value2) {
            addCriterion("cost_share_org not between", value1, value2, "costShareOrg");
            return (Criteria) this;
        }

        public Criteria andStaffTypeIsNull() {
            addCriterion("staff_type is null");
            return (Criteria) this;
        }

        public Criteria andStaffTypeIsNotNull() {
            addCriterion("staff_type is not null");
            return (Criteria) this;
        }

        public Criteria andStaffTypeEqualTo(String value) {
            addCriterion("staff_type =", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeNotEqualTo(String value) {
            addCriterion("staff_type <>", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeGreaterThan(String value) {
            addCriterion("staff_type >", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeGreaterThanOrEqualTo(String value) {
            addCriterion("staff_type >=", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeLessThan(String value) {
            addCriterion("staff_type <", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeLessThanOrEqualTo(String value) {
            addCriterion("staff_type <=", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeLike(String value) {
            addCriterion("staff_type like", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeNotLike(String value) {
            addCriterion("staff_type not like", value, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeIn(List<String> values) {
            addCriterion("staff_type in", values, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeNotIn(List<String> values) {
            addCriterion("staff_type not in", values, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeBetween(String value1, String value2) {
            addCriterion("staff_type between", value1, value2, "staffType");
            return (Criteria) this;
        }

        public Criteria andStaffTypeNotBetween(String value1, String value2) {
            addCriterion("staff_type not between", value1, value2, "staffType");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIsNull() {
            addCriterion("currency_name is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIsNotNull() {
            addCriterion("currency_name is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameEqualTo(String value) {
            addCriterion("currency_name =", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotEqualTo(String value) {
            addCriterion("currency_name <>", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameGreaterThan(String value) {
            addCriterion("currency_name >", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameGreaterThanOrEqualTo(String value) {
            addCriterion("currency_name >=", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLessThan(String value) {
            addCriterion("currency_name <", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLessThanOrEqualTo(String value) {
            addCriterion("currency_name <=", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLike(String value) {
            addCriterion("currency_name like", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotLike(String value) {
            addCriterion("currency_name not like", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIn(List<String> values) {
            addCriterion("currency_name in", values, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotIn(List<String> values) {
            addCriterion("currency_name not in", values, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameBetween(String value1, String value2) {
            addCriterion("currency_name between", value1, value2, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotBetween(String value1, String value2) {
            addCriterion("currency_name not between", value1, value2, "currencyName");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadIsNull() {
            addCriterion("yearly_total_head is null");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadIsNotNull() {
            addCriterion("yearly_total_head is not null");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadEqualTo(Integer value) {
            addCriterion("yearly_total_head =", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadNotEqualTo(Integer value) {
            addCriterion("yearly_total_head <>", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadGreaterThan(Integer value) {
            addCriterion("yearly_total_head >", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("yearly_total_head >=", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadLessThan(Integer value) {
            addCriterion("yearly_total_head <", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadLessThanOrEqualTo(Integer value) {
            addCriterion("yearly_total_head <=", value, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadIn(List<Integer> values) {
            addCriterion("yearly_total_head in", values, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadNotIn(List<Integer> values) {
            addCriterion("yearly_total_head not in", values, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadBetween(Integer value1, Integer value2) {
            addCriterion("yearly_total_head between", value1, value2, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("yearly_total_head not between", value1, value2, "yearlyTotalHead");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryIsNull() {
            addCriterion("yearly_total_salary is null");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryIsNotNull() {
            addCriterion("yearly_total_salary is not null");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryEqualTo(BigDecimal value) {
            addCriterion("yearly_total_salary =", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryNotEqualTo(BigDecimal value) {
            addCriterion("yearly_total_salary <>", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryGreaterThan(BigDecimal value) {
            addCriterion("yearly_total_salary >", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("yearly_total_salary >=", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryLessThan(BigDecimal value) {
            addCriterion("yearly_total_salary <", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryLessThanOrEqualTo(BigDecimal value) {
            addCriterion("yearly_total_salary <=", value, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryIn(List<BigDecimal> values) {
            addCriterion("yearly_total_salary in", values, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryNotIn(List<BigDecimal> values) {
            addCriterion("yearly_total_salary not in", values, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("yearly_total_salary between", value1, value2, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andYearlyTotalSalaryNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("yearly_total_salary not between", value1, value2, "yearlyTotalSalary");
            return (Criteria) this;
        }

        public Criteria andJanHeadIsNull() {
            addCriterion("jan_head is null");
            return (Criteria) this;
        }

        public Criteria andJanHeadIsNotNull() {
            addCriterion("jan_head is not null");
            return (Criteria) this;
        }

        public Criteria andJanHeadEqualTo(Integer value) {
            addCriterion("jan_head =", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadNotEqualTo(Integer value) {
            addCriterion("jan_head <>", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadGreaterThan(Integer value) {
            addCriterion("jan_head >", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("jan_head >=", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadLessThan(Integer value) {
            addCriterion("jan_head <", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadLessThanOrEqualTo(Integer value) {
            addCriterion("jan_head <=", value, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadIn(List<Integer> values) {
            addCriterion("jan_head in", values, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadNotIn(List<Integer> values) {
            addCriterion("jan_head not in", values, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadBetween(Integer value1, Integer value2) {
            addCriterion("jan_head between", value1, value2, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("jan_head not between", value1, value2, "janHead");
            return (Criteria) this;
        }

        public Criteria andJanAmountIsNull() {
            addCriterion("jan_amount is null");
            return (Criteria) this;
        }

        public Criteria andJanAmountIsNotNull() {
            addCriterion("jan_amount is not null");
            return (Criteria) this;
        }

        public Criteria andJanAmountEqualTo(BigDecimal value) {
            addCriterion("jan_amount =", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountNotEqualTo(BigDecimal value) {
            addCriterion("jan_amount <>", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountGreaterThan(BigDecimal value) {
            addCriterion("jan_amount >", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("jan_amount >=", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountLessThan(BigDecimal value) {
            addCriterion("jan_amount <", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("jan_amount <=", value, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountIn(List<BigDecimal> values) {
            addCriterion("jan_amount in", values, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountNotIn(List<BigDecimal> values) {
            addCriterion("jan_amount not in", values, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jan_amount between", value1, value2, "janAmount");
            return (Criteria) this;
        }

        public Criteria andJanAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jan_amount not between", value1, value2, "janAmount");
            return (Criteria) this;
        }

        public Criteria andFebHeadIsNull() {
            addCriterion("feb_head is null");
            return (Criteria) this;
        }

        public Criteria andFebHeadIsNotNull() {
            addCriterion("feb_head is not null");
            return (Criteria) this;
        }

        public Criteria andFebHeadEqualTo(Integer value) {
            addCriterion("feb_head =", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadNotEqualTo(Integer value) {
            addCriterion("feb_head <>", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadGreaterThan(Integer value) {
            addCriterion("feb_head >", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("feb_head >=", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadLessThan(Integer value) {
            addCriterion("feb_head <", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadLessThanOrEqualTo(Integer value) {
            addCriterion("feb_head <=", value, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadIn(List<Integer> values) {
            addCriterion("feb_head in", values, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadNotIn(List<Integer> values) {
            addCriterion("feb_head not in", values, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadBetween(Integer value1, Integer value2) {
            addCriterion("feb_head between", value1, value2, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("feb_head not between", value1, value2, "febHead");
            return (Criteria) this;
        }

        public Criteria andFebAmountIsNull() {
            addCriterion("feb_amount is null");
            return (Criteria) this;
        }

        public Criteria andFebAmountIsNotNull() {
            addCriterion("feb_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFebAmountEqualTo(BigDecimal value) {
            addCriterion("feb_amount =", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountNotEqualTo(BigDecimal value) {
            addCriterion("feb_amount <>", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountGreaterThan(BigDecimal value) {
            addCriterion("feb_amount >", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("feb_amount >=", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountLessThan(BigDecimal value) {
            addCriterion("feb_amount <", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("feb_amount <=", value, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountIn(List<BigDecimal> values) {
            addCriterion("feb_amount in", values, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountNotIn(List<BigDecimal> values) {
            addCriterion("feb_amount not in", values, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("feb_amount between", value1, value2, "febAmount");
            return (Criteria) this;
        }

        public Criteria andFebAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("feb_amount not between", value1, value2, "febAmount");
            return (Criteria) this;
        }

        public Criteria andMarHeadIsNull() {
            addCriterion("mar_head is null");
            return (Criteria) this;
        }

        public Criteria andMarHeadIsNotNull() {
            addCriterion("mar_head is not null");
            return (Criteria) this;
        }

        public Criteria andMarHeadEqualTo(Integer value) {
            addCriterion("mar_head =", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadNotEqualTo(Integer value) {
            addCriterion("mar_head <>", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadGreaterThan(Integer value) {
            addCriterion("mar_head >", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("mar_head >=", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadLessThan(Integer value) {
            addCriterion("mar_head <", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadLessThanOrEqualTo(Integer value) {
            addCriterion("mar_head <=", value, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadIn(List<Integer> values) {
            addCriterion("mar_head in", values, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadNotIn(List<Integer> values) {
            addCriterion("mar_head not in", values, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadBetween(Integer value1, Integer value2) {
            addCriterion("mar_head between", value1, value2, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("mar_head not between", value1, value2, "marHead");
            return (Criteria) this;
        }

        public Criteria andMarAmountIsNull() {
            addCriterion("mar_amount is null");
            return (Criteria) this;
        }

        public Criteria andMarAmountIsNotNull() {
            addCriterion("mar_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMarAmountEqualTo(BigDecimal value) {
            addCriterion("mar_amount =", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountNotEqualTo(BigDecimal value) {
            addCriterion("mar_amount <>", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountGreaterThan(BigDecimal value) {
            addCriterion("mar_amount >", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("mar_amount >=", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountLessThan(BigDecimal value) {
            addCriterion("mar_amount <", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("mar_amount <=", value, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountIn(List<BigDecimal> values) {
            addCriterion("mar_amount in", values, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountNotIn(List<BigDecimal> values) {
            addCriterion("mar_amount not in", values, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("mar_amount between", value1, value2, "marAmount");
            return (Criteria) this;
        }

        public Criteria andMarAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("mar_amount not between", value1, value2, "marAmount");
            return (Criteria) this;
        }

        public Criteria andAprHeadIsNull() {
            addCriterion("apr_head is null");
            return (Criteria) this;
        }

        public Criteria andAprHeadIsNotNull() {
            addCriterion("apr_head is not null");
            return (Criteria) this;
        }

        public Criteria andAprHeadEqualTo(Integer value) {
            addCriterion("apr_head =", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadNotEqualTo(Integer value) {
            addCriterion("apr_head <>", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadGreaterThan(Integer value) {
            addCriterion("apr_head >", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("apr_head >=", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadLessThan(Integer value) {
            addCriterion("apr_head <", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadLessThanOrEqualTo(Integer value) {
            addCriterion("apr_head <=", value, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadIn(List<Integer> values) {
            addCriterion("apr_head in", values, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadNotIn(List<Integer> values) {
            addCriterion("apr_head not in", values, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadBetween(Integer value1, Integer value2) {
            addCriterion("apr_head between", value1, value2, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("apr_head not between", value1, value2, "aprHead");
            return (Criteria) this;
        }

        public Criteria andAprAmountIsNull() {
            addCriterion("apr_amount is null");
            return (Criteria) this;
        }

        public Criteria andAprAmountIsNotNull() {
            addCriterion("apr_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAprAmountEqualTo(BigDecimal value) {
            addCriterion("apr_amount =", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountNotEqualTo(BigDecimal value) {
            addCriterion("apr_amount <>", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountGreaterThan(BigDecimal value) {
            addCriterion("apr_amount >", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apr_amount >=", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountLessThan(BigDecimal value) {
            addCriterion("apr_amount <", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apr_amount <=", value, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountIn(List<BigDecimal> values) {
            addCriterion("apr_amount in", values, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountNotIn(List<BigDecimal> values) {
            addCriterion("apr_amount not in", values, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apr_amount between", value1, value2, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andAprAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apr_amount not between", value1, value2, "aprAmount");
            return (Criteria) this;
        }

        public Criteria andMayHeadIsNull() {
            addCriterion("may_head is null");
            return (Criteria) this;
        }

        public Criteria andMayHeadIsNotNull() {
            addCriterion("may_head is not null");
            return (Criteria) this;
        }

        public Criteria andMayHeadEqualTo(Integer value) {
            addCriterion("may_head =", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadNotEqualTo(Integer value) {
            addCriterion("may_head <>", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadGreaterThan(Integer value) {
            addCriterion("may_head >", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("may_head >=", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadLessThan(Integer value) {
            addCriterion("may_head <", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadLessThanOrEqualTo(Integer value) {
            addCriterion("may_head <=", value, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadIn(List<Integer> values) {
            addCriterion("may_head in", values, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadNotIn(List<Integer> values) {
            addCriterion("may_head not in", values, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadBetween(Integer value1, Integer value2) {
            addCriterion("may_head between", value1, value2, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("may_head not between", value1, value2, "mayHead");
            return (Criteria) this;
        }

        public Criteria andMayAmountIsNull() {
            addCriterion("may_amount is null");
            return (Criteria) this;
        }

        public Criteria andMayAmountIsNotNull() {
            addCriterion("may_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMayAmountEqualTo(BigDecimal value) {
            addCriterion("may_amount =", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountNotEqualTo(BigDecimal value) {
            addCriterion("may_amount <>", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountGreaterThan(BigDecimal value) {
            addCriterion("may_amount >", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("may_amount >=", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountLessThan(BigDecimal value) {
            addCriterion("may_amount <", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("may_amount <=", value, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountIn(List<BigDecimal> values) {
            addCriterion("may_amount in", values, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountNotIn(List<BigDecimal> values) {
            addCriterion("may_amount not in", values, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("may_amount between", value1, value2, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andMayAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("may_amount not between", value1, value2, "mayAmount");
            return (Criteria) this;
        }

        public Criteria andJunHeadIsNull() {
            addCriterion("jun_head is null");
            return (Criteria) this;
        }

        public Criteria andJunHeadIsNotNull() {
            addCriterion("jun_head is not null");
            return (Criteria) this;
        }

        public Criteria andJunHeadEqualTo(Integer value) {
            addCriterion("jun_head =", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadNotEqualTo(Integer value) {
            addCriterion("jun_head <>", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadGreaterThan(Integer value) {
            addCriterion("jun_head >", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("jun_head >=", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadLessThan(Integer value) {
            addCriterion("jun_head <", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadLessThanOrEqualTo(Integer value) {
            addCriterion("jun_head <=", value, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadIn(List<Integer> values) {
            addCriterion("jun_head in", values, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadNotIn(List<Integer> values) {
            addCriterion("jun_head not in", values, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadBetween(Integer value1, Integer value2) {
            addCriterion("jun_head between", value1, value2, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("jun_head not between", value1, value2, "junHead");
            return (Criteria) this;
        }

        public Criteria andJunAmountIsNull() {
            addCriterion("jun_amount is null");
            return (Criteria) this;
        }

        public Criteria andJunAmountIsNotNull() {
            addCriterion("jun_amount is not null");
            return (Criteria) this;
        }

        public Criteria andJunAmountEqualTo(BigDecimal value) {
            addCriterion("jun_amount =", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountNotEqualTo(BigDecimal value) {
            addCriterion("jun_amount <>", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountGreaterThan(BigDecimal value) {
            addCriterion("jun_amount >", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("jun_amount >=", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountLessThan(BigDecimal value) {
            addCriterion("jun_amount <", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("jun_amount <=", value, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountIn(List<BigDecimal> values) {
            addCriterion("jun_amount in", values, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountNotIn(List<BigDecimal> values) {
            addCriterion("jun_amount not in", values, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jun_amount between", value1, value2, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJunAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jun_amount not between", value1, value2, "junAmount");
            return (Criteria) this;
        }

        public Criteria andJulHeadIsNull() {
            addCriterion("jul_head is null");
            return (Criteria) this;
        }

        public Criteria andJulHeadIsNotNull() {
            addCriterion("jul_head is not null");
            return (Criteria) this;
        }

        public Criteria andJulHeadEqualTo(Integer value) {
            addCriterion("jul_head =", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadNotEqualTo(Integer value) {
            addCriterion("jul_head <>", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadGreaterThan(Integer value) {
            addCriterion("jul_head >", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("jul_head >=", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadLessThan(Integer value) {
            addCriterion("jul_head <", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadLessThanOrEqualTo(Integer value) {
            addCriterion("jul_head <=", value, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadIn(List<Integer> values) {
            addCriterion("jul_head in", values, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadNotIn(List<Integer> values) {
            addCriterion("jul_head not in", values, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadBetween(Integer value1, Integer value2) {
            addCriterion("jul_head between", value1, value2, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("jul_head not between", value1, value2, "julHead");
            return (Criteria) this;
        }

        public Criteria andJulAmountIsNull() {
            addCriterion("jul_amount is null");
            return (Criteria) this;
        }

        public Criteria andJulAmountIsNotNull() {
            addCriterion("jul_amount is not null");
            return (Criteria) this;
        }

        public Criteria andJulAmountEqualTo(BigDecimal value) {
            addCriterion("jul_amount =", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountNotEqualTo(BigDecimal value) {
            addCriterion("jul_amount <>", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountGreaterThan(BigDecimal value) {
            addCriterion("jul_amount >", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("jul_amount >=", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountLessThan(BigDecimal value) {
            addCriterion("jul_amount <", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("jul_amount <=", value, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountIn(List<BigDecimal> values) {
            addCriterion("jul_amount in", values, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountNotIn(List<BigDecimal> values) {
            addCriterion("jul_amount not in", values, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jul_amount between", value1, value2, "julAmount");
            return (Criteria) this;
        }

        public Criteria andJulAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jul_amount not between", value1, value2, "julAmount");
            return (Criteria) this;
        }

        public Criteria andAugHeadIsNull() {
            addCriterion("aug_head is null");
            return (Criteria) this;
        }

        public Criteria andAugHeadIsNotNull() {
            addCriterion("aug_head is not null");
            return (Criteria) this;
        }

        public Criteria andAugHeadEqualTo(Integer value) {
            addCriterion("aug_head =", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadNotEqualTo(Integer value) {
            addCriterion("aug_head <>", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadGreaterThan(Integer value) {
            addCriterion("aug_head >", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("aug_head >=", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadLessThan(Integer value) {
            addCriterion("aug_head <", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadLessThanOrEqualTo(Integer value) {
            addCriterion("aug_head <=", value, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadIn(List<Integer> values) {
            addCriterion("aug_head in", values, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadNotIn(List<Integer> values) {
            addCriterion("aug_head not in", values, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadBetween(Integer value1, Integer value2) {
            addCriterion("aug_head between", value1, value2, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("aug_head not between", value1, value2, "augHead");
            return (Criteria) this;
        }

        public Criteria andAugAmountIsNull() {
            addCriterion("aug_amount is null");
            return (Criteria) this;
        }

        public Criteria andAugAmountIsNotNull() {
            addCriterion("aug_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAugAmountEqualTo(BigDecimal value) {
            addCriterion("aug_amount =", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountNotEqualTo(BigDecimal value) {
            addCriterion("aug_amount <>", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountGreaterThan(BigDecimal value) {
            addCriterion("aug_amount >", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("aug_amount >=", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountLessThan(BigDecimal value) {
            addCriterion("aug_amount <", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("aug_amount <=", value, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountIn(List<BigDecimal> values) {
            addCriterion("aug_amount in", values, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountNotIn(List<BigDecimal> values) {
            addCriterion("aug_amount not in", values, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aug_amount between", value1, value2, "augAmount");
            return (Criteria) this;
        }

        public Criteria andAugAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aug_amount not between", value1, value2, "augAmount");
            return (Criteria) this;
        }

        public Criteria andSepHeadIsNull() {
            addCriterion("sep_head is null");
            return (Criteria) this;
        }

        public Criteria andSepHeadIsNotNull() {
            addCriterion("sep_head is not null");
            return (Criteria) this;
        }

        public Criteria andSepHeadEqualTo(Integer value) {
            addCriterion("sep_head =", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadNotEqualTo(Integer value) {
            addCriterion("sep_head <>", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadGreaterThan(Integer value) {
            addCriterion("sep_head >", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("sep_head >=", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadLessThan(Integer value) {
            addCriterion("sep_head <", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadLessThanOrEqualTo(Integer value) {
            addCriterion("sep_head <=", value, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadIn(List<Integer> values) {
            addCriterion("sep_head in", values, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadNotIn(List<Integer> values) {
            addCriterion("sep_head not in", values, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadBetween(Integer value1, Integer value2) {
            addCriterion("sep_head between", value1, value2, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("sep_head not between", value1, value2, "sepHead");
            return (Criteria) this;
        }

        public Criteria andSepAmountIsNull() {
            addCriterion("sep_amount is null");
            return (Criteria) this;
        }

        public Criteria andSepAmountIsNotNull() {
            addCriterion("sep_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSepAmountEqualTo(BigDecimal value) {
            addCriterion("sep_amount =", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountNotEqualTo(BigDecimal value) {
            addCriterion("sep_amount <>", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountGreaterThan(BigDecimal value) {
            addCriterion("sep_amount >", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sep_amount >=", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountLessThan(BigDecimal value) {
            addCriterion("sep_amount <", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sep_amount <=", value, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountIn(List<BigDecimal> values) {
            addCriterion("sep_amount in", values, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountNotIn(List<BigDecimal> values) {
            addCriterion("sep_amount not in", values, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sep_amount between", value1, value2, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andSepAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sep_amount not between", value1, value2, "sepAmount");
            return (Criteria) this;
        }

        public Criteria andOctHeadIsNull() {
            addCriterion("oct_head is null");
            return (Criteria) this;
        }

        public Criteria andOctHeadIsNotNull() {
            addCriterion("oct_head is not null");
            return (Criteria) this;
        }

        public Criteria andOctHeadEqualTo(Integer value) {
            addCriterion("oct_head =", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadNotEqualTo(Integer value) {
            addCriterion("oct_head <>", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadGreaterThan(Integer value) {
            addCriterion("oct_head >", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("oct_head >=", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadLessThan(Integer value) {
            addCriterion("oct_head <", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadLessThanOrEqualTo(Integer value) {
            addCriterion("oct_head <=", value, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadIn(List<Integer> values) {
            addCriterion("oct_head in", values, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadNotIn(List<Integer> values) {
            addCriterion("oct_head not in", values, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadBetween(Integer value1, Integer value2) {
            addCriterion("oct_head between", value1, value2, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("oct_head not between", value1, value2, "octHead");
            return (Criteria) this;
        }

        public Criteria andOctAmountIsNull() {
            addCriterion("oct_amount is null");
            return (Criteria) this;
        }

        public Criteria andOctAmountIsNotNull() {
            addCriterion("oct_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOctAmountEqualTo(BigDecimal value) {
            addCriterion("oct_amount =", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountNotEqualTo(BigDecimal value) {
            addCriterion("oct_amount <>", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountGreaterThan(BigDecimal value) {
            addCriterion("oct_amount >", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("oct_amount >=", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountLessThan(BigDecimal value) {
            addCriterion("oct_amount <", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("oct_amount <=", value, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountIn(List<BigDecimal> values) {
            addCriterion("oct_amount in", values, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountNotIn(List<BigDecimal> values) {
            addCriterion("oct_amount not in", values, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("oct_amount between", value1, value2, "octAmount");
            return (Criteria) this;
        }

        public Criteria andOctAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("oct_amount not between", value1, value2, "octAmount");
            return (Criteria) this;
        }

        public Criteria andNovHeadIsNull() {
            addCriterion("nov_head is null");
            return (Criteria) this;
        }

        public Criteria andNovHeadIsNotNull() {
            addCriterion("nov_head is not null");
            return (Criteria) this;
        }

        public Criteria andNovHeadEqualTo(Integer value) {
            addCriterion("nov_head =", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadNotEqualTo(Integer value) {
            addCriterion("nov_head <>", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadGreaterThan(Integer value) {
            addCriterion("nov_head >", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("nov_head >=", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadLessThan(Integer value) {
            addCriterion("nov_head <", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadLessThanOrEqualTo(Integer value) {
            addCriterion("nov_head <=", value, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadIn(List<Integer> values) {
            addCriterion("nov_head in", values, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadNotIn(List<Integer> values) {
            addCriterion("nov_head not in", values, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadBetween(Integer value1, Integer value2) {
            addCriterion("nov_head between", value1, value2, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("nov_head not between", value1, value2, "novHead");
            return (Criteria) this;
        }

        public Criteria andNovAmountIsNull() {
            addCriterion("nov_amount is null");
            return (Criteria) this;
        }

        public Criteria andNovAmountIsNotNull() {
            addCriterion("nov_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNovAmountEqualTo(BigDecimal value) {
            addCriterion("nov_amount =", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountNotEqualTo(BigDecimal value) {
            addCriterion("nov_amount <>", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountGreaterThan(BigDecimal value) {
            addCriterion("nov_amount >", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("nov_amount >=", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountLessThan(BigDecimal value) {
            addCriterion("nov_amount <", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("nov_amount <=", value, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountIn(List<BigDecimal> values) {
            addCriterion("nov_amount in", values, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountNotIn(List<BigDecimal> values) {
            addCriterion("nov_amount not in", values, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nov_amount between", value1, value2, "novAmount");
            return (Criteria) this;
        }

        public Criteria andNovAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nov_amount not between", value1, value2, "novAmount");
            return (Criteria) this;
        }

        public Criteria andDecHeadIsNull() {
            addCriterion("dec_head is null");
            return (Criteria) this;
        }

        public Criteria andDecHeadIsNotNull() {
            addCriterion("dec_head is not null");
            return (Criteria) this;
        }

        public Criteria andDecHeadEqualTo(Integer value) {
            addCriterion("dec_head =", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadNotEqualTo(Integer value) {
            addCriterion("dec_head <>", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadGreaterThan(Integer value) {
            addCriterion("dec_head >", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("dec_head >=", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadLessThan(Integer value) {
            addCriterion("dec_head <", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadLessThanOrEqualTo(Integer value) {
            addCriterion("dec_head <=", value, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadIn(List<Integer> values) {
            addCriterion("dec_head in", values, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadNotIn(List<Integer> values) {
            addCriterion("dec_head not in", values, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadBetween(Integer value1, Integer value2) {
            addCriterion("dec_head between", value1, value2, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("dec_head not between", value1, value2, "decHead");
            return (Criteria) this;
        }

        public Criteria andDecAmountIsNull() {
            addCriterion("dec_amount is null");
            return (Criteria) this;
        }

        public Criteria andDecAmountIsNotNull() {
            addCriterion("dec_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDecAmountEqualTo(BigDecimal value) {
            addCriterion("dec_amount =", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountNotEqualTo(BigDecimal value) {
            addCriterion("dec_amount <>", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountGreaterThan(BigDecimal value) {
            addCriterion("dec_amount >", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dec_amount >=", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountLessThan(BigDecimal value) {
            addCriterion("dec_amount <", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dec_amount <=", value, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountIn(List<BigDecimal> values) {
            addCriterion("dec_amount in", values, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountNotIn(List<BigDecimal> values) {
            addCriterion("dec_amount not in", values, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dec_amount between", value1, value2, "decAmount");
            return (Criteria) this;
        }

        public Criteria andDecAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dec_amount not between", value1, value2, "decAmount");
            return (Criteria) this;
        }

        public Criteria andTotalHeadIsNull() {
            addCriterion("total_head is null");
            return (Criteria) this;
        }

        public Criteria andTotalHeadIsNotNull() {
            addCriterion("total_head is not null");
            return (Criteria) this;
        }

        public Criteria andTotalHeadEqualTo(Integer value) {
            addCriterion("total_head =", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadNotEqualTo(Integer value) {
            addCriterion("total_head <>", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadGreaterThan(Integer value) {
            addCriterion("total_head >", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_head >=", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadLessThan(Integer value) {
            addCriterion("total_head <", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadLessThanOrEqualTo(Integer value) {
            addCriterion("total_head <=", value, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadIn(List<Integer> values) {
            addCriterion("total_head in", values, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadNotIn(List<Integer> values) {
            addCriterion("total_head not in", values, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadBetween(Integer value1, Integer value2) {
            addCriterion("total_head between", value1, value2, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalHeadNotBetween(Integer value1, Integer value2) {
            addCriterion("total_head not between", value1, value2, "totalHead");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearIsNull() {
            addCriterion("bonus_half_year is null");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearIsNotNull() {
            addCriterion("bonus_half_year is not null");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearEqualTo(BigDecimal value) {
            addCriterion("bonus_half_year =", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearNotEqualTo(BigDecimal value) {
            addCriterion("bonus_half_year <>", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearGreaterThan(BigDecimal value) {
            addCriterion("bonus_half_year >", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_half_year >=", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearLessThan(BigDecimal value) {
            addCriterion("bonus_half_year <", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_half_year <=", value, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearIn(List<BigDecimal> values) {
            addCriterion("bonus_half_year in", values, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearNotIn(List<BigDecimal> values) {
            addCriterion("bonus_half_year not in", values, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_half_year between", value1, value2, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusHalfYearNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_half_year not between", value1, value2, "bonusHalfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearIsNull() {
            addCriterion("bonus_end_of_year is null");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearIsNotNull() {
            addCriterion("bonus_end_of_year is not null");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearEqualTo(BigDecimal value) {
            addCriterion("bonus_end_of_year =", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearNotEqualTo(BigDecimal value) {
            addCriterion("bonus_end_of_year <>", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearGreaterThan(BigDecimal value) {
            addCriterion("bonus_end_of_year >", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_end_of_year >=", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearLessThan(BigDecimal value) {
            addCriterion("bonus_end_of_year <", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_end_of_year <=", value, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearIn(List<BigDecimal> values) {
            addCriterion("bonus_end_of_year in", values, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearNotIn(List<BigDecimal> values) {
            addCriterion("bonus_end_of_year not in", values, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_end_of_year between", value1, value2, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusEndOfYearNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_end_of_year not between", value1, value2, "bonusEndOfYear");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryIsNull() {
            addCriterion("bonus_annual_salary is null");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryIsNotNull() {
            addCriterion("bonus_annual_salary is not null");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryEqualTo(BigDecimal value) {
            addCriterion("bonus_annual_salary =", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryNotEqualTo(BigDecimal value) {
            addCriterion("bonus_annual_salary <>", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryGreaterThan(BigDecimal value) {
            addCriterion("bonus_annual_salary >", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_annual_salary >=", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryLessThan(BigDecimal value) {
            addCriterion("bonus_annual_salary <", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_annual_salary <=", value, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryIn(List<BigDecimal> values) {
            addCriterion("bonus_annual_salary in", values, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryNotIn(List<BigDecimal> values) {
            addCriterion("bonus_annual_salary not in", values, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_annual_salary between", value1, value2, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusAnnualSalaryNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_annual_salary not between", value1, value2, "bonusAnnualSalary");
            return (Criteria) this;
        }

        public Criteria andBonusTotalIsNull() {
            addCriterion("bonus_total is null");
            return (Criteria) this;
        }

        public Criteria andBonusTotalIsNotNull() {
            addCriterion("bonus_total is not null");
            return (Criteria) this;
        }

        public Criteria andBonusTotalEqualTo(BigDecimal value) {
            addCriterion("bonus_total =", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalNotEqualTo(BigDecimal value) {
            addCriterion("bonus_total <>", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalGreaterThan(BigDecimal value) {
            addCriterion("bonus_total >", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_total >=", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalLessThan(BigDecimal value) {
            addCriterion("bonus_total <", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bonus_total <=", value, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalIn(List<BigDecimal> values) {
            addCriterion("bonus_total in", values, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalNotIn(List<BigDecimal> values) {
            addCriterion("bonus_total not in", values, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_total between", value1, value2, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andBonusTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bonus_total not between", value1, value2, "bonusTotal");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionIsNull() {
            addCriterion("delete_version is null");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionIsNotNull() {
            addCriterion("delete_version is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionEqualTo(Integer value) {
            addCriterion("delete_version =", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionNotEqualTo(Integer value) {
            addCriterion("delete_version <>", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionGreaterThan(Integer value) {
            addCriterion("delete_version >", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_version >=", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionLessThan(Integer value) {
            addCriterion("delete_version <", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionLessThanOrEqualTo(Integer value) {
            addCriterion("delete_version <=", value, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionIn(List<Integer> values) {
            addCriterion("delete_version in", values, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionNotIn(List<Integer> values) {
            addCriterion("delete_version not in", values, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionBetween(Integer value1, Integer value2) {
            addCriterion("delete_version between", value1, value2, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeleteVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_version not between", value1, value2, "deleteVersion");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_hr_salary_outs_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}