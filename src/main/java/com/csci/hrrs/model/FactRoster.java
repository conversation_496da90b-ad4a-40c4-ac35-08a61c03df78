package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("fact_roster")
@Data
public class FactRoster {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.sort
     *
     * @mbg.generated
     */
    @TableField("sort")
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERNR
     *
     * @mbg.generated
     */
    @TableField("PERNR")
    private String pernr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.name
     *
     * @mbg.generated
     */
    @TableField("name")
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.englishname
     *
     * @mbg.generated
     */
    @TableField("englishname")
    private String englishname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.company
     *
     * @mbg.generated
     */
    @TableField("company")
    private String company;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.department
     *
     * @mbg.generated
     */
    @TableField("department")
    private String department;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.project
     *
     * @mbg.generated
     */
    @TableField("project")
    private String project;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job
     *
     * @mbg.generated
     */
    @TableField("job")
    private String job;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.parttime_job
     *
     * @mbg.generated
     */
    @TableField("parttime_job")
    private String parttimeJob;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_responsible
     *
     * @mbg.generated
     */
    @TableField("is_responsible")
    private String isResponsible;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.work_place
     *
     * @mbg.generated
     */
    @TableField("work_place")
    private String workPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_type
     *
     * @mbg.generated
     */
    @TableField("job_type")
    private String jobType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.speciality
     *
     * @mbg.generated
     */
    @TableField("speciality")
    private String speciality;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_level_level
     *
     * @mbg.generated
     */
    @TableField("job_level_level")
    private String jobLevelLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_level
     *
     * @mbg.generated
     */
    @TableField("job_level")
    private String jobLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.rank
     *
     * @mbg.generated
     */
    @TableField("rank")
    private String rank;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.rank_hk
     *
     * @mbg.generated
     */
    @TableField("rank_hk")
    private String rankHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.sex
     *
     * @mbg.generated
     */
    @TableField("sex")
    private String sex;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.star_work_date
     *
     * @mbg.generated
     */
    @TableField("star_work_date")
    private String starWorkDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.join_zh_date
     *
     * @mbg.generated
     */
    @TableField("join_zh_date")
    private String joinZhDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.join_3311_date
     *
     * @mbg.generated
     */
    @TableField("join_3311_date")
    private String join3311Date;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.abroad_date
     *
     * @mbg.generated
     */
    @TableField("abroad_date")
    private String abroadDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_position_date
     *
     * @mbg.generated
     */
    @TableField("current_position_date")
    private String currentPositionDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_joblevel_date
     *
     * @mbg.generated
     */
    @TableField("current_joblevel_date")
    private String currentJoblevelDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_rank_date
     *
     * @mbg.generated
     */
    @TableField("current_rank_date")
    private String currentRankDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.birthdate
     *
     * @mbg.generated
     */
    @TableField("birthdate")
    private String birthdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.age
     *
     * @mbg.generated
     */
    @TableField("age")
    private Integer age;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.nation
     *
     * @mbg.generated
     */
    @TableField("nation")
    private String nation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.native_place
     *
     * @mbg.generated
     */
    @TableField("native_place")
    private String nativePlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.birth_place
     *
     * @mbg.generated
     */
    @TableField("birth_place")
    private String birthPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.family_place
     *
     * @mbg.generated
     */
    @TableField("family_place")
    private String familyPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.marriage
     *
     * @mbg.generated
     */
    @TableField("marriage")
    private String marriage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.give_birth_to
     *
     * @mbg.generated
     */
    @TableField("give_birth_to")
    private String giveBirthTo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.edu
     *
     * @mbg.generated
     */
    @TableField("edu")
    private String edu;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.edu_school
     *
     * @mbg.generated
     */
    @TableField("edu_school")
    private String eduSchool;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.title
     *
     * @mbg.generated
     */
    @TableField("title")
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.prac_qualification
     *
     * @mbg.generated
     */
    @TableField("prac_qualification")
    private String pracQualification;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.source
     *
     * @mbg.generated
     */
    @TableField("source")
    private String source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.ADname
     *
     * @mbg.generated
     */
    @TableField("ADname")
    private String adname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.family_Phone
     *
     * @mbg.generated
     */
    @TableField("family_Phone")
    private String familyPhone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.person_email
     *
     * @mbg.generated
     */
    @TableField("person_email")
    private String personEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.wechat
     *
     * @mbg.generated
     */
    @TableField("wechat")
    private String wechat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.phone
     *
     * @mbg.generated
     */
    @TableField("phone")
    private String phone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.email
     *
     * @mbg.generated
     */
    @TableField("email")
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.staff_class3311
     *
     * @mbg.generated
     */
    @TableField("staff_class3311")
    private String staffClass3311;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.hzz_year
     *
     * @mbg.generated
     */
    @TableField("hzz_year")
    private String hzzYear;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.hzz_sort
     *
     * @mbg.generated
     */
    @TableField("hzz_sort")
    private String hzzSort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.politic
     *
     * @mbg.generated
     */
    @TableField("politic")
    private String politic;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.date_party
     *
     * @mbg.generated
     */
    @TableField("date_party")
    private String dateParty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.interest
     *
     * @mbg.generated
     */
    @TableField("interest")
    private String interest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.spouse_place
     *
     * @mbg.generated
     */
    @TableField("spouse_place")
    private String spousePlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.parent_place
     *
     * @mbg.generated
     */
    @TableField("parent_place")
    private String parentPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.blood_type
     *
     * @mbg.generated
     */
    @TableField("blood_type")
    private String bloodType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.blood_type_M
     *
     * @mbg.generated
     */
    @TableField("blood_type_M")
    private String bloodTypeM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.blood_type_E
     *
     * @mbg.generated
     */
    @TableField("blood_type_E")
    private String bloodTypeE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.health
     *
     * @mbg.generated
     */
    @TableField("health")
    private String health;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.health_M
     *
     * @mbg.generated
     */
    @TableField("health_M")
    private String healthM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.health_E
     *
     * @mbg.generated
     */
    @TableField("health_E")
    private String healthE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.files_place
     *
     * @mbg.generated
     */
    @TableField("files_place")
    private String filesPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.resident_place
     *
     * @mbg.generated
     */
    @TableField("resident_place")
    private String residentPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.resident_type
     *
     * @mbg.generated
     */
    @TableField("resident_type")
    private String residentType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.resident_type_M
     *
     * @mbg.generated
     */
    @TableField("resident_type_M")
    private String residentTypeM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.resident_type_E
     *
     * @mbg.generated
     */
    @TableField("resident_type_E")
    private String residentTypeE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.cardid_inland
     *
     * @mbg.generated
     */
    @TableField("cardid_inland")
    private String cardidInland;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.cardid_hm
     *
     * @mbg.generated
     */
    @TableField("cardid_hm")
    private String cardidHm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.cardid_hm_expdate
     *
     * @mbg.generated
     */
    @TableField("cardid_hm_expdate")
    private String cardidHmExpdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.cardid_outland
     *
     * @mbg.generated
     */
    @TableField("cardid_outland")
    private String cardidOutland;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.entry_type
     *
     * @mbg.generated
     */
    @TableField("entry_type")
    private String entryType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.entry_type_M
     *
     * @mbg.generated
     */
    @TableField("entry_type_M")
    private String entryTypeM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.entry_type_E
     *
     * @mbg.generated
     */
    @TableField("entry_type_E")
    private String entryTypeE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.social_care_company
     *
     * @mbg.generated
     */
    @TableField("social_care_company")
    private String socialCareCompany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.acc_fund_company
     *
     * @mbg.generated
     */
    @TableField("acc_fund_company")
    private String accFundCompany;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.notes
     *
     * @mbg.generated
     */
    @TableField("notes")
    private String notes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.notes_M
     *
     * @mbg.generated
     */
    @TableField("notes_M")
    private String notesM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.notes_E
     *
     * @mbg.generated
     */
    @TableField("notes_E")
    private String notesE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.ORGEH
     *
     * @mbg.generated
     */
    @TableField("ORGEH")
    private String orgeh;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.BUKRS
     *
     * @mbg.generated
     */
    @TableField("BUKRS")
    private String bukrs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.ABKRS
     *
     * @mbg.generated
     */
    @TableField("ABKRS")
    private String abkrs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.WERKS
     *
     * @mbg.generated
     */
    @TableField("WERKS")
    private String werks;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.BTRTL
     *
     * @mbg.generated
     */
    @TableField("BTRTL")
    private String btrtl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSG
     *
     * @mbg.generated
     */
    @TableField("PERSG")
    private String persg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSGTEXT
     *
     * @mbg.generated
     */
    @TableField("PERSGTEXT")
    private String persgtext;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSKL
     *
     * @mbg.generated
     */
    @TableField("PERSKL")
    private String perskl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSKTEXT
     *
     * @mbg.generated
     */
    @TableField("PERSKTEXT")
    private String persktext;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.recoverydate
     *
     * @mbg.generated
     */
    @TableField("recoverydate")
    private String recoverydate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.recoverymonth
     *
     * @mbg.generated
     */
    @TableField("recoverymonth")
    private String recoverymonth;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.dimissiondate
     *
     * @mbg.generated
     */
    @TableField("dimissiondate")
    private String dimissiondate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.nation_M
     *
     * @mbg.generated
     */
    @TableField("nation_M")
    private String nationM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.nation_E
     *
     * @mbg.generated
     */
    @TableField("nation_E")
    private String nationE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.politic_M
     *
     * @mbg.generated
     */
    @TableField("politic_M")
    private String politicM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.politic_E
     *
     * @mbg.generated
     */
    @TableField("politic_E")
    private String politicE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.company_M
     *
     * @mbg.generated
     */
    @TableField("company_M")
    private String companyM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.company_E
     *
     * @mbg.generated
     */
    @TableField("company_E")
    private String companyE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.department_M
     *
     * @mbg.generated
     */
    @TableField("department_M")
    private String departmentM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.department_E
     *
     * @mbg.generated
     */
    @TableField("department_E")
    private String departmentE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.project_M
     *
     * @mbg.generated
     */
    @TableField("project_M")
    private String projectM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.project_E
     *
     * @mbg.generated
     */
    @TableField("project_E")
    private String projectE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.talent
     *
     * @mbg.generated
     */
    @TableField("talent")
    private String talent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.appraisal_1
     *
     * @mbg.generated
     */
    @TableField("appraisal_1")
    private String appraisal1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.appraisal_2
     *
     * @mbg.generated
     */
    @TableField("appraisal_2")
    private String appraisal2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.appraisal_3
     *
     * @mbg.generated
     */
    @TableField("appraisal_3")
    private String appraisal3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_responsible_M
     *
     * @mbg.generated
     */
    @TableField("is_responsible_M")
    private String isResponsibleM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_responsible_E
     *
     * @mbg.generated
     */
    @TableField("is_responsible_E")
    private String isResponsibleE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_type_M
     *
     * @mbg.generated
     */
    @TableField("job_type_M")
    private String jobTypeM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_type_E
     *
     * @mbg.generated
     */
    @TableField("job_type_E")
    private String jobTypeE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.sex_M
     *
     * @mbg.generated
     */
    @TableField("sex_M")
    private String sexM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.sex_E
     *
     * @mbg.generated
     */
    @TableField("sex_E")
    private String sexE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSGTEXT_M
     *
     * @mbg.generated
     */
    @TableField("PERSGTEXT_M")
    private String persgtextM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSGTEXT_E
     *
     * @mbg.generated
     */
    @TableField("PERSGTEXT_E")
    private String persgtextE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSKTEXT_M
     *
     * @mbg.generated
     */
    @TableField("PERSKTEXT_M")
    private String persktextM;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.PERSKTEXT_E
     *
     * @mbg.generated
     */
    @TableField("PERSKTEXT_E")
    private String persktextE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_position_seniority
     *
     * @mbg.generated
     */
    @TableField("current_position_seniority")
    private Float currentPositionSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_joblevel_seniority
     *
     * @mbg.generated
     */
    @TableField("current_joblevel_seniority")
    private Float currentJoblevelSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.current_rank_seniority
     *
     * @mbg.generated
     */
    @TableField("current_rank_seniority")
    private Float currentRankSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.photo
     *
     * @mbg.generated
     */
    @TableField("photo")
    private String photo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_abroad_exp
     *
     * @mbg.generated
     */
    @TableField("is_abroad_exp")
    private Integer isAbroadExp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.family_place_hm
     *
     * @mbg.generated
     */
    @TableField("family_place_hm")
    private String familyPlaceHm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Phone_hm
     *
     * @mbg.generated
     */
    @TableField("Phone_hm")
    private String phoneHm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.ZZ_SJSJ
     *
     * @mbg.generated
     */
    @TableField("ZZ_SJSJ")
    private String zzSjsj;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.CTEDT
     *
     * @mbg.generated
     */
    @TableField("CTEDT")
    private String ctedt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.full_work_place
     *
     * @mbg.generated
     */
    @TableField("full_work_place")
    private String fullWorkPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.full_family_place
     *
     * @mbg.generated
     */
    @TableField("full_family_place")
    private String fullFamilyPlace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.full_family_place_hm
     *
     * @mbg.generated
     */
    @TableField("full_family_place_hm")
    private String fullFamilyPlaceHm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.social_service
     *
     * @mbg.generated
     */
    @TableField("social_service")
    private String socialService;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.province
     *
     * @mbg.generated
     */
    @TableField("province")
    private String province;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Work_Seniority
     *
     * @mbg.generated
     */
    @TableField("Work_Seniority")
    private Float workSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.join_zh_Seniority
     *
     * @mbg.generated
     */
    @TableField("join_zh_Seniority")
    private Float joinZhSeniority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.XZ
     *
     * @mbg.generated
     */
    @TableField("XZ")
    private String xz;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Office_Phone
     *
     * @mbg.generated
     */
    @TableField("Office_Phone")
    private String officePhone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.JoinSysDate
     *
     * @mbg.generated
     */
    @TableField("JoinSysDate")
    private LocalDate joinsysdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_code
     *
     * @mbg.generated
     */
    @TableField("job_code")
    private String jobCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.hire_type
     *
     * @mbg.generated
     */
    @TableField("hire_type")
    private String hireType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.manage_type
     *
     * @mbg.generated
     */
    @TableField("manage_type")
    private String manageType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.zw_code
     *
     * @mbg.generated
     */
    @TableField("zw_code")
    private String zwCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.IsOnJob
     *
     * @mbg.generated
     */
    @TableField("IsOnJob")
    private Integer isonjob;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.recruit_type
     *
     * @mbg.generated
     */
    @TableField("recruit_type")
    private String recruitType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.BeforeCohlUnit
     *
     * @mbg.generated
     */
    @TableField("BeforeCohlUnit")
    private String beforecohlunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_level_GP
     *
     * @mbg.generated
     */
    @TableField("job_level_GP")
    private String jobLevelGp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_carders
     *
     * @mbg.generated
     */
    @TableField("is_carders")
    private Integer isCarders;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.is_hzz
     *
     * @mbg.generated
     */
    @TableField("is_hzz")
    private Integer isHzz;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Edu_All
     *
     * @mbg.generated
     */
    @TableField("Edu_All")
    private String eduAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.School_All
     *
     * @mbg.generated
     */
    @TableField("School_All")
    private String schoolAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Major_All
     *
     * @mbg.generated
     */
    @TableField("Major_All")
    private String majorAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Graduation_Date
     *
     * @mbg.generated
     */
    @TableField("Graduation_Date")
    private String graduationDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.SPECIAL_LINE
     *
     * @mbg.generated
     */
    @TableField("SPECIAL_LINE")
    private String specialLine;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Staffclass
     *
     * @mbg.generated
     */
    @TableField("Staffclass")
    private String staffclass;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Nationality
     *
     * @mbg.generated
     */
    @TableField("Nationality")
    private String nationality;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.Name_Simplified
     *
     * @mbg.generated
     */
    @TableField("Name_Simplified")
    private String nameSimplified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.JobLevelName
     *
     * @mbg.generated
     */
    @TableField("JobLevelName")
    private String joblevelname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.JobLevelPilot
     *
     * @mbg.generated
     */
    @TableField("JobLevelPilot")
    private String joblevelpilot;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.StaffClass_Macau
     *
     * @mbg.generated
     */
    @TableField("StaffClass_Macau")
    private String staffclassMacau;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.company_origin
     *
     * @mbg.generated
     */
    @TableField("company_origin")
    private String companyOrigin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.department_origin
     *
     * @mbg.generated
     */
    @TableField("department_origin")
    private String departmentOrigin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.project_origin
     *
     * @mbg.generated
     */
    @TableField("project_origin")
    private String projectOrigin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.height
     *
     * @mbg.generated
     */
    @TableField("height")
    private String height;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.cardid_hm_cdexpdate
     *
     * @mbg.generated
     */
    @TableField("cardid_hm_cdexpdate")
    private String cardidHmCdexpdate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_type_HK
     *
     * @mbg.generated
     */
    @TableField("job_type_HK")
    private String jobTypeHk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.updatetime
     *
     * @mbg.generated
     */
    @TableField("updatetime")
    private LocalDateTime updatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.bank
     *
     * @mbg.generated
     */
    @TableField("bank")
    private String bank;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.date_party_REG
     *
     * @mbg.generated
     */
    @TableField("date_party_REG")
    private String datePartyReg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.englishname_s
     *
     * @mbg.generated
     */
    @TableField("englishname_s")
    private String englishnameS;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.source_rcbq
     *
     * @mbg.generated
     */
    @TableField("source_rcbq")
    private String sourceRcbq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fact_roster.job_type_ND
     *
     * @mbg.generated
     */
    @TableField("job_type_ND")
    private String jobTypeNd;

    @TableField("join_830_date")
    private String join830Date;

    // 转正类型
    @TableField("ZZLX_T")
    private String zzType;

    // 合约约定转正时间
    @TableField("HT_ZZSJ")
    private String contractZzDate;

    // 最近到港日期
    @TableField("latestArriveHkDate")
    private String latestArriveHkDate;

    @TableField("HeadcountType_830")
    private String headcountType830;

    @TableField("ZZ_MZ")
    private String ethnicityCode;

    @TableField("ZZ_XX")
    private String bloodTypeCode;

    @TableField("ZZ_HYZK")
    private String maritalStatusCode;

}