package com.csci.hrrs.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AuditNodeExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public AuditNodeExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNull() {
			addCriterion("organization_id is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNotNull() {
			addCriterion("organization_id is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdEqualTo(String value) {
			addCriterion("organization_id =", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotEqualTo(String value) {
			addCriterion("organization_id <>", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThan(String value) {
			addCriterion("organization_id >", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
			addCriterion("organization_id >=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThan(String value) {
			addCriterion("organization_id <", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
			addCriterion("organization_id <=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLike(String value) {
			addCriterion("organization_id like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotLike(String value) {
			addCriterion("organization_id not like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIn(List<String> values) {
			addCriterion("organization_id in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotIn(List<String> values) {
			addCriterion("organization_id not in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdBetween(String value1, String value2) {
			addCriterion("organization_id between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotBetween(String value1, String value2) {
			addCriterion("organization_id not between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andFormCodeIsNull() {
			addCriterion("form_code is null");
			return (Criteria) this;
		}

		public Criteria andFormCodeIsNotNull() {
			addCriterion("form_code is not null");
			return (Criteria) this;
		}

		public Criteria andFormCodeEqualTo(String value) {
			addCriterion("form_code =", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotEqualTo(String value) {
			addCriterion("form_code <>", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeGreaterThan(String value) {
			addCriterion("form_code >", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeGreaterThanOrEqualTo(String value) {
			addCriterion("form_code >=", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLessThan(String value) {
			addCriterion("form_code <", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLessThanOrEqualTo(String value) {
			addCriterion("form_code <=", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLike(String value) {
			addCriterion("form_code like", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotLike(String value) {
			addCriterion("form_code not like", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeIn(List<String> values) {
			addCriterion("form_code in", values, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotIn(List<String> values) {
			addCriterion("form_code not in", values, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeBetween(String value1, String value2) {
			addCriterion("form_code between", value1, value2, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotBetween(String value1, String value2) {
			addCriterion("form_code not between", value1, value2, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormIdIsNull() {
			addCriterion("form_id is null");
			return (Criteria) this;
		}

		public Criteria andFormIdIsNotNull() {
			addCriterion("form_id is not null");
			return (Criteria) this;
		}

		public Criteria andFormIdEqualTo(String value) {
			addCriterion("form_id =", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdNotEqualTo(String value) {
			addCriterion("form_id <>", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdGreaterThan(String value) {
			addCriterion("form_id >", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdGreaterThanOrEqualTo(String value) {
			addCriterion("form_id >=", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdLessThan(String value) {
			addCriterion("form_id <", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdLessThanOrEqualTo(String value) {
			addCriterion("form_id <=", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdLike(String value) {
			addCriterion("form_id like", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdNotLike(String value) {
			addCriterion("form_id not like", value, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdIn(List<String> values) {
			addCriterion("form_id in", values, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdNotIn(List<String> values) {
			addCriterion("form_id not in", values, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdBetween(String value1, String value2) {
			addCriterion("form_id between", value1, value2, "formId");
			return (Criteria) this;
		}

		public Criteria andFormIdNotBetween(String value1, String value2) {
			addCriterion("form_id not between", value1, value2, "formId");
			return (Criteria) this;
		}

		public Criteria andUserIdIsNull() {
			addCriterion("user_id is null");
			return (Criteria) this;
		}

		public Criteria andUserIdIsNotNull() {
			addCriterion("user_id is not null");
			return (Criteria) this;
		}

		public Criteria andUserIdEqualTo(String value) {
			addCriterion("user_id =", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdNotEqualTo(String value) {
			addCriterion("user_id <>", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdGreaterThan(String value) {
			addCriterion("user_id >", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("user_id >=", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdLessThan(String value) {
			addCriterion("user_id <", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdLessThanOrEqualTo(String value) {
			addCriterion("user_id <=", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdLike(String value) {
			addCriterion("user_id like", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdNotLike(String value) {
			addCriterion("user_id not like", value, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdIn(List<String> values) {
			addCriterion("user_id in", values, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdNotIn(List<String> values) {
			addCriterion("user_id not in", values, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdBetween(String value1, String value2) {
			addCriterion("user_id between", value1, value2, "userId");
			return (Criteria) this;
		}

		public Criteria andUserIdNotBetween(String value1, String value2) {
			addCriterion("user_id not between", value1, value2, "userId");
			return (Criteria) this;
		}

		public Criteria andRemarkIsNull() {
			addCriterion("remark is null");
			return (Criteria) this;
		}

		public Criteria andRemarkIsNotNull() {
			addCriterion("remark is not null");
			return (Criteria) this;
		}

		public Criteria andRemarkEqualTo(String value) {
			addCriterion("remark =", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotEqualTo(String value) {
			addCriterion("remark <>", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkGreaterThan(String value) {
			addCriterion("remark >", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkGreaterThanOrEqualTo(String value) {
			addCriterion("remark >=", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLessThan(String value) {
			addCriterion("remark <", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLessThanOrEqualTo(String value) {
			addCriterion("remark <=", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLike(String value) {
			addCriterion("remark like", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotLike(String value) {
			addCriterion("remark not like", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkIn(List<String> values) {
			addCriterion("remark in", values, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotIn(List<String> values) {
			addCriterion("remark not in", values, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkBetween(String value1, String value2) {
			addCriterion("remark between", value1, value2, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotBetween(String value1, String value2) {
			addCriterion("remark not between", value1, value2, "remark");
			return (Criteria) this;
		}

		public Criteria andIsPassIsNull() {
			addCriterion("is_pass is null");
			return (Criteria) this;
		}

		public Criteria andIsPassIsNotNull() {
			addCriterion("is_pass is not null");
			return (Criteria) this;
		}

		public Criteria andIsPassEqualTo(Boolean value) {
			addCriterion("is_pass =", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassNotEqualTo(Boolean value) {
			addCriterion("is_pass <>", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassGreaterThan(Boolean value) {
			addCriterion("is_pass >", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_pass >=", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassLessThan(Boolean value) {
			addCriterion("is_pass <", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassLessThanOrEqualTo(Boolean value) {
			addCriterion("is_pass <=", value, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassIn(List<Boolean> values) {
			addCriterion("is_pass in", values, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassNotIn(List<Boolean> values) {
			addCriterion("is_pass not in", values, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassBetween(Boolean value1, Boolean value2) {
			addCriterion("is_pass between", value1, value2, "isPass");
			return (Criteria) this;
		}

		public Criteria andIsPassNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_pass not between", value1, value2, "isPass");
			return (Criteria) this;
		}

		public Criteria andSeqIsNull() {
			addCriterion("seq is null");
			return (Criteria) this;
		}

		public Criteria andSeqIsNotNull() {
			addCriterion("seq is not null");
			return (Criteria) this;
		}

		public Criteria andSeqEqualTo(Integer value) {
			addCriterion("seq =", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotEqualTo(Integer value) {
			addCriterion("seq <>", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThan(Integer value) {
			addCriterion("seq >", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
			addCriterion("seq >=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThan(Integer value) {
			addCriterion("seq <", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThanOrEqualTo(Integer value) {
			addCriterion("seq <=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqIn(List<Integer> values) {
			addCriterion("seq in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotIn(List<Integer> values) {
			addCriterion("seq not in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqBetween(Integer value1, Integer value2) {
			addCriterion("seq between", value1, value2, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotBetween(Integer value1, Integer value2) {
			addCriterion("seq not between", value1, value2, "seq");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_audit_node
	 * @mbg.generated  Thu Jun 16 16:22:53 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_audit_node
     *
     * @mbg.generated do_not_delete_during_merge Tue May 17 11:24:52 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}