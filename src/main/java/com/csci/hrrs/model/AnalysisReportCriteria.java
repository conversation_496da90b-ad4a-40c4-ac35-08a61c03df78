package com.csci.hrrs.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_analysis_report_criteria
 */
@TableName("t_analysis_report_criteria")
public class AnalysisReportCriteria {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.id
     *
     * @mbg.generated
     */
    @TableField("id")
    @TableId
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.indicator_group
     *
     * @mbg.generated
     */
    @TableField("indicator_group")
    private String indicatorGroup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.indicator_name
     *
     * @mbg.generated
     */
    @TableField("indicator_name")
    private String indicatorName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.comparison_item
     *
     * @mbg.generated
     */
    @TableField("comparison_item")
    private String comparisonItem;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.criteria_1
     *
     * @mbg.generated
     */
    @TableField("criteria_1")
    private String criteria1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.criteria_2
     *
     * @mbg.generated
     */
    @TableField("criteria_2")
    private String criteria2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.criteria_3
     *
     * @mbg.generated
     */
    @TableField("criteria_3")
    private String criteria3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.criteria_4
     *
     * @mbg.generated
     */
    @TableField("criteria_4")
    private String criteria4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.criteria_5
     *
     * @mbg.generated
     */
    @TableField("criteria_5")
    private String criteria5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.reference_value
     *
     * @mbg.generated
     */
    @TableField("reference_value")
    private String referenceValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.sort
     *
     * @mbg.generated
     */
    @TableField("sort")
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.comments
     *
     * @mbg.generated
     */
    @TableField("comments")
    private String comments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.is_deleted
     *
     * @mbg.generated
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.creation_time
     *
     * @mbg.generated
     */
    @TableField("creation_time")
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.create_user_id
     *
     * @mbg.generated
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.create_username
     *
     * @mbg.generated
     */
    @TableField("create_username")
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.last_update_time
     *
     * @mbg.generated
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.last_update_user_id
     *
     * @mbg.generated
     */
    @TableField("last_update_user_id")
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.last_update_username
     *
     * @mbg.generated
     */
    @TableField("last_update_username")
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_analysis_report_criteria.last_update_version
     *
     * @mbg.generated
     */
    @TableField("last_update_version")
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.id
     *
     * @return the value of t_analysis_report_criteria.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.id
     *
     * @param id the value for t_analysis_report_criteria.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.indicator_group
     *
     * @return the value of t_analysis_report_criteria.indicator_group
     *
     * @mbg.generated
     */
    public String getIndicatorGroup() {
        return indicatorGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.indicator_group
     *
     * @param indicatorGroup the value for t_analysis_report_criteria.indicator_group
     *
     * @mbg.generated
     */
    public void setIndicatorGroup(String indicatorGroup) {
        this.indicatorGroup = indicatorGroup == null ? null : indicatorGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.indicator_name
     *
     * @return the value of t_analysis_report_criteria.indicator_name
     *
     * @mbg.generated
     */
    public String getIndicatorName() {
        return indicatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.indicator_name
     *
     * @param indicatorName the value for t_analysis_report_criteria.indicator_name
     *
     * @mbg.generated
     */
    public void setIndicatorName(String indicatorName) {
        this.indicatorName = indicatorName == null ? null : indicatorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.comparison_item
     *
     * @return the value of t_analysis_report_criteria.comparison_item
     *
     * @mbg.generated
     */
    public String getComparisonItem() {
        return comparisonItem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.comparison_item
     *
     * @param comparisonItem the value for t_analysis_report_criteria.comparison_item
     *
     * @mbg.generated
     */
    public void setComparisonItem(String comparisonItem) {
        this.comparisonItem = comparisonItem == null ? null : comparisonItem.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.criteria_1
     *
     * @return the value of t_analysis_report_criteria.criteria_1
     *
     * @mbg.generated
     */
    public String getCriteria1() {
        return criteria1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.criteria_1
     *
     * @param criteria1 the value for t_analysis_report_criteria.criteria_1
     *
     * @mbg.generated
     */
    public void setCriteria1(String criteria1) {
        this.criteria1 = criteria1 == null ? null : criteria1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.criteria_2
     *
     * @return the value of t_analysis_report_criteria.criteria_2
     *
     * @mbg.generated
     */
    public String getCriteria2() {
        return criteria2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.criteria_2
     *
     * @param criteria2 the value for t_analysis_report_criteria.criteria_2
     *
     * @mbg.generated
     */
    public void setCriteria2(String criteria2) {
        this.criteria2 = criteria2 == null ? null : criteria2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.criteria_3
     *
     * @return the value of t_analysis_report_criteria.criteria_3
     *
     * @mbg.generated
     */
    public String getCriteria3() {
        return criteria3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.criteria_3
     *
     * @param criteria3 the value for t_analysis_report_criteria.criteria_3
     *
     * @mbg.generated
     */
    public void setCriteria3(String criteria3) {
        this.criteria3 = criteria3 == null ? null : criteria3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.criteria_4
     *
     * @return the value of t_analysis_report_criteria.criteria_4
     *
     * @mbg.generated
     */
    public String getCriteria4() {
        return criteria4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.criteria_4
     *
     * @param criteria4 the value for t_analysis_report_criteria.criteria_4
     *
     * @mbg.generated
     */
    public void setCriteria4(String criteria4) {
        this.criteria4 = criteria4 == null ? null : criteria4.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.criteria_5
     *
     * @return the value of t_analysis_report_criteria.criteria_5
     *
     * @mbg.generated
     */
    public String getCriteria5() {
        return criteria5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.criteria_5
     *
     * @param criteria5 the value for t_analysis_report_criteria.criteria_5
     *
     * @mbg.generated
     */
    public void setCriteria5(String criteria5) {
        this.criteria5 = criteria5 == null ? null : criteria5.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.reference_value
     *
     * @return the value of t_analysis_report_criteria.reference_value
     *
     * @mbg.generated
     */
    public String getReferenceValue() {
        return referenceValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.reference_value
     *
     * @param referenceValue the value for t_analysis_report_criteria.reference_value
     *
     * @mbg.generated
     */
    public void setReferenceValue(String referenceValue) {
        this.referenceValue = referenceValue == null ? null : referenceValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.sort
     *
     * @return the value of t_analysis_report_criteria.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.sort
     *
     * @param sort the value for t_analysis_report_criteria.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.comments
     *
     * @return the value of t_analysis_report_criteria.comments
     *
     * @mbg.generated
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.comments
     *
     * @param comments the value for t_analysis_report_criteria.comments
     *
     * @mbg.generated
     */
    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.is_deleted
     *
     * @return the value of t_analysis_report_criteria.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.is_deleted
     *
     * @param isDeleted the value for t_analysis_report_criteria.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.creation_time
     *
     * @return the value of t_analysis_report_criteria.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.creation_time
     *
     * @param creationTime the value for t_analysis_report_criteria.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.create_user_id
     *
     * @return the value of t_analysis_report_criteria.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.create_user_id
     *
     * @param createUserId the value for t_analysis_report_criteria.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.create_username
     *
     * @return the value of t_analysis_report_criteria.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.create_username
     *
     * @param createUsername the value for t_analysis_report_criteria.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.last_update_time
     *
     * @return the value of t_analysis_report_criteria.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.last_update_time
     *
     * @param lastUpdateTime the value for t_analysis_report_criteria.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.last_update_user_id
     *
     * @return the value of t_analysis_report_criteria.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_analysis_report_criteria.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.last_update_username
     *
     * @return the value of t_analysis_report_criteria.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.last_update_username
     *
     * @param lastUpdateUsername the value for t_analysis_report_criteria.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_analysis_report_criteria.last_update_version
     *
     * @return the value of t_analysis_report_criteria.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_analysis_report_criteria.last_update_version
     *
     * @param lastUpdateVersion the value for t_analysis_report_criteria.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}