package com.csci.hrrs.model;

import java.time.LocalDateTime;

public class ResourcesEnergyHead {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.head_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String headId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.organization_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String organizationId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.organization_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String organizationName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.form_code
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String formCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.form_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String formName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.reporting_year
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Integer reportingYear;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.reporting_month
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Integer reportingMonth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.reporting_date
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private LocalDateTime reportingDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String username;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.user_real_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String userRealName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.creation_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.create_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.last_update_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.last_update_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.last_update_version
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Integer lastUpdateVersion;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.is_submitted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Boolean isSubmitted;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.audit_node
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Integer auditNode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.is_audited
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Boolean isAudited;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_resources_energy_head.is_deleted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	private Boolean isDeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.id
	 * @return  the value of t_resources_energy_head.id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.id
	 * @param id  the value for t_resources_energy_head.id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.head_id
	 * @return  the value of t_resources_energy_head.head_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getHeadId() {
		return headId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.head_id
	 * @param headId  the value for t_resources_energy_head.head_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setHeadId(String headId) {
		this.headId = headId == null ? null : headId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.organization_id
	 * @return  the value of t_resources_energy_head.organization_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getOrganizationId() {
		return organizationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.organization_id
	 * @param organizationId  the value for t_resources_energy_head.organization_id
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId == null ? null : organizationId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.organization_name
	 * @return  the value of t_resources_energy_head.organization_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getOrganizationName() {
		return organizationName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.organization_name
	 * @param organizationName  the value for t_resources_energy_head.organization_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName == null ? null : organizationName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.form_code
	 * @return  the value of t_resources_energy_head.form_code
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getFormCode() {
		return formCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.form_code
	 * @param formCode  the value for t_resources_energy_head.form_code
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setFormCode(String formCode) {
		this.formCode = formCode == null ? null : formCode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.form_name
	 * @return  the value of t_resources_energy_head.form_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getFormName() {
		return formName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.form_name
	 * @param formName  the value for t_resources_energy_head.form_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setFormName(String formName) {
		this.formName = formName == null ? null : formName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.reporting_year
	 * @return  the value of t_resources_energy_head.reporting_year
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Integer getReportingYear() {
		return reportingYear;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.reporting_year
	 * @param reportingYear  the value for t_resources_energy_head.reporting_year
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setReportingYear(Integer reportingYear) {
		this.reportingYear = reportingYear;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.reporting_month
	 * @return  the value of t_resources_energy_head.reporting_month
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Integer getReportingMonth() {
		return reportingMonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.reporting_month
	 * @param reportingMonth  the value for t_resources_energy_head.reporting_month
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setReportingMonth(Integer reportingMonth) {
		this.reportingMonth = reportingMonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.reporting_date
	 * @return  the value of t_resources_energy_head.reporting_date
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public LocalDateTime getReportingDate() {
		return reportingDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.reporting_date
	 * @param reportingDate  the value for t_resources_energy_head.reporting_date
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setReportingDate(LocalDateTime reportingDate) {
		this.reportingDate = reportingDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.username
	 * @return  the value of t_resources_energy_head.username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getUsername() {
		return username;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.username
	 * @param username  the value for t_resources_energy_head.username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setUsername(String username) {
		this.username = username == null ? null : username.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.user_real_name
	 * @return  the value of t_resources_energy_head.user_real_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getUserRealName() {
		return userRealName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.user_real_name
	 * @param userRealName  the value for t_resources_energy_head.user_real_name
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setUserRealName(String userRealName) {
		this.userRealName = userRealName == null ? null : userRealName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.creation_time
	 * @return  the value of t_resources_energy_head.creation_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.creation_time
	 * @param creationTime  the value for t_resources_energy_head.creation_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.create_username
	 * @return  the value of t_resources_energy_head.create_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.create_username
	 * @param createUsername  the value for t_resources_energy_head.create_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.last_update_time
	 * @return  the value of t_resources_energy_head.last_update_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.last_update_time
	 * @param lastUpdateTime  the value for t_resources_energy_head.last_update_time
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.last_update_username
	 * @return  the value of t_resources_energy_head.last_update_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.last_update_username
	 * @param lastUpdateUsername  the value for t_resources_energy_head.last_update_username
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.last_update_version
	 * @return  the value of t_resources_energy_head.last_update_version
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.last_update_version
	 * @param lastUpdateVersion  the value for t_resources_energy_head.last_update_version
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.is_submitted
	 * @return  the value of t_resources_energy_head.is_submitted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Boolean getIsSubmitted() {
		return isSubmitted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.is_submitted
	 * @param isSubmitted  the value for t_resources_energy_head.is_submitted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setIsSubmitted(Boolean isSubmitted) {
		this.isSubmitted = isSubmitted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.audit_node
	 * @return  the value of t_resources_energy_head.audit_node
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Integer getAuditNode() {
		return auditNode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.audit_node
	 * @param auditNode  the value for t_resources_energy_head.audit_node
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setAuditNode(Integer auditNode) {
		this.auditNode = auditNode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.is_audited
	 * @return  the value of t_resources_energy_head.is_audited
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Boolean getIsAudited() {
		return isAudited;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.is_audited
	 * @param isAudited  the value for t_resources_energy_head.is_audited
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setIsAudited(Boolean isAudited) {
		this.isAudited = isAudited;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_resources_energy_head.is_deleted
	 * @return  the value of t_resources_energy_head.is_deleted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public Boolean getIsDeleted() {
		return isDeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_resources_energy_head.is_deleted
	 * @param isDeleted  the value for t_resources_energy_head.is_deleted
	 * @mbg.generated  Mon Jun 27 14:33:00 HKT 2022
	 */
	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
}