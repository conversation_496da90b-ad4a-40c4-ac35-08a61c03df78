package com.csci.hrrs.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class LaborCostDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.head_id
     *
     * @mbg.generated
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.organization_id
     *
     * @mbg.generated
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.organization_name
     *
     * @mbg.generated
     */
    private String organizationName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_past
     *
     * @mbg.generated
     */
    private BigDecimal laborCostPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_now
     *
     * @mbg.generated
     */
    private BigDecimal laborCostNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_rate
     *
     * @mbg.generated
     */
    private BigDecimal laborCostRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.total_salary_past
     *
     * @mbg.generated
     */
    private BigDecimal totalSalaryPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.total_salary_now
     *
     * @mbg.generated
     */
    private BigDecimal totalSalaryNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.total_salary_rate
     *
     * @mbg.generated
     */
    private BigDecimal totalSalaryRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.curr_emp_salary_past
     *
     * @mbg.generated
     */
    private BigDecimal currEmpSalaryPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.curr_emp_salary_now
     *
     * @mbg.generated
     */
    private BigDecimal currEmpSalaryNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.curr_emp_salary_rate
     *
     * @mbg.generated
     */
    private BigDecimal currEmpSalaryRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_count_past
     *
     * @mbg.generated
     */
    private Integer avgEmpCountPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_count_now
     *
     * @mbg.generated
     */
    private Integer avgEmpCountNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_count_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgEmpCountRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_duty_count_past
     *
     * @mbg.generated
     */
    private Integer avgEmpDutyCountPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_duty_count_now
     *
     * @mbg.generated
     */
    private Integer avgEmpDutyCountNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_emp_duty_count_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgEmpDutyCountRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_emp_count_past
     *
     * @mbg.generated
     */
    private Integer endEmpCountPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_emp_count_now
     *
     * @mbg.generated
     */
    private Integer endEmpCountNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_emp_count_rate
     *
     * @mbg.generated
     */
    private BigDecimal endEmpCountRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_on_duty_emp_past
     *
     * @mbg.generated
     */
    private Integer endOnDutyEmpPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_on_duty_emp_now
     *
     * @mbg.generated
     */
    private Integer endOnDutyEmpNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.end_on_duty_emp_rate
     *
     * @mbg.generated
     */
    private BigDecimal endOnDutyEmpRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_output_past
     *
     * @mbg.generated
     */
    private BigDecimal avgOutputPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_output_now
     *
     * @mbg.generated
     */
    private BigDecimal avgOutputNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_output_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgOutputRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_profit_past
     *
     * @mbg.generated
     */
    private BigDecimal avgProfitPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_profit_now
     *
     * @mbg.generated
     */
    private BigDecimal avgProfitNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_profit_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgProfitRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_salary_past
     *
     * @mbg.generated
     */
    private BigDecimal avgSalaryPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_salary_now
     *
     * @mbg.generated
     */
    private BigDecimal avgSalaryNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_salary_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgSalaryRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_wages_past
     *
     * @mbg.generated
     */
    private BigDecimal avgWagesPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_wages_now
     *
     * @mbg.generated
     */
    private BigDecimal avgWagesNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.avg_wages_rate
     *
     * @mbg.generated
     */
    private BigDecimal avgWagesRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_profit_past
     *
     * @mbg.generated
     */
    private BigDecimal laborProfitPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_profit_now
     *
     * @mbg.generated
     */
    private BigDecimal laborProfitNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_profit_rate
     *
     * @mbg.generated
     */
    private BigDecimal laborProfitRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_pctg_past
     *
     * @mbg.generated
     */
    private BigDecimal laborCostPctgPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_pctg_now
     *
     * @mbg.generated
     */
    private BigDecimal laborCostPctgNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.labor_cost_pctg_rate
     *
     * @mbg.generated
     */
    private BigDecimal laborCostPctgRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_profit_past
     *
     * @mbg.generated
     */
    private BigDecimal salaryProfitPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_profit_now
     *
     * @mbg.generated
     */
    private BigDecimal salaryProfitNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_profit_rate
     *
     * @mbg.generated
     */
    private BigDecimal salaryProfitRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_output_past
     *
     * @mbg.generated
     */
    private BigDecimal salaryOutputPast;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_output_now
     *
     * @mbg.generated
     */
    private BigDecimal salaryOutputNow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.salary_output_rate
     *
     * @mbg.generated
     */
    private BigDecimal salaryOutputRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.seq
     *
     * @mbg.generated
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.is_deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.create_user_id
     *
     * @mbg.generated
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.create_username
     *
     * @mbg.generated
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.last_update_time
     *
     * @mbg.generated
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.last_update_user_id
     *
     * @mbg.generated
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.last_update_username
     *
     * @mbg.generated
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_hr_labor_cost_detail.last_update_version
     *
     * @mbg.generated
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.id
     *
     * @return the value of t_hr_labor_cost_detail.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.id
     *
     * @param id the value for t_hr_labor_cost_detail.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.head_id
     *
     * @return the value of t_hr_labor_cost_detail.head_id
     *
     * @mbg.generated
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.head_id
     *
     * @param headId the value for t_hr_labor_cost_detail.head_id
     *
     * @mbg.generated
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.organization_id
     *
     * @return the value of t_hr_labor_cost_detail.organization_id
     *
     * @mbg.generated
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.organization_id
     *
     * @param organizationId the value for t_hr_labor_cost_detail.organization_id
     *
     * @mbg.generated
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.organization_name
     *
     * @return the value of t_hr_labor_cost_detail.organization_name
     *
     * @mbg.generated
     */
    public String getOrganizationName() {
        return organizationName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.organization_name
     *
     * @param organizationName the value for t_hr_labor_cost_detail.organization_name
     *
     * @mbg.generated
     */
    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName == null ? null : organizationName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_past
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_past
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostPast() {
        return laborCostPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_past
     *
     * @param laborCostPast the value for t_hr_labor_cost_detail.labor_cost_past
     *
     * @mbg.generated
     */
    public void setLaborCostPast(BigDecimal laborCostPast) {
        this.laborCostPast = laborCostPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_now
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_now
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostNow() {
        return laborCostNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_now
     *
     * @param laborCostNow the value for t_hr_labor_cost_detail.labor_cost_now
     *
     * @mbg.generated
     */
    public void setLaborCostNow(BigDecimal laborCostNow) {
        this.laborCostNow = laborCostNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_rate
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_rate
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostRate() {
        return laborCostRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_rate
     *
     * @param laborCostRate the value for t_hr_labor_cost_detail.labor_cost_rate
     *
     * @mbg.generated
     */
    public void setLaborCostRate(BigDecimal laborCostRate) {
        this.laborCostRate = laborCostRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.total_salary_past
     *
     * @return the value of t_hr_labor_cost_detail.total_salary_past
     *
     * @mbg.generated
     */
    public BigDecimal getTotalSalaryPast() {
        return totalSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.total_salary_past
     *
     * @param totalSalaryPast the value for t_hr_labor_cost_detail.total_salary_past
     *
     * @mbg.generated
     */
    public void setTotalSalaryPast(BigDecimal totalSalaryPast) {
        this.totalSalaryPast = totalSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.total_salary_now
     *
     * @return the value of t_hr_labor_cost_detail.total_salary_now
     *
     * @mbg.generated
     */
    public BigDecimal getTotalSalaryNow() {
        return totalSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.total_salary_now
     *
     * @param totalSalaryNow the value for t_hr_labor_cost_detail.total_salary_now
     *
     * @mbg.generated
     */
    public void setTotalSalaryNow(BigDecimal totalSalaryNow) {
        this.totalSalaryNow = totalSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.total_salary_rate
     *
     * @return the value of t_hr_labor_cost_detail.total_salary_rate
     *
     * @mbg.generated
     */
    public BigDecimal getTotalSalaryRate() {
        return totalSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.total_salary_rate
     *
     * @param totalSalaryRate the value for t_hr_labor_cost_detail.total_salary_rate
     *
     * @mbg.generated
     */
    public void setTotalSalaryRate(BigDecimal totalSalaryRate) {
        this.totalSalaryRate = totalSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.curr_emp_salary_past
     *
     * @return the value of t_hr_labor_cost_detail.curr_emp_salary_past
     *
     * @mbg.generated
     */
    public BigDecimal getCurrEmpSalaryPast() {
        return currEmpSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.curr_emp_salary_past
     *
     * @param currEmpSalaryPast the value for t_hr_labor_cost_detail.curr_emp_salary_past
     *
     * @mbg.generated
     */
    public void setCurrEmpSalaryPast(BigDecimal currEmpSalaryPast) {
        this.currEmpSalaryPast = currEmpSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.curr_emp_salary_now
     *
     * @return the value of t_hr_labor_cost_detail.curr_emp_salary_now
     *
     * @mbg.generated
     */
    public BigDecimal getCurrEmpSalaryNow() {
        return currEmpSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.curr_emp_salary_now
     *
     * @param currEmpSalaryNow the value for t_hr_labor_cost_detail.curr_emp_salary_now
     *
     * @mbg.generated
     */
    public void setCurrEmpSalaryNow(BigDecimal currEmpSalaryNow) {
        this.currEmpSalaryNow = currEmpSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.curr_emp_salary_rate
     *
     * @return the value of t_hr_labor_cost_detail.curr_emp_salary_rate
     *
     * @mbg.generated
     */
    public BigDecimal getCurrEmpSalaryRate() {
        return currEmpSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.curr_emp_salary_rate
     *
     * @param currEmpSalaryRate the value for t_hr_labor_cost_detail.curr_emp_salary_rate
     *
     * @mbg.generated
     */
    public void setCurrEmpSalaryRate(BigDecimal currEmpSalaryRate) {
        this.currEmpSalaryRate = currEmpSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_count_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_count_past
     *
     * @mbg.generated
     */
    public Integer getAvgEmpCountPast() {
        return avgEmpCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_count_past
     *
     * @param avgEmpCountPast the value for t_hr_labor_cost_detail.avg_emp_count_past
     *
     * @mbg.generated
     */
    public void setAvgEmpCountPast(Integer avgEmpCountPast) {
        this.avgEmpCountPast = avgEmpCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_count_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_count_now
     *
     * @mbg.generated
     */
    public Integer getAvgEmpCountNow() {
        return avgEmpCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_count_now
     *
     * @param avgEmpCountNow the value for t_hr_labor_cost_detail.avg_emp_count_now
     *
     * @mbg.generated
     */
    public void setAvgEmpCountNow(Integer avgEmpCountNow) {
        this.avgEmpCountNow = avgEmpCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_count_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_count_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgEmpCountRate() {
        return avgEmpCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_count_rate
     *
     * @param avgEmpCountRate the value for t_hr_labor_cost_detail.avg_emp_count_rate
     *
     * @mbg.generated
     */
    public void setAvgEmpCountRate(BigDecimal avgEmpCountRate) {
        this.avgEmpCountRate = avgEmpCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_duty_count_past
     *
     * @mbg.generated
     */
    public Integer getAvgEmpDutyCountPast() {
        return avgEmpDutyCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_past
     *
     * @param avgEmpDutyCountPast the value for t_hr_labor_cost_detail.avg_emp_duty_count_past
     *
     * @mbg.generated
     */
    public void setAvgEmpDutyCountPast(Integer avgEmpDutyCountPast) {
        this.avgEmpDutyCountPast = avgEmpDutyCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_duty_count_now
     *
     * @mbg.generated
     */
    public Integer getAvgEmpDutyCountNow() {
        return avgEmpDutyCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_now
     *
     * @param avgEmpDutyCountNow the value for t_hr_labor_cost_detail.avg_emp_duty_count_now
     *
     * @mbg.generated
     */
    public void setAvgEmpDutyCountNow(Integer avgEmpDutyCountNow) {
        this.avgEmpDutyCountNow = avgEmpDutyCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_emp_duty_count_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgEmpDutyCountRate() {
        return avgEmpDutyCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_emp_duty_count_rate
     *
     * @param avgEmpDutyCountRate the value for t_hr_labor_cost_detail.avg_emp_duty_count_rate
     *
     * @mbg.generated
     */
    public void setAvgEmpDutyCountRate(BigDecimal avgEmpDutyCountRate) {
        this.avgEmpDutyCountRate = avgEmpDutyCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_emp_count_past
     *
     * @return the value of t_hr_labor_cost_detail.end_emp_count_past
     *
     * @mbg.generated
     */
    public Integer getEndEmpCountPast() {
        return endEmpCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_emp_count_past
     *
     * @param endEmpCountPast the value for t_hr_labor_cost_detail.end_emp_count_past
     *
     * @mbg.generated
     */
    public void setEndEmpCountPast(Integer endEmpCountPast) {
        this.endEmpCountPast = endEmpCountPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_emp_count_now
     *
     * @return the value of t_hr_labor_cost_detail.end_emp_count_now
     *
     * @mbg.generated
     */
    public Integer getEndEmpCountNow() {
        return endEmpCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_emp_count_now
     *
     * @param endEmpCountNow the value for t_hr_labor_cost_detail.end_emp_count_now
     *
     * @mbg.generated
     */
    public void setEndEmpCountNow(Integer endEmpCountNow) {
        this.endEmpCountNow = endEmpCountNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_emp_count_rate
     *
     * @return the value of t_hr_labor_cost_detail.end_emp_count_rate
     *
     * @mbg.generated
     */
    public BigDecimal getEndEmpCountRate() {
        return endEmpCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_emp_count_rate
     *
     * @param endEmpCountRate the value for t_hr_labor_cost_detail.end_emp_count_rate
     *
     * @mbg.generated
     */
    public void setEndEmpCountRate(BigDecimal endEmpCountRate) {
        this.endEmpCountRate = endEmpCountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_past
     *
     * @return the value of t_hr_labor_cost_detail.end_on_duty_emp_past
     *
     * @mbg.generated
     */
    public Integer getEndOnDutyEmpPast() {
        return endOnDutyEmpPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_past
     *
     * @param endOnDutyEmpPast the value for t_hr_labor_cost_detail.end_on_duty_emp_past
     *
     * @mbg.generated
     */
    public void setEndOnDutyEmpPast(Integer endOnDutyEmpPast) {
        this.endOnDutyEmpPast = endOnDutyEmpPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_now
     *
     * @return the value of t_hr_labor_cost_detail.end_on_duty_emp_now
     *
     * @mbg.generated
     */
    public Integer getEndOnDutyEmpNow() {
        return endOnDutyEmpNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_now
     *
     * @param endOnDutyEmpNow the value for t_hr_labor_cost_detail.end_on_duty_emp_now
     *
     * @mbg.generated
     */
    public void setEndOnDutyEmpNow(Integer endOnDutyEmpNow) {
        this.endOnDutyEmpNow = endOnDutyEmpNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_rate
     *
     * @return the value of t_hr_labor_cost_detail.end_on_duty_emp_rate
     *
     * @mbg.generated
     */
    public BigDecimal getEndOnDutyEmpRate() {
        return endOnDutyEmpRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.end_on_duty_emp_rate
     *
     * @param endOnDutyEmpRate the value for t_hr_labor_cost_detail.end_on_duty_emp_rate
     *
     * @mbg.generated
     */
    public void setEndOnDutyEmpRate(BigDecimal endOnDutyEmpRate) {
        this.endOnDutyEmpRate = endOnDutyEmpRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_output_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_output_past
     *
     * @mbg.generated
     */
    public BigDecimal getAvgOutputPast() {
        return avgOutputPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_output_past
     *
     * @param avgOutputPast the value for t_hr_labor_cost_detail.avg_output_past
     *
     * @mbg.generated
     */
    public void setAvgOutputPast(BigDecimal avgOutputPast) {
        this.avgOutputPast = avgOutputPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_output_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_output_now
     *
     * @mbg.generated
     */
    public BigDecimal getAvgOutputNow() {
        return avgOutputNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_output_now
     *
     * @param avgOutputNow the value for t_hr_labor_cost_detail.avg_output_now
     *
     * @mbg.generated
     */
    public void setAvgOutputNow(BigDecimal avgOutputNow) {
        this.avgOutputNow = avgOutputNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_output_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_output_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgOutputRate() {
        return avgOutputRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_output_rate
     *
     * @param avgOutputRate the value for t_hr_labor_cost_detail.avg_output_rate
     *
     * @mbg.generated
     */
    public void setAvgOutputRate(BigDecimal avgOutputRate) {
        this.avgOutputRate = avgOutputRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_profit_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_profit_past
     *
     * @mbg.generated
     */
    public BigDecimal getAvgProfitPast() {
        return avgProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_profit_past
     *
     * @param avgProfitPast the value for t_hr_labor_cost_detail.avg_profit_past
     *
     * @mbg.generated
     */
    public void setAvgProfitPast(BigDecimal avgProfitPast) {
        this.avgProfitPast = avgProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_profit_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_profit_now
     *
     * @mbg.generated
     */
    public BigDecimal getAvgProfitNow() {
        return avgProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_profit_now
     *
     * @param avgProfitNow the value for t_hr_labor_cost_detail.avg_profit_now
     *
     * @mbg.generated
     */
    public void setAvgProfitNow(BigDecimal avgProfitNow) {
        this.avgProfitNow = avgProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_profit_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_profit_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgProfitRate() {
        return avgProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_profit_rate
     *
     * @param avgProfitRate the value for t_hr_labor_cost_detail.avg_profit_rate
     *
     * @mbg.generated
     */
    public void setAvgProfitRate(BigDecimal avgProfitRate) {
        this.avgProfitRate = avgProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_salary_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_salary_past
     *
     * @mbg.generated
     */
    public BigDecimal getAvgSalaryPast() {
        return avgSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_salary_past
     *
     * @param avgSalaryPast the value for t_hr_labor_cost_detail.avg_salary_past
     *
     * @mbg.generated
     */
    public void setAvgSalaryPast(BigDecimal avgSalaryPast) {
        this.avgSalaryPast = avgSalaryPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_salary_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_salary_now
     *
     * @mbg.generated
     */
    public BigDecimal getAvgSalaryNow() {
        return avgSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_salary_now
     *
     * @param avgSalaryNow the value for t_hr_labor_cost_detail.avg_salary_now
     *
     * @mbg.generated
     */
    public void setAvgSalaryNow(BigDecimal avgSalaryNow) {
        this.avgSalaryNow = avgSalaryNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_salary_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_salary_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgSalaryRate() {
        return avgSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_salary_rate
     *
     * @param avgSalaryRate the value for t_hr_labor_cost_detail.avg_salary_rate
     *
     * @mbg.generated
     */
    public void setAvgSalaryRate(BigDecimal avgSalaryRate) {
        this.avgSalaryRate = avgSalaryRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_wages_past
     *
     * @return the value of t_hr_labor_cost_detail.avg_wages_past
     *
     * @mbg.generated
     */
    public BigDecimal getAvgWagesPast() {
        return avgWagesPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_wages_past
     *
     * @param avgWagesPast the value for t_hr_labor_cost_detail.avg_wages_past
     *
     * @mbg.generated
     */
    public void setAvgWagesPast(BigDecimal avgWagesPast) {
        this.avgWagesPast = avgWagesPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_wages_now
     *
     * @return the value of t_hr_labor_cost_detail.avg_wages_now
     *
     * @mbg.generated
     */
    public BigDecimal getAvgWagesNow() {
        return avgWagesNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_wages_now
     *
     * @param avgWagesNow the value for t_hr_labor_cost_detail.avg_wages_now
     *
     * @mbg.generated
     */
    public void setAvgWagesNow(BigDecimal avgWagesNow) {
        this.avgWagesNow = avgWagesNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.avg_wages_rate
     *
     * @return the value of t_hr_labor_cost_detail.avg_wages_rate
     *
     * @mbg.generated
     */
    public BigDecimal getAvgWagesRate() {
        return avgWagesRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.avg_wages_rate
     *
     * @param avgWagesRate the value for t_hr_labor_cost_detail.avg_wages_rate
     *
     * @mbg.generated
     */
    public void setAvgWagesRate(BigDecimal avgWagesRate) {
        this.avgWagesRate = avgWagesRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_profit_past
     *
     * @return the value of t_hr_labor_cost_detail.labor_profit_past
     *
     * @mbg.generated
     */
    public BigDecimal getLaborProfitPast() {
        return laborProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_profit_past
     *
     * @param laborProfitPast the value for t_hr_labor_cost_detail.labor_profit_past
     *
     * @mbg.generated
     */
    public void setLaborProfitPast(BigDecimal laborProfitPast) {
        this.laborProfitPast = laborProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_profit_now
     *
     * @return the value of t_hr_labor_cost_detail.labor_profit_now
     *
     * @mbg.generated
     */
    public BigDecimal getLaborProfitNow() {
        return laborProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_profit_now
     *
     * @param laborProfitNow the value for t_hr_labor_cost_detail.labor_profit_now
     *
     * @mbg.generated
     */
    public void setLaborProfitNow(BigDecimal laborProfitNow) {
        this.laborProfitNow = laborProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_profit_rate
     *
     * @return the value of t_hr_labor_cost_detail.labor_profit_rate
     *
     * @mbg.generated
     */
    public BigDecimal getLaborProfitRate() {
        return laborProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_profit_rate
     *
     * @param laborProfitRate the value for t_hr_labor_cost_detail.labor_profit_rate
     *
     * @mbg.generated
     */
    public void setLaborProfitRate(BigDecimal laborProfitRate) {
        this.laborProfitRate = laborProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_past
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_pctg_past
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostPctgPast() {
        return laborCostPctgPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_past
     *
     * @param laborCostPctgPast the value for t_hr_labor_cost_detail.labor_cost_pctg_past
     *
     * @mbg.generated
     */
    public void setLaborCostPctgPast(BigDecimal laborCostPctgPast) {
        this.laborCostPctgPast = laborCostPctgPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_now
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_pctg_now
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostPctgNow() {
        return laborCostPctgNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_now
     *
     * @param laborCostPctgNow the value for t_hr_labor_cost_detail.labor_cost_pctg_now
     *
     * @mbg.generated
     */
    public void setLaborCostPctgNow(BigDecimal laborCostPctgNow) {
        this.laborCostPctgNow = laborCostPctgNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_rate
     *
     * @return the value of t_hr_labor_cost_detail.labor_cost_pctg_rate
     *
     * @mbg.generated
     */
    public BigDecimal getLaborCostPctgRate() {
        return laborCostPctgRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.labor_cost_pctg_rate
     *
     * @param laborCostPctgRate the value for t_hr_labor_cost_detail.labor_cost_pctg_rate
     *
     * @mbg.generated
     */
    public void setLaborCostPctgRate(BigDecimal laborCostPctgRate) {
        this.laborCostPctgRate = laborCostPctgRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_profit_past
     *
     * @return the value of t_hr_labor_cost_detail.salary_profit_past
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryProfitPast() {
        return salaryProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_profit_past
     *
     * @param salaryProfitPast the value for t_hr_labor_cost_detail.salary_profit_past
     *
     * @mbg.generated
     */
    public void setSalaryProfitPast(BigDecimal salaryProfitPast) {
        this.salaryProfitPast = salaryProfitPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_profit_now
     *
     * @return the value of t_hr_labor_cost_detail.salary_profit_now
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryProfitNow() {
        return salaryProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_profit_now
     *
     * @param salaryProfitNow the value for t_hr_labor_cost_detail.salary_profit_now
     *
     * @mbg.generated
     */
    public void setSalaryProfitNow(BigDecimal salaryProfitNow) {
        this.salaryProfitNow = salaryProfitNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_profit_rate
     *
     * @return the value of t_hr_labor_cost_detail.salary_profit_rate
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryProfitRate() {
        return salaryProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_profit_rate
     *
     * @param salaryProfitRate the value for t_hr_labor_cost_detail.salary_profit_rate
     *
     * @mbg.generated
     */
    public void setSalaryProfitRate(BigDecimal salaryProfitRate) {
        this.salaryProfitRate = salaryProfitRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_output_past
     *
     * @return the value of t_hr_labor_cost_detail.salary_output_past
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryOutputPast() {
        return salaryOutputPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_output_past
     *
     * @param salaryOutputPast the value for t_hr_labor_cost_detail.salary_output_past
     *
     * @mbg.generated
     */
    public void setSalaryOutputPast(BigDecimal salaryOutputPast) {
        this.salaryOutputPast = salaryOutputPast;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_output_now
     *
     * @return the value of t_hr_labor_cost_detail.salary_output_now
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryOutputNow() {
        return salaryOutputNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_output_now
     *
     * @param salaryOutputNow the value for t_hr_labor_cost_detail.salary_output_now
     *
     * @mbg.generated
     */
    public void setSalaryOutputNow(BigDecimal salaryOutputNow) {
        this.salaryOutputNow = salaryOutputNow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.salary_output_rate
     *
     * @return the value of t_hr_labor_cost_detail.salary_output_rate
     *
     * @mbg.generated
     */
    public BigDecimal getSalaryOutputRate() {
        return salaryOutputRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.salary_output_rate
     *
     * @param salaryOutputRate the value for t_hr_labor_cost_detail.salary_output_rate
     *
     * @mbg.generated
     */
    public void setSalaryOutputRate(BigDecimal salaryOutputRate) {
        this.salaryOutputRate = salaryOutputRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.seq
     *
     * @return the value of t_hr_labor_cost_detail.seq
     *
     * @mbg.generated
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.seq
     *
     * @param seq the value for t_hr_labor_cost_detail.seq
     *
     * @mbg.generated
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.is_deleted
     *
     * @return the value of t_hr_labor_cost_detail.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.is_deleted
     *
     * @param deleted the value for t_hr_labor_cost_detail.is_deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.creation_time
     *
     * @return the value of t_hr_labor_cost_detail.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.creation_time
     *
     * @param creationTime the value for t_hr_labor_cost_detail.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.create_user_id
     *
     * @return the value of t_hr_labor_cost_detail.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.create_user_id
     *
     * @param createUserId the value for t_hr_labor_cost_detail.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.create_username
     *
     * @return the value of t_hr_labor_cost_detail.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.create_username
     *
     * @param createUsername the value for t_hr_labor_cost_detail.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.last_update_time
     *
     * @return the value of t_hr_labor_cost_detail.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.last_update_time
     *
     * @param lastUpdateTime the value for t_hr_labor_cost_detail.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.last_update_user_id
     *
     * @return the value of t_hr_labor_cost_detail.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_hr_labor_cost_detail.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.last_update_username
     *
     * @return the value of t_hr_labor_cost_detail.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.last_update_username
     *
     * @param lastUpdateUsername the value for t_hr_labor_cost_detail.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_hr_labor_cost_detail.last_update_version
     *
     * @return the value of t_hr_labor_cost_detail.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_hr_labor_cost_detail.last_update_version
     *
     * @param lastUpdateVersion the value for t_hr_labor_cost_detail.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}