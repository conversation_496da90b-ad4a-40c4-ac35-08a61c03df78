package com.csci.hrrs.model;

import java.util.ArrayList;
import java.util.List;

public class DynamicFormByItemDetailExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public DynamicFormByItemDetailExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNull() {
			addCriterion("head_id is null");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNotNull() {
			addCriterion("head_id is not null");
			return (Criteria) this;
		}

		public Criteria andHeadIdEqualTo(String value) {
			addCriterion("head_id =", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotEqualTo(String value) {
			addCriterion("head_id <>", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThan(String value) {
			addCriterion("head_id >", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
			addCriterion("head_id >=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThan(String value) {
			addCriterion("head_id <", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThanOrEqualTo(String value) {
			addCriterion("head_id <=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLike(String value) {
			addCriterion("head_id like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotLike(String value) {
			addCriterion("head_id not like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdIn(List<String> values) {
			addCriterion("head_id in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotIn(List<String> values) {
			addCriterion("head_id not in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdBetween(String value1, String value2) {
			addCriterion("head_id between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotBetween(String value1, String value2) {
			addCriterion("head_id not between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andDisplayNameIsNull() {
			addCriterion("display_name is null");
			return (Criteria) this;
		}

		public Criteria andDisplayNameIsNotNull() {
			addCriterion("display_name is not null");
			return (Criteria) this;
		}

		public Criteria andDisplayNameEqualTo(String value) {
			addCriterion("display_name =", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameNotEqualTo(String value) {
			addCriterion("display_name <>", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameGreaterThan(String value) {
			addCriterion("display_name >", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameGreaterThanOrEqualTo(String value) {
			addCriterion("display_name >=", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameLessThan(String value) {
			addCriterion("display_name <", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameLessThanOrEqualTo(String value) {
			addCriterion("display_name <=", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameLike(String value) {
			addCriterion("display_name like", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameNotLike(String value) {
			addCriterion("display_name not like", value, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameIn(List<String> values) {
			addCriterion("display_name in", values, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameNotIn(List<String> values) {
			addCriterion("display_name not in", values, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameBetween(String value1, String value2) {
			addCriterion("display_name between", value1, value2, "displayName");
			return (Criteria) this;
		}

		public Criteria andDisplayNameNotBetween(String value1, String value2) {
			addCriterion("display_name not between", value1, value2, "displayName");
			return (Criteria) this;
		}

		public Criteria andCodeIsNull() {
			addCriterion("code is null");
			return (Criteria) this;
		}

		public Criteria andCodeIsNotNull() {
			addCriterion("code is not null");
			return (Criteria) this;
		}

		public Criteria andCodeEqualTo(String value) {
			addCriterion("code =", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotEqualTo(String value) {
			addCriterion("code <>", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThan(String value) {
			addCriterion("code >", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThanOrEqualTo(String value) {
			addCriterion("code >=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThan(String value) {
			addCriterion("code <", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThanOrEqualTo(String value) {
			addCriterion("code <=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLike(String value) {
			addCriterion("code like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotLike(String value) {
			addCriterion("code not like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeIn(List<String> values) {
			addCriterion("code in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotIn(List<String> values) {
			addCriterion("code not in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeBetween(String value1, String value2) {
			addCriterion("code between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotBetween(String value1, String value2) {
			addCriterion("code not between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andColName1IsNull() {
			addCriterion("col_name_1 is null");
			return (Criteria) this;
		}

		public Criteria andColName1IsNotNull() {
			addCriterion("col_name_1 is not null");
			return (Criteria) this;
		}

		public Criteria andColName1EqualTo(String value) {
			addCriterion("col_name_1 =", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1NotEqualTo(String value) {
			addCriterion("col_name_1 <>", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1GreaterThan(String value) {
			addCriterion("col_name_1 >", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_1 >=", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1LessThan(String value) {
			addCriterion("col_name_1 <", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1LessThanOrEqualTo(String value) {
			addCriterion("col_name_1 <=", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1Like(String value) {
			addCriterion("col_name_1 like", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1NotLike(String value) {
			addCriterion("col_name_1 not like", value, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1In(List<String> values) {
			addCriterion("col_name_1 in", values, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1NotIn(List<String> values) {
			addCriterion("col_name_1 not in", values, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1Between(String value1, String value2) {
			addCriterion("col_name_1 between", value1, value2, "colName1");
			return (Criteria) this;
		}

		public Criteria andColName1NotBetween(String value1, String value2) {
			addCriterion("col_name_1 not between", value1, value2, "colName1");
			return (Criteria) this;
		}

		public Criteria andColValue1IsNull() {
			addCriterion("col_value_1 is null");
			return (Criteria) this;
		}

		public Criteria andColValue1IsNotNull() {
			addCriterion("col_value_1 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue1EqualTo(String value) {
			addCriterion("col_value_1 =", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1NotEqualTo(String value) {
			addCriterion("col_value_1 <>", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1GreaterThan(String value) {
			addCriterion("col_value_1 >", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_1 >=", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1LessThan(String value) {
			addCriterion("col_value_1 <", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1LessThanOrEqualTo(String value) {
			addCriterion("col_value_1 <=", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1Like(String value) {
			addCriterion("col_value_1 like", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1NotLike(String value) {
			addCriterion("col_value_1 not like", value, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1In(List<String> values) {
			addCriterion("col_value_1 in", values, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1NotIn(List<String> values) {
			addCriterion("col_value_1 not in", values, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1Between(String value1, String value2) {
			addCriterion("col_value_1 between", value1, value2, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColValue1NotBetween(String value1, String value2) {
			addCriterion("col_value_1 not between", value1, value2, "colValue1");
			return (Criteria) this;
		}

		public Criteria andColName2IsNull() {
			addCriterion("col_name_2 is null");
			return (Criteria) this;
		}

		public Criteria andColName2IsNotNull() {
			addCriterion("col_name_2 is not null");
			return (Criteria) this;
		}

		public Criteria andColName2EqualTo(String value) {
			addCriterion("col_name_2 =", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2NotEqualTo(String value) {
			addCriterion("col_name_2 <>", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2GreaterThan(String value) {
			addCriterion("col_name_2 >", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_2 >=", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2LessThan(String value) {
			addCriterion("col_name_2 <", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2LessThanOrEqualTo(String value) {
			addCriterion("col_name_2 <=", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2Like(String value) {
			addCriterion("col_name_2 like", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2NotLike(String value) {
			addCriterion("col_name_2 not like", value, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2In(List<String> values) {
			addCriterion("col_name_2 in", values, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2NotIn(List<String> values) {
			addCriterion("col_name_2 not in", values, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2Between(String value1, String value2) {
			addCriterion("col_name_2 between", value1, value2, "colName2");
			return (Criteria) this;
		}

		public Criteria andColName2NotBetween(String value1, String value2) {
			addCriterion("col_name_2 not between", value1, value2, "colName2");
			return (Criteria) this;
		}

		public Criteria andColValue2IsNull() {
			addCriterion("col_value_2 is null");
			return (Criteria) this;
		}

		public Criteria andColValue2IsNotNull() {
			addCriterion("col_value_2 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue2EqualTo(String value) {
			addCriterion("col_value_2 =", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2NotEqualTo(String value) {
			addCriterion("col_value_2 <>", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2GreaterThan(String value) {
			addCriterion("col_value_2 >", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_2 >=", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2LessThan(String value) {
			addCriterion("col_value_2 <", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2LessThanOrEqualTo(String value) {
			addCriterion("col_value_2 <=", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2Like(String value) {
			addCriterion("col_value_2 like", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2NotLike(String value) {
			addCriterion("col_value_2 not like", value, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2In(List<String> values) {
			addCriterion("col_value_2 in", values, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2NotIn(List<String> values) {
			addCriterion("col_value_2 not in", values, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2Between(String value1, String value2) {
			addCriterion("col_value_2 between", value1, value2, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColValue2NotBetween(String value1, String value2) {
			addCriterion("col_value_2 not between", value1, value2, "colValue2");
			return (Criteria) this;
		}

		public Criteria andColName3IsNull() {
			addCriterion("col_name_3 is null");
			return (Criteria) this;
		}

		public Criteria andColName3IsNotNull() {
			addCriterion("col_name_3 is not null");
			return (Criteria) this;
		}

		public Criteria andColName3EqualTo(String value) {
			addCriterion("col_name_3 =", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3NotEqualTo(String value) {
			addCriterion("col_name_3 <>", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3GreaterThan(String value) {
			addCriterion("col_name_3 >", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_3 >=", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3LessThan(String value) {
			addCriterion("col_name_3 <", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3LessThanOrEqualTo(String value) {
			addCriterion("col_name_3 <=", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3Like(String value) {
			addCriterion("col_name_3 like", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3NotLike(String value) {
			addCriterion("col_name_3 not like", value, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3In(List<String> values) {
			addCriterion("col_name_3 in", values, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3NotIn(List<String> values) {
			addCriterion("col_name_3 not in", values, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3Between(String value1, String value2) {
			addCriterion("col_name_3 between", value1, value2, "colName3");
			return (Criteria) this;
		}

		public Criteria andColName3NotBetween(String value1, String value2) {
			addCriterion("col_name_3 not between", value1, value2, "colName3");
			return (Criteria) this;
		}

		public Criteria andColValue3IsNull() {
			addCriterion("col_value_3 is null");
			return (Criteria) this;
		}

		public Criteria andColValue3IsNotNull() {
			addCriterion("col_value_3 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue3EqualTo(String value) {
			addCriterion("col_value_3 =", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3NotEqualTo(String value) {
			addCriterion("col_value_3 <>", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3GreaterThan(String value) {
			addCriterion("col_value_3 >", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_3 >=", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3LessThan(String value) {
			addCriterion("col_value_3 <", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3LessThanOrEqualTo(String value) {
			addCriterion("col_value_3 <=", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3Like(String value) {
			addCriterion("col_value_3 like", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3NotLike(String value) {
			addCriterion("col_value_3 not like", value, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3In(List<String> values) {
			addCriterion("col_value_3 in", values, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3NotIn(List<String> values) {
			addCriterion("col_value_3 not in", values, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3Between(String value1, String value2) {
			addCriterion("col_value_3 between", value1, value2, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColValue3NotBetween(String value1, String value2) {
			addCriterion("col_value_3 not between", value1, value2, "colValue3");
			return (Criteria) this;
		}

		public Criteria andColName4IsNull() {
			addCriterion("col_name_4 is null");
			return (Criteria) this;
		}

		public Criteria andColName4IsNotNull() {
			addCriterion("col_name_4 is not null");
			return (Criteria) this;
		}

		public Criteria andColName4EqualTo(String value) {
			addCriterion("col_name_4 =", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4NotEqualTo(String value) {
			addCriterion("col_name_4 <>", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4GreaterThan(String value) {
			addCriterion("col_name_4 >", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_4 >=", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4LessThan(String value) {
			addCriterion("col_name_4 <", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4LessThanOrEqualTo(String value) {
			addCriterion("col_name_4 <=", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4Like(String value) {
			addCriterion("col_name_4 like", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4NotLike(String value) {
			addCriterion("col_name_4 not like", value, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4In(List<String> values) {
			addCriterion("col_name_4 in", values, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4NotIn(List<String> values) {
			addCriterion("col_name_4 not in", values, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4Between(String value1, String value2) {
			addCriterion("col_name_4 between", value1, value2, "colName4");
			return (Criteria) this;
		}

		public Criteria andColName4NotBetween(String value1, String value2) {
			addCriterion("col_name_4 not between", value1, value2, "colName4");
			return (Criteria) this;
		}

		public Criteria andColValue4IsNull() {
			addCriterion("col_value_4 is null");
			return (Criteria) this;
		}

		public Criteria andColValue4IsNotNull() {
			addCriterion("col_value_4 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue4EqualTo(String value) {
			addCriterion("col_value_4 =", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4NotEqualTo(String value) {
			addCriterion("col_value_4 <>", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4GreaterThan(String value) {
			addCriterion("col_value_4 >", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_4 >=", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4LessThan(String value) {
			addCriterion("col_value_4 <", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4LessThanOrEqualTo(String value) {
			addCriterion("col_value_4 <=", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4Like(String value) {
			addCriterion("col_value_4 like", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4NotLike(String value) {
			addCriterion("col_value_4 not like", value, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4In(List<String> values) {
			addCriterion("col_value_4 in", values, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4NotIn(List<String> values) {
			addCriterion("col_value_4 not in", values, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4Between(String value1, String value2) {
			addCriterion("col_value_4 between", value1, value2, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColValue4NotBetween(String value1, String value2) {
			addCriterion("col_value_4 not between", value1, value2, "colValue4");
			return (Criteria) this;
		}

		public Criteria andColName5IsNull() {
			addCriterion("col_name_5 is null");
			return (Criteria) this;
		}

		public Criteria andColName5IsNotNull() {
			addCriterion("col_name_5 is not null");
			return (Criteria) this;
		}

		public Criteria andColName5EqualTo(String value) {
			addCriterion("col_name_5 =", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5NotEqualTo(String value) {
			addCriterion("col_name_5 <>", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5GreaterThan(String value) {
			addCriterion("col_name_5 >", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_5 >=", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5LessThan(String value) {
			addCriterion("col_name_5 <", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5LessThanOrEqualTo(String value) {
			addCriterion("col_name_5 <=", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5Like(String value) {
			addCriterion("col_name_5 like", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5NotLike(String value) {
			addCriterion("col_name_5 not like", value, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5In(List<String> values) {
			addCriterion("col_name_5 in", values, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5NotIn(List<String> values) {
			addCriterion("col_name_5 not in", values, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5Between(String value1, String value2) {
			addCriterion("col_name_5 between", value1, value2, "colName5");
			return (Criteria) this;
		}

		public Criteria andColName5NotBetween(String value1, String value2) {
			addCriterion("col_name_5 not between", value1, value2, "colName5");
			return (Criteria) this;
		}

		public Criteria andColValue5IsNull() {
			addCriterion("col_value_5 is null");
			return (Criteria) this;
		}

		public Criteria andColValue5IsNotNull() {
			addCriterion("col_value_5 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue5EqualTo(String value) {
			addCriterion("col_value_5 =", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5NotEqualTo(String value) {
			addCriterion("col_value_5 <>", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5GreaterThan(String value) {
			addCriterion("col_value_5 >", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_5 >=", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5LessThan(String value) {
			addCriterion("col_value_5 <", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5LessThanOrEqualTo(String value) {
			addCriterion("col_value_5 <=", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5Like(String value) {
			addCriterion("col_value_5 like", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5NotLike(String value) {
			addCriterion("col_value_5 not like", value, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5In(List<String> values) {
			addCriterion("col_value_5 in", values, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5NotIn(List<String> values) {
			addCriterion("col_value_5 not in", values, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5Between(String value1, String value2) {
			addCriterion("col_value_5 between", value1, value2, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColValue5NotBetween(String value1, String value2) {
			addCriterion("col_value_5 not between", value1, value2, "colValue5");
			return (Criteria) this;
		}

		public Criteria andColName6IsNull() {
			addCriterion("col_name_6 is null");
			return (Criteria) this;
		}

		public Criteria andColName6IsNotNull() {
			addCriterion("col_name_6 is not null");
			return (Criteria) this;
		}

		public Criteria andColName6EqualTo(String value) {
			addCriterion("col_name_6 =", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6NotEqualTo(String value) {
			addCriterion("col_name_6 <>", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6GreaterThan(String value) {
			addCriterion("col_name_6 >", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_6 >=", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6LessThan(String value) {
			addCriterion("col_name_6 <", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6LessThanOrEqualTo(String value) {
			addCriterion("col_name_6 <=", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6Like(String value) {
			addCriterion("col_name_6 like", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6NotLike(String value) {
			addCriterion("col_name_6 not like", value, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6In(List<String> values) {
			addCriterion("col_name_6 in", values, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6NotIn(List<String> values) {
			addCriterion("col_name_6 not in", values, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6Between(String value1, String value2) {
			addCriterion("col_name_6 between", value1, value2, "colName6");
			return (Criteria) this;
		}

		public Criteria andColName6NotBetween(String value1, String value2) {
			addCriterion("col_name_6 not between", value1, value2, "colName6");
			return (Criteria) this;
		}

		public Criteria andColValue6IsNull() {
			addCriterion("col_value_6 is null");
			return (Criteria) this;
		}

		public Criteria andColValue6IsNotNull() {
			addCriterion("col_value_6 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue6EqualTo(String value) {
			addCriterion("col_value_6 =", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6NotEqualTo(String value) {
			addCriterion("col_value_6 <>", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6GreaterThan(String value) {
			addCriterion("col_value_6 >", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_6 >=", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6LessThan(String value) {
			addCriterion("col_value_6 <", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6LessThanOrEqualTo(String value) {
			addCriterion("col_value_6 <=", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6Like(String value) {
			addCriterion("col_value_6 like", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6NotLike(String value) {
			addCriterion("col_value_6 not like", value, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6In(List<String> values) {
			addCriterion("col_value_6 in", values, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6NotIn(List<String> values) {
			addCriterion("col_value_6 not in", values, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6Between(String value1, String value2) {
			addCriterion("col_value_6 between", value1, value2, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColValue6NotBetween(String value1, String value2) {
			addCriterion("col_value_6 not between", value1, value2, "colValue6");
			return (Criteria) this;
		}

		public Criteria andColName7IsNull() {
			addCriterion("col_name7 is null");
			return (Criteria) this;
		}

		public Criteria andColName7IsNotNull() {
			addCriterion("col_name7 is not null");
			return (Criteria) this;
		}

		public Criteria andColName7EqualTo(String value) {
			addCriterion("col_name7 =", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7NotEqualTo(String value) {
			addCriterion("col_name7 <>", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7GreaterThan(String value) {
			addCriterion("col_name7 >", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7GreaterThanOrEqualTo(String value) {
			addCriterion("col_name7 >=", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7LessThan(String value) {
			addCriterion("col_name7 <", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7LessThanOrEqualTo(String value) {
			addCriterion("col_name7 <=", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7Like(String value) {
			addCriterion("col_name7 like", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7NotLike(String value) {
			addCriterion("col_name7 not like", value, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7In(List<String> values) {
			addCriterion("col_name7 in", values, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7NotIn(List<String> values) {
			addCriterion("col_name7 not in", values, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7Between(String value1, String value2) {
			addCriterion("col_name7 between", value1, value2, "colName7");
			return (Criteria) this;
		}

		public Criteria andColName7NotBetween(String value1, String value2) {
			addCriterion("col_name7 not between", value1, value2, "colName7");
			return (Criteria) this;
		}

		public Criteria andColValue7IsNull() {
			addCriterion("col_value_7 is null");
			return (Criteria) this;
		}

		public Criteria andColValue7IsNotNull() {
			addCriterion("col_value_7 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue7EqualTo(String value) {
			addCriterion("col_value_7 =", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7NotEqualTo(String value) {
			addCriterion("col_value_7 <>", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7GreaterThan(String value) {
			addCriterion("col_value_7 >", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_7 >=", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7LessThan(String value) {
			addCriterion("col_value_7 <", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7LessThanOrEqualTo(String value) {
			addCriterion("col_value_7 <=", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7Like(String value) {
			addCriterion("col_value_7 like", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7NotLike(String value) {
			addCriterion("col_value_7 not like", value, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7In(List<String> values) {
			addCriterion("col_value_7 in", values, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7NotIn(List<String> values) {
			addCriterion("col_value_7 not in", values, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7Between(String value1, String value2) {
			addCriterion("col_value_7 between", value1, value2, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColValue7NotBetween(String value1, String value2) {
			addCriterion("col_value_7 not between", value1, value2, "colValue7");
			return (Criteria) this;
		}

		public Criteria andColName8IsNull() {
			addCriterion("col_name_8 is null");
			return (Criteria) this;
		}

		public Criteria andColName8IsNotNull() {
			addCriterion("col_name_8 is not null");
			return (Criteria) this;
		}

		public Criteria andColName8EqualTo(String value) {
			addCriterion("col_name_8 =", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8NotEqualTo(String value) {
			addCriterion("col_name_8 <>", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8GreaterThan(String value) {
			addCriterion("col_name_8 >", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_8 >=", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8LessThan(String value) {
			addCriterion("col_name_8 <", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8LessThanOrEqualTo(String value) {
			addCriterion("col_name_8 <=", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8Like(String value) {
			addCriterion("col_name_8 like", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8NotLike(String value) {
			addCriterion("col_name_8 not like", value, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8In(List<String> values) {
			addCriterion("col_name_8 in", values, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8NotIn(List<String> values) {
			addCriterion("col_name_8 not in", values, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8Between(String value1, String value2) {
			addCriterion("col_name_8 between", value1, value2, "colName8");
			return (Criteria) this;
		}

		public Criteria andColName8NotBetween(String value1, String value2) {
			addCriterion("col_name_8 not between", value1, value2, "colName8");
			return (Criteria) this;
		}

		public Criteria andColValue8IsNull() {
			addCriterion("col_value_8 is null");
			return (Criteria) this;
		}

		public Criteria andColValue8IsNotNull() {
			addCriterion("col_value_8 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue8EqualTo(String value) {
			addCriterion("col_value_8 =", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8NotEqualTo(String value) {
			addCriterion("col_value_8 <>", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8GreaterThan(String value) {
			addCriterion("col_value_8 >", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_8 >=", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8LessThan(String value) {
			addCriterion("col_value_8 <", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8LessThanOrEqualTo(String value) {
			addCriterion("col_value_8 <=", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8Like(String value) {
			addCriterion("col_value_8 like", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8NotLike(String value) {
			addCriterion("col_value_8 not like", value, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8In(List<String> values) {
			addCriterion("col_value_8 in", values, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8NotIn(List<String> values) {
			addCriterion("col_value_8 not in", values, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8Between(String value1, String value2) {
			addCriterion("col_value_8 between", value1, value2, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColValue8NotBetween(String value1, String value2) {
			addCriterion("col_value_8 not between", value1, value2, "colValue8");
			return (Criteria) this;
		}

		public Criteria andColName9IsNull() {
			addCriterion("col_name_9 is null");
			return (Criteria) this;
		}

		public Criteria andColName9IsNotNull() {
			addCriterion("col_name_9 is not null");
			return (Criteria) this;
		}

		public Criteria andColName9EqualTo(String value) {
			addCriterion("col_name_9 =", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9NotEqualTo(String value) {
			addCriterion("col_name_9 <>", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9GreaterThan(String value) {
			addCriterion("col_name_9 >", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_9 >=", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9LessThan(String value) {
			addCriterion("col_name_9 <", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9LessThanOrEqualTo(String value) {
			addCriterion("col_name_9 <=", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9Like(String value) {
			addCriterion("col_name_9 like", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9NotLike(String value) {
			addCriterion("col_name_9 not like", value, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9In(List<String> values) {
			addCriterion("col_name_9 in", values, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9NotIn(List<String> values) {
			addCriterion("col_name_9 not in", values, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9Between(String value1, String value2) {
			addCriterion("col_name_9 between", value1, value2, "colName9");
			return (Criteria) this;
		}

		public Criteria andColName9NotBetween(String value1, String value2) {
			addCriterion("col_name_9 not between", value1, value2, "colName9");
			return (Criteria) this;
		}

		public Criteria andColValue9IsNull() {
			addCriterion("col_value_9 is null");
			return (Criteria) this;
		}

		public Criteria andColValue9IsNotNull() {
			addCriterion("col_value_9 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue9EqualTo(String value) {
			addCriterion("col_value_9 =", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9NotEqualTo(String value) {
			addCriterion("col_value_9 <>", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9GreaterThan(String value) {
			addCriterion("col_value_9 >", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_9 >=", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9LessThan(String value) {
			addCriterion("col_value_9 <", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9LessThanOrEqualTo(String value) {
			addCriterion("col_value_9 <=", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9Like(String value) {
			addCriterion("col_value_9 like", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9NotLike(String value) {
			addCriterion("col_value_9 not like", value, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9In(List<String> values) {
			addCriterion("col_value_9 in", values, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9NotIn(List<String> values) {
			addCriterion("col_value_9 not in", values, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9Between(String value1, String value2) {
			addCriterion("col_value_9 between", value1, value2, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColValue9NotBetween(String value1, String value2) {
			addCriterion("col_value_9 not between", value1, value2, "colValue9");
			return (Criteria) this;
		}

		public Criteria andColName10IsNull() {
			addCriterion("col_name_10 is null");
			return (Criteria) this;
		}

		public Criteria andColName10IsNotNull() {
			addCriterion("col_name_10 is not null");
			return (Criteria) this;
		}

		public Criteria andColName10EqualTo(String value) {
			addCriterion("col_name_10 =", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10NotEqualTo(String value) {
			addCriterion("col_name_10 <>", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10GreaterThan(String value) {
			addCriterion("col_name_10 >", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10GreaterThanOrEqualTo(String value) {
			addCriterion("col_name_10 >=", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10LessThan(String value) {
			addCriterion("col_name_10 <", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10LessThanOrEqualTo(String value) {
			addCriterion("col_name_10 <=", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10Like(String value) {
			addCriterion("col_name_10 like", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10NotLike(String value) {
			addCriterion("col_name_10 not like", value, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10In(List<String> values) {
			addCriterion("col_name_10 in", values, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10NotIn(List<String> values) {
			addCriterion("col_name_10 not in", values, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10Between(String value1, String value2) {
			addCriterion("col_name_10 between", value1, value2, "colName10");
			return (Criteria) this;
		}

		public Criteria andColName10NotBetween(String value1, String value2) {
			addCriterion("col_name_10 not between", value1, value2, "colName10");
			return (Criteria) this;
		}

		public Criteria andColValue10IsNull() {
			addCriterion("col_value_10 is null");
			return (Criteria) this;
		}

		public Criteria andColValue10IsNotNull() {
			addCriterion("col_value_10 is not null");
			return (Criteria) this;
		}

		public Criteria andColValue10EqualTo(String value) {
			addCriterion("col_value_10 =", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10NotEqualTo(String value) {
			addCriterion("col_value_10 <>", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10GreaterThan(String value) {
			addCriterion("col_value_10 >", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10GreaterThanOrEqualTo(String value) {
			addCriterion("col_value_10 >=", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10LessThan(String value) {
			addCriterion("col_value_10 <", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10LessThanOrEqualTo(String value) {
			addCriterion("col_value_10 <=", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10Like(String value) {
			addCriterion("col_value_10 like", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10NotLike(String value) {
			addCriterion("col_value_10 not like", value, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10In(List<String> values) {
			addCriterion("col_value_10 in", values, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10NotIn(List<String> values) {
			addCriterion("col_value_10 not in", values, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10Between(String value1, String value2) {
			addCriterion("col_value_10 between", value1, value2, "colValue10");
			return (Criteria) this;
		}

		public Criteria andColValue10NotBetween(String value1, String value2) {
			addCriterion("col_value_10 not between", value1, value2, "colValue10");
			return (Criteria) this;
		}

		public Criteria andSeqIsNull() {
			addCriterion("seq is null");
			return (Criteria) this;
		}

		public Criteria andSeqIsNotNull() {
			addCriterion("seq is not null");
			return (Criteria) this;
		}

		public Criteria andSeqEqualTo(Integer value) {
			addCriterion("seq =", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotEqualTo(Integer value) {
			addCriterion("seq <>", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThan(Integer value) {
			addCriterion("seq >", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
			addCriterion("seq >=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThan(Integer value) {
			addCriterion("seq <", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThanOrEqualTo(Integer value) {
			addCriterion("seq <=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqIn(List<Integer> values) {
			addCriterion("seq in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotIn(List<Integer> values) {
			addCriterion("seq not in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqBetween(Integer value1, Integer value2) {
			addCriterion("seq between", value1, value2, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotBetween(Integer value1, Integer value2) {
			addCriterion("seq not between", value1, value2, "seq");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_dynamic_form_by_item_detail
	 * @mbg.generated  Tue Jun 28 10:49:20 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_dynamic_form_by_item_detail
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 19 21:48:28 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}