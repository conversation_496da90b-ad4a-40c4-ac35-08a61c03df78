package com.csci.hrrs.model;

import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;

public class OtherFactorsHeadExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public OtherFactorsHeadExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNull() {
			addCriterion("head_id is null");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNotNull() {
			addCriterion("head_id is not null");
			return (Criteria) this;
		}

		public Criteria andHeadIdEqualTo(String value) {
			addCriterion("head_id =", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotEqualTo(String value) {
			addCriterion("head_id <>", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThan(String value) {
			addCriterion("head_id >", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
			addCriterion("head_id >=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThan(String value) {
			addCriterion("head_id <", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThanOrEqualTo(String value) {
			addCriterion("head_id <=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLike(String value) {
			addCriterion("head_id like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotLike(String value) {
			addCriterion("head_id not like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdIn(List<String> values) {
			addCriterion("head_id in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotIn(List<String> values) {
			addCriterion("head_id not in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdBetween(String value1, String value2) {
			addCriterion("head_id between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotBetween(String value1, String value2) {
			addCriterion("head_id not between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNull() {
			addCriterion("organization_id is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNotNull() {
			addCriterion("organization_id is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdEqualTo(String value) {
			addCriterion("organization_id =", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotEqualTo(String value) {
			addCriterion("organization_id <>", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThan(String value) {
			addCriterion("organization_id >", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
			addCriterion("organization_id >=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThan(String value) {
			addCriterion("organization_id <", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
			addCriterion("organization_id <=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLike(String value) {
			addCriterion("organization_id like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotLike(String value) {
			addCriterion("organization_id not like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIn(List<String> values) {
			addCriterion("organization_id in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotIn(List<String> values) {
			addCriterion("organization_id not in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdBetween(String value1, String value2) {
			addCriterion("organization_id between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotBetween(String value1, String value2) {
			addCriterion("organization_id not between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIsNull() {
			addCriterion("organization_name is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIsNotNull() {
			addCriterion("organization_name is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameEqualTo(String value) {
			addCriterion("organization_name =", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotEqualTo(String value) {
			addCriterion("organization_name <>", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameGreaterThan(String value) {
			addCriterion("organization_name >", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameGreaterThanOrEqualTo(String value) {
			addCriterion("organization_name >=", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLessThan(String value) {
			addCriterion("organization_name <", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLessThanOrEqualTo(String value) {
			addCriterion("organization_name <=", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLike(String value) {
			addCriterion("organization_name like", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotLike(String value) {
			addCriterion("organization_name not like", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIn(List<String> values) {
			addCriterion("organization_name in", values, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotIn(List<String> values) {
			addCriterion("organization_name not in", values, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameBetween(String value1, String value2) {
			addCriterion("organization_name between", value1, value2, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotBetween(String value1, String value2) {
			addCriterion("organization_name not between", value1, value2, "organizationName");
			return (Criteria) this;
		}

		public Criteria andFormCodeIsNull() {
			addCriterion("form_code is null");
			return (Criteria) this;
		}

		public Criteria andFormCodeIsNotNull() {
			addCriterion("form_code is not null");
			return (Criteria) this;
		}

		public Criteria andFormCodeEqualTo(String value) {
			addCriterion("form_code =", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotEqualTo(String value) {
			addCriterion("form_code <>", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeGreaterThan(String value) {
			addCriterion("form_code >", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeGreaterThanOrEqualTo(String value) {
			addCriterion("form_code >=", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLessThan(String value) {
			addCriterion("form_code <", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLessThanOrEqualTo(String value) {
			addCriterion("form_code <=", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeLike(String value) {
			addCriterion("form_code like", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotLike(String value) {
			addCriterion("form_code not like", value, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeIn(List<String> values) {
			addCriterion("form_code in", values, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotIn(List<String> values) {
			addCriterion("form_code not in", values, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeBetween(String value1, String value2) {
			addCriterion("form_code between", value1, value2, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormCodeNotBetween(String value1, String value2) {
			addCriterion("form_code not between", value1, value2, "formCode");
			return (Criteria) this;
		}

		public Criteria andFormNameIsNull() {
			addCriterion("form_name is null");
			return (Criteria) this;
		}

		public Criteria andFormNameIsNotNull() {
			addCriterion("form_name is not null");
			return (Criteria) this;
		}

		public Criteria andFormNameEqualTo(String value) {
			addCriterion("form_name =", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameNotEqualTo(String value) {
			addCriterion("form_name <>", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameGreaterThan(String value) {
			addCriterion("form_name >", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameGreaterThanOrEqualTo(String value) {
			addCriterion("form_name >=", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameLessThan(String value) {
			addCriterion("form_name <", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameLessThanOrEqualTo(String value) {
			addCriterion("form_name <=", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameLike(String value) {
			addCriterion("form_name like", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameNotLike(String value) {
			addCriterion("form_name not like", value, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameIn(List<String> values) {
			addCriterion("form_name in", values, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameNotIn(List<String> values) {
			addCriterion("form_name not in", values, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameBetween(String value1, String value2) {
			addCriterion("form_name between", value1, value2, "formName");
			return (Criteria) this;
		}

		public Criteria andFormNameNotBetween(String value1, String value2) {
			addCriterion("form_name not between", value1, value2, "formName");
			return (Criteria) this;
		}

		public Criteria andReportingYearIsNull() {
			addCriterion("reporting_year is null");
			return (Criteria) this;
		}

		public Criteria andReportingYearIsNotNull() {
			addCriterion("reporting_year is not null");
			return (Criteria) this;
		}

		public Criteria andReportingYearEqualTo(Integer value) {
			addCriterion("reporting_year =", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearNotEqualTo(Integer value) {
			addCriterion("reporting_year <>", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearGreaterThan(Integer value) {
			addCriterion("reporting_year >", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearGreaterThanOrEqualTo(Integer value) {
			addCriterion("reporting_year >=", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearLessThan(Integer value) {
			addCriterion("reporting_year <", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearLessThanOrEqualTo(Integer value) {
			addCriterion("reporting_year <=", value, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearIn(List<Integer> values) {
			addCriterion("reporting_year in", values, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearNotIn(List<Integer> values) {
			addCriterion("reporting_year not in", values, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearBetween(Integer value1, Integer value2) {
			addCriterion("reporting_year between", value1, value2, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingYearNotBetween(Integer value1, Integer value2) {
			addCriterion("reporting_year not between", value1, value2, "reportingYear");
			return (Criteria) this;
		}

		public Criteria andReportingMonthIsNull() {
			addCriterion("reporting_month is null");
			return (Criteria) this;
		}

		public Criteria andReportingMonthIsNotNull() {
			addCriterion("reporting_month is not null");
			return (Criteria) this;
		}

		public Criteria andReportingMonthEqualTo(Integer value) {
			addCriterion("reporting_month =", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthNotEqualTo(Integer value) {
			addCriterion("reporting_month <>", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthGreaterThan(Integer value) {
			addCriterion("reporting_month >", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthGreaterThanOrEqualTo(Integer value) {
			addCriterion("reporting_month >=", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthLessThan(Integer value) {
			addCriterion("reporting_month <", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthLessThanOrEqualTo(Integer value) {
			addCriterion("reporting_month <=", value, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthIn(List<Integer> values) {
			addCriterion("reporting_month in", values, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthNotIn(List<Integer> values) {
			addCriterion("reporting_month not in", values, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthBetween(Integer value1, Integer value2) {
			addCriterion("reporting_month between", value1, value2, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingMonthNotBetween(Integer value1, Integer value2) {
			addCriterion("reporting_month not between", value1, value2, "reportingMonth");
			return (Criteria) this;
		}

		public Criteria andReportingDateIsNull() {
			addCriterion("reporting_date is null");
			return (Criteria) this;
		}

		public Criteria andReportingDateIsNotNull() {
			addCriterion("reporting_date is not null");
			return (Criteria) this;
		}

		public Criteria andReportingDateEqualTo(LocalDateTime value) {
			addCriterion("reporting_date =", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateNotEqualTo(LocalDateTime value) {
			addCriterion("reporting_date <>", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateGreaterThan(LocalDateTime value) {
			addCriterion("reporting_date >", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("reporting_date >=", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateLessThan(LocalDateTime value) {
			addCriterion("reporting_date <", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("reporting_date <=", value, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateIn(List<LocalDateTime> values) {
			addCriterion("reporting_date in", values, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateNotIn(List<LocalDateTime> values) {
			addCriterion("reporting_date not in", values, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("reporting_date between", value1, value2, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andReportingDateNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("reporting_date not between", value1, value2, "reportingDate");
			return (Criteria) this;
		}

		public Criteria andUsernameIsNull() {
			addCriterion("username is null");
			return (Criteria) this;
		}

		public Criteria andUsernameIsNotNull() {
			addCriterion("username is not null");
			return (Criteria) this;
		}

		public Criteria andUsernameEqualTo(String value) {
			addCriterion("username =", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotEqualTo(String value) {
			addCriterion("username <>", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameGreaterThan(String value) {
			addCriterion("username >", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("username >=", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLessThan(String value) {
			addCriterion("username <", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLessThanOrEqualTo(String value) {
			addCriterion("username <=", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLike(String value) {
			addCriterion("username like", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotLike(String value) {
			addCriterion("username not like", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameIn(List<String> values) {
			addCriterion("username in", values, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotIn(List<String> values) {
			addCriterion("username not in", values, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameBetween(String value1, String value2) {
			addCriterion("username between", value1, value2, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotBetween(String value1, String value2) {
			addCriterion("username not between", value1, value2, "username");
			return (Criteria) this;
		}

		public Criteria andUserRealNameIsNull() {
			addCriterion("user_real_name is null");
			return (Criteria) this;
		}

		public Criteria andUserRealNameIsNotNull() {
			addCriterion("user_real_name is not null");
			return (Criteria) this;
		}

		public Criteria andUserRealNameEqualTo(String value) {
			addCriterion("user_real_name =", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameNotEqualTo(String value) {
			addCriterion("user_real_name <>", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameGreaterThan(String value) {
			addCriterion("user_real_name >", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameGreaterThanOrEqualTo(String value) {
			addCriterion("user_real_name >=", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameLessThan(String value) {
			addCriterion("user_real_name <", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameLessThanOrEqualTo(String value) {
			addCriterion("user_real_name <=", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameLike(String value) {
			addCriterion("user_real_name like", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameNotLike(String value) {
			addCriterion("user_real_name not like", value, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameIn(List<String> values) {
			addCriterion("user_real_name in", values, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameNotIn(List<String> values) {
			addCriterion("user_real_name not in", values, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameBetween(String value1, String value2) {
			addCriterion("user_real_name between", value1, value2, "userRealName");
			return (Criteria) this;
		}

		public Criteria andUserRealNameNotBetween(String value1, String value2) {
			addCriterion("user_real_name not between", value1, value2, "userRealName");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNull() {
			addCriterion("last_update_version is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNotNull() {
			addCriterion("last_update_version is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionEqualTo(Integer value) {
			addCriterion("last_update_version =", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
			addCriterion("last_update_version <>", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThan(Integer value) {
			addCriterion("last_update_version >", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
			addCriterion("last_update_version >=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThan(Integer value) {
			addCriterion("last_update_version <", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
			addCriterion("last_update_version <=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIn(List<Integer> values) {
			addCriterion("last_update_version in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
			addCriterion("last_update_version not in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedIsNull() {
			addCriterion("is_submitted is null");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedIsNotNull() {
			addCriterion("is_submitted is not null");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedEqualTo(Boolean value) {
			addCriterion("is_submitted =", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedNotEqualTo(Boolean value) {
			addCriterion("is_submitted <>", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedGreaterThan(Boolean value) {
			addCriterion("is_submitted >", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_submitted >=", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedLessThan(Boolean value) {
			addCriterion("is_submitted <", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedLessThanOrEqualTo(Boolean value) {
			addCriterion("is_submitted <=", value, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedIn(List<Boolean> values) {
			addCriterion("is_submitted in", values, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedNotIn(List<Boolean> values) {
			addCriterion("is_submitted not in", values, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedBetween(Boolean value1, Boolean value2) {
			addCriterion("is_submitted between", value1, value2, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andIsSubmittedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_submitted not between", value1, value2, "isSubmitted");
			return (Criteria) this;
		}

		public Criteria andAuditNodeIsNull() {
			addCriterion("audit_node is null");
			return (Criteria) this;
		}

		public Criteria andAuditNodeIsNotNull() {
			addCriterion("audit_node is not null");
			return (Criteria) this;
		}

		public Criteria andAuditNodeEqualTo(Integer value) {
			addCriterion("audit_node =", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeNotEqualTo(Integer value) {
			addCriterion("audit_node <>", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeGreaterThan(Integer value) {
			addCriterion("audit_node >", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeGreaterThanOrEqualTo(Integer value) {
			addCriterion("audit_node >=", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeLessThan(Integer value) {
			addCriterion("audit_node <", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeLessThanOrEqualTo(Integer value) {
			addCriterion("audit_node <=", value, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeIn(List<Integer> values) {
			addCriterion("audit_node in", values, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeNotIn(List<Integer> values) {
			addCriterion("audit_node not in", values, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeBetween(Integer value1, Integer value2) {
			addCriterion("audit_node between", value1, value2, "auditNode");
			return (Criteria) this;
		}

		public Criteria andAuditNodeNotBetween(Integer value1, Integer value2) {
			addCriterion("audit_node not between", value1, value2, "auditNode");
			return (Criteria) this;
		}

		public Criteria andIsAuditedIsNull() {
			addCriterion("is_audited is null");
			return (Criteria) this;
		}

		public Criteria andIsAuditedIsNotNull() {
			addCriterion("is_audited is not null");
			return (Criteria) this;
		}

		public Criteria andIsAuditedEqualTo(Boolean value) {
			addCriterion("is_audited =", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedNotEqualTo(Boolean value) {
			addCriterion("is_audited <>", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedGreaterThan(Boolean value) {
			addCriterion("is_audited >", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_audited >=", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedLessThan(Boolean value) {
			addCriterion("is_audited <", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedLessThanOrEqualTo(Boolean value) {
			addCriterion("is_audited <=", value, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedIn(List<Boolean> values) {
			addCriterion("is_audited in", values, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedNotIn(List<Boolean> values) {
			addCriterion("is_audited not in", values, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedBetween(Boolean value1, Boolean value2) {
			addCriterion("is_audited between", value1, value2, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsAuditedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_audited not between", value1, value2, "isAudited");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIsNull() {
			addCriterion("is_deleted is null");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIsNotNull() {
			addCriterion("is_deleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsDeletedEqualTo(Boolean value) {
			addCriterion("is_deleted =", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotEqualTo(Boolean value) {
			addCriterion("is_deleted <>", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedGreaterThan(Boolean value) {
			addCriterion("is_deleted >", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_deleted >=", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedLessThan(Boolean value) {
			addCriterion("is_deleted <", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("is_deleted <=", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIn(List<Boolean> values) {
			addCriterion("is_deleted in", values, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotIn(List<Boolean> values) {
			addCriterion("is_deleted not in", values, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("is_deleted between", value1, value2, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_deleted not between", value1, value2, "isDeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_other_factors_head
	 * @mbg.generated  Mon Jun 27 14:31:51 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_other_factors_head
     *
     * @mbg.generated do_not_delete_during_merge Thu Apr 14 16:28:57 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}