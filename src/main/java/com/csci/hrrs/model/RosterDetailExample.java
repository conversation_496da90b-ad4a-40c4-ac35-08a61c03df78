package com.csci.hrrs.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RosterDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public RosterDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(String value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(String value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(String value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(String value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(String value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLike(String value) {
            addCriterion("head_id like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotLike(String value) {
            addCriterion("head_id not like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<String> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<String> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(String value1, String value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(String value1, String value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andPernrIsNull() {
            addCriterion("pernr is null");
            return (Criteria) this;
        }

        public Criteria andPernrIsNotNull() {
            addCriterion("pernr is not null");
            return (Criteria) this;
        }

        public Criteria andPernrEqualTo(String value) {
            addCriterion("pernr =", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotEqualTo(String value) {
            addCriterion("pernr <>", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThan(String value) {
            addCriterion("pernr >", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrGreaterThanOrEqualTo(String value) {
            addCriterion("pernr >=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThan(String value) {
            addCriterion("pernr <", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLessThanOrEqualTo(String value) {
            addCriterion("pernr <=", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrLike(String value) {
            addCriterion("pernr like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotLike(String value) {
            addCriterion("pernr not like", value, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrIn(List<String> values) {
            addCriterion("pernr in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotIn(List<String> values) {
            addCriterion("pernr not in", values, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrBetween(String value1, String value2) {
            addCriterion("pernr between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andPernrNotBetween(String value1, String value2) {
            addCriterion("pernr not between", value1, value2, "pernr");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andPinyinNameIsNull() {
            addCriterion("pinyin_name is null");
            return (Criteria) this;
        }

        public Criteria andPinyinNameIsNotNull() {
            addCriterion("pinyin_name is not null");
            return (Criteria) this;
        }

        public Criteria andPinyinNameEqualTo(String value) {
            addCriterion("pinyin_name =", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameNotEqualTo(String value) {
            addCriterion("pinyin_name <>", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameGreaterThan(String value) {
            addCriterion("pinyin_name >", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameGreaterThanOrEqualTo(String value) {
            addCriterion("pinyin_name >=", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameLessThan(String value) {
            addCriterion("pinyin_name <", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameLessThanOrEqualTo(String value) {
            addCriterion("pinyin_name <=", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameLike(String value) {
            addCriterion("pinyin_name like", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameNotLike(String value) {
            addCriterion("pinyin_name not like", value, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameIn(List<String> values) {
            addCriterion("pinyin_name in", values, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameNotIn(List<String> values) {
            addCriterion("pinyin_name not in", values, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameBetween(String value1, String value2) {
            addCriterion("pinyin_name between", value1, value2, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andPinyinNameNotBetween(String value1, String value2) {
            addCriterion("pinyin_name not between", value1, value2, "pinyinName");
            return (Criteria) this;
        }

        public Criteria andEngNameIsNull() {
            addCriterion("eng_name is null");
            return (Criteria) this;
        }

        public Criteria andEngNameIsNotNull() {
            addCriterion("eng_name is not null");
            return (Criteria) this;
        }

        public Criteria andEngNameEqualTo(String value) {
            addCriterion("eng_name =", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameNotEqualTo(String value) {
            addCriterion("eng_name <>", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameGreaterThan(String value) {
            addCriterion("eng_name >", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameGreaterThanOrEqualTo(String value) {
            addCriterion("eng_name >=", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameLessThan(String value) {
            addCriterion("eng_name <", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameLessThanOrEqualTo(String value) {
            addCriterion("eng_name <=", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameLike(String value) {
            addCriterion("eng_name like", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameNotLike(String value) {
            addCriterion("eng_name not like", value, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameIn(List<String> values) {
            addCriterion("eng_name in", values, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameNotIn(List<String> values) {
            addCriterion("eng_name not in", values, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameBetween(String value1, String value2) {
            addCriterion("eng_name between", value1, value2, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameNotBetween(String value1, String value2) {
            addCriterion("eng_name not between", value1, value2, "engName");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayIsNull() {
            addCriterion("eng_name_display is null");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayIsNotNull() {
            addCriterion("eng_name_display is not null");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayEqualTo(String value) {
            addCriterion("eng_name_display =", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayNotEqualTo(String value) {
            addCriterion("eng_name_display <>", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayGreaterThan(String value) {
            addCriterion("eng_name_display >", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayGreaterThanOrEqualTo(String value) {
            addCriterion("eng_name_display >=", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayLessThan(String value) {
            addCriterion("eng_name_display <", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayLessThanOrEqualTo(String value) {
            addCriterion("eng_name_display <=", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayLike(String value) {
            addCriterion("eng_name_display like", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayNotLike(String value) {
            addCriterion("eng_name_display not like", value, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayIn(List<String> values) {
            addCriterion("eng_name_display in", values, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayNotIn(List<String> values) {
            addCriterion("eng_name_display not in", values, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayBetween(String value1, String value2) {
            addCriterion("eng_name_display between", value1, value2, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andEngNameDisplayNotBetween(String value1, String value2) {
            addCriterion("eng_name_display not between", value1, value2, "engNameDisplay");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(String value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(String value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(String value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(String value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLike(String value) {
            addCriterion("platform like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotLike(String value) {
            addCriterion("platform not like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<String> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<String> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(String value1, String value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(String value1, String value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformTradIsNull() {
            addCriterion("platform_trad is null");
            return (Criteria) this;
        }

        public Criteria andPlatformTradIsNotNull() {
            addCriterion("platform_trad is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformTradEqualTo(String value) {
            addCriterion("platform_trad =", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradNotEqualTo(String value) {
            addCriterion("platform_trad <>", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradGreaterThan(String value) {
            addCriterion("platform_trad >", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradGreaterThanOrEqualTo(String value) {
            addCriterion("platform_trad >=", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradLessThan(String value) {
            addCriterion("platform_trad <", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradLessThanOrEqualTo(String value) {
            addCriterion("platform_trad <=", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradLike(String value) {
            addCriterion("platform_trad like", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradNotLike(String value) {
            addCriterion("platform_trad not like", value, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradIn(List<String> values) {
            addCriterion("platform_trad in", values, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradNotIn(List<String> values) {
            addCriterion("platform_trad not in", values, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradBetween(String value1, String value2) {
            addCriterion("platform_trad between", value1, value2, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformTradNotBetween(String value1, String value2) {
            addCriterion("platform_trad not between", value1, value2, "platformTrad");
            return (Criteria) this;
        }

        public Criteria andPlatformEngIsNull() {
            addCriterion("platform_eng is null");
            return (Criteria) this;
        }

        public Criteria andPlatformEngIsNotNull() {
            addCriterion("platform_eng is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEngEqualTo(String value) {
            addCriterion("platform_eng =", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngNotEqualTo(String value) {
            addCriterion("platform_eng <>", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngGreaterThan(String value) {
            addCriterion("platform_eng >", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngGreaterThanOrEqualTo(String value) {
            addCriterion("platform_eng >=", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngLessThan(String value) {
            addCriterion("platform_eng <", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngLessThanOrEqualTo(String value) {
            addCriterion("platform_eng <=", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngLike(String value) {
            addCriterion("platform_eng like", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngNotLike(String value) {
            addCriterion("platform_eng not like", value, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngIn(List<String> values) {
            addCriterion("platform_eng in", values, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngNotIn(List<String> values) {
            addCriterion("platform_eng not in", values, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngBetween(String value1, String value2) {
            addCriterion("platform_eng between", value1, value2, "platformEng");
            return (Criteria) this;
        }

        public Criteria andPlatformEngNotBetween(String value1, String value2) {
            addCriterion("platform_eng not between", value1, value2, "platformEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryIsNull() {
            addCriterion("subsidiary is null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryIsNotNull() {
            addCriterion("subsidiary is not null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEqualTo(String value) {
            addCriterion("subsidiary =", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryNotEqualTo(String value) {
            addCriterion("subsidiary <>", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryGreaterThan(String value) {
            addCriterion("subsidiary >", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryGreaterThanOrEqualTo(String value) {
            addCriterion("subsidiary >=", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryLessThan(String value) {
            addCriterion("subsidiary <", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryLessThanOrEqualTo(String value) {
            addCriterion("subsidiary <=", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryLike(String value) {
            addCriterion("subsidiary like", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryNotLike(String value) {
            addCriterion("subsidiary not like", value, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryIn(List<String> values) {
            addCriterion("subsidiary in", values, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryNotIn(List<String> values) {
            addCriterion("subsidiary not in", values, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryBetween(String value1, String value2) {
            addCriterion("subsidiary between", value1, value2, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryNotBetween(String value1, String value2) {
            addCriterion("subsidiary not between", value1, value2, "subsidiary");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradIsNull() {
            addCriterion("subsidiary_trad is null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradIsNotNull() {
            addCriterion("subsidiary_trad is not null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradEqualTo(String value) {
            addCriterion("subsidiary_trad =", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradNotEqualTo(String value) {
            addCriterion("subsidiary_trad <>", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradGreaterThan(String value) {
            addCriterion("subsidiary_trad >", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradGreaterThanOrEqualTo(String value) {
            addCriterion("subsidiary_trad >=", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradLessThan(String value) {
            addCriterion("subsidiary_trad <", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradLessThanOrEqualTo(String value) {
            addCriterion("subsidiary_trad <=", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradLike(String value) {
            addCriterion("subsidiary_trad like", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradNotLike(String value) {
            addCriterion("subsidiary_trad not like", value, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradIn(List<String> values) {
            addCriterion("subsidiary_trad in", values, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradNotIn(List<String> values) {
            addCriterion("subsidiary_trad not in", values, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradBetween(String value1, String value2) {
            addCriterion("subsidiary_trad between", value1, value2, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryTradNotBetween(String value1, String value2) {
            addCriterion("subsidiary_trad not between", value1, value2, "subsidiaryTrad");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngIsNull() {
            addCriterion("subsidiary_eng is null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngIsNotNull() {
            addCriterion("subsidiary_eng is not null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngEqualTo(String value) {
            addCriterion("subsidiary_eng =", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngNotEqualTo(String value) {
            addCriterion("subsidiary_eng <>", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngGreaterThan(String value) {
            addCriterion("subsidiary_eng >", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngGreaterThanOrEqualTo(String value) {
            addCriterion("subsidiary_eng >=", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngLessThan(String value) {
            addCriterion("subsidiary_eng <", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngLessThanOrEqualTo(String value) {
            addCriterion("subsidiary_eng <=", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngLike(String value) {
            addCriterion("subsidiary_eng like", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngNotLike(String value) {
            addCriterion("subsidiary_eng not like", value, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngIn(List<String> values) {
            addCriterion("subsidiary_eng in", values, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngNotIn(List<String> values) {
            addCriterion("subsidiary_eng not in", values, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngBetween(String value1, String value2) {
            addCriterion("subsidiary_eng between", value1, value2, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryEngNotBetween(String value1, String value2) {
            addCriterion("subsidiary_eng not between", value1, value2, "subsidiaryEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNull() {
            addCriterion("sub_project is null");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNotNull() {
            addCriterion("sub_project is not null");
            return (Criteria) this;
        }

        public Criteria andSubProjectEqualTo(String value) {
            addCriterion("sub_project =", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotEqualTo(String value) {
            addCriterion("sub_project <>", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThan(String value) {
            addCriterion("sub_project >", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanOrEqualTo(String value) {
            addCriterion("sub_project >=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThan(String value) {
            addCriterion("sub_project <", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanOrEqualTo(String value) {
            addCriterion("sub_project <=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLike(String value) {
            addCriterion("sub_project like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotLike(String value) {
            addCriterion("sub_project not like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectIn(List<String> values) {
            addCriterion("sub_project in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotIn(List<String> values) {
            addCriterion("sub_project not in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectBetween(String value1, String value2) {
            addCriterion("sub_project between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotBetween(String value1, String value2) {
            addCriterion("sub_project not between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradIsNull() {
            addCriterion("sub_project_trad is null");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradIsNotNull() {
            addCriterion("sub_project_trad is not null");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradEqualTo(String value) {
            addCriterion("sub_project_trad =", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradNotEqualTo(String value) {
            addCriterion("sub_project_trad <>", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradGreaterThan(String value) {
            addCriterion("sub_project_trad >", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradGreaterThanOrEqualTo(String value) {
            addCriterion("sub_project_trad >=", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradLessThan(String value) {
            addCriterion("sub_project_trad <", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradLessThanOrEqualTo(String value) {
            addCriterion("sub_project_trad <=", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradLike(String value) {
            addCriterion("sub_project_trad like", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradNotLike(String value) {
            addCriterion("sub_project_trad not like", value, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradIn(List<String> values) {
            addCriterion("sub_project_trad in", values, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradNotIn(List<String> values) {
            addCriterion("sub_project_trad not in", values, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradBetween(String value1, String value2) {
            addCriterion("sub_project_trad between", value1, value2, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectTradNotBetween(String value1, String value2) {
            addCriterion("sub_project_trad not between", value1, value2, "subProjectTrad");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngIsNull() {
            addCriterion("sub_project_eng is null");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngIsNotNull() {
            addCriterion("sub_project_eng is not null");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngEqualTo(String value) {
            addCriterion("sub_project_eng =", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngNotEqualTo(String value) {
            addCriterion("sub_project_eng <>", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngGreaterThan(String value) {
            addCriterion("sub_project_eng >", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngGreaterThanOrEqualTo(String value) {
            addCriterion("sub_project_eng >=", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngLessThan(String value) {
            addCriterion("sub_project_eng <", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngLessThanOrEqualTo(String value) {
            addCriterion("sub_project_eng <=", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngLike(String value) {
            addCriterion("sub_project_eng like", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngNotLike(String value) {
            addCriterion("sub_project_eng not like", value, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngIn(List<String> values) {
            addCriterion("sub_project_eng in", values, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngNotIn(List<String> values) {
            addCriterion("sub_project_eng not in", values, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngBetween(String value1, String value2) {
            addCriterion("sub_project_eng between", value1, value2, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andSubProjectEngNotBetween(String value1, String value2) {
            addCriterion("sub_project_eng not between", value1, value2, "subProjectEng");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionIsNull() {
            addCriterion("primary_position is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionIsNotNull() {
            addCriterion("primary_position is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionEqualTo(String value) {
            addCriterion("primary_position =", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionNotEqualTo(String value) {
            addCriterion("primary_position <>", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionGreaterThan(String value) {
            addCriterion("primary_position >", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionGreaterThanOrEqualTo(String value) {
            addCriterion("primary_position >=", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionLessThan(String value) {
            addCriterion("primary_position <", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionLessThanOrEqualTo(String value) {
            addCriterion("primary_position <=", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionLike(String value) {
            addCriterion("primary_position like", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionNotLike(String value) {
            addCriterion("primary_position not like", value, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionIn(List<String> values) {
            addCriterion("primary_position in", values, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionNotIn(List<String> values) {
            addCriterion("primary_position not in", values, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionBetween(String value1, String value2) {
            addCriterion("primary_position between", value1, value2, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andPrimaryPositionNotBetween(String value1, String value2) {
            addCriterion("primary_position not between", value1, value2, "primaryPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionIsNull() {
            addCriterion("conc_position is null");
            return (Criteria) this;
        }

        public Criteria andConcPositionIsNotNull() {
            addCriterion("conc_position is not null");
            return (Criteria) this;
        }

        public Criteria andConcPositionEqualTo(String value) {
            addCriterion("conc_position =", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionNotEqualTo(String value) {
            addCriterion("conc_position <>", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionGreaterThan(String value) {
            addCriterion("conc_position >", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionGreaterThanOrEqualTo(String value) {
            addCriterion("conc_position >=", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionLessThan(String value) {
            addCriterion("conc_position <", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionLessThanOrEqualTo(String value) {
            addCriterion("conc_position <=", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionLike(String value) {
            addCriterion("conc_position like", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionNotLike(String value) {
            addCriterion("conc_position not like", value, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionIn(List<String> values) {
            addCriterion("conc_position in", values, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionNotIn(List<String> values) {
            addCriterion("conc_position not in", values, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionBetween(String value1, String value2) {
            addCriterion("conc_position between", value1, value2, "concPosition");
            return (Criteria) this;
        }

        public Criteria andConcPositionNotBetween(String value1, String value2) {
            addCriterion("conc_position not between", value1, value2, "concPosition");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeIsNull() {
            addCriterion("person_in_charge is null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeIsNotNull() {
            addCriterion("person_in_charge is not null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEqualTo(String value) {
            addCriterion("person_in_charge =", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeNotEqualTo(String value) {
            addCriterion("person_in_charge <>", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeGreaterThan(String value) {
            addCriterion("person_in_charge >", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeGreaterThanOrEqualTo(String value) {
            addCriterion("person_in_charge >=", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeLessThan(String value) {
            addCriterion("person_in_charge <", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeLessThanOrEqualTo(String value) {
            addCriterion("person_in_charge <=", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeLike(String value) {
            addCriterion("person_in_charge like", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeNotLike(String value) {
            addCriterion("person_in_charge not like", value, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeIn(List<String> values) {
            addCriterion("person_in_charge in", values, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeNotIn(List<String> values) {
            addCriterion("person_in_charge not in", values, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeBetween(String value1, String value2) {
            addCriterion("person_in_charge between", value1, value2, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeNotBetween(String value1, String value2) {
            addCriterion("person_in_charge not between", value1, value2, "personInCharge");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradIsNull() {
            addCriterion("person_in_charge_trad is null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradIsNotNull() {
            addCriterion("person_in_charge_trad is not null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradEqualTo(String value) {
            addCriterion("person_in_charge_trad =", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradNotEqualTo(String value) {
            addCriterion("person_in_charge_trad <>", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradGreaterThan(String value) {
            addCriterion("person_in_charge_trad >", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradGreaterThanOrEqualTo(String value) {
            addCriterion("person_in_charge_trad >=", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradLessThan(String value) {
            addCriterion("person_in_charge_trad <", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradLessThanOrEqualTo(String value) {
            addCriterion("person_in_charge_trad <=", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradLike(String value) {
            addCriterion("person_in_charge_trad like", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradNotLike(String value) {
            addCriterion("person_in_charge_trad not like", value, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradIn(List<String> values) {
            addCriterion("person_in_charge_trad in", values, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradNotIn(List<String> values) {
            addCriterion("person_in_charge_trad not in", values, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradBetween(String value1, String value2) {
            addCriterion("person_in_charge_trad between", value1, value2, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeTradNotBetween(String value1, String value2) {
            addCriterion("person_in_charge_trad not between", value1, value2, "personInChargeTrad");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngIsNull() {
            addCriterion("person_in_charge_eng is null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngIsNotNull() {
            addCriterion("person_in_charge_eng is not null");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngEqualTo(String value) {
            addCriterion("person_in_charge_eng =", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngNotEqualTo(String value) {
            addCriterion("person_in_charge_eng <>", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngGreaterThan(String value) {
            addCriterion("person_in_charge_eng >", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngGreaterThanOrEqualTo(String value) {
            addCriterion("person_in_charge_eng >=", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngLessThan(String value) {
            addCriterion("person_in_charge_eng <", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngLessThanOrEqualTo(String value) {
            addCriterion("person_in_charge_eng <=", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngLike(String value) {
            addCriterion("person_in_charge_eng like", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngNotLike(String value) {
            addCriterion("person_in_charge_eng not like", value, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngIn(List<String> values) {
            addCriterion("person_in_charge_eng in", values, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngNotIn(List<String> values) {
            addCriterion("person_in_charge_eng not in", values, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngBetween(String value1, String value2) {
            addCriterion("person_in_charge_eng between", value1, value2, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andPersonInChargeEngNotBetween(String value1, String value2) {
            addCriterion("person_in_charge_eng not between", value1, value2, "personInChargeEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIsNull() {
            addCriterion("employee_category is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIsNotNull() {
            addCriterion("employee_category is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryEqualTo(String value) {
            addCriterion("employee_category =", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotEqualTo(String value) {
            addCriterion("employee_category <>", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryGreaterThan(String value) {
            addCriterion("employee_category >", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("employee_category >=", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryLessThan(String value) {
            addCriterion("employee_category <", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryLessThanOrEqualTo(String value) {
            addCriterion("employee_category <=", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryLike(String value) {
            addCriterion("employee_category like", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotLike(String value) {
            addCriterion("employee_category not like", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIn(List<String> values) {
            addCriterion("employee_category in", values, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotIn(List<String> values) {
            addCriterion("employee_category not in", values, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryBetween(String value1, String value2) {
            addCriterion("employee_category between", value1, value2, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotBetween(String value1, String value2) {
            addCriterion("employee_category not between", value1, value2, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameIsNull() {
            addCriterion("employee_category_name is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameIsNotNull() {
            addCriterion("employee_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameEqualTo(String value) {
            addCriterion("employee_category_name =", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameNotEqualTo(String value) {
            addCriterion("employee_category_name <>", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameGreaterThan(String value) {
            addCriterion("employee_category_name >", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("employee_category_name >=", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameLessThan(String value) {
            addCriterion("employee_category_name <", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("employee_category_name <=", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameLike(String value) {
            addCriterion("employee_category_name like", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameNotLike(String value) {
            addCriterion("employee_category_name not like", value, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameIn(List<String> values) {
            addCriterion("employee_category_name in", values, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameNotIn(List<String> values) {
            addCriterion("employee_category_name not in", values, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameBetween(String value1, String value2) {
            addCriterion("employee_category_name between", value1, value2, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNameNotBetween(String value1, String value2) {
            addCriterion("employee_category_name not between", value1, value2, "employeeCategoryName");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIsNull() {
            addCriterion("work_place is null");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIsNotNull() {
            addCriterion("work_place is not null");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceEqualTo(String value) {
            addCriterion("work_place =", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotEqualTo(String value) {
            addCriterion("work_place <>", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceGreaterThan(String value) {
            addCriterion("work_place >", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("work_place >=", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLessThan(String value) {
            addCriterion("work_place <", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLessThanOrEqualTo(String value) {
            addCriterion("work_place <=", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceLike(String value) {
            addCriterion("work_place like", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotLike(String value) {
            addCriterion("work_place not like", value, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceIn(List<String> values) {
            addCriterion("work_place in", values, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotIn(List<String> values) {
            addCriterion("work_place not in", values, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceBetween(String value1, String value2) {
            addCriterion("work_place between", value1, value2, "workPlace");
            return (Criteria) this;
        }

        public Criteria andWorkPlaceNotBetween(String value1, String value2) {
            addCriterion("work_place not between", value1, value2, "workPlace");
            return (Criteria) this;
        }

        public Criteria andPositionTypeIsNull() {
            addCriterion("position_type is null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeIsNotNull() {
            addCriterion("position_type is not null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEqualTo(String value) {
            addCriterion("position_type =", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeNotEqualTo(String value) {
            addCriterion("position_type <>", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeGreaterThan(String value) {
            addCriterion("position_type >", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("position_type >=", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeLessThan(String value) {
            addCriterion("position_type <", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeLessThanOrEqualTo(String value) {
            addCriterion("position_type <=", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeLike(String value) {
            addCriterion("position_type like", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeNotLike(String value) {
            addCriterion("position_type not like", value, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeIn(List<String> values) {
            addCriterion("position_type in", values, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeNotIn(List<String> values) {
            addCriterion("position_type not in", values, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeBetween(String value1, String value2) {
            addCriterion("position_type between", value1, value2, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeNotBetween(String value1, String value2) {
            addCriterion("position_type not between", value1, value2, "positionType");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradIsNull() {
            addCriterion("position_type_trad is null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradIsNotNull() {
            addCriterion("position_type_trad is not null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradEqualTo(String value) {
            addCriterion("position_type_trad =", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradNotEqualTo(String value) {
            addCriterion("position_type_trad <>", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradGreaterThan(String value) {
            addCriterion("position_type_trad >", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradGreaterThanOrEqualTo(String value) {
            addCriterion("position_type_trad >=", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradLessThan(String value) {
            addCriterion("position_type_trad <", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradLessThanOrEqualTo(String value) {
            addCriterion("position_type_trad <=", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradLike(String value) {
            addCriterion("position_type_trad like", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradNotLike(String value) {
            addCriterion("position_type_trad not like", value, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradIn(List<String> values) {
            addCriterion("position_type_trad in", values, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradNotIn(List<String> values) {
            addCriterion("position_type_trad not in", values, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradBetween(String value1, String value2) {
            addCriterion("position_type_trad between", value1, value2, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeTradNotBetween(String value1, String value2) {
            addCriterion("position_type_trad not between", value1, value2, "positionTypeTrad");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngIsNull() {
            addCriterion("position_type_eng is null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngIsNotNull() {
            addCriterion("position_type_eng is not null");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngEqualTo(String value) {
            addCriterion("position_type_eng =", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngNotEqualTo(String value) {
            addCriterion("position_type_eng <>", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngGreaterThan(String value) {
            addCriterion("position_type_eng >", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngGreaterThanOrEqualTo(String value) {
            addCriterion("position_type_eng >=", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngLessThan(String value) {
            addCriterion("position_type_eng <", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngLessThanOrEqualTo(String value) {
            addCriterion("position_type_eng <=", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngLike(String value) {
            addCriterion("position_type_eng like", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngNotLike(String value) {
            addCriterion("position_type_eng not like", value, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngIn(List<String> values) {
            addCriterion("position_type_eng in", values, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngNotIn(List<String> values) {
            addCriterion("position_type_eng not in", values, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngBetween(String value1, String value2) {
            addCriterion("position_type_eng between", value1, value2, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andPositionTypeEngNotBetween(String value1, String value2) {
            addCriterion("position_type_eng not between", value1, value2, "positionTypeEng");
            return (Criteria) this;
        }

        public Criteria andExpertiseIsNull() {
            addCriterion("expertise is null");
            return (Criteria) this;
        }

        public Criteria andExpertiseIsNotNull() {
            addCriterion("expertise is not null");
            return (Criteria) this;
        }

        public Criteria andExpertiseEqualTo(String value) {
            addCriterion("expertise =", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseNotEqualTo(String value) {
            addCriterion("expertise <>", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseGreaterThan(String value) {
            addCriterion("expertise >", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseGreaterThanOrEqualTo(String value) {
            addCriterion("expertise >=", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseLessThan(String value) {
            addCriterion("expertise <", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseLessThanOrEqualTo(String value) {
            addCriterion("expertise <=", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseLike(String value) {
            addCriterion("expertise like", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseNotLike(String value) {
            addCriterion("expertise not like", value, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseIn(List<String> values) {
            addCriterion("expertise in", values, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseNotIn(List<String> values) {
            addCriterion("expertise not in", values, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseBetween(String value1, String value2) {
            addCriterion("expertise between", value1, value2, "expertise");
            return (Criteria) this;
        }

        public Criteria andExpertiseNotBetween(String value1, String value2) {
            addCriterion("expertise not between", value1, value2, "expertise");
            return (Criteria) this;
        }

        public Criteria andPositionLevelIsNull() {
            addCriterion("position_level is null");
            return (Criteria) this;
        }

        public Criteria andPositionLevelIsNotNull() {
            addCriterion("position_level is not null");
            return (Criteria) this;
        }

        public Criteria andPositionLevelEqualTo(String value) {
            addCriterion("position_level =", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelNotEqualTo(String value) {
            addCriterion("position_level <>", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelGreaterThan(String value) {
            addCriterion("position_level >", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelGreaterThanOrEqualTo(String value) {
            addCriterion("position_level >=", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelLessThan(String value) {
            addCriterion("position_level <", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelLessThanOrEqualTo(String value) {
            addCriterion("position_level <=", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelLike(String value) {
            addCriterion("position_level like", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelNotLike(String value) {
            addCriterion("position_level not like", value, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelIn(List<String> values) {
            addCriterion("position_level in", values, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelNotIn(List<String> values) {
            addCriterion("position_level not in", values, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelBetween(String value1, String value2) {
            addCriterion("position_level between", value1, value2, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andPositionLevelNotBetween(String value1, String value2) {
            addCriterion("position_level not between", value1, value2, "positionLevel");
            return (Criteria) this;
        }

        public Criteria andJobGradeIsNull() {
            addCriterion("job_grade is null");
            return (Criteria) this;
        }

        public Criteria andJobGradeIsNotNull() {
            addCriterion("job_grade is not null");
            return (Criteria) this;
        }

        public Criteria andJobGradeEqualTo(String value) {
            addCriterion("job_grade =", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeNotEqualTo(String value) {
            addCriterion("job_grade <>", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeGreaterThan(String value) {
            addCriterion("job_grade >", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeGreaterThanOrEqualTo(String value) {
            addCriterion("job_grade >=", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeLessThan(String value) {
            addCriterion("job_grade <", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeLessThanOrEqualTo(String value) {
            addCriterion("job_grade <=", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeLike(String value) {
            addCriterion("job_grade like", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeNotLike(String value) {
            addCriterion("job_grade not like", value, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeIn(List<String> values) {
            addCriterion("job_grade in", values, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeNotIn(List<String> values) {
            addCriterion("job_grade not in", values, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeBetween(String value1, String value2) {
            addCriterion("job_grade between", value1, value2, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andJobGradeNotBetween(String value1, String value2) {
            addCriterion("job_grade not between", value1, value2, "jobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeIsNull() {
            addCriterion("hk_job_grade is null");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeIsNotNull() {
            addCriterion("hk_job_grade is not null");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeEqualTo(String value) {
            addCriterion("hk_job_grade =", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeNotEqualTo(String value) {
            addCriterion("hk_job_grade <>", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeGreaterThan(String value) {
            addCriterion("hk_job_grade >", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeGreaterThanOrEqualTo(String value) {
            addCriterion("hk_job_grade >=", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeLessThan(String value) {
            addCriterion("hk_job_grade <", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeLessThanOrEqualTo(String value) {
            addCriterion("hk_job_grade <=", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeLike(String value) {
            addCriterion("hk_job_grade like", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeNotLike(String value) {
            addCriterion("hk_job_grade not like", value, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeIn(List<String> values) {
            addCriterion("hk_job_grade in", values, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeNotIn(List<String> values) {
            addCriterion("hk_job_grade not in", values, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeBetween(String value1, String value2) {
            addCriterion("hk_job_grade between", value1, value2, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andHkJobGradeNotBetween(String value1, String value2) {
            addCriterion("hk_job_grade not between", value1, value2, "hkJobGrade");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("gender is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("gender is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(Integer value) {
            addCriterion("gender =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(Integer value) {
            addCriterion("gender <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(Integer value) {
            addCriterion("gender >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(Integer value) {
            addCriterion("gender >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(Integer value) {
            addCriterion("gender <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(Integer value) {
            addCriterion("gender <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<Integer> values) {
            addCriterion("gender in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<Integer> values) {
            addCriterion("gender not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(Integer value1, Integer value2) {
            addCriterion("gender between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(Integer value1, Integer value2) {
            addCriterion("gender not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeIsNull() {
            addCriterion("start_work_time is null");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeIsNotNull() {
            addCriterion("start_work_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeEqualTo(LocalDate value) {
            addCriterion("start_work_time =", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeNotEqualTo(LocalDate value) {
            addCriterion("start_work_time <>", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeGreaterThan(LocalDate value) {
            addCriterion("start_work_time >", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("start_work_time >=", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeLessThan(LocalDate value) {
            addCriterion("start_work_time <", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeLessThanOrEqualTo(LocalDate value) {
            addCriterion("start_work_time <=", value, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeIn(List<LocalDate> values) {
            addCriterion("start_work_time in", values, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeNotIn(List<LocalDate> values) {
            addCriterion("start_work_time not in", values, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeBetween(LocalDate value1, LocalDate value2) {
            addCriterion("start_work_time between", value1, value2, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andStartWorkTimeNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("start_work_time not between", value1, value2, "startWorkTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeIsNull() {
            addCriterion("join_cohl_time is null");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeIsNotNull() {
            addCriterion("join_cohl_time is not null");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeEqualTo(LocalDate value) {
            addCriterion("join_cohl_time =", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeNotEqualTo(LocalDate value) {
            addCriterion("join_cohl_time <>", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeGreaterThan(LocalDate value) {
            addCriterion("join_cohl_time >", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("join_cohl_time >=", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeLessThan(LocalDate value) {
            addCriterion("join_cohl_time <", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeLessThanOrEqualTo(LocalDate value) {
            addCriterion("join_cohl_time <=", value, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeIn(List<LocalDate> values) {
            addCriterion("join_cohl_time in", values, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeNotIn(List<LocalDate> values) {
            addCriterion("join_cohl_time not in", values, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeBetween(LocalDate value1, LocalDate value2) {
            addCriterion("join_cohl_time between", value1, value2, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoinCohlTimeNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("join_cohl_time not between", value1, value2, "joinCohlTime");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeIsNull() {
            addCriterion("join_3311_time is null");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeIsNotNull() {
            addCriterion("join_3311_time is not null");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeEqualTo(LocalDate value) {
            addCriterion("join_3311_time =", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeNotEqualTo(LocalDate value) {
            addCriterion("join_3311_time <>", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeGreaterThan(LocalDate value) {
            addCriterion("join_3311_time >", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("join_3311_time >=", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeLessThan(LocalDate value) {
            addCriterion("join_3311_time <", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeLessThanOrEqualTo(LocalDate value) {
            addCriterion("join_3311_time <=", value, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeIn(List<LocalDate> values) {
            addCriterion("join_3311_time in", values, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeNotIn(List<LocalDate> values) {
            addCriterion("join_3311_time not in", values, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeBetween(LocalDate value1, LocalDate value2) {
            addCriterion("join_3311_time between", value1, value2, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andJoin3311TimeNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("join_3311_time not between", value1, value2, "join3311Time");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeIsNull() {
            addCriterion("start_overseas_time is null");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeIsNotNull() {
            addCriterion("start_overseas_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeEqualTo(LocalDate value) {
            addCriterion("start_overseas_time =", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeNotEqualTo(LocalDate value) {
            addCriterion("start_overseas_time <>", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeGreaterThan(LocalDate value) {
            addCriterion("start_overseas_time >", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("start_overseas_time >=", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeLessThan(LocalDate value) {
            addCriterion("start_overseas_time <", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeLessThanOrEqualTo(LocalDate value) {
            addCriterion("start_overseas_time <=", value, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeIn(List<LocalDate> values) {
            addCriterion("start_overseas_time in", values, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeNotIn(List<LocalDate> values) {
            addCriterion("start_overseas_time not in", values, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeBetween(LocalDate value1, LocalDate value2) {
            addCriterion("start_overseas_time between", value1, value2, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andStartOverseasTimeNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("start_overseas_time not between", value1, value2, "startOverseasTime");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosIsNull() {
            addCriterion("duration_current_pos is null");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosIsNotNull() {
            addCriterion("duration_current_pos is not null");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosEqualTo(LocalDate value) {
            addCriterion("duration_current_pos =", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosNotEqualTo(LocalDate value) {
            addCriterion("duration_current_pos <>", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosGreaterThan(LocalDate value) {
            addCriterion("duration_current_pos >", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("duration_current_pos >=", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosLessThan(LocalDate value) {
            addCriterion("duration_current_pos <", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosLessThanOrEqualTo(LocalDate value) {
            addCriterion("duration_current_pos <=", value, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosIn(List<LocalDate> values) {
            addCriterion("duration_current_pos in", values, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosNotIn(List<LocalDate> values) {
            addCriterion("duration_current_pos not in", values, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_current_pos between", value1, value2, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentPosNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_current_pos not between", value1, value2, "durationCurrentPos");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelIsNull() {
            addCriterion("duration_current_level is null");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelIsNotNull() {
            addCriterion("duration_current_level is not null");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelEqualTo(LocalDate value) {
            addCriterion("duration_current_level =", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelNotEqualTo(LocalDate value) {
            addCriterion("duration_current_level <>", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelGreaterThan(LocalDate value) {
            addCriterion("duration_current_level >", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("duration_current_level >=", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelLessThan(LocalDate value) {
            addCriterion("duration_current_level <", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelLessThanOrEqualTo(LocalDate value) {
            addCriterion("duration_current_level <=", value, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelIn(List<LocalDate> values) {
            addCriterion("duration_current_level in", values, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelNotIn(List<LocalDate> values) {
            addCriterion("duration_current_level not in", values, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_current_level between", value1, value2, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurrentLevelNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_current_level not between", value1, value2, "durationCurrentLevel");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeIsNull() {
            addCriterion("duration_cur_job_grade is null");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeIsNotNull() {
            addCriterion("duration_cur_job_grade is not null");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeEqualTo(LocalDate value) {
            addCriterion("duration_cur_job_grade =", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeNotEqualTo(LocalDate value) {
            addCriterion("duration_cur_job_grade <>", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeGreaterThan(LocalDate value) {
            addCriterion("duration_cur_job_grade >", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("duration_cur_job_grade >=", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeLessThan(LocalDate value) {
            addCriterion("duration_cur_job_grade <", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeLessThanOrEqualTo(LocalDate value) {
            addCriterion("duration_cur_job_grade <=", value, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeIn(List<LocalDate> values) {
            addCriterion("duration_cur_job_grade in", values, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeNotIn(List<LocalDate> values) {
            addCriterion("duration_cur_job_grade not in", values, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_cur_job_grade between", value1, value2, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andDurationCurJobGradeNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("duration_cur_job_grade not between", value1, value2, "durationCurJobGrade");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNull() {
            addCriterion("birthdate is null");
            return (Criteria) this;
        }

        public Criteria andBirthdateIsNotNull() {
            addCriterion("birthdate is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdateEqualTo(LocalDate value) {
            addCriterion("birthdate =", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotEqualTo(LocalDate value) {
            addCriterion("birthdate <>", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThan(LocalDate value) {
            addCriterion("birthdate >", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("birthdate >=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThan(LocalDate value) {
            addCriterion("birthdate <", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateLessThanOrEqualTo(LocalDate value) {
            addCriterion("birthdate <=", value, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateIn(List<LocalDate> values) {
            addCriterion("birthdate in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotIn(List<LocalDate> values) {
            addCriterion("birthdate not in", values, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("birthdate between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andBirthdateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("birthdate not between", value1, value2, "birthdate");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andEthnicityIsNull() {
            addCriterion("ethnicity is null");
            return (Criteria) this;
        }

        public Criteria andEthnicityIsNotNull() {
            addCriterion("ethnicity is not null");
            return (Criteria) this;
        }

        public Criteria andEthnicityEqualTo(String value) {
            addCriterion("ethnicity =", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityNotEqualTo(String value) {
            addCriterion("ethnicity <>", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityGreaterThan(String value) {
            addCriterion("ethnicity >", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityGreaterThanOrEqualTo(String value) {
            addCriterion("ethnicity >=", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityLessThan(String value) {
            addCriterion("ethnicity <", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityLessThanOrEqualTo(String value) {
            addCriterion("ethnicity <=", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityLike(String value) {
            addCriterion("ethnicity like", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityNotLike(String value) {
            addCriterion("ethnicity not like", value, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityIn(List<String> values) {
            addCriterion("ethnicity in", values, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityNotIn(List<String> values) {
            addCriterion("ethnicity not in", values, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityBetween(String value1, String value2) {
            addCriterion("ethnicity between", value1, value2, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityNotBetween(String value1, String value2) {
            addCriterion("ethnicity not between", value1, value2, "ethnicity");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradIsNull() {
            addCriterion("ethnicity_trad is null");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradIsNotNull() {
            addCriterion("ethnicity_trad is not null");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradEqualTo(String value) {
            addCriterion("ethnicity_trad =", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradNotEqualTo(String value) {
            addCriterion("ethnicity_trad <>", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradGreaterThan(String value) {
            addCriterion("ethnicity_trad >", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradGreaterThanOrEqualTo(String value) {
            addCriterion("ethnicity_trad >=", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradLessThan(String value) {
            addCriterion("ethnicity_trad <", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradLessThanOrEqualTo(String value) {
            addCriterion("ethnicity_trad <=", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradLike(String value) {
            addCriterion("ethnicity_trad like", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradNotLike(String value) {
            addCriterion("ethnicity_trad not like", value, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradIn(List<String> values) {
            addCriterion("ethnicity_trad in", values, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradNotIn(List<String> values) {
            addCriterion("ethnicity_trad not in", values, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradBetween(String value1, String value2) {
            addCriterion("ethnicity_trad between", value1, value2, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityTradNotBetween(String value1, String value2) {
            addCriterion("ethnicity_trad not between", value1, value2, "ethnicityTrad");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngIsNull() {
            addCriterion("ethnicity_eng is null");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngIsNotNull() {
            addCriterion("ethnicity_eng is not null");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngEqualTo(String value) {
            addCriterion("ethnicity_eng =", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngNotEqualTo(String value) {
            addCriterion("ethnicity_eng <>", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngGreaterThan(String value) {
            addCriterion("ethnicity_eng >", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngGreaterThanOrEqualTo(String value) {
            addCriterion("ethnicity_eng >=", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngLessThan(String value) {
            addCriterion("ethnicity_eng <", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngLessThanOrEqualTo(String value) {
            addCriterion("ethnicity_eng <=", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngLike(String value) {
            addCriterion("ethnicity_eng like", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngNotLike(String value) {
            addCriterion("ethnicity_eng not like", value, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngIn(List<String> values) {
            addCriterion("ethnicity_eng in", values, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngNotIn(List<String> values) {
            addCriterion("ethnicity_eng not in", values, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngBetween(String value1, String value2) {
            addCriterion("ethnicity_eng between", value1, value2, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andEthnicityEngNotBetween(String value1, String value2) {
            addCriterion("ethnicity_eng not between", value1, value2, "ethnicityEng");
            return (Criteria) this;
        }

        public Criteria andHometownIsNull() {
            addCriterion("hometown is null");
            return (Criteria) this;
        }

        public Criteria andHometownIsNotNull() {
            addCriterion("hometown is not null");
            return (Criteria) this;
        }

        public Criteria andHometownEqualTo(String value) {
            addCriterion("hometown =", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownNotEqualTo(String value) {
            addCriterion("hometown <>", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownGreaterThan(String value) {
            addCriterion("hometown >", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownGreaterThanOrEqualTo(String value) {
            addCriterion("hometown >=", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownLessThan(String value) {
            addCriterion("hometown <", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownLessThanOrEqualTo(String value) {
            addCriterion("hometown <=", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownLike(String value) {
            addCriterion("hometown like", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownNotLike(String value) {
            addCriterion("hometown not like", value, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownIn(List<String> values) {
            addCriterion("hometown in", values, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownNotIn(List<String> values) {
            addCriterion("hometown not in", values, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownBetween(String value1, String value2) {
            addCriterion("hometown between", value1, value2, "hometown");
            return (Criteria) this;
        }

        public Criteria andHometownNotBetween(String value1, String value2) {
            addCriterion("hometown not between", value1, value2, "hometown");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNull() {
            addCriterion("birth_place is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNotNull() {
            addCriterion("birth_place is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceEqualTo(String value) {
            addCriterion("birth_place =", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotEqualTo(String value) {
            addCriterion("birth_place <>", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThan(String value) {
            addCriterion("birth_place >", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("birth_place >=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThan(String value) {
            addCriterion("birth_place <", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThanOrEqualTo(String value) {
            addCriterion("birth_place <=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLike(String value) {
            addCriterion("birth_place like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotLike(String value) {
            addCriterion("birth_place not like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIn(List<String> values) {
            addCriterion("birth_place in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotIn(List<String> values) {
            addCriterion("birth_place not in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceBetween(String value1, String value2) {
            addCriterion("birth_place between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotBetween(String value1, String value2) {
            addCriterion("birth_place not between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andResidenceIsNull() {
            addCriterion("residence is null");
            return (Criteria) this;
        }

        public Criteria andResidenceIsNotNull() {
            addCriterion("residence is not null");
            return (Criteria) this;
        }

        public Criteria andResidenceEqualTo(String value) {
            addCriterion("residence =", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceNotEqualTo(String value) {
            addCriterion("residence <>", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceGreaterThan(String value) {
            addCriterion("residence >", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceGreaterThanOrEqualTo(String value) {
            addCriterion("residence >=", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceLessThan(String value) {
            addCriterion("residence <", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceLessThanOrEqualTo(String value) {
            addCriterion("residence <=", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceLike(String value) {
            addCriterion("residence like", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceNotLike(String value) {
            addCriterion("residence not like", value, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceIn(List<String> values) {
            addCriterion("residence in", values, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceNotIn(List<String> values) {
            addCriterion("residence not in", values, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceBetween(String value1, String value2) {
            addCriterion("residence between", value1, value2, "residence");
            return (Criteria) this;
        }

        public Criteria andResidenceNotBetween(String value1, String value2) {
            addCriterion("residence not between", value1, value2, "residence");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusIsNull() {
            addCriterion("marital_status is null");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusIsNotNull() {
            addCriterion("marital_status is not null");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusEqualTo(String value) {
            addCriterion("marital_status =", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusNotEqualTo(String value) {
            addCriterion("marital_status <>", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusGreaterThan(String value) {
            addCriterion("marital_status >", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusGreaterThanOrEqualTo(String value) {
            addCriterion("marital_status >=", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusLessThan(String value) {
            addCriterion("marital_status <", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusLessThanOrEqualTo(String value) {
            addCriterion("marital_status <=", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusLike(String value) {
            addCriterion("marital_status like", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusNotLike(String value) {
            addCriterion("marital_status not like", value, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusIn(List<String> values) {
            addCriterion("marital_status in", values, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusNotIn(List<String> values) {
            addCriterion("marital_status not in", values, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusBetween(String value1, String value2) {
            addCriterion("marital_status between", value1, value2, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andMaritalStatusNotBetween(String value1, String value2) {
            addCriterion("marital_status not between", value1, value2, "maritalStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusIsNull() {
            addCriterion("child_status is null");
            return (Criteria) this;
        }

        public Criteria andChildStatusIsNotNull() {
            addCriterion("child_status is not null");
            return (Criteria) this;
        }

        public Criteria andChildStatusEqualTo(String value) {
            addCriterion("child_status =", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusNotEqualTo(String value) {
            addCriterion("child_status <>", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusGreaterThan(String value) {
            addCriterion("child_status >", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusGreaterThanOrEqualTo(String value) {
            addCriterion("child_status >=", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusLessThan(String value) {
            addCriterion("child_status <", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusLessThanOrEqualTo(String value) {
            addCriterion("child_status <=", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusLike(String value) {
            addCriterion("child_status like", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusNotLike(String value) {
            addCriterion("child_status not like", value, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusIn(List<String> values) {
            addCriterion("child_status in", values, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusNotIn(List<String> values) {
            addCriterion("child_status not in", values, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusBetween(String value1, String value2) {
            addCriterion("child_status between", value1, value2, "childStatus");
            return (Criteria) this;
        }

        public Criteria andChildStatusNotBetween(String value1, String value2) {
            addCriterion("child_status not between", value1, value2, "childStatus");
            return (Criteria) this;
        }

        public Criteria andEducationIsNull() {
            addCriterion("education is null");
            return (Criteria) this;
        }

        public Criteria andEducationIsNotNull() {
            addCriterion("education is not null");
            return (Criteria) this;
        }

        public Criteria andEducationEqualTo(String value) {
            addCriterion("education =", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationNotEqualTo(String value) {
            addCriterion("education <>", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationGreaterThan(String value) {
            addCriterion("education >", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationGreaterThanOrEqualTo(String value) {
            addCriterion("education >=", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationLessThan(String value) {
            addCriterion("education <", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationLessThanOrEqualTo(String value) {
            addCriterion("education <=", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationLike(String value) {
            addCriterion("education like", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationNotLike(String value) {
            addCriterion("education not like", value, "education");
            return (Criteria) this;
        }

        public Criteria andEducationIn(List<String> values) {
            addCriterion("education in", values, "education");
            return (Criteria) this;
        }

        public Criteria andEducationNotIn(List<String> values) {
            addCriterion("education not in", values, "education");
            return (Criteria) this;
        }

        public Criteria andEducationBetween(String value1, String value2) {
            addCriterion("education between", value1, value2, "education");
            return (Criteria) this;
        }

        public Criteria andEducationNotBetween(String value1, String value2) {
            addCriterion("education not between", value1, value2, "education");
            return (Criteria) this;
        }

        public Criteria andMajorIsNull() {
            addCriterion("major is null");
            return (Criteria) this;
        }

        public Criteria andMajorIsNotNull() {
            addCriterion("major is not null");
            return (Criteria) this;
        }

        public Criteria andMajorEqualTo(String value) {
            addCriterion("major =", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotEqualTo(String value) {
            addCriterion("major <>", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorGreaterThan(String value) {
            addCriterion("major >", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorGreaterThanOrEqualTo(String value) {
            addCriterion("major >=", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLessThan(String value) {
            addCriterion("major <", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLessThanOrEqualTo(String value) {
            addCriterion("major <=", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorLike(String value) {
            addCriterion("major like", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotLike(String value) {
            addCriterion("major not like", value, "major");
            return (Criteria) this;
        }

        public Criteria andMajorIn(List<String> values) {
            addCriterion("major in", values, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotIn(List<String> values) {
            addCriterion("major not in", values, "major");
            return (Criteria) this;
        }

        public Criteria andMajorBetween(String value1, String value2) {
            addCriterion("major between", value1, value2, "major");
            return (Criteria) this;
        }

        public Criteria andMajorNotBetween(String value1, String value2) {
            addCriterion("major not between", value1, value2, "major");
            return (Criteria) this;
        }

        public Criteria andJobTitleIsNull() {
            addCriterion("job_title is null");
            return (Criteria) this;
        }

        public Criteria andJobTitleIsNotNull() {
            addCriterion("job_title is not null");
            return (Criteria) this;
        }

        public Criteria andJobTitleEqualTo(String value) {
            addCriterion("job_title =", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleNotEqualTo(String value) {
            addCriterion("job_title <>", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleGreaterThan(String value) {
            addCriterion("job_title >", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleGreaterThanOrEqualTo(String value) {
            addCriterion("job_title >=", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleLessThan(String value) {
            addCriterion("job_title <", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleLessThanOrEqualTo(String value) {
            addCriterion("job_title <=", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleLike(String value) {
            addCriterion("job_title like", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleNotLike(String value) {
            addCriterion("job_title not like", value, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleIn(List<String> values) {
            addCriterion("job_title in", values, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleNotIn(List<String> values) {
            addCriterion("job_title not in", values, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleBetween(String value1, String value2) {
            addCriterion("job_title between", value1, value2, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andJobTitleNotBetween(String value1, String value2) {
            addCriterion("job_title not between", value1, value2, "jobTitle");
            return (Criteria) this;
        }

        public Criteria andCredentialsIsNull() {
            addCriterion("credentials is null");
            return (Criteria) this;
        }

        public Criteria andCredentialsIsNotNull() {
            addCriterion("credentials is not null");
            return (Criteria) this;
        }

        public Criteria andCredentialsEqualTo(String value) {
            addCriterion("credentials =", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsNotEqualTo(String value) {
            addCriterion("credentials <>", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsGreaterThan(String value) {
            addCriterion("credentials >", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsGreaterThanOrEqualTo(String value) {
            addCriterion("credentials >=", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsLessThan(String value) {
            addCriterion("credentials <", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsLessThanOrEqualTo(String value) {
            addCriterion("credentials <=", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsLike(String value) {
            addCriterion("credentials like", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsNotLike(String value) {
            addCriterion("credentials not like", value, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsIn(List<String> values) {
            addCriterion("credentials in", values, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsNotIn(List<String> values) {
            addCriterion("credentials not in", values, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsBetween(String value1, String value2) {
            addCriterion("credentials between", value1, value2, "credentials");
            return (Criteria) this;
        }

        public Criteria andCredentialsNotBetween(String value1, String value2) {
            addCriterion("credentials not between", value1, value2, "credentials");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeIsNull() {
            addCriterion("public_office is null");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeIsNotNull() {
            addCriterion("public_office is not null");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeEqualTo(String value) {
            addCriterion("public_office =", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeNotEqualTo(String value) {
            addCriterion("public_office <>", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeGreaterThan(String value) {
            addCriterion("public_office >", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeGreaterThanOrEqualTo(String value) {
            addCriterion("public_office >=", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeLessThan(String value) {
            addCriterion("public_office <", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeLessThanOrEqualTo(String value) {
            addCriterion("public_office <=", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeLike(String value) {
            addCriterion("public_office like", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeNotLike(String value) {
            addCriterion("public_office not like", value, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeIn(List<String> values) {
            addCriterion("public_office in", values, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeNotIn(List<String> values) {
            addCriterion("public_office not in", values, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeBetween(String value1, String value2) {
            addCriterion("public_office between", value1, value2, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andPublicOfficeNotBetween(String value1, String value2) {
            addCriterion("public_office not between", value1, value2, "publicOffice");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andMobileIsNull() {
            addCriterion("mobile is null");
            return (Criteria) this;
        }

        public Criteria andMobileIsNotNull() {
            addCriterion("mobile is not null");
            return (Criteria) this;
        }

        public Criteria andMobileEqualTo(String value) {
            addCriterion("mobile =", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotEqualTo(String value) {
            addCriterion("mobile <>", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThan(String value) {
            addCriterion("mobile >", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThanOrEqualTo(String value) {
            addCriterion("mobile >=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThan(String value) {
            addCriterion("mobile <", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThanOrEqualTo(String value) {
            addCriterion("mobile <=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLike(String value) {
            addCriterion("mobile like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotLike(String value) {
            addCriterion("mobile not like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileIn(List<String> values) {
            addCriterion("mobile in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotIn(List<String> values) {
            addCriterion("mobile not in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileBetween(String value1, String value2) {
            addCriterion("mobile between", value1, value2, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotBetween(String value1, String value2) {
            addCriterion("mobile not between", value1, value2, "mobile");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkTradIsNull() {
            addCriterion("remark_trad is null");
            return (Criteria) this;
        }

        public Criteria andRemarkTradIsNotNull() {
            addCriterion("remark_trad is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkTradEqualTo(String value) {
            addCriterion("remark_trad =", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradNotEqualTo(String value) {
            addCriterion("remark_trad <>", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradGreaterThan(String value) {
            addCriterion("remark_trad >", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradGreaterThanOrEqualTo(String value) {
            addCriterion("remark_trad >=", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradLessThan(String value) {
            addCriterion("remark_trad <", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradLessThanOrEqualTo(String value) {
            addCriterion("remark_trad <=", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradLike(String value) {
            addCriterion("remark_trad like", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradNotLike(String value) {
            addCriterion("remark_trad not like", value, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradIn(List<String> values) {
            addCriterion("remark_trad in", values, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradNotIn(List<String> values) {
            addCriterion("remark_trad not in", values, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradBetween(String value1, String value2) {
            addCriterion("remark_trad between", value1, value2, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkTradNotBetween(String value1, String value2) {
            addCriterion("remark_trad not between", value1, value2, "remarkTrad");
            return (Criteria) this;
        }

        public Criteria andRemarkEngIsNull() {
            addCriterion("remark_eng is null");
            return (Criteria) this;
        }

        public Criteria andRemarkEngIsNotNull() {
            addCriterion("remark_eng is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEngEqualTo(String value) {
            addCriterion("remark_eng =", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngNotEqualTo(String value) {
            addCriterion("remark_eng <>", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngGreaterThan(String value) {
            addCriterion("remark_eng >", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngGreaterThanOrEqualTo(String value) {
            addCriterion("remark_eng >=", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngLessThan(String value) {
            addCriterion("remark_eng <", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngLessThanOrEqualTo(String value) {
            addCriterion("remark_eng <=", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngLike(String value) {
            addCriterion("remark_eng like", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngNotLike(String value) {
            addCriterion("remark_eng not like", value, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngIn(List<String> values) {
            addCriterion("remark_eng in", values, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngNotIn(List<String> values) {
            addCriterion("remark_eng not in", values, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngBetween(String value1, String value2) {
            addCriterion("remark_eng between", value1, value2, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andRemarkEngNotBetween(String value1, String value2) {
            addCriterion("remark_eng not between", value1, value2, "remarkEng");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNull() {
            addCriterion("organization_id is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNotNull() {
            addCriterion("organization_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdEqualTo(String value) {
            addCriterion("organization_id =", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotEqualTo(String value) {
            addCriterion("organization_id <>", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThan(String value) {
            addCriterion("organization_id >", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
            addCriterion("organization_id >=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThan(String value) {
            addCriterion("organization_id <", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
            addCriterion("organization_id <=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLike(String value) {
            addCriterion("organization_id like", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotLike(String value) {
            addCriterion("organization_id not like", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIn(List<String> values) {
            addCriterion("organization_id in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotIn(List<String> values) {
            addCriterion("organization_id not in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdBetween(String value1, String value2) {
            addCriterion("organization_id between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotBetween(String value1, String value2) {
            addCriterion("organization_id not between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeIsNull() {
            addCriterion("salary_code is null");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeIsNotNull() {
            addCriterion("salary_code is not null");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeEqualTo(String value) {
            addCriterion("salary_code =", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeNotEqualTo(String value) {
            addCriterion("salary_code <>", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeGreaterThan(String value) {
            addCriterion("salary_code >", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("salary_code >=", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeLessThan(String value) {
            addCriterion("salary_code <", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeLessThanOrEqualTo(String value) {
            addCriterion("salary_code <=", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeLike(String value) {
            addCriterion("salary_code like", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeNotLike(String value) {
            addCriterion("salary_code not like", value, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeIn(List<String> values) {
            addCriterion("salary_code in", values, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeNotIn(List<String> values) {
            addCriterion("salary_code not in", values, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeBetween(String value1, String value2) {
            addCriterion("salary_code between", value1, value2, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andSalaryCodeNotBetween(String value1, String value2) {
            addCriterion("salary_code not between", value1, value2, "salaryCode");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeIsNull() {
            addCriterion("personnel_scope is null");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeIsNotNull() {
            addCriterion("personnel_scope is not null");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeEqualTo(String value) {
            addCriterion("personnel_scope =", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeNotEqualTo(String value) {
            addCriterion("personnel_scope <>", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeGreaterThan(String value) {
            addCriterion("personnel_scope >", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeGreaterThanOrEqualTo(String value) {
            addCriterion("personnel_scope >=", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeLessThan(String value) {
            addCriterion("personnel_scope <", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeLessThanOrEqualTo(String value) {
            addCriterion("personnel_scope <=", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeLike(String value) {
            addCriterion("personnel_scope like", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeNotLike(String value) {
            addCriterion("personnel_scope not like", value, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeIn(List<String> values) {
            addCriterion("personnel_scope in", values, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeNotIn(List<String> values) {
            addCriterion("personnel_scope not in", values, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeBetween(String value1, String value2) {
            addCriterion("personnel_scope between", value1, value2, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelScopeNotBetween(String value1, String value2) {
            addCriterion("personnel_scope not between", value1, value2, "personnelScope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeIsNull() {
            addCriterion("personnel_subscope is null");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeIsNotNull() {
            addCriterion("personnel_subscope is not null");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeEqualTo(String value) {
            addCriterion("personnel_subscope =", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeNotEqualTo(String value) {
            addCriterion("personnel_subscope <>", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeGreaterThan(String value) {
            addCriterion("personnel_subscope >", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeGreaterThanOrEqualTo(String value) {
            addCriterion("personnel_subscope >=", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeLessThan(String value) {
            addCriterion("personnel_subscope <", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeLessThanOrEqualTo(String value) {
            addCriterion("personnel_subscope <=", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeLike(String value) {
            addCriterion("personnel_subscope like", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeNotLike(String value) {
            addCriterion("personnel_subscope not like", value, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeIn(List<String> values) {
            addCriterion("personnel_subscope in", values, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeNotIn(List<String> values) {
            addCriterion("personnel_subscope not in", values, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeBetween(String value1, String value2) {
            addCriterion("personnel_subscope between", value1, value2, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andPersonnelSubscopeNotBetween(String value1, String value2) {
            addCriterion("personnel_subscope not between", value1, value2, "personnelSubscope");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupIsNull() {
            addCriterion("employee_group is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupIsNotNull() {
            addCriterion("employee_group is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupEqualTo(String value) {
            addCriterion("employee_group =", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupNotEqualTo(String value) {
            addCriterion("employee_group <>", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupGreaterThan(String value) {
            addCriterion("employee_group >", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupGreaterThanOrEqualTo(String value) {
            addCriterion("employee_group >=", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupLessThan(String value) {
            addCriterion("employee_group <", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupLessThanOrEqualTo(String value) {
            addCriterion("employee_group <=", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupLike(String value) {
            addCriterion("employee_group like", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupNotLike(String value) {
            addCriterion("employee_group not like", value, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupIn(List<String> values) {
            addCriterion("employee_group in", values, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupNotIn(List<String> values) {
            addCriterion("employee_group not in", values, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupBetween(String value1, String value2) {
            addCriterion("employee_group between", value1, value2, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupNotBetween(String value1, String value2) {
            addCriterion("employee_group not between", value1, value2, "employeeGroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextIsNull() {
            addCriterion("employee_group_text is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextIsNotNull() {
            addCriterion("employee_group_text is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEqualTo(String value) {
            addCriterion("employee_group_text =", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextNotEqualTo(String value) {
            addCriterion("employee_group_text <>", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextGreaterThan(String value) {
            addCriterion("employee_group_text >", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextGreaterThanOrEqualTo(String value) {
            addCriterion("employee_group_text >=", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextLessThan(String value) {
            addCriterion("employee_group_text <", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextLessThanOrEqualTo(String value) {
            addCriterion("employee_group_text <=", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextLike(String value) {
            addCriterion("employee_group_text like", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextNotLike(String value) {
            addCriterion("employee_group_text not like", value, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextIn(List<String> values) {
            addCriterion("employee_group_text in", values, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextNotIn(List<String> values) {
            addCriterion("employee_group_text not in", values, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextBetween(String value1, String value2) {
            addCriterion("employee_group_text between", value1, value2, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextNotBetween(String value1, String value2) {
            addCriterion("employee_group_text not between", value1, value2, "employeeGroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradIsNull() {
            addCriterion("employee_group_text_trad is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradIsNotNull() {
            addCriterion("employee_group_text_trad is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradEqualTo(String value) {
            addCriterion("employee_group_text_trad =", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradNotEqualTo(String value) {
            addCriterion("employee_group_text_trad <>", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradGreaterThan(String value) {
            addCriterion("employee_group_text_trad >", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradGreaterThanOrEqualTo(String value) {
            addCriterion("employee_group_text_trad >=", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradLessThan(String value) {
            addCriterion("employee_group_text_trad <", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradLessThanOrEqualTo(String value) {
            addCriterion("employee_group_text_trad <=", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradLike(String value) {
            addCriterion("employee_group_text_trad like", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradNotLike(String value) {
            addCriterion("employee_group_text_trad not like", value, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradIn(List<String> values) {
            addCriterion("employee_group_text_trad in", values, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradNotIn(List<String> values) {
            addCriterion("employee_group_text_trad not in", values, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradBetween(String value1, String value2) {
            addCriterion("employee_group_text_trad between", value1, value2, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextTradNotBetween(String value1, String value2) {
            addCriterion("employee_group_text_trad not between", value1, value2, "employeeGroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngIsNull() {
            addCriterion("employee_group_text_eng is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngIsNotNull() {
            addCriterion("employee_group_text_eng is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngEqualTo(String value) {
            addCriterion("employee_group_text_eng =", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngNotEqualTo(String value) {
            addCriterion("employee_group_text_eng <>", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngGreaterThan(String value) {
            addCriterion("employee_group_text_eng >", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngGreaterThanOrEqualTo(String value) {
            addCriterion("employee_group_text_eng >=", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngLessThan(String value) {
            addCriterion("employee_group_text_eng <", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngLessThanOrEqualTo(String value) {
            addCriterion("employee_group_text_eng <=", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngLike(String value) {
            addCriterion("employee_group_text_eng like", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngNotLike(String value) {
            addCriterion("employee_group_text_eng not like", value, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngIn(List<String> values) {
            addCriterion("employee_group_text_eng in", values, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngNotIn(List<String> values) {
            addCriterion("employee_group_text_eng not in", values, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngBetween(String value1, String value2) {
            addCriterion("employee_group_text_eng between", value1, value2, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeGroupTextEngNotBetween(String value1, String value2) {
            addCriterion("employee_group_text_eng not between", value1, value2, "employeeGroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupIsNull() {
            addCriterion("employee_subgroup is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupIsNotNull() {
            addCriterion("employee_subgroup is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupEqualTo(String value) {
            addCriterion("employee_subgroup =", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupNotEqualTo(String value) {
            addCriterion("employee_subgroup <>", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupGreaterThan(String value) {
            addCriterion("employee_subgroup >", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupGreaterThanOrEqualTo(String value) {
            addCriterion("employee_subgroup >=", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupLessThan(String value) {
            addCriterion("employee_subgroup <", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupLessThanOrEqualTo(String value) {
            addCriterion("employee_subgroup <=", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupLike(String value) {
            addCriterion("employee_subgroup like", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupNotLike(String value) {
            addCriterion("employee_subgroup not like", value, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupIn(List<String> values) {
            addCriterion("employee_subgroup in", values, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupNotIn(List<String> values) {
            addCriterion("employee_subgroup not in", values, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupBetween(String value1, String value2) {
            addCriterion("employee_subgroup between", value1, value2, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupNotBetween(String value1, String value2) {
            addCriterion("employee_subgroup not between", value1, value2, "employeeSubgroup");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextIsNull() {
            addCriterion("employee_subgroup_text is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextIsNotNull() {
            addCriterion("employee_subgroup_text is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEqualTo(String value) {
            addCriterion("employee_subgroup_text =", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextNotEqualTo(String value) {
            addCriterion("employee_subgroup_text <>", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextGreaterThan(String value) {
            addCriterion("employee_subgroup_text >", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextGreaterThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text >=", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextLessThan(String value) {
            addCriterion("employee_subgroup_text <", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextLessThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text <=", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextLike(String value) {
            addCriterion("employee_subgroup_text like", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextNotLike(String value) {
            addCriterion("employee_subgroup_text not like", value, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextIn(List<String> values) {
            addCriterion("employee_subgroup_text in", values, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextNotIn(List<String> values) {
            addCriterion("employee_subgroup_text not in", values, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text between", value1, value2, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextNotBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text not between", value1, value2, "employeeSubgroupText");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradIsNull() {
            addCriterion("employee_subgroup_text_trad is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradIsNotNull() {
            addCriterion("employee_subgroup_text_trad is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradEqualTo(String value) {
            addCriterion("employee_subgroup_text_trad =", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradNotEqualTo(String value) {
            addCriterion("employee_subgroup_text_trad <>", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradGreaterThan(String value) {
            addCriterion("employee_subgroup_text_trad >", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradGreaterThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text_trad >=", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradLessThan(String value) {
            addCriterion("employee_subgroup_text_trad <", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradLessThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text_trad <=", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradLike(String value) {
            addCriterion("employee_subgroup_text_trad like", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradNotLike(String value) {
            addCriterion("employee_subgroup_text_trad not like", value, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradIn(List<String> values) {
            addCriterion("employee_subgroup_text_trad in", values, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradNotIn(List<String> values) {
            addCriterion("employee_subgroup_text_trad not in", values, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text_trad between", value1, value2, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextTradNotBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text_trad not between", value1, value2, "employeeSubgroupTextTrad");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngIsNull() {
            addCriterion("employee_subgroup_text_eng is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngIsNotNull() {
            addCriterion("employee_subgroup_text_eng is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngEqualTo(String value) {
            addCriterion("employee_subgroup_text_eng =", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngNotEqualTo(String value) {
            addCriterion("employee_subgroup_text_eng <>", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngGreaterThan(String value) {
            addCriterion("employee_subgroup_text_eng >", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngGreaterThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text_eng >=", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngLessThan(String value) {
            addCriterion("employee_subgroup_text_eng <", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngLessThanOrEqualTo(String value) {
            addCriterion("employee_subgroup_text_eng <=", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngLike(String value) {
            addCriterion("employee_subgroup_text_eng like", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngNotLike(String value) {
            addCriterion("employee_subgroup_text_eng not like", value, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngIn(List<String> values) {
            addCriterion("employee_subgroup_text_eng in", values, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngNotIn(List<String> values) {
            addCriterion("employee_subgroup_text_eng not in", values, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text_eng between", value1, value2, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andEmployeeSubgroupTextEngNotBetween(String value1, String value2) {
            addCriterion("employee_subgroup_text_eng not between", value1, value2, "employeeSubgroupTextEng");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardIsNull() {
            addCriterion("mainland_id_card is null");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardIsNotNull() {
            addCriterion("mainland_id_card is not null");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardEqualTo(String value) {
            addCriterion("mainland_id_card =", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardNotEqualTo(String value) {
            addCriterion("mainland_id_card <>", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardGreaterThan(String value) {
            addCriterion("mainland_id_card >", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardGreaterThanOrEqualTo(String value) {
            addCriterion("mainland_id_card >=", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardLessThan(String value) {
            addCriterion("mainland_id_card <", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardLessThanOrEqualTo(String value) {
            addCriterion("mainland_id_card <=", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardLike(String value) {
            addCriterion("mainland_id_card like", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardNotLike(String value) {
            addCriterion("mainland_id_card not like", value, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardIn(List<String> values) {
            addCriterion("mainland_id_card in", values, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardNotIn(List<String> values) {
            addCriterion("mainland_id_card not in", values, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardBetween(String value1, String value2) {
            addCriterion("mainland_id_card between", value1, value2, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andMainlandIdCardNotBetween(String value1, String value2) {
            addCriterion("mainland_id_card not between", value1, value2, "mainlandIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardIsNull() {
            addCriterion("foreign_id_card is null");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardIsNotNull() {
            addCriterion("foreign_id_card is not null");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardEqualTo(String value) {
            addCriterion("foreign_id_card =", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardNotEqualTo(String value) {
            addCriterion("foreign_id_card <>", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardGreaterThan(String value) {
            addCriterion("foreign_id_card >", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardGreaterThanOrEqualTo(String value) {
            addCriterion("foreign_id_card >=", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardLessThan(String value) {
            addCriterion("foreign_id_card <", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardLessThanOrEqualTo(String value) {
            addCriterion("foreign_id_card <=", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardLike(String value) {
            addCriterion("foreign_id_card like", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardNotLike(String value) {
            addCriterion("foreign_id_card not like", value, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardIn(List<String> values) {
            addCriterion("foreign_id_card in", values, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardNotIn(List<String> values) {
            addCriterion("foreign_id_card not in", values, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardBetween(String value1, String value2) {
            addCriterion("foreign_id_card between", value1, value2, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andForeignIdCardNotBetween(String value1, String value2) {
            addCriterion("foreign_id_card not between", value1, value2, "foreignIdCard");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportIsNull() {
            addCriterion("hk_mo_passport is null");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportIsNotNull() {
            addCriterion("hk_mo_passport is not null");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportEqualTo(String value) {
            addCriterion("hk_mo_passport =", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportNotEqualTo(String value) {
            addCriterion("hk_mo_passport <>", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportGreaterThan(String value) {
            addCriterion("hk_mo_passport >", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportGreaterThanOrEqualTo(String value) {
            addCriterion("hk_mo_passport >=", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportLessThan(String value) {
            addCriterion("hk_mo_passport <", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportLessThanOrEqualTo(String value) {
            addCriterion("hk_mo_passport <=", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportLike(String value) {
            addCriterion("hk_mo_passport like", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportNotLike(String value) {
            addCriterion("hk_mo_passport not like", value, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportIn(List<String> values) {
            addCriterion("hk_mo_passport in", values, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportNotIn(List<String> values) {
            addCriterion("hk_mo_passport not in", values, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportBetween(String value1, String value2) {
            addCriterion("hk_mo_passport between", value1, value2, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoPassportNotBetween(String value1, String value2) {
            addCriterion("hk_mo_passport not between", value1, value2, "hkMoPassport");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryIsNull() {
            addCriterion("hk_mo_end_expiry is null");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryIsNotNull() {
            addCriterion("hk_mo_end_expiry is not null");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryEqualTo(String value) {
            addCriterion("hk_mo_end_expiry =", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryNotEqualTo(String value) {
            addCriterion("hk_mo_end_expiry <>", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryGreaterThan(String value) {
            addCriterion("hk_mo_end_expiry >", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryGreaterThanOrEqualTo(String value) {
            addCriterion("hk_mo_end_expiry >=", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryLessThan(String value) {
            addCriterion("hk_mo_end_expiry <", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryLessThanOrEqualTo(String value) {
            addCriterion("hk_mo_end_expiry <=", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryLike(String value) {
            addCriterion("hk_mo_end_expiry like", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryNotLike(String value) {
            addCriterion("hk_mo_end_expiry not like", value, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryIn(List<String> values) {
            addCriterion("hk_mo_end_expiry in", values, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryNotIn(List<String> values) {
            addCriterion("hk_mo_end_expiry not in", values, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryBetween(String value1, String value2) {
            addCriterion("hk_mo_end_expiry between", value1, value2, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andHkMoEndExpiryNotBetween(String value1, String value2) {
            addCriterion("hk_mo_end_expiry not between", value1, value2, "hkMoEndExpiry");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeIsNull() {
            addCriterion("employment_mode is null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeIsNotNull() {
            addCriterion("employment_mode is not null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEqualTo(String value) {
            addCriterion("employment_mode =", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeNotEqualTo(String value) {
            addCriterion("employment_mode <>", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeGreaterThan(String value) {
            addCriterion("employment_mode >", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeGreaterThanOrEqualTo(String value) {
            addCriterion("employment_mode >=", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeLessThan(String value) {
            addCriterion("employment_mode <", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeLessThanOrEqualTo(String value) {
            addCriterion("employment_mode <=", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeLike(String value) {
            addCriterion("employment_mode like", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeNotLike(String value) {
            addCriterion("employment_mode not like", value, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeIn(List<String> values) {
            addCriterion("employment_mode in", values, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeNotIn(List<String> values) {
            addCriterion("employment_mode not in", values, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeBetween(String value1, String value2) {
            addCriterion("employment_mode between", value1, value2, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeNotBetween(String value1, String value2) {
            addCriterion("employment_mode not between", value1, value2, "employmentMode");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradIsNull() {
            addCriterion("employment_mode_trad is null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradIsNotNull() {
            addCriterion("employment_mode_trad is not null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradEqualTo(String value) {
            addCriterion("employment_mode_trad =", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradNotEqualTo(String value) {
            addCriterion("employment_mode_trad <>", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradGreaterThan(String value) {
            addCriterion("employment_mode_trad >", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradGreaterThanOrEqualTo(String value) {
            addCriterion("employment_mode_trad >=", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradLessThan(String value) {
            addCriterion("employment_mode_trad <", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradLessThanOrEqualTo(String value) {
            addCriterion("employment_mode_trad <=", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradLike(String value) {
            addCriterion("employment_mode_trad like", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradNotLike(String value) {
            addCriterion("employment_mode_trad not like", value, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradIn(List<String> values) {
            addCriterion("employment_mode_trad in", values, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradNotIn(List<String> values) {
            addCriterion("employment_mode_trad not in", values, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradBetween(String value1, String value2) {
            addCriterion("employment_mode_trad between", value1, value2, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeTradNotBetween(String value1, String value2) {
            addCriterion("employment_mode_trad not between", value1, value2, "employmentModeTrad");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngIsNull() {
            addCriterion("employment_mode_eng is null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngIsNotNull() {
            addCriterion("employment_mode_eng is not null");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngEqualTo(String value) {
            addCriterion("employment_mode_eng =", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngNotEqualTo(String value) {
            addCriterion("employment_mode_eng <>", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngGreaterThan(String value) {
            addCriterion("employment_mode_eng >", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngGreaterThanOrEqualTo(String value) {
            addCriterion("employment_mode_eng >=", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngLessThan(String value) {
            addCriterion("employment_mode_eng <", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngLessThanOrEqualTo(String value) {
            addCriterion("employment_mode_eng <=", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngLike(String value) {
            addCriterion("employment_mode_eng like", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngNotLike(String value) {
            addCriterion("employment_mode_eng not like", value, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngIn(List<String> values) {
            addCriterion("employment_mode_eng in", values, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngNotIn(List<String> values) {
            addCriterion("employment_mode_eng not in", values, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngBetween(String value1, String value2) {
            addCriterion("employment_mode_eng between", value1, value2, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andEmploymentModeEngNotBetween(String value1, String value2) {
            addCriterion("employment_mode_eng not between", value1, value2, "employmentModeEng");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationIsNull() {
            addCriterion("hzz_generation is null");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationIsNotNull() {
            addCriterion("hzz_generation is not null");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationEqualTo(String value) {
            addCriterion("hzz_generation =", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationNotEqualTo(String value) {
            addCriterion("hzz_generation <>", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationGreaterThan(String value) {
            addCriterion("hzz_generation >", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationGreaterThanOrEqualTo(String value) {
            addCriterion("hzz_generation >=", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationLessThan(String value) {
            addCriterion("hzz_generation <", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationLessThanOrEqualTo(String value) {
            addCriterion("hzz_generation <=", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationLike(String value) {
            addCriterion("hzz_generation like", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationNotLike(String value) {
            addCriterion("hzz_generation not like", value, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationIn(List<String> values) {
            addCriterion("hzz_generation in", values, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationNotIn(List<String> values) {
            addCriterion("hzz_generation not in", values, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationBetween(String value1, String value2) {
            addCriterion("hzz_generation between", value1, value2, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzGenerationNotBetween(String value1, String value2) {
            addCriterion("hzz_generation not between", value1, value2, "hzzGeneration");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceIsNull() {
            addCriterion("hzz_sequence is null");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceIsNotNull() {
            addCriterion("hzz_sequence is not null");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceEqualTo(String value) {
            addCriterion("hzz_sequence =", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceNotEqualTo(String value) {
            addCriterion("hzz_sequence <>", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceGreaterThan(String value) {
            addCriterion("hzz_sequence >", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceGreaterThanOrEqualTo(String value) {
            addCriterion("hzz_sequence >=", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceLessThan(String value) {
            addCriterion("hzz_sequence <", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceLessThanOrEqualTo(String value) {
            addCriterion("hzz_sequence <=", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceLike(String value) {
            addCriterion("hzz_sequence like", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceNotLike(String value) {
            addCriterion("hzz_sequence not like", value, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceIn(List<String> values) {
            addCriterion("hzz_sequence in", values, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceNotIn(List<String> values) {
            addCriterion("hzz_sequence not in", values, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceBetween(String value1, String value2) {
            addCriterion("hzz_sequence between", value1, value2, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andHzzSequenceNotBetween(String value1, String value2) {
            addCriterion("hzz_sequence not between", value1, value2, "hzzSequence");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusIsNull() {
            addCriterion("political_status is null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusIsNotNull() {
            addCriterion("political_status is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEqualTo(String value) {
            addCriterion("political_status =", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusNotEqualTo(String value) {
            addCriterion("political_status <>", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusGreaterThan(String value) {
            addCriterion("political_status >", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusGreaterThanOrEqualTo(String value) {
            addCriterion("political_status >=", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusLessThan(String value) {
            addCriterion("political_status <", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusLessThanOrEqualTo(String value) {
            addCriterion("political_status <=", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusLike(String value) {
            addCriterion("political_status like", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusNotLike(String value) {
            addCriterion("political_status not like", value, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusIn(List<String> values) {
            addCriterion("political_status in", values, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusNotIn(List<String> values) {
            addCriterion("political_status not in", values, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusBetween(String value1, String value2) {
            addCriterion("political_status between", value1, value2, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusNotBetween(String value1, String value2) {
            addCriterion("political_status not between", value1, value2, "politicalStatus");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradIsNull() {
            addCriterion("political_status_trad is null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradIsNotNull() {
            addCriterion("political_status_trad is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradEqualTo(String value) {
            addCriterion("political_status_trad =", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradNotEqualTo(String value) {
            addCriterion("political_status_trad <>", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradGreaterThan(String value) {
            addCriterion("political_status_trad >", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradGreaterThanOrEqualTo(String value) {
            addCriterion("political_status_trad >=", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradLessThan(String value) {
            addCriterion("political_status_trad <", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradLessThanOrEqualTo(String value) {
            addCriterion("political_status_trad <=", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradLike(String value) {
            addCriterion("political_status_trad like", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradNotLike(String value) {
            addCriterion("political_status_trad not like", value, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradIn(List<String> values) {
            addCriterion("political_status_trad in", values, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradNotIn(List<String> values) {
            addCriterion("political_status_trad not in", values, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradBetween(String value1, String value2) {
            addCriterion("political_status_trad between", value1, value2, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusTradNotBetween(String value1, String value2) {
            addCriterion("political_status_trad not between", value1, value2, "politicalStatusTrad");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngIsNull() {
            addCriterion("political_status_eng is null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngIsNotNull() {
            addCriterion("political_status_eng is not null");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngEqualTo(String value) {
            addCriterion("political_status_eng =", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngNotEqualTo(String value) {
            addCriterion("political_status_eng <>", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngGreaterThan(String value) {
            addCriterion("political_status_eng >", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngGreaterThanOrEqualTo(String value) {
            addCriterion("political_status_eng >=", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngLessThan(String value) {
            addCriterion("political_status_eng <", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngLessThanOrEqualTo(String value) {
            addCriterion("political_status_eng <=", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngLike(String value) {
            addCriterion("political_status_eng like", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngNotLike(String value) {
            addCriterion("political_status_eng not like", value, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngIn(List<String> values) {
            addCriterion("political_status_eng in", values, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngNotIn(List<String> values) {
            addCriterion("political_status_eng not in", values, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngBetween(String value1, String value2) {
            addCriterion("political_status_eng between", value1, value2, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPoliticalStatusEngNotBetween(String value1, String value2) {
            addCriterion("political_status_eng not between", value1, value2, "politicalStatusEng");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateIsNull() {
            addCriterion("party_joining_date is null");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateIsNotNull() {
            addCriterion("party_joining_date is not null");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateEqualTo(LocalDate value) {
            addCriterion("party_joining_date =", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateNotEqualTo(LocalDate value) {
            addCriterion("party_joining_date <>", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateGreaterThan(LocalDate value) {
            addCriterion("party_joining_date >", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("party_joining_date >=", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateLessThan(LocalDate value) {
            addCriterion("party_joining_date <", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateLessThanOrEqualTo(LocalDate value) {
            addCriterion("party_joining_date <=", value, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateIn(List<LocalDate> values) {
            addCriterion("party_joining_date in", values, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateNotIn(List<LocalDate> values) {
            addCriterion("party_joining_date not in", values, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("party_joining_date between", value1, value2, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andPartyJoiningDateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("party_joining_date not between", value1, value2, "partyJoiningDate");
            return (Criteria) this;
        }

        public Criteria andSpecialtyIsNull() {
            addCriterion("specialty is null");
            return (Criteria) this;
        }

        public Criteria andSpecialtyIsNotNull() {
            addCriterion("specialty is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialtyEqualTo(String value) {
            addCriterion("specialty =", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyNotEqualTo(String value) {
            addCriterion("specialty <>", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyGreaterThan(String value) {
            addCriterion("specialty >", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyGreaterThanOrEqualTo(String value) {
            addCriterion("specialty >=", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyLessThan(String value) {
            addCriterion("specialty <", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyLessThanOrEqualTo(String value) {
            addCriterion("specialty <=", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyLike(String value) {
            addCriterion("specialty like", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyNotLike(String value) {
            addCriterion("specialty not like", value, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyIn(List<String> values) {
            addCriterion("specialty in", values, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyNotIn(List<String> values) {
            addCriterion("specialty not in", values, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyBetween(String value1, String value2) {
            addCriterion("specialty between", value1, value2, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpecialtyNotBetween(String value1, String value2) {
            addCriterion("specialty not between", value1, value2, "specialty");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationIsNull() {
            addCriterion("spouse_location is null");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationIsNotNull() {
            addCriterion("spouse_location is not null");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationEqualTo(String value) {
            addCriterion("spouse_location =", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationNotEqualTo(String value) {
            addCriterion("spouse_location <>", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationGreaterThan(String value) {
            addCriterion("spouse_location >", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationGreaterThanOrEqualTo(String value) {
            addCriterion("spouse_location >=", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationLessThan(String value) {
            addCriterion("spouse_location <", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationLessThanOrEqualTo(String value) {
            addCriterion("spouse_location <=", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationLike(String value) {
            addCriterion("spouse_location like", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationNotLike(String value) {
            addCriterion("spouse_location not like", value, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationIn(List<String> values) {
            addCriterion("spouse_location in", values, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationNotIn(List<String> values) {
            addCriterion("spouse_location not in", values, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationBetween(String value1, String value2) {
            addCriterion("spouse_location between", value1, value2, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andSpouseLocationNotBetween(String value1, String value2) {
            addCriterion("spouse_location not between", value1, value2, "spouseLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationIsNull() {
            addCriterion("parents_location is null");
            return (Criteria) this;
        }

        public Criteria andParentsLocationIsNotNull() {
            addCriterion("parents_location is not null");
            return (Criteria) this;
        }

        public Criteria andParentsLocationEqualTo(String value) {
            addCriterion("parents_location =", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationNotEqualTo(String value) {
            addCriterion("parents_location <>", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationGreaterThan(String value) {
            addCriterion("parents_location >", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationGreaterThanOrEqualTo(String value) {
            addCriterion("parents_location >=", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationLessThan(String value) {
            addCriterion("parents_location <", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationLessThanOrEqualTo(String value) {
            addCriterion("parents_location <=", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationLike(String value) {
            addCriterion("parents_location like", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationNotLike(String value) {
            addCriterion("parents_location not like", value, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationIn(List<String> values) {
            addCriterion("parents_location in", values, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationNotIn(List<String> values) {
            addCriterion("parents_location not in", values, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationBetween(String value1, String value2) {
            addCriterion("parents_location between", value1, value2, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andParentsLocationNotBetween(String value1, String value2) {
            addCriterion("parents_location not between", value1, value2, "parentsLocation");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIsNull() {
            addCriterion("blood_type is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIsNotNull() {
            addCriterion("blood_type is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEqualTo(String value) {
            addCriterion("blood_type =", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotEqualTo(String value) {
            addCriterion("blood_type <>", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeGreaterThan(String value) {
            addCriterion("blood_type >", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type >=", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLessThan(String value) {
            addCriterion("blood_type <", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLessThanOrEqualTo(String value) {
            addCriterion("blood_type <=", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeLike(String value) {
            addCriterion("blood_type like", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotLike(String value) {
            addCriterion("blood_type not like", value, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeIn(List<String> values) {
            addCriterion("blood_type in", values, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotIn(List<String> values) {
            addCriterion("blood_type not in", values, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeBetween(String value1, String value2) {
            addCriterion("blood_type between", value1, value2, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeNotBetween(String value1, String value2) {
            addCriterion("blood_type not between", value1, value2, "bloodType");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradIsNull() {
            addCriterion("blood_type_trad is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradIsNotNull() {
            addCriterion("blood_type_trad is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradEqualTo(String value) {
            addCriterion("blood_type_trad =", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradNotEqualTo(String value) {
            addCriterion("blood_type_trad <>", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradGreaterThan(String value) {
            addCriterion("blood_type_trad >", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type_trad >=", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradLessThan(String value) {
            addCriterion("blood_type_trad <", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradLessThanOrEqualTo(String value) {
            addCriterion("blood_type_trad <=", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradLike(String value) {
            addCriterion("blood_type_trad like", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradNotLike(String value) {
            addCriterion("blood_type_trad not like", value, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradIn(List<String> values) {
            addCriterion("blood_type_trad in", values, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradNotIn(List<String> values) {
            addCriterion("blood_type_trad not in", values, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradBetween(String value1, String value2) {
            addCriterion("blood_type_trad between", value1, value2, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeTradNotBetween(String value1, String value2) {
            addCriterion("blood_type_trad not between", value1, value2, "bloodTypeTrad");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngIsNull() {
            addCriterion("blood_type_eng is null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngIsNotNull() {
            addCriterion("blood_type_eng is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngEqualTo(String value) {
            addCriterion("blood_type_eng =", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngNotEqualTo(String value) {
            addCriterion("blood_type_eng <>", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngGreaterThan(String value) {
            addCriterion("blood_type_eng >", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngGreaterThanOrEqualTo(String value) {
            addCriterion("blood_type_eng >=", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngLessThan(String value) {
            addCriterion("blood_type_eng <", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngLessThanOrEqualTo(String value) {
            addCriterion("blood_type_eng <=", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngLike(String value) {
            addCriterion("blood_type_eng like", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngNotLike(String value) {
            addCriterion("blood_type_eng not like", value, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngIn(List<String> values) {
            addCriterion("blood_type_eng in", values, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngNotIn(List<String> values) {
            addCriterion("blood_type_eng not in", values, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngBetween(String value1, String value2) {
            addCriterion("blood_type_eng between", value1, value2, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andBloodTypeEngNotBetween(String value1, String value2) {
            addCriterion("blood_type_eng not between", value1, value2, "bloodTypeEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionIsNull() {
            addCriterion("health_condition is null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionIsNotNull() {
            addCriterion("health_condition is not null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEqualTo(String value) {
            addCriterion("health_condition =", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionNotEqualTo(String value) {
            addCriterion("health_condition <>", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionGreaterThan(String value) {
            addCriterion("health_condition >", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionGreaterThanOrEqualTo(String value) {
            addCriterion("health_condition >=", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionLessThan(String value) {
            addCriterion("health_condition <", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionLessThanOrEqualTo(String value) {
            addCriterion("health_condition <=", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionLike(String value) {
            addCriterion("health_condition like", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionNotLike(String value) {
            addCriterion("health_condition not like", value, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionIn(List<String> values) {
            addCriterion("health_condition in", values, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionNotIn(List<String> values) {
            addCriterion("health_condition not in", values, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionBetween(String value1, String value2) {
            addCriterion("health_condition between", value1, value2, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionNotBetween(String value1, String value2) {
            addCriterion("health_condition not between", value1, value2, "healthCondition");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradIsNull() {
            addCriterion("health_condition_trad is null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradIsNotNull() {
            addCriterion("health_condition_trad is not null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradEqualTo(String value) {
            addCriterion("health_condition_trad =", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradNotEqualTo(String value) {
            addCriterion("health_condition_trad <>", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradGreaterThan(String value) {
            addCriterion("health_condition_trad >", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradGreaterThanOrEqualTo(String value) {
            addCriterion("health_condition_trad >=", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradLessThan(String value) {
            addCriterion("health_condition_trad <", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradLessThanOrEqualTo(String value) {
            addCriterion("health_condition_trad <=", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradLike(String value) {
            addCriterion("health_condition_trad like", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradNotLike(String value) {
            addCriterion("health_condition_trad not like", value, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradIn(List<String> values) {
            addCriterion("health_condition_trad in", values, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradNotIn(List<String> values) {
            addCriterion("health_condition_trad not in", values, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradBetween(String value1, String value2) {
            addCriterion("health_condition_trad between", value1, value2, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionTradNotBetween(String value1, String value2) {
            addCriterion("health_condition_trad not between", value1, value2, "healthConditionTrad");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngIsNull() {
            addCriterion("health_condition_eng is null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngIsNotNull() {
            addCriterion("health_condition_eng is not null");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngEqualTo(String value) {
            addCriterion("health_condition_eng =", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngNotEqualTo(String value) {
            addCriterion("health_condition_eng <>", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngGreaterThan(String value) {
            addCriterion("health_condition_eng >", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngGreaterThanOrEqualTo(String value) {
            addCriterion("health_condition_eng >=", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngLessThan(String value) {
            addCriterion("health_condition_eng <", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngLessThanOrEqualTo(String value) {
            addCriterion("health_condition_eng <=", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngLike(String value) {
            addCriterion("health_condition_eng like", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngNotLike(String value) {
            addCriterion("health_condition_eng not like", value, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngIn(List<String> values) {
            addCriterion("health_condition_eng in", values, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngNotIn(List<String> values) {
            addCriterion("health_condition_eng not in", values, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngBetween(String value1, String value2) {
            addCriterion("health_condition_eng between", value1, value2, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andHealthConditionEngNotBetween(String value1, String value2) {
            addCriterion("health_condition_eng not between", value1, value2, "healthConditionEng");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesIsNull() {
            addCriterion("interests_hobbies is null");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesIsNotNull() {
            addCriterion("interests_hobbies is not null");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesEqualTo(String value) {
            addCriterion("interests_hobbies =", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesNotEqualTo(String value) {
            addCriterion("interests_hobbies <>", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesGreaterThan(String value) {
            addCriterion("interests_hobbies >", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesGreaterThanOrEqualTo(String value) {
            addCriterion("interests_hobbies >=", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesLessThan(String value) {
            addCriterion("interests_hobbies <", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesLessThanOrEqualTo(String value) {
            addCriterion("interests_hobbies <=", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesLike(String value) {
            addCriterion("interests_hobbies like", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesNotLike(String value) {
            addCriterion("interests_hobbies not like", value, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesIn(List<String> values) {
            addCriterion("interests_hobbies in", values, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesNotIn(List<String> values) {
            addCriterion("interests_hobbies not in", values, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesBetween(String value1, String value2) {
            addCriterion("interests_hobbies between", value1, value2, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andInterestsHobbiesNotBetween(String value1, String value2) {
            addCriterion("interests_hobbies not between", value1, value2, "interestsHobbies");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyIsNull() {
            addCriterion("archive_company is null");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyIsNotNull() {
            addCriterion("archive_company is not null");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyEqualTo(String value) {
            addCriterion("archive_company =", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyNotEqualTo(String value) {
            addCriterion("archive_company <>", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyGreaterThan(String value) {
            addCriterion("archive_company >", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("archive_company >=", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyLessThan(String value) {
            addCriterion("archive_company <", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyLessThanOrEqualTo(String value) {
            addCriterion("archive_company <=", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyLike(String value) {
            addCriterion("archive_company like", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyNotLike(String value) {
            addCriterion("archive_company not like", value, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyIn(List<String> values) {
            addCriterion("archive_company in", values, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyNotIn(List<String> values) {
            addCriterion("archive_company not in", values, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyBetween(String value1, String value2) {
            addCriterion("archive_company between", value1, value2, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andArchiveCompanyNotBetween(String value1, String value2) {
            addCriterion("archive_company not between", value1, value2, "archiveCompany");
            return (Criteria) this;
        }

        public Criteria andResLocIsNull() {
            addCriterion("res_loc is null");
            return (Criteria) this;
        }

        public Criteria andResLocIsNotNull() {
            addCriterion("res_loc is not null");
            return (Criteria) this;
        }

        public Criteria andResLocEqualTo(String value) {
            addCriterion("res_loc =", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocNotEqualTo(String value) {
            addCriterion("res_loc <>", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocGreaterThan(String value) {
            addCriterion("res_loc >", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocGreaterThanOrEqualTo(String value) {
            addCriterion("res_loc >=", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocLessThan(String value) {
            addCriterion("res_loc <", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocLessThanOrEqualTo(String value) {
            addCriterion("res_loc <=", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocLike(String value) {
            addCriterion("res_loc like", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocNotLike(String value) {
            addCriterion("res_loc not like", value, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocIn(List<String> values) {
            addCriterion("res_loc in", values, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocNotIn(List<String> values) {
            addCriterion("res_loc not in", values, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocBetween(String value1, String value2) {
            addCriterion("res_loc between", value1, value2, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResLocNotBetween(String value1, String value2) {
            addCriterion("res_loc not between", value1, value2, "resLoc");
            return (Criteria) this;
        }

        public Criteria andResTypeIsNull() {
            addCriterion("res_type is null");
            return (Criteria) this;
        }

        public Criteria andResTypeIsNotNull() {
            addCriterion("res_type is not null");
            return (Criteria) this;
        }

        public Criteria andResTypeEqualTo(String value) {
            addCriterion("res_type =", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeNotEqualTo(String value) {
            addCriterion("res_type <>", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeGreaterThan(String value) {
            addCriterion("res_type >", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeGreaterThanOrEqualTo(String value) {
            addCriterion("res_type >=", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeLessThan(String value) {
            addCriterion("res_type <", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeLessThanOrEqualTo(String value) {
            addCriterion("res_type <=", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeLike(String value) {
            addCriterion("res_type like", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeNotLike(String value) {
            addCriterion("res_type not like", value, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeIn(List<String> values) {
            addCriterion("res_type in", values, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeNotIn(List<String> values) {
            addCriterion("res_type not in", values, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeBetween(String value1, String value2) {
            addCriterion("res_type between", value1, value2, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeNotBetween(String value1, String value2) {
            addCriterion("res_type not between", value1, value2, "resType");
            return (Criteria) this;
        }

        public Criteria andResTypeTradIsNull() {
            addCriterion("res_type_trad is null");
            return (Criteria) this;
        }

        public Criteria andResTypeTradIsNotNull() {
            addCriterion("res_type_trad is not null");
            return (Criteria) this;
        }

        public Criteria andResTypeTradEqualTo(String value) {
            addCriterion("res_type_trad =", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradNotEqualTo(String value) {
            addCriterion("res_type_trad <>", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradGreaterThan(String value) {
            addCriterion("res_type_trad >", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradGreaterThanOrEqualTo(String value) {
            addCriterion("res_type_trad >=", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradLessThan(String value) {
            addCriterion("res_type_trad <", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradLessThanOrEqualTo(String value) {
            addCriterion("res_type_trad <=", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradLike(String value) {
            addCriterion("res_type_trad like", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradNotLike(String value) {
            addCriterion("res_type_trad not like", value, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradIn(List<String> values) {
            addCriterion("res_type_trad in", values, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradNotIn(List<String> values) {
            addCriterion("res_type_trad not in", values, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradBetween(String value1, String value2) {
            addCriterion("res_type_trad between", value1, value2, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeTradNotBetween(String value1, String value2) {
            addCriterion("res_type_trad not between", value1, value2, "resTypeTrad");
            return (Criteria) this;
        }

        public Criteria andResTypeEngIsNull() {
            addCriterion("res_type_eng is null");
            return (Criteria) this;
        }

        public Criteria andResTypeEngIsNotNull() {
            addCriterion("res_type_eng is not null");
            return (Criteria) this;
        }

        public Criteria andResTypeEngEqualTo(String value) {
            addCriterion("res_type_eng =", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngNotEqualTo(String value) {
            addCriterion("res_type_eng <>", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngGreaterThan(String value) {
            addCriterion("res_type_eng >", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngGreaterThanOrEqualTo(String value) {
            addCriterion("res_type_eng >=", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngLessThan(String value) {
            addCriterion("res_type_eng <", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngLessThanOrEqualTo(String value) {
            addCriterion("res_type_eng <=", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngLike(String value) {
            addCriterion("res_type_eng like", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngNotLike(String value) {
            addCriterion("res_type_eng not like", value, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngIn(List<String> values) {
            addCriterion("res_type_eng in", values, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngNotIn(List<String> values) {
            addCriterion("res_type_eng not in", values, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngBetween(String value1, String value2) {
            addCriterion("res_type_eng between", value1, value2, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andResTypeEngNotBetween(String value1, String value2) {
            addCriterion("res_type_eng not between", value1, value2, "resTypeEng");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsSyncIsNull() {
            addCriterion("is_sync is null");
            return (Criteria) this;
        }

        public Criteria andIsSyncIsNotNull() {
            addCriterion("is_sync is not null");
            return (Criteria) this;
        }

        public Criteria andIsSyncEqualTo(Boolean value) {
            addCriterion("is_sync =", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncNotEqualTo(Boolean value) {
            addCriterion("is_sync <>", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncGreaterThan(Boolean value) {
            addCriterion("is_sync >", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_sync >=", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncLessThan(Boolean value) {
            addCriterion("is_sync <", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncLessThanOrEqualTo(Boolean value) {
            addCriterion("is_sync <=", value, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncIn(List<Boolean> values) {
            addCriterion("is_sync in", values, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncNotIn(List<Boolean> values) {
            addCriterion("is_sync not in", values, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncBetween(Boolean value1, Boolean value2) {
            addCriterion("is_sync between", value1, value2, "isSync");
            return (Criteria) this;
        }

        public Criteria andIsSyncNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_sync not between", value1, value2, "isSync");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionIsNull() {
            addCriterion("years_in_current_position is null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionIsNotNull() {
            addCriterion("years_in_current_position is not null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionEqualTo(BigDecimal value) {
            addCriterion("years_in_current_position =", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionNotEqualTo(BigDecimal value) {
            addCriterion("years_in_current_position <>", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionGreaterThan(BigDecimal value) {
            addCriterion("years_in_current_position >", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_position >=", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionLessThan(BigDecimal value) {
            addCriterion("years_in_current_position <", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_position <=", value, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionIn(List<BigDecimal> values) {
            addCriterion("years_in_current_position in", values, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionNotIn(List<BigDecimal> values) {
            addCriterion("years_in_current_position not in", values, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_position between", value1, value2, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentPositionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_position not between", value1, value2, "yearsInCurrentPosition");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelIsNull() {
            addCriterion("years_in_current_job_level is null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelIsNotNull() {
            addCriterion("years_in_current_job_level is not null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelEqualTo(BigDecimal value) {
            addCriterion("years_in_current_job_level =", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelNotEqualTo(BigDecimal value) {
            addCriterion("years_in_current_job_level <>", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelGreaterThan(BigDecimal value) {
            addCriterion("years_in_current_job_level >", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_job_level >=", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelLessThan(BigDecimal value) {
            addCriterion("years_in_current_job_level <", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelLessThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_job_level <=", value, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelIn(List<BigDecimal> values) {
            addCriterion("years_in_current_job_level in", values, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelNotIn(List<BigDecimal> values) {
            addCriterion("years_in_current_job_level not in", values, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_job_level between", value1, value2, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentJobLevelNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_job_level not between", value1, value2, "yearsInCurrentJobLevel");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankIsNull() {
            addCriterion("years_in_current_rank is null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankIsNotNull() {
            addCriterion("years_in_current_rank is not null");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankEqualTo(BigDecimal value) {
            addCriterion("years_in_current_rank =", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankNotEqualTo(BigDecimal value) {
            addCriterion("years_in_current_rank <>", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankGreaterThan(BigDecimal value) {
            addCriterion("years_in_current_rank >", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_rank >=", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankLessThan(BigDecimal value) {
            addCriterion("years_in_current_rank <", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankLessThanOrEqualTo(BigDecimal value) {
            addCriterion("years_in_current_rank <=", value, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankIn(List<BigDecimal> values) {
            addCriterion("years_in_current_rank in", values, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankNotIn(List<BigDecimal> values) {
            addCriterion("years_in_current_rank not in", values, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_rank between", value1, value2, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andYearsInCurrentRankNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("years_in_current_rank not between", value1, value2, "yearsInCurrentRank");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementIsNull() {
            addCriterion("talent_inventory_placement is null");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementIsNotNull() {
            addCriterion("talent_inventory_placement is not null");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementEqualTo(String value) {
            addCriterion("talent_inventory_placement =", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementNotEqualTo(String value) {
            addCriterion("talent_inventory_placement <>", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementGreaterThan(String value) {
            addCriterion("talent_inventory_placement >", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementGreaterThanOrEqualTo(String value) {
            addCriterion("talent_inventory_placement >=", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementLessThan(String value) {
            addCriterion("talent_inventory_placement <", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementLessThanOrEqualTo(String value) {
            addCriterion("talent_inventory_placement <=", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementLike(String value) {
            addCriterion("talent_inventory_placement like", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementNotLike(String value) {
            addCriterion("talent_inventory_placement not like", value, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementIn(List<String> values) {
            addCriterion("talent_inventory_placement in", values, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementNotIn(List<String> values) {
            addCriterion("talent_inventory_placement not in", values, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementBetween(String value1, String value2) {
            addCriterion("talent_inventory_placement between", value1, value2, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andTalentInventoryPlacementNotBetween(String value1, String value2) {
            addCriterion("talent_inventory_placement not between", value1, value2, "talentInventoryPlacement");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1IsNull() {
            addCriterion("appraisal_result_year1 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1IsNotNull() {
            addCriterion("appraisal_result_year1 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1EqualTo(String value) {
            addCriterion("appraisal_result_year1 =", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1NotEqualTo(String value) {
            addCriterion("appraisal_result_year1 <>", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1GreaterThan(String value) {
            addCriterion("appraisal_result_year1 >", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year1 >=", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1LessThan(String value) {
            addCriterion("appraisal_result_year1 <", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1LessThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year1 <=", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1Like(String value) {
            addCriterion("appraisal_result_year1 like", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1NotLike(String value) {
            addCriterion("appraisal_result_year1 not like", value, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1In(List<String> values) {
            addCriterion("appraisal_result_year1 in", values, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1NotIn(List<String> values) {
            addCriterion("appraisal_result_year1 not in", values, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1Between(String value1, String value2) {
            addCriterion("appraisal_result_year1 between", value1, value2, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear1NotBetween(String value1, String value2) {
            addCriterion("appraisal_result_year1 not between", value1, value2, "appraisalResultYear1");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2IsNull() {
            addCriterion("appraisal_result_year2 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2IsNotNull() {
            addCriterion("appraisal_result_year2 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2EqualTo(String value) {
            addCriterion("appraisal_result_year2 =", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2NotEqualTo(String value) {
            addCriterion("appraisal_result_year2 <>", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2GreaterThan(String value) {
            addCriterion("appraisal_result_year2 >", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year2 >=", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2LessThan(String value) {
            addCriterion("appraisal_result_year2 <", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2LessThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year2 <=", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2Like(String value) {
            addCriterion("appraisal_result_year2 like", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2NotLike(String value) {
            addCriterion("appraisal_result_year2 not like", value, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2In(List<String> values) {
            addCriterion("appraisal_result_year2 in", values, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2NotIn(List<String> values) {
            addCriterion("appraisal_result_year2 not in", values, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2Between(String value1, String value2) {
            addCriterion("appraisal_result_year2 between", value1, value2, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear2NotBetween(String value1, String value2) {
            addCriterion("appraisal_result_year2 not between", value1, value2, "appraisalResultYear2");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3IsNull() {
            addCriterion("appraisal_result_year3 is null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3IsNotNull() {
            addCriterion("appraisal_result_year3 is not null");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3EqualTo(String value) {
            addCriterion("appraisal_result_year3 =", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3NotEqualTo(String value) {
            addCriterion("appraisal_result_year3 <>", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3GreaterThan(String value) {
            addCriterion("appraisal_result_year3 >", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3GreaterThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year3 >=", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3LessThan(String value) {
            addCriterion("appraisal_result_year3 <", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3LessThanOrEqualTo(String value) {
            addCriterion("appraisal_result_year3 <=", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3Like(String value) {
            addCriterion("appraisal_result_year3 like", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3NotLike(String value) {
            addCriterion("appraisal_result_year3 not like", value, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3In(List<String> values) {
            addCriterion("appraisal_result_year3 in", values, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3NotIn(List<String> values) {
            addCriterion("appraisal_result_year3 not in", values, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3Between(String value1, String value2) {
            addCriterion("appraisal_result_year3 between", value1, value2, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andAppraisalResultYear3NotBetween(String value1, String value2) {
            addCriterion("appraisal_result_year3 not between", value1, value2, "appraisalResultYear3");
            return (Criteria) this;
        }

        public Criteria andPhotoIsNull() {
            addCriterion("photo is null");
            return (Criteria) this;
        }

        public Criteria andPhotoIsNotNull() {
            addCriterion("photo is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoEqualTo(String value) {
            addCriterion("photo =", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotEqualTo(String value) {
            addCriterion("photo <>", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoGreaterThan(String value) {
            addCriterion("photo >", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoGreaterThanOrEqualTo(String value) {
            addCriterion("photo >=", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLessThan(String value) {
            addCriterion("photo <", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLessThanOrEqualTo(String value) {
            addCriterion("photo <=", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoLike(String value) {
            addCriterion("photo like", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotLike(String value) {
            addCriterion("photo not like", value, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoIn(List<String> values) {
            addCriterion("photo in", values, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotIn(List<String> values) {
            addCriterion("photo not in", values, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoBetween(String value1, String value2) {
            addCriterion("photo between", value1, value2, "photo");
            return (Criteria) this;
        }

        public Criteria andPhotoNotBetween(String value1, String value2) {
            addCriterion("photo not between", value1, value2, "photo");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpIsNull() {
            addCriterion("has_overseas_work_exp is null");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpIsNotNull() {
            addCriterion("has_overseas_work_exp is not null");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpEqualTo(Boolean value) {
            addCriterion("has_overseas_work_exp =", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpNotEqualTo(Boolean value) {
            addCriterion("has_overseas_work_exp <>", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpGreaterThan(Boolean value) {
            addCriterion("has_overseas_work_exp >", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpGreaterThanOrEqualTo(Boolean value) {
            addCriterion("has_overseas_work_exp >=", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpLessThan(Boolean value) {
            addCriterion("has_overseas_work_exp <", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpLessThanOrEqualTo(Boolean value) {
            addCriterion("has_overseas_work_exp <=", value, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpIn(List<Boolean> values) {
            addCriterion("has_overseas_work_exp in", values, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpNotIn(List<Boolean> values) {
            addCriterion("has_overseas_work_exp not in", values, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpBetween(Boolean value1, Boolean value2) {
            addCriterion("has_overseas_work_exp between", value1, value2, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andHasOverseasWorkExpNotBetween(Boolean value1, Boolean value2) {
            addCriterion("has_overseas_work_exp not between", value1, value2, "hasOverseasWorkExp");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeIsNull() {
            addCriterion("position_level_code is null");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeIsNotNull() {
            addCriterion("position_level_code is not null");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeEqualTo(String value) {
            addCriterion("position_level_code =", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeNotEqualTo(String value) {
            addCriterion("position_level_code <>", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeGreaterThan(String value) {
            addCriterion("position_level_code >", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("position_level_code >=", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeLessThan(String value) {
            addCriterion("position_level_code <", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeLessThanOrEqualTo(String value) {
            addCriterion("position_level_code <=", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeLike(String value) {
            addCriterion("position_level_code like", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeNotLike(String value) {
            addCriterion("position_level_code not like", value, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeIn(List<String> values) {
            addCriterion("position_level_code in", values, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeNotIn(List<String> values) {
            addCriterion("position_level_code not in", values, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeBetween(String value1, String value2) {
            addCriterion("position_level_code between", value1, value2, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andPositionLevelCodeNotBetween(String value1, String value2) {
            addCriterion("position_level_code not between", value1, value2, "positionLevelCode");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIsNull() {
            addCriterion("social_service is null");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIsNotNull() {
            addCriterion("social_service is not null");
            return (Criteria) this;
        }

        public Criteria andSocialServiceEqualTo(String value) {
            addCriterion("social_service =", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotEqualTo(String value) {
            addCriterion("social_service <>", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceGreaterThan(String value) {
            addCriterion("social_service >", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceGreaterThanOrEqualTo(String value) {
            addCriterion("social_service >=", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLessThan(String value) {
            addCriterion("social_service <", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLessThanOrEqualTo(String value) {
            addCriterion("social_service <=", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceLike(String value) {
            addCriterion("social_service like", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotLike(String value) {
            addCriterion("social_service not like", value, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceIn(List<String> values) {
            addCriterion("social_service in", values, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotIn(List<String> values) {
            addCriterion("social_service not in", values, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceBetween(String value1, String value2) {
            addCriterion("social_service between", value1, value2, "socialService");
            return (Criteria) this;
        }

        public Criteria andSocialServiceNotBetween(String value1, String value2) {
            addCriterion("social_service not between", value1, value2, "socialService");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andIsOnJobIsNull() {
            addCriterion("is_on_job is null");
            return (Criteria) this;
        }

        public Criteria andIsOnJobIsNotNull() {
            addCriterion("is_on_job is not null");
            return (Criteria) this;
        }

        public Criteria andIsOnJobEqualTo(Byte value) {
            addCriterion("is_on_job =", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobNotEqualTo(Byte value) {
            addCriterion("is_on_job <>", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobGreaterThan(Byte value) {
            addCriterion("is_on_job >", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_on_job >=", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobLessThan(Byte value) {
            addCriterion("is_on_job <", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobLessThanOrEqualTo(Byte value) {
            addCriterion("is_on_job <=", value, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobIn(List<Byte> values) {
            addCriterion("is_on_job in", values, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobNotIn(List<Byte> values) {
            addCriterion("is_on_job not in", values, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobBetween(Byte value1, Byte value2) {
            addCriterion("is_on_job between", value1, value2, "isOnJob");
            return (Criteria) this;
        }

        public Criteria andIsOnJobNotBetween(Byte value1, Byte value2) {
            addCriterion("is_on_job not between", value1, value2, "isOnJob");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_roster_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_roster_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}