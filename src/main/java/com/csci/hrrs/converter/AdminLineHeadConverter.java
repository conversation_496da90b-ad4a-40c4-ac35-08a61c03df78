package com.csci.hrrs.converter;

import com.csci.hrrs.model.AdminLineHead;
import com.csci.hrrs.vo.AdminLineHeadVO;
import org.springframework.beans.BeanUtils;

public class AdminLineHeadConverter {

    public static AdminLineHead convert(AdminLineHeadVO adminLineHeadVO) {
        AdminLineHead adminLineHead = new AdminLineHead();
        BeanUtils.copyProperties(adminLineHeadVO, adminLineHead);
        return adminLineHead;
    }

    public static AdminLineHeadVO convert(AdminLineHead adminLineHead) {
        AdminLineHeadVO adminLineHeadVO = new AdminLineHeadVO();
        BeanUtils.copyProperties(adminLineHead, adminLineHeadVO);
        return adminLineHeadVO;
    }

}
