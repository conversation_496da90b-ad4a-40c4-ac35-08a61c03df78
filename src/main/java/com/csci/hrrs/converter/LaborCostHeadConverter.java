package com.csci.hrrs.converter;

import com.csci.hrrs.model.LaborCostHead;
import com.csci.hrrs.vo.LaborCostHeadVO;
import org.springframework.beans.BeanUtils;

public class LaborCostHeadConverter {

    public static LaborCostHead convert(LaborCostHeadVO laborCostHeadVO) {
        LaborCostHead laborCostHead = new LaborCostHead();
        BeanUtils.copyProperties(laborCostHeadVO, laborCostHead);
        return laborCostHead;
    }

    public static LaborCostHeadVO convert(LaborCostHead laborCostHead) {
        LaborCostHeadVO laborCostHeadVO = new LaborCostHeadVO();
        BeanUtils.copyProperties(laborCostHead, laborCostHeadVO);
        return laborCostHeadVO;
    }

}
