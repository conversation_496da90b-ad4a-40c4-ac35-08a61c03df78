package com.csci.hrrs.converter;

import com.csci.hrrs.model.ExternalExperience;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.vo.WorkExperienceVO;

public class ExternalExperienceConverter {

    public static WorkExperienceVO convert(ExternalExperience externalExperience) {
        WorkExperienceVO workExperienceVO = new WorkExperienceVO();
        workExperienceVO.setStartDate(DemoUtils.convertDateFormat(externalExperience.getBegda()));
        workExperienceVO.setEndDate(DemoUtils.convertDateFormat(externalExperience.getEndda()));
        workExperienceVO.setOrganizationName(externalExperience.getCompanyName());
        workExperienceVO.setDepartment(externalExperience.getDepartmentName());
        workExperienceVO.setPosition(externalExperience.getPositionName());
        workExperienceVO.setPositionExtend(externalExperience.getPositionName());
        workExperienceVO.setIsCohl(Boolean.FALSE);
        return workExperienceVO;
    }

}
