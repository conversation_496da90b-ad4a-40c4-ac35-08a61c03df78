package com.csci.hrrs.aspectj;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.log.ILogStorage;
import com.csci.hrrs.log.LogStorageBuilder;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.common.util.CommonUtils.truncateString;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 11/15/2019
 */
@Component
@Aspect
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class LogMethodAspect {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(LogMethodAspect.class);

    @Value("${spring.profiles.active}")
    private String profiles;

    // private final Gson gson = CustomGsonBuilder.createGson();

    // || this(com.baomidou.mybatisplus.extension.service.IService)
    // || target(com.baomidou.mybatisplus.extension.service.impl.ServiceImpl)
    @Around("""
            @within(com.csci.hrrs.annotation.LogMethod) || @annotation(com.csci.hrrs.annotation.LogMethod)
            || @within(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.stereotype.Controller)
            """)
    public Object logMethod(ProceedingJoinPoint joinPoint) throws Throwable {

        ILogStorage logStorage = LogStorageBuilder.create();
        logStorage.setFromController(isFromController(joinPoint));
        long start = System.currentTimeMillis();

        Object[] args = joinPoint.getArgs();

        String className = Optional.of(joinPoint).map(JoinPoint::getSignature).map(Signature::getDeclaringTypeName).orElse("unknownClass");
        String methodName = Optional.of(joinPoint).map(JoinPoint::getSignature).map(Signature::getName).orElse("unknownMethod");
        logStorage.setBasic(className, methodName, LocalDateTime.now());

        String params = convertArgs(args);
        if (StringUtils.containsAnyIgnoreCase(params, "password", "pwd", "passwd")) {
            params = "******";
        }
        logStorage.setParameter(params);

        String jsonResult = "";
        String strException = "";

        try {
            Object result = joinPoint.proceed(joinPoint.getArgs());
            jsonResult = result instanceof String ? (String) result : parseToJson(result);
            logStorage.setResult(jsonResult);
            return result;
        } catch (Throwable throwable) {
            logger.error("{}.{}({}) -----> exception caught", className, methodName, params, throwable);
            strException = truncateString(convertThrowable(throwable), 50000);
            logStorage.setException(strException);
            throw throwable;
        } finally {
            long duration = System.currentTimeMillis() - start;
            logStorage.setDuration(duration);
            logStorage.store();
            logger.info("{}.{}: <方法调用>\n| params: {}\n| result: {}\n| duration: {}\n| exception: {}",
                        className, methodName, truncateString(params, 5000), truncateString(jsonResult, 50000), duration, strException);
        }
    }

    /**
     * 将异常信息转换为字符串,最多5000字符
     *
     * @param throwable
     * @return
     */
    String convertThrowable(Throwable throwable) {
        return throwable.getLocalizedMessage() +
                ";\n" +
                Arrays.stream(throwable.getStackTrace()).map(StackTraceElement::toString).collect(Collectors.joining("\n\t"));
    }

    /**
     * 忽略掉 request 和 response 参数
     *
     * @param args
     * @return
     */
    String convertArgs(Object[] args) {
        try {
            StringBuilder sbResult = new StringBuilder();
            int count = 0;
            for (Object arg : args) {
                if (arg instanceof ServletRequest || arg instanceof ServletResponse) {
                    continue;
                }
                if (count > 0) {
                    // 从第二个参数开始，用分号分隔
                    sbResult.append(";");
                }
                if (arg instanceof InputStream || arg instanceof OutputStream) {
                    sbResult.append("InputStream/OutputStream");
                } else if (arg instanceof MultipartFile) {
                    // sbResult.append("ServletRequest/ServletResponse");
                    sbResult.append(((MultipartFile) arg).getOriginalFilename());
                } else if (arg instanceof String) {
                    sbResult.append((String) arg);
                } else {
                    sbResult.append(CommonUtils.toJson(arg));
                }
                count++;
            }
            if (sbResult.isEmpty()) {
                sbResult.append("null");
            }
            return sbResult.toString();
        } catch (Exception e) {
            logger.error("LogMethodAspect#convertArgs, e", e);
            return Arrays.toString(args);
        }
    }

    String parseToJson(Object obj) {
        try {
            return CommonUtils.toJson(obj);
        } catch (Exception e) {
            logger.error("LogMethodAspect#parseToJson, e", e);
            return String.valueOf(obj);
        }
    }

    private static boolean isFromController(JoinPoint joinPoint) {
        return Objects.nonNull(joinPoint.getTarget().getClass().getAnnotation(Controller.class)) || Objects.nonNull(joinPoint.getTarget().getClass().getAnnotation(RestController.class));
    }

}
