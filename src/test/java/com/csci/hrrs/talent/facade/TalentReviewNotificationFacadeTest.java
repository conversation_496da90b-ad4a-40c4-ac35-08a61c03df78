package com.csci.hrrs.talent.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("localprod")
class TalentReviewNotificationFacadeTest extends BaseTest {

    @Resource
    private TalentReviewNotificationFacade facade;

    @Test
    void sendReviewTaskNotification() {
        facade.sendReviewTaskNotification("tao_li", "bc17c969-837e-41a8-a75e-40bc51b5da55");
        facade.sendReviewTaskNotification("longi.cheng", "bc17c969-837e-41a8-a75e-40bc51b5da55");
        facade.sendReviewTaskNotification("joyce_chan", "bc17c969-837e-41a8-a75e-40bc51b5da55");
        facade.sendReviewTaskNotification("wuguangyao01", "bc17c969-837e-41a8-a75e-40bc51b5da55");
    }

    @Test
    void sendSiteManagerNotification() {
        facade.sendSiteManagerNotification("tao_li", "bc17c969-837e-41a8-a75e-40bc51b5da55", 1, 1, "2025-01-01");
    }

    @Test
    void sendFirstReminderNotificationTest() {
        facade.sendFirstReminderNotificationTest("tao_li", LocalDate.of(2025, 7, 12));
    }

    @Test
    void sendFirstReminderNotification() {
        facade.sendFirstReminderNotification("d4ae326a-3953-406e-ae8f-653070742498", null, LocalDate.of(2025, 7, 12));
    }
}