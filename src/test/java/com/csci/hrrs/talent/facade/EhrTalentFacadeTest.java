package com.csci.hrrs.talent.facade;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.talent.facade.EhrTalentFacade.ApplyType;

import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class EhrTalentFacadeTest extends BaseTest {

    @Resource
    private EhrTalentFacade facade;

    @Test
    void createTalentInventory() {
        List<Map<String, Object>> paramList = List.of(
                Map.of("optionNo", "50034492",
                        "optionAd", "3311HK_TEST27",
                        "empNo", "50034493",
                        "applyType", ApplyType.PROMOTION,
                        "reason", "测试02"));
        List<Map<String, Object>> response = facade.createTalentInventory(paramList);
        System.out.println(CommonUtils.toJson(response));
    }

    @Test
    void getSsoUrl() {
        String ssoUrl = facade.getSsoUrl("CSCI_HR_TEST", facade.getRedirectPath("0050569EF5821FE0939F13FEABF7413A"));
        System.out.println(ssoUrl);
    }
}