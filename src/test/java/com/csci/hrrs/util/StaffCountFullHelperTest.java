package com.csci.hrrs.util;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.StaffCountFull;
import com.csci.hrrs.service.StaffCountFullService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

class StaffCountFullHelperTest extends BaseTest {

    @Resource
    private StaffCountFullService staffCountFullService;

    @Test
    void getMaxStaffCountDateAndValue() {
        List<StaffCountFull> staffCountFullList = staffCountFullService.listByOrgCodes(List.of("00100124"));
        Map.Entry<LocalDate, BigDecimal> entry = StaffCountFullHelper.getMaxStaffCountDateAndValue(staffCountFullList);
        System.out.println(CommonUtils.toJson(entry));
    }
}