package com.csci.hrrs.util.holder;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class OrganizationHolderTest extends BaseTest {

    @Resource
    private OrganizationHolder organizationHolder;

    @Test
    void listData() {
        System.out.println("data size: " + organizationHolder.get().size());
    }
}