package com.csci.hrrs.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.exception.ServiceException;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.model.RosterHead;
import com.csci.hrrs.service.OrganizationService;
import com.csci.hrrs.service.RosterDetailService;
import com.csci.hrrs.service.RosterHeadService;
import com.github.pagehelper.PageHelper;

import java.util.List;

public class BaseTestUtils {

    public static String getLatestRosterHeadId() {
        RosterHeadService rosterHeadService = SpringContextHolder.getBean(RosterHeadService.class);
        LambdaQueryWrapper<RosterHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RosterHead::getDeleted, Boolean.FALSE);
        queryWrapper.orderByDesc(RosterHead::getCreationTime);

        PageHelper.startPage(1, 10);
        List<RosterHead> lstHead = rosterHeadService.list(queryWrapper);
        RosterDetailService rosterDetailService = SpringContextHolder.getBean(RosterDetailService.class);
        for (RosterHead rosterHead : lstHead) {
            LambdaQueryWrapper<RosterDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.eq(RosterDetail::getIsDeleted, Boolean.FALSE).eq(RosterDetail::getHeadId, rosterHead.getId());
            if (rosterDetailService.exists(detailQueryWrapper)) {
                return rosterHead.getId();
            }
        }
        throw new ServiceException("未找到存在花名册数据的头表记录");
    }

    public static List<String> getHKCodeList() {
        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        Organization hk = organizationService.getByCode("00100017");
        return organizationService.listOrganizationTreeByParentId(hk.getId()).stream().map(Organization::getCode).toList();
    }

    public static List<String> getAllOrgCodeList() {
        OrganizationService organizationService = SpringContextHolder.getBean(OrganizationService.class);
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Organization::getIsDeleted, Boolean.FALSE);
        queryWrapper.select(Organization::getCode);
        return organizationService.list(queryWrapper).stream().map(Organization::getCode).distinct().toList();
    }
}
