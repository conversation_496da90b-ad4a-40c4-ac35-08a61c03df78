package com.csci.hrrs.visa.service.impl.hk;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.visa.model.VisaStaffWorkExperience;
import com.csci.hrrs.visa.service.impl.VisaStaffWorkExperienceServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
class VisaStaffWorkExperienceServiceImplTest extends BaseTest {

    @Resource
    private VisaStaffWorkExperienceServiceImpl visaStaffWorkExperienceService;
    @Test
    void saveTest(){
        VisaStaffWorkExperience visaStaffWorkExperience = new VisaStaffWorkExperience();
        visaStaffWorkExperience.setId(CommonUtils.randomUuid());
        visaStaffWorkExperience.setCreateUsername("usrName");
        visaStaffWorkExperience.setIsDelete(false);
        visaStaffWorkExperience.setDepartment("1");
        visaStaffWorkExperience.setJob("1");
        visaStaffWorkExperience.setEndDate("2026-01");
        visaStaffWorkExperience.setVisaStaffId("1");
        visaStaffWorkExperience.setStatus("1");
        visaStaffWorkExperience.setLastUpdateVersion(1);
        visaStaffWorkExperience.setCreationTime(LocalDateTime.now());
        visaStaffWorkExperienceService.save(visaStaffWorkExperience);
    }
}