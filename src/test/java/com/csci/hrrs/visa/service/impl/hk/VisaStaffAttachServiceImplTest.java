package com.csci.hrrs.visa.service.impl.hk;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.visa.model.VisaStaffAttach;
import com.csci.hrrs.visa.service.impl.VisaStaffAttachServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
class VisaStaffAttachServiceImplTest extends BaseTest {

    @Resource
    private VisaStaffAttachServiceImpl visaStaffAttachService;

    @Test
    void save() {
        VisaStaffAttach visaStaffAttach = new VisaStaffAttach();
        visaStaffAttach.setId(CommonUtils.randomUuid());
        visaStaffAttach.setAttachmentOriginalName("NAME");
        visaStaffAttach.setCode("1");
        visaStaffAttach.setIsDelete(false);
        visaStaffAttach.setMinioAttachmentId("1");
        visaStaffAttach.setCreateUsername("d");
        visaStaffAttach.setStatus("1");
        visaStaffAttach.setCreateUserId("1");
        visaStaffAttach.setLastUpdateVersion(1);
        visaStaffAttach.setLastUpdateUserId("1");
        visaStaffAttach.setLastUpdateUsername("1");
        visaStaffAttach.setCreationTime(LocalDateTime.now());
        visaStaffAttachService.save(visaStaffAttach);
    }
}