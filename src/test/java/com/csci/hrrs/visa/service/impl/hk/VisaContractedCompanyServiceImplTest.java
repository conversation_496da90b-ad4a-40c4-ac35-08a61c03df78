package com.csci.hrrs.visa.service.impl.hk;

import com.alibaba.fastjson.JSONObject;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.visa.dto.VisaContractedCompanyQueryDTO;
import com.csci.hrrs.visa.model.VisaContractedCompany;
import com.csci.hrrs.visa.service.VisaContractedCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
class VisaContractedCompanyServiceImplTest extends BaseTest {

    @Resource
    private VisaContractedCompanyService visaContractedCompanyService;

    @Test
    public void getAllByBusOrgId() {
        VisaContractedCompanyQueryDTO queryDTO = new VisaContractedCompanyQueryDTO();
        queryDTO.setBusinessOrganizationId("123");
        List<VisaContractedCompany> allByBusOrgId = visaContractedCompanyService.getAllByBusOrgId(queryDTO);
        log.info("allByBusOrgId: {}", JSONObject.toJSONString(allByBusOrgId));
    }

    @Test
    public void save() {
        VisaContractedCompany visaContractedCompany = new VisaContractedCompany();
        visaContractedCompany.setId(CommonUtils.randomUuid());
        visaContractedCompany.setBusinessOrganizationId("123");
        visaContractedCompany.setCompanyAddress("公司地址");
        visaContractedCompany.setMainlandContractedCompany("公司名称");
        visaContractedCompany.setBusinessCityName("香港");
        visaContractedCompany.setIsDelete(true);
        visaContractedCompany.setSignPlace("深圳市福保街道莲花道5号");
        visaContractedCompany.setLegalPerson("法人");
        visaContractedCompany.setBusinessOrganizationCode("1234567890");
        visaContractedCompany.setCreateUsername("user");
        visaContractedCompany.setLastUpdateVersion(1);
        visaContractedCompany.setBusinessCityCode("00");
        visaContractedCompany.setStatus("start");
        visaContractedCompany.setBusinessOrganizationNo("01");
        visaContractedCompany.setCreateUserId("1");
        visaContractedCompany.setCreationTime(LocalDateTime.now());
        int save = visaContractedCompanyService.save(visaContractedCompany);
        log.info("save: {}", save);
    }
}