package com.csci.hrrs.visa.strategy.staff.impl;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.visa.model.VisaStaff;
import com.csci.hrrs.visa.service.VisaStaffService;
import com.csci.hrrs.visa.vo.VisaStaffVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

@Slf4j
class HKVisaStaffProfessionalServiceStrategyImplTest extends BaseTest {

    @Resource
    private HKVisaStaffProfessionalServiceStrategyImpl hkVisaStaffProfessionalServiceStrategy;

    @Resource
    private VisaStaffService visaStaffService;

    @Test
    void TestVisaNo(){
        VisaStaff visaStaff = visaStaffService.selectById("1");
        VisaStaffVo visaStaffVo = new VisaStaffVo();
        BeanUtils.copyProperties(visaStaff, visaStaffVo);
        String visaNo = visaStaffService.getVisaNo(visaStaffVo);
        log.info("visaNo:{}", visaNo);
    }
}