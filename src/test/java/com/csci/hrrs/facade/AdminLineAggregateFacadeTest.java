package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class AdminLineAggregateFacadeTest extends BaseTest {

    @Resource
    AdminLineAggregateFacade adminLineAggregateFacade;

    @Test
    void aggregate() {
        adminLineAggregateFacade.aggregate("3ed686d3-8acc-4815-83c8-b563f22d42d8", 1);
    }
}