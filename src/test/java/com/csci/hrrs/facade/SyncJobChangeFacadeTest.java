package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class SyncJobChangeFacadeTest extends BaseTest {

    @Resource
    private SyncJobChangeFacade syncJobChangeFacade;

    /*@Test
    void sync() {
        syncJobChangeFacade.process(true);
    }*/

    @Test
    void processNew() {
        syncJobChangeFacade.processNew();
    }
}