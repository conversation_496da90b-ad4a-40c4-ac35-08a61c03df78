package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class InitSalaryStatUserFacadeTest extends BaseTest {

    @Resource
    private InitSalaryStatUserFacade initSalaryStatUserFacade;

    @Test
    void initSalaryStatUser() {
        initSalaryStatUserFacade.initSalaryStatUser();
    }
}