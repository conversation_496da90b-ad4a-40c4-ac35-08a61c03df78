package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class TalentReviewInitTest extends BaseTest {

    @Resource
    TalentReviewInit talentReviewInit;

    @Test
    void readExcel() {
        talentReviewInit.readExcel("initdata/审批人员名单_25042025.xlsx");
    }

    @Test
    void initFlowFromSettings() {
        talentReviewInit.initFlowFromSettings();
    }
}