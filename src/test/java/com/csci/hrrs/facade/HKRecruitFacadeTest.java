package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.HKDashQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

class HKRecruitFacadeTest extends BaseTest {

    @Resource
    HKRecruitFacade hkRecruitFacade;

    @Test
    void getHKRecruitOverview() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitFacade.getHKRecruitOverview(qo);
    }

    @Test
    void distributeByManageType() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitFacade.distributeByManageType(qo);
    }

    @Test
    void distributeByTalentType() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitFacade.distributeByTalentType(qo);
    }

    @Test
    void distributeByAge() {
        hkRecruitFacade.distributeByAge(new HKDashQO());
    }

    @Test
    void distributeByJobType() {
        hkRecruitFacade.distributeByJobType(new HKDashQO());
    }

    @Test
    void distributeByPosition() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitFacade.distributeByPosition(qo);
    }

    @Test
    void getYearlyStaffStat() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitFacade.getYearlyStaffStat(qo);
    }

    @Test
    void distributeByCohlSeniority() {
        hkRecruitFacade.distributeByCohlSeniority(new HKDashQO());
    }

    @Test
    void distributeByEducation() {
        hkRecruitFacade.distributeByEducation(new HKDashQO());
    }
}