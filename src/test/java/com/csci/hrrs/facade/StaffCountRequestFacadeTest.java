package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.vo.StaffCountRequestVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class StaffCountRequestFacadeTest extends BaseTest {

    @Resource
    StaffCountRequestFacade staffCountRequestFacade;

    @Test
    void checkCreateStaffCountRequest() {
        /*{"orgCode":"50001730","orgName":"50001730","recruitmentType":"地盘基层（非地盘合约）","recruitmentQuantity":1}*/
        StaffCountRequestVO staffCountRequestVO = new StaffCountRequestVO();
        staffCountRequestVO.setOrgCode("50001730");
        staffCountRequestVO.setOrgName("50001730");
        staffCountRequestVO.setRecruitmentType("地盘基层（非地盘合约）");
        staffCountRequestVO.setRecruitmentQuantity(1);
        staffCountRequestFacade.checkCreateStaffCountRequest(staffCountRequestVO);
    }
}