package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class SendMessageFacadeTest extends BaseTest {

    @Resource
    private SendMessageFacade sendMessageFacade;

    @Test
    void sendFeishuMessageToUser() {
        sendMessageFacade.sendFeishuMessageToUser(List.of("tao_li"), "Test title", "Test Message");
    }
}