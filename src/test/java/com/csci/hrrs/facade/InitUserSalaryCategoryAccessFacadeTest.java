package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class InitUserSalaryCategoryAccessFacadeTest extends BaseTest {

    @Resource
    InitUserSalaryCategoryAccessFacade initUserSalaryCategoryAccessFacade;

    @Test
    void process() {
        initUserSalaryCategoryAccessFacade.process();
    }

    @Test
    void handleRow() {
        // initUserSalaryCategoryAccessFacade.handleRow("hlzhangxs,外包管理人员,外包操作人员");
        // initUserSalaryCategoryAccessFacade.handleRow("sunzhaowei02,内地,外包管理人员,外包操作人员");
        // initUserSalaryCategoryAccessFacade.handleRow("zhangli8029,内地");
        // wudanxia,内地,外包管理人员,外包操作人员
        // initUserSalaryCategoryAccessFacade.handleRow("wudanxia,内地,外包管理人员,外包操作人员");
        // calvin_tam,港澳聘月薪,港澳聘日薪,海外
        // initUserSalaryCategoryAccessFacade.handleRow("calvin_tam,港澳聘月薪,港澳聘日薪,海外");
        // initUserSalaryCategoryAccessFacade.handleRow("liqian25,港澳聘月薪,港澳聘日薪");
        // initUserSalaryCategoryAccessFacade.handleRow("xiongna,内派");
        // initUserSalaryCategoryAccessFacade.handleRow("szchanglin,内派");
        // initUserSalaryCategoryAccessFacade.handleRow("mengzhaoyan,内地,外包管理人员,外包操作人员");
        initUserSalaryCategoryAccessFacade.handleRow("mengzhaoyan,内派");
        initUserSalaryCategoryAccessFacade.handleRow("yang_mei,内派");
    }
}