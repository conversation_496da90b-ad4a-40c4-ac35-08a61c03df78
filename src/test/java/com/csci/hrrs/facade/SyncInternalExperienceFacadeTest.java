package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class SyncInternalExperienceFacadeTest extends BaseTest {

    @Resource
    private SyncInternalExperienceFacade syncInternalExperienceFacade;

    @Test
    void process() {
        syncInternalExperienceFacade.process(true);
    }

    @Test
    void doSync() {
        syncInternalExperienceFacade.doSync();
    }
}