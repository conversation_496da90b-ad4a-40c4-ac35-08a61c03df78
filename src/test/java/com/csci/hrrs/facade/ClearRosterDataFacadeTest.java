package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class ClearRosterDataFacadeTest extends BaseTest {

    @Resource
    private ClearRosterDataFacade clearRosterDataFacade;

    @Test
    void process() {
        clearRosterDataFacade.process();
    }

    @Test
    void clearRosterByMonth() {
        // clearRosterDataFacade.clearRosterByMonth(2024, 1);
        // clearRosterDataFacade.clearRosterByMonth(2024, 2);
        // clearRosterDataFacade.clearRosterByMonth(2024, 3);
        clearRosterDataFacade.clearRosterByMonth(2023, 12);
        // clearRosterDataFacade.clearRosterByMonth(2024, 9);
    }
}