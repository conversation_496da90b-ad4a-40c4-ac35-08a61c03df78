package com.csci.hrrs.facade;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class ConvertRosterPinyinFacadeTest extends BaseTest {

    @Resource
    ConvertRosterPinyinFacade convertRosterPinyinFacade;

    @Test
    void process() {
        convertRosterPinyinFacade.process();
    }

    @Test
    void processByHeadId() {
        convertRosterPinyinFacade.processByHeadId("7547fab9-02dc-4623-a062-ffdf57e473e2");
    }
}