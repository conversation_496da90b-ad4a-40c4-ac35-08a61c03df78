package com.csci.hrrs.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 类型	单位	编码	名称	排序	来源
 *
 * @ClassName: SubjectCodeExcelDTO
 * @Auther: <EMAIL>
 * @Date: 2025/5/7 12:28
 * @Description: 导入科目
 */
@Data
public class SubjectCodeExcelDTO {
    @ExcelProperty("类型")
    private String type;
    @ExcelProperty("单位")
    private String unit;
    @ExcelProperty("编码")
    private String code;
    @ExcelProperty("名称")
    private String name;
    @ExcelProperty("排序")
    private Integer sort;
    @ExcelProperty("来源")
    private String source;
}
