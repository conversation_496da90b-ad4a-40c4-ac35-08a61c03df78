package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.SubjectBalance;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

class SubjectBalanceServiceTest extends BaseTest {

    @Resource
    private SubjectBalanceService subjectBalanceService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<SubjectBalance> subjectBalanceList = subjectBalanceService.list();
        System.out.println(CommonUtils.toJson(subjectBalanceList));
    }

    @Test
    void listSalaryByProjectIdAndYearMonth() {
        subjectBalanceService.listSalaryByProjectIdAndYearMonth(List.of("57C3039F-A8D6-460C-ADBC-3BF3305C5EFE"), 2024, 11);
    }
}