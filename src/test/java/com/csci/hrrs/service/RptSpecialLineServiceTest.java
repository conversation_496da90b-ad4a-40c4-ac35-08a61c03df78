package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.RptSpecialLine;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

class RptSpecialLineServiceTest extends BaseTest {

    @Resource
    private RptSpecialLineService rptSpecialLineService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<RptSpecialLine> lineList = rptSpecialLineService.list();
        System.out.println(lineList.size());
        System.out.println(CommonUtils.toJson(lineList));
    }

    @Test
    void listThisMonthData() {
        rptSpecialLineService.listThisMonthData();
    }
}