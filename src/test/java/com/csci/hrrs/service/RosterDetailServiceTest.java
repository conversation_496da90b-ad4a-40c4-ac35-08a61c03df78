package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.model.PageableVO;
import com.csci.common.model.ResultPage;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.RosterDetail;
import com.csci.hrrs.model.RosterHead;
import com.csci.hrrs.qo.DashboardQO;
import com.csci.hrrs.qo.GeneralHKDashboardQO;
import com.csci.hrrs.qo.PageableHKDashboardQO;
import com.csci.hrrs.util.BaseTestUtils;
import com.csci.hrrs.util.context.ContextUtils;
import com.csci.hrrs.vo.AgeGenderDistrVO;
import com.csci.hrrs.vo.NameCountVO;
import com.csci.hrrs.vo.RosterDetailVO;
import com.github.pagehelper.PageHelper;
import org.apache.ibatis.annotations.Select;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@ActiveProfiles("dev")
class RosterDetailServiceTest extends BaseTest {

    @Resource
    RosterDetailService rosterDetailService;

    @Resource
    RosterHeadService rosterHeadService;

    @Resource
    OrganizationService organizationService;

    @Value("${hk.subsidiary.all}")
    private List<String> hkSubsidiaryList;

    @Test
    void addRosterDetail() {

        RosterHead rosterHead = rosterHeadService.getOne(new LambdaQueryWrapper<>());

        RosterDetailVO rosterDetailVO = new RosterDetailVO();
        // rosterDetailVO.setId();
        rosterDetailVO.setHeadId(rosterHead.getId());
        rosterDetailVO.setName("张三");
        rosterDetailVO.setPlatform("平台");
        rosterDetailVO.setSubsidiary("子公司");
        rosterDetailVO.setPrimaryPosition("主职");
        rosterDetailVO.setConcPosition("兼职");
        rosterDetailVO.setPersonInCharge("负责人");
        rosterDetailVO.setWorkPlace("工作地");
        rosterDetailVO.setPositionType("职位类型");
        rosterDetailVO.setExpertise("专业");
        rosterDetailVO.setPositionLevel("职级");
        rosterDetailVO.setJobGrade("职等");
        rosterDetailVO.setGender(1);
        rosterDetailVO.setStartWorkTime(LocalDate.now());
        rosterDetailVO.setJoinCohlTime(LocalDate.now());
        rosterDetailVO.setJoin3311Time(LocalDate.now());
        rosterDetailVO.setStartOverseasTime(LocalDate.now());
        rosterDetailVO.setDurationCurrentPos(LocalDate.now());
        rosterDetailVO.setDurationCurrentLevel(LocalDate.now());
        rosterDetailVO.setDurationCurJobGrade(LocalDate.now());
        rosterDetailVO.setBirthdate(LocalDate.now());
        rosterDetailVO.setAge(18);
        rosterDetailVO.setEthnicity("民族");
        rosterDetailVO.setHometown("籍贯");
        rosterDetailVO.setBirthPlace("出生地");
        rosterDetailVO.setResidence("户口所在地");
        rosterDetailVO.setMaritalStatus("婚姻状况");
        rosterDetailVO.setChildStatus("子女状况");
        rosterDetailVO.setEducation("学历");
        rosterDetailVO.setJobTitle("职称");
        rosterDetailVO.setCredentials("职业资格");
        rosterDetailVO.setPublicOffice("公职");
        rosterDetailVO.setSource("来源");
        rosterDetailVO.setMobile("手机号码");
        rosterDetailVO.setEmail("邮箱");
        rosterDetailVO.setRemark("备注");
        rosterDetailVO.setLastUpdateVersion(1);

        rosterDetailService.addRosterDetail(rosterDetailVO);
    }

    @Test
    void deleteRosterDetail() {
    }

    @Test
    void updateRosterDetail() {
    }

    @Test
    void getRosterDetail() {
        rosterDetailService.getRosterDetail("1cc3b4dc-7cd7-40ec-9bda-ea13dba78183", HrrsConsts.Language.simplifiedChinese);
    }

    @Test
    void getPersonCountByOrgNo() {
        rosterDetailService.getPersonCountByOrgNo("3d318196-8650-46ef-a6de-3940f8d9a415", "00100001/00000003");
    }

    private DashboardQO getDashboardQO() {
        DashboardQO dashboardQO = new DashboardQO();
        dashboardQO.setPlatformOrgCodeList(List.of("00100001"));
        // dashboardQO.setDeptCodeList(List.of("00100005"));
        dashboardQO.setDate(LocalDate.of(2024, 2, 1));
        dashboardQO.setHireType("自有");
        dashboardQO.setRosterHeadId("e5f77bd2-1be1-4091-bfae-a9296d6ec9ab");
        dashboardQO.setType(1);
        dashboardQO.setDefaultValues();
        return dashboardQO;
    }

    @Test
    void selectRosterDetailWithOrgInfo() {
        DashboardQO dashboardQO = getDashboardQO();
        dashboardQO.setDefaultValues();
        rosterDetailService.selectRosterDetailWithOrgInfo(dashboardQO);
    }

    @Test
    void getEmployeeCategoryNameByUsername() {
        rosterDetailService.getEmployeeCategoryNameByUsername("tao_li");
    }

    @Test
    void listByStaffClass() {
        ContextUtils.setContextValue("loadPhoto", Boolean.FALSE);
        rosterDetailService.listByStaffClass("0115af12-25a0-463c-95a4-7b114d8bc232", "00100001/00100017", "zh-CN");
    }

    @Test
    void getRosterCountByStaffClass() {
        rosterDetailService.getRosterCountByStaffClass("0115af12-25a0-463c-95a4-7b114d8bc232", "00100001/00100017%");
    }

    @Test
    void selectRosterCountByStaffClass() {
        rosterDetailService.selectRosterCountByStaffClass("0115af12-25a0-463c-95a4-7b114d8bc232", List.of("00100017"), ContextUtils.getCurrentUser().getId(), false);
    }

    @Test
    void listAgeGenderDistrVO() {
        List<AgeGenderDistrVO> lstAgeGender = rosterDetailService.listAgeGenderDistrVO("f7fac5ae-9a71-42b4-896f-1f32aacfde0c", getHKCodeList(), false);
        System.out.println(lstAgeGender.stream().map(AgeGenderDistrVO::getValue).reduce(0L, Long::sum));
    }

    private List<String> getHKCodeList() {
        Organization hk = organizationService.getByCode("00100017");
        return organizationService.listOrganizationTreeByParentId(hk.getId()).stream().map(Organization::getCode).toList();
    }

    @Test
    void selectPositionLevelCode() {
        List<String> positionLevelCode = rosterDetailService.selectPositionLevelCode("f7fac5ae-9a71-42b4-896f-1f32aacfde0c", getHKCodeList(), ContextUtils.getCurrentUser().getId(), false);
        System.out.println(positionLevelCode.size());
    }

    @Test
    void listByPositionLevelCodeDistr() {
        rosterDetailService.listByPositionLevelCodeDistr(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), false);
    }

    @Test
    void listFormerRoster() {
        rosterDetailService.listFormerRoster();
    }

    @Test
    void listFormerRosterInMonth() {
        rosterDetailService.listFormerRosterInMonth(LocalDate.now(), getHKCodeList(), false);
    }

    @Test
    void selectAgeCohlSeniority() {
        rosterDetailService.selectAgeCohlSeniority(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), ContextUtils.getCurrentUser().getId(), false);
    }

    @Test
    void selectFormerTalentCount() {
        rosterDetailService.selectFormerTalentCount(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), ContextUtils.getCurrentUser().getId(), LocalDate.now(), LocalDate.now());
    }

    @Test
    void listQuitStaffByDate() {
        rosterDetailService.listQuitStaffByDate(getHKCodeList(), LocalDate.of(2024, 1, 1), LocalDate.now(), false);
    }

    @Test
    void listRosterCountByEducation() {
        rosterDetailService.listRosterCountByEducation(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), false);
    }

    @Test
    void selectOnJobCount() {
        // 7352
        rosterDetailService.selectOnJobCount(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), ContextUtils.getCurrentUser().getId(), true);
    }

    @Test
    void distributeByPositionTypeAndStaffClass() {
        rosterDetailService.distributeByPositionTypeAndStaffClass(BaseTestUtils.getLatestRosterHeadId(), getHKCodeList(), false);
    }

    void pageByRosterByHeadIdAndOrgCodeList() {
        PageableHKDashboardQO qo = new PageableHKDashboardQO();
        qo.setHeadId(BaseTestUtils.getLatestRosterHeadId());
        qo.setOrganizationCodeList(getHKCodeList());
        rosterDetailService.pageByRosterByHeadIdAndOrgCodeList(qo);
    }

    @Test
    void selectRosterByDashboardQO() {
        GeneralHKDashboardQO generalHKDashboardQO = new GeneralHKDashboardQO();
        generalHKDashboardQO.setHeadId(BaseTestUtils.getLatestRosterHeadId());
        generalHKDashboardQO.setOrganizationCodeList(getHKCodeList());
        generalHKDashboardQO.setIsCoreTalent(Boolean.TRUE);
        generalHKDashboardQO.setUserId(ContextUtils.getCurrentUser().getId());
        rosterDetailService.selectRosterByDashboardQO(generalHKDashboardQO);
    }

    @Test
    void listRosterByHKDashboardQO() {
        GeneralHKDashboardQO generalHKDashboardQO = new GeneralHKDashboardQO();
        generalHKDashboardQO.setHeadId(BaseTestUtils.getLatestRosterHeadId());
        generalHKDashboardQO.setOrganizationCodeList(getHKCodeList());
        generalHKDashboardQO.setUserId(ContextUtils.getCurrentUser().getId());
        // generalHKDashboardQO.setIsCoreTalent(Boolean.FALSE);
        // generalHKDashboardQO.setIsMale(Boolean.FALSE);
        generalHKDashboardQO.setEducation("博士");
        rosterDetailService.listRosterByHKDashboardQO(generalHKDashboardQO);
    }

    @Test
    void getRosterDetailByPernr() {
        rosterDetailService.getRosterDetailByPernr("00014980", HrrsConsts.Language.traditionalChinese);
    }

    @Test
    void listIdByPernr() {
        rosterDetailService.listIdByPernr(HrrsConsts.formerEmployeeHeadId, "15040984");
    }

    @Test
    void clearDuplicatedFormerEmployee() {
        rosterDetailService.clearDuplicatedFormerEmployee();
    }

    @Test
    void selectRosterForHKDash() {

        LocalDate firstDayOfThisMonth = LocalDate.now().withDayOfMonth(1);
        LocalDate lastDayOfThisMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());

        rosterDetailService.selectRosterForHKDash(HrrsConsts.formerEmployeeHeadId, getHKCodeList(), ContextUtils.getCurrentUser().getId(), firstDayOfThisMonth, lastDayOfThisMonth, false);
    }

    @Test
    void updateAllHireTypeQuery() {
        rosterDetailService.updateAllHireTypeQuery();
    }

    @Test
    void listPage() {
        PageableVO pageableVO = new PageableVO();
        pageableVO.setCurPage(21);
        pageableVO.setPageSize(1000);
        ResultPage<RosterDetail> resultPage = rosterDetailService.listByHeadIdByPage("d4bf6ca8-d042-4912-bf28-0e26bc42a2cb", pageableVO);
        System.out.println(resultPage.getPages());
        System.out.println(resultPage.getTotal());
    }

    @Test
    void selectRosterDetail() {
        long sizeRoster = rosterDetailService.selectRosterDetail(getDashboardQO()).size();
        long sizeProvince = rosterDetailService.selectProvinceList(getDashboardQO()).size();
        System.out.println(sizeRoster);
        System.out.println(sizeProvince);
    }

    @Test
    void selectPersonCountByHeadIdsAndOrgCode() {
        rosterDetailService.selectPersonCountByHeadIdsAndOrgCode(List.of("5f4ba9e1-5bf3-40c8-8d0b-22da816ea0e8", "325f4baf-65f9-4bdc-a3ad-3dd01b95faea", "604204e3-788c-48f1-b0e1-dad43a7e67d6"),
                "50001586", "内派");
    }

    @Test
    void selectHKPersonCountGroupBySubsidiary() {
        rosterDetailService.selectHKPersonCountGroupBySubsidiary(HrrsConsts.HK_SUBSIDIARY_LIST, LocalDate.now().with(TemporalAdjusters.firstDayOfYear()), LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()));
    }

    @Test
    void selectHKOrgOnJobCount() {
        List<NameCountVO> nameCountVOList = rosterDetailService.selectHKOrgOnJobCount(List.of("50000986", "50001106", "50001662"), LocalDate.now().with(TemporalAdjusters.firstDayOfYear()), LocalDate.now());
        System.out.println(CommonUtils.toJson(nameCountVOList));
    }

    @Test
    void selectHKOutsourceCount() {
        rosterDetailService.selectHKOutsourceCount(null, rosterHeadService.getLatestHeadId());
    }

    @Test
    void selectHKZZOutsourceCount() {
        rosterDetailService.selectZZOutsourceCount(null, rosterHeadService.getLatestHeadId());
    }

    @Test
    void selectHKStaffCount() {
        LocalDate now = LocalDate.now();
        rosterDetailService.selectHKStaffCount(null, now.withDayOfMonth(1), now.withDayOfMonth(now.lengthOfMonth()));
    }

    @Test
    void selectSpecialistCount() {
        rosterDetailService.selectSpecialistCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()));
    }

    @Test
    void selectHKRosterTypeList() {
        String headId = rosterHeadService.getLatestRosterHead(LocalDate.now()).getId();
        PageHelper.startPage(1, 10);
        List<RosterDetail> resultList = rosterDetailService.selectHKRosterTypeList(null, headId, "港聘", 1, 1);
        System.out.println(CommonUtils.toJson(resultList));
    }

    @Test
    void queryRosterDetailDistributionByCategory() {
        String headId = rosterHeadService.getLatestRosterHead(LocalDate.now()).getId();
        PageHelper.startPage(1, 10);
        List<RosterDetail> resultList = rosterDetailService.queryRosterDetailDistributionByCategory(null, headId, "大团队核心层", 1, 5);
        System.out.println(CommonUtils.toJson(resultList));
    }

    @Test
    void selectHKPersonCountByOrgCodeAndDateRange() {
        rosterDetailService.selectHKPersonCountByOrgCodeAndDateRange(null, LocalDate.now().with(TemporalAdjusters.firstDayOfYear()), LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), "管理人员");
    }

    @Test
    void selectMinStatDate() {
        System.out.println(CommonUtils.toJson(rosterDetailService.selectMinStatDate("50001743")));
    }

    @Test
    void selectEmployeeSubGroup() {
        rosterDetailService.selectEmployeeSubGroup(null);
    }
}