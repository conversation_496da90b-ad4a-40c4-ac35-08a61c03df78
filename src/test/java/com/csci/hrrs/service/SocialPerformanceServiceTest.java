package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.SocialPerformanceQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class SocialPerformanceServiceTest extends BaseTest {

    @Resource
    private SocialPerformanceService socialPerformanceService;

    @Test
    void getOrInit() {
        SocialPerformanceQO qo = new SocialPerformanceQO();
        qo.setOrganizationId("1");
        qo.setYear(2020);
        qo.setMonth(4);

        socialPerformanceService.getOrInit(qo);
    }

    @Test
    void listSocialPerformanceDetail() {
        socialPerformanceService.listSocialPerformanceDetail("be3f433c-33b6-46b7-8859-4faa8cc2ba48");
    }

    @Test
    void initSocialPerformanceDetail() {
        socialPerformanceService.initSocialPerformanceDetail("1");
    }
}