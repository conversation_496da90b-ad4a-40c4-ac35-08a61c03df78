package com.csci.hrrs.service;

import com.csci.common.model.PageableVO;
import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class UserSessionServiceTest extends BaseTest {

    @Resource
    private UserSessionService userSessionService;

    @Test
    void listByPage() {
        userSessionService.listByPage(new PageableVO());
    }
}