package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.HKDashQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;

class StaffCountFullServiceTest extends BaseTest {

    @Resource
    private StaffCountFullService staffCountFullService;

    @Test
    void list() {
        staffCountFullService.list();
    }

    @Test
    void selectManagerCount() {
        staffCountFullService.selectManagerCount(null);
    }

    @Test
    void selectOnGoingProjectCountBySubsidiary() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        staffCountFullService.selectOnGoingProjectCountBySubsidiary(qo.getOrganizationCodeList(), qo.getStatDate());
    }

    @Test
    void selectManagerCountBySubsidiary() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        staffCountFullService.selectManagerCountBySubsidiary(qo.getOrganizationCodeList(), qo.getStatDate());
    }

    @Test
    void selectSubsidiaryManagerDistrByPosition() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        staffCountFullService.selectSubsidiaryManagerDistrByPosition(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth()));
    }
}