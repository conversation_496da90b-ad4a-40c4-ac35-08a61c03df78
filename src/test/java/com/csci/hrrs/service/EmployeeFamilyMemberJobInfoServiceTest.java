package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.EmployeeFamilyMemberJobInfo;
import com.csci.hrrs.qo.EmployeeFamilyMemberJobInfoQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class EmployeeFamilyMemberJobInfoServiceTest extends BaseTest {

    @Resource
    private EmployeeFamilyMemberJobInfoService employeeFamilyMemberJobInfoService;

    @Test
    void page() {
        EmployeeFamilyMemberJobInfoQO qo = new EmployeeFamilyMemberJobInfoQO();
        qo.setCurPage(1);
        qo.setPageSize(10);
        employeeFamilyMemberJobInfoService.page(qo);
    }
}