package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.constant.SalaryCategoryEnum;
import com.csci.hrrs.model.UserSalaryCategoryAccess;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.csci.hrrs.constant.SalaryCategoryEnum.*;

class UserSalaryCategoryAccessServiceTest extends BaseTest {

    @Resource
    private UserSalaryCategoryAccessService userSalaryCategoryAccessService;

    @Test
    void initAccess() {
        // addAccessToUser("wuguangyao01", allCategoryList());
        // addAccessToUser("wangjinghao02", allCategoryList());
        // addAccessToUser("quyangshu124", allCategoryList());

        // addAccessToUser("wangzirong", List.of(INNER_ASSIGNED.getCode(), HK_MONTHLY.getCode(), MAINLAND.getCode()));
        // addAccessToUser("wangjinghao02", List.of(INNER_ASSIGNED.getCode()));
        // addAccessToUser("ut-test", List.of(INNER_ASSIGNED.getCode()));
        addAccessToUser("casper_chia", allCategoryList());
    }

    private List<String> allCategoryList() {
        return Arrays.stream(SalaryCategoryEnum.values()).map(SalaryCategoryEnum::getCode).toList();
    }

    /**
     * 为用户添加薪酬类别访问权限
     * 已经添加过的不会重复添加，只添加不存在的
     *
     * @param username     用户名
     * @param categoryCodeList 薪酬类别列表
     */
    private void addAccessToUser(String username, List<String> categoryCodeList) {
        List<UserSalaryCategoryAccess> accessList = new ArrayList<>();
        for (String category : categoryCodeList) {
            accessList.add(newAccess(username, category));
        }

        LambdaQueryWrapper<UserSalaryCategoryAccess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSalaryCategoryAccess::getIsDeleted, Boolean.FALSE)
                .eq(UserSalaryCategoryAccess::getUsername, username)
                .in(UserSalaryCategoryAccess::getSalaryCategoryCode, accessList.stream().map(UserSalaryCategoryAccess::getSalaryCategoryCode).toArray());

        List<UserSalaryCategoryAccess> existList = userSalaryCategoryAccessService.list(queryWrapper);
        List<String> code = existList.stream().map(UserSalaryCategoryAccess::getSalaryCategoryCode).toList();

        accessList.removeIf(x -> code.contains(x.getSalaryCategoryCode()));
        if (CollectionUtils.isNotEmpty(accessList)) {
            userSalaryCategoryAccessService.saveBatch(accessList);
        }
    }

    private UserSalaryCategoryAccess newAccess(String username, String code) {
        UserSalaryCategoryAccess access = new UserSalaryCategoryAccess();
        // access.setId();
        access.setUsername(username);
        access.setSalaryCategoryCode(code);
        access.setIsDeleted(Boolean.FALSE);
        // access.setCreationTime();
        // access.setCreateUserId();
        // access.setCreateUsername();
        // access.setLastUpdateTime();
        // access.setLastUpdateUserId();
        // access.setLastUpdateUsername();
        // access.setLastUpdateVersion();
        return access;
    }

}