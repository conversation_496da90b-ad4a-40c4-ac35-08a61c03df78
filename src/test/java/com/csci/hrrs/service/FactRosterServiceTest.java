package com.csci.hrrs.service;

import com.csci.common.model.PageableVO;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.FactRosterExample;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;

@Slf4j
class FactRosterServiceTest extends BaseTest {

    @Resource
    FactRosterService factRosterService;

    @Test
    void listByPage() {
        PageableVO pageableVO = new PageableVO();
        pageableVO.setCurPage(20);
        pageableVO.setPageSize(1000);
        factRosterService.listByPage(pageableVO);
    }

    @Test
    void selectByEmployeeNo() {
        factRosterService.selectByEmployeeNo("40100001");
    }

    @Test
    void requiresSync() {
        log.info("requiresSync: {}", factRosterService.isAllowToSync());
    }

    @Test
    void getMaxRecoverDateLocalDate() {
        factRosterService.getMaxRecoverDate();
    }

    @Test
    void getRecoverydateInMonth() {
        factRosterService.getRecoverydateInMonth(LocalDate.of(2023, 6, 1));
    }

    @Test
    void getEmployeeByUsername() {
        factRosterService.getEmployeeByUsername("tao_li");
    }

    @Test
    void getStaffClassMacauByPernr() {

        factRosterService.getStaffClassMacauByPernr("00035926", LocalDate.now());
    }

    @Test
    void getLatestRecoverydate() {
        System.out.println(factRosterService.getLatestRecoverydate(LocalDate.of(2020, 12, 2)));
    }
}