package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.SalaryStatHead;
import org.junit.jupiter.api.Test;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class SalaryStatHeadServiceTest extends BaseTest {

    @Resource
    private SalaryStatHeadService salaryStatHeadService;

    @Test
    void findByYearAndOrg() {
        assertNull(salaryStatHeadService.findByYearAndOrg(2024, "123456"));
    }

    @Test
    void createHead() {
        SalaryStatHead head = new SalaryStatHead();
        head.setYear(2024);
        head.setOrganizationCode("00100017");
        assertThrowsExactly(DuplicateKeyException.class,() -> salaryStatHeadService.save(head));
    }
}