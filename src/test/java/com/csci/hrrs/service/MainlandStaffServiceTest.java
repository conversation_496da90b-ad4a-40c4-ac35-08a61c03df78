package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.HKDashQO;
import com.csci.hrrs.qo.MainlandStaffQO;
import com.csci.hrrs.vo.MainlandStaffBreakdownVO;
import com.csci.hrrs.vo.MainlandStaffRank;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MainlandStaffServiceTest extends BaseTest {

    @Resource
    private MainlandStaffService mainlandStaffService;

    @Test
    void getJoinYearBreakdown() {
        MainlandStaffQO qo = new MainlandStaffQO();
        qo.setYear(2024);
        qo.setMonth(12);
        mainlandStaffService.getJoinYearBreakdown(qo);
    }

    @Test
    void getJoinMonthBreakdown() {
        MainlandStaffQO qo = new MainlandStaffQO();
        qo.setYear(2025);
        qo.setMonth(2);
        mainlandStaffService.getMainlandUnitBreakdown(qo);
    }

    @Test
    void getMainlandUnitBreakdown() {
        MainlandStaffQO qo = new MainlandStaffQO();
        qo.setYear(2025);
        qo.setMonth(1);
        mainlandStaffService.getMainlandBreakdown(qo);
    }

    @Test
    void getHROverallStat() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        mainlandStaffService.getHROverallStat(qo);
    }

    @Test
    void getPositionBreakdownV1() {
        MainlandStaffQO qo = new MainlandStaffQO();
        qo.setYear(2025);
        qo.setMonth(1);
        List<MainlandStaffRank> positionBreakdownV1 = mainlandStaffService.getPositionBreakdownV1(qo);
        System.out.println(positionBreakdownV1);
    }
}