package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class CompanyPerformanceServiceTest extends BaseTest {

    @Resource
    private CompanyPerformanceService companyPerformanceService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        System.out.println(CommonUtils.toJson(companyPerformanceService.list()));;
    }

}