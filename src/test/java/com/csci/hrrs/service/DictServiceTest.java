package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.Dict;
import com.csci.hrrs.qo.KeywordPageQO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ActiveProfiles("prod")
@Slf4j
class DictServiceTest extends BaseTest {

    @Resource
    private DictService dictService;

    @Test
    void selectPrimaryPosition() {
        dictService.selectPrimaryPosition(new KeywordPageQO().setKeyword("")).getList().forEach(System.out::println);
    }

    @Test
    void selectEmployeeSubGroup() {
        dictService.selectEmployeeSubGroup("25").forEach(System.out::println);
    }

    @Test
    void selectPositionType() {
        dictService.selectPositionType().forEach(System.out::println);
    }

    @Test
    void selectPositionLevel() {
        dictService.selectPositionLevel().forEach(System.out::println);
    }

    @Test
    void selectEmployeeCategory() {
        dictService.selectEmployeeCategory().forEach(System.out::println);
    }

    @Test
    void initDict() {
        List<String> lstJobGrade = new ArrayList<>();
        lstJobGrade.add("P27");
        lstJobGrade.add("M26");
        lstJobGrade.add("M23");
        lstJobGrade.add("P23");
        lstJobGrade.add("M22");
        lstJobGrade.add("M21");
        lstJobGrade.add("P21");
        lstJobGrade.add("M20");
        lstJobGrade.add("M19");
        lstJobGrade.add("M18");
        lstJobGrade.add("P18");
        lstJobGrade.add("M17");
        lstJobGrade.add("M16");
        lstJobGrade.add("P16");
        lstJobGrade.add("M15");
        lstJobGrade.add("P15");
        lstJobGrade.add("M14");
        lstJobGrade.add("P14");
        lstJobGrade.add("M13");
        lstJobGrade.add("P13");
        lstJobGrade.add("M12");
        lstJobGrade.add("P12");
        lstJobGrade.add("M11");
        lstJobGrade.add("P11");
        lstJobGrade.add("M10");
        lstJobGrade.add("P10");
        // lstJobGrade.add("M");
        lstJobGrade.add("M9");
        lstJobGrade.add("P9");
        lstJobGrade.add("M8");
        lstJobGrade.add("P8");
        lstJobGrade.add("M7");
        lstJobGrade.add("P7");
        lstJobGrade.add("M6");
        lstJobGrade.add("P6");
        lstJobGrade.add("M5");
        lstJobGrade.add("P5");
        lstJobGrade.add("M4");
        lstJobGrade.add("P4");
        lstJobGrade.add("M3");
        lstJobGrade.add("P3");
        lstJobGrade.add("M2");
        lstJobGrade.add("P2");
        lstJobGrade.add("M1");
        lstJobGrade.add("P1");
        // lstJobGrade.add("M未定");
        // lstJobGrade.add("P");
        // lstJobGrade.add("G01");
        // lstJobGrade.add("G02");
        // lstJobGrade.add("G03");
        // lstJobGrade.add("G04");
        // lstJobGrade.add("G05");
        // lstJobGrade.add("G06");
        // lstJobGrade.add("G07");
        // lstJobGrade.add("G08");
        // lstJobGrade.add("G09");
        lstJobGrade.add("G20");
        lstJobGrade.add("G19");
        lstJobGrade.add("G18");
        lstJobGrade.add("G17");
        lstJobGrade.add("G16");
        lstJobGrade.add("G15");
        lstJobGrade.add("G14");
        lstJobGrade.add("G13");
        lstJobGrade.add("G12");
        lstJobGrade.add("G11");
        lstJobGrade.add("G10");
        lstJobGrade.add("G09");
        lstJobGrade.add("G08");
        lstJobGrade.add("G07");
        lstJobGrade.add("G06");
        lstJobGrade.add("G05");
        lstJobGrade.add("G04");
        lstJobGrade.add("G03");
        lstJobGrade.add("G02");
        lstJobGrade.add("G01");

        lstJobGrade.add("S8");
        lstJobGrade.add("S7");
        lstJobGrade.add("S6");
        lstJobGrade.add("S5");
        lstJobGrade.add("S4");
        lstJobGrade.add("S3");
        lstJobGrade.add("S2");
        lstJobGrade.add("S1");
        // lstJobGrade.add("A");
        lstJobGrade.add("A1");
        lstJobGrade.add("A2");
        lstJobGrade.add("A3");
        lstJobGrade.add("A4");
        lstJobGrade.add("A5");
        // lstJobGrade.add("A未定");
        // lstJobGrade.add("GS");

        // lstJobGrade.add("S");
        // lstJobGrade.add("Z未定");

        int seq = 1;
        for (String jobGrade : lstJobGrade) {
            System.out.println(jobGrade);
            Dict dictDTO = new Dict();
            // dictDTO.setId();
            dictDTO.setCode("job_grade");
            dictDTO.setDictName(jobGrade);
            // dictDTO.setDictValue();
            dictDTO.setIsDeleted(Boolean.FALSE);
            dictDTO.setSeq(seq++);

            if (!dictService.isExistByCodeAndName(dictDTO.getCode(), jobGrade)) {
                dictService.save(dictDTO);
            } else {
                // 根据 code 和name 更新 seq
                dictService.update(dictDTO, new QueryWrapper<Dict>().eq("code", dictDTO.getCode()).eq("dict_name", dictDTO.getDictName()).eq("is_deleted", 0));
            }
        }

    }

    @Test
    void isExistByCodeAndName() {
        log.info("isExistByCodeAndName: {}", dictService.isExistByCodeAndName("job_grade", "M26"));
        log.info("isExistByCodeAndName: {}", dictService.isExistByCodeAndName("job_grade", "M30"));
    }

    @Test
    void selectJobGrade() {
        dictService.selectJobGrade().forEach(System.out::println);
    }

    @Test
    void testInsert() {
        Dict dictDTO = new Dict();
        // dictDTO.setId();
        dictDTO.setCode("test");
        dictDTO.setDictName("test name");
        dictDTO.setDictValue("test value");
        dictDTO.setIsDeleted(Boolean.FALSE);
        dictDTO.setSeq(0);

        dictService.save(dictDTO);
    }

    @Test
    void testUpdate() {
        Dict dictDTO = new Dict();
        dictDTO.setId("15549cb5-82d0-4cf8-8c4b-3a517bbca3dc");
        dictDTO.setDictValue("test value 4");
        // dictDTO.setIsDeleted(Boolean.TRUE);
        // dictDTO.setDictName(null);
        // LambdaUpdateWrapper<DictDTO> updateWrapper = new LambdaUpdateWrapper<>();
        // updateWrapper/*.set(DictDTO::getDictValue, "test value 2")*/.eq(DictDTO::getCode, "test").eq(DictDTO::getDictName, "test name");
        // dictService.update(dictDTO, updateWrapper);
        dictService.updateById(dictDTO);
    }

    @Test
    void dashboardQueryOptions() {

    }

    @Test
    void selectPoliticalStatus() {

        dictService.selectPoliticalStatus();
    }

    /**
     * 初始化专业
     */
    @Test
    void initJobType() {
        String code = "staff_count_job_type";
        List<String> lstJobType = new ArrayList<>();
        lstJobType.add("生产");
        lstJobType.add("生产管理");
        lstJobType.add("企业文化/传讯");
        lstJobType.add("合约");
        lstJobType.add("行政");
        lstJobType.add("投资");
        lstJobType.add("其他");
        lstJobType.add("其它");
        lstJobType.add("法务");
        lstJobType.add("物资");
        lstJobType.add("采购");
        lstJobType.add("信息化");
        lstJobType.add("研发");
        lstJobType.add("科技");
        lstJobType.add("创新业务");
        lstJobType.add("审计");
        lstJobType.add("监察");
        lstJobType.add("设计");
        lstJobType.add("财务/金融");
        lstJobType.add("质量安全");
        lstJobType.add("运营");
        lstJobType.add("项目管理");
        lstJobType.add("项目负责人");

        int iSeq = 1;
        for (String jobType : lstJobType) {
            Dict dictDTO = new Dict();
            dictDTO.setCode(code);
            dictDTO.setDictName(jobType);
            dictDTO.setIsDeleted(Boolean.FALSE);
            dictDTO.setSeq(iSeq++);

            if (!dictService.isExistByCodeAndName(dictDTO.getCode(), jobType)) {
                dictService.save(dictDTO);
            } else {
                // 根据 code 和name 更新 seq
                dictService.update(dictDTO, new QueryWrapper<Dict>().eq("code", dictDTO.getCode()).eq("dict_name", dictDTO.getDictName()).eq("is_deleted", 0));
            }
        }
    }

    private Dict createDict(String code, String name, String value, int seq) {
        Dict dict = new Dict();
        // dict.setId();
        dict.setCode(code);
        dict.setDictName(name);
        dict.setDictValue(value);
        dict.setIsDeleted(Boolean.FALSE);
        dict.setSeq(seq);
        dict.setParentId(null);
        return dict;
    }

    // recruitment_type
    @Test
    void initRecruitmentType() {
        String code = "recruitment_type";
        List<String> dictNames = Arrays.asList("內派", "地盘日薪", "港聘核心", "地盘月薪", "地盘基层", "內地中台", "实习生");

        int seq = 1;
        for (String dictName : dictNames) {
            Dict dict = createDict(code, dictName, dictName, seq++);
            if (!dictService.isExistByCodeAndName(dict.getCode(), dict.getDictName())) {
                dictService.save(dict);
            } else {
                dictService.update(dict, new QueryWrapper<Dict>().eq("code", dict.getCode()).eq("dict_name", dict.getDictName()).eq("is_deleted", 0));
            }
        }
    }

    // 情况说明类型
    @Test
    void initOverviewType() {
        String code = "overview_type";
        List<String> dictNames = Arrays.asList("初始化", "工期提前/延迟", "里程碑節點變動", "其他原因");

        int seq = 1;
        for (String dictName : dictNames) {
            Dict dict = createDict(code, dictName, dictName, seq++);
            if (!dictService.isExistByCodeAndName(dict.getCode(), dict.getDictName())) {
                dictService.save(dict);
            } else {
                dictService.update(dict, new QueryWrapper<Dict>().eq("code", dict.getCode()).eq("dict_name", dict.getDictName()).eq("is_deleted", 0));
            }
        }
    }

    @Test
    void initStudyMode() {
        String code = "study_mode";
        List<Dict> dictList = new ArrayList<>();
        dictList.add(createDict(code, "全日制统招", "01", 1));
        dictList.add(createDict(code, "自考", "02", 2));
        dictList.add(createDict(code, "成教", "03", 3));
        dictList.add(createDict(code, "夜大", "05", 5));
        dictList.add(createDict(code, "党校", "06", 6));
        dictList.add(createDict(code, "函授", "07", 7));
        dictList.add(createDict(code, "远程教育", "08", 8));
        dictList.add(createDict(code, "在职教育/兼读制", "09", 9));
        dictList.add(createDict(code, "其他", "99", 99));

        for (Dict dict : dictList) {
            if (!dictService.isExistByCodeAndName(dict.getCode(), dict.getDictName())) {
                dictService.save(dict);
            } else {
                dictService.update(dict, new QueryWrapper<Dict>().eq("code", dict.getCode()).eq("dict_name", dict.getDictName()).eq("is_deleted", 0));
            }
        }
    }
}