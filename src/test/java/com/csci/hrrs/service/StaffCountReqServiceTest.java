package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.StaffCountQO;
import com.csci.hrrs.util.BaseTestUtils;
import com.csci.hrrs.util.context.ContextUtils;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@ActiveProfiles("dev")
class StaffCountReqServiceTest extends BaseTest {

    @Resource
    private StaffCountReqService staffCountReqService;

    @Test
    void listStaffCountReq() {
        StaffCountQO staffCountQO = new StaffCountQO();
        staffCountQO.setPlatformCode("1");
        staffCountQO.setFromDate(LocalDate.of(2023,1,1));
        staffCountQO.setToDate(LocalDate.of(2024, 12, 1));
        staffCountReqService.listStaffCountReq(staffCountQO);
    }

    @Test
    void listStaffCountByDate() {
        // staffCountReqService.listStaffCountByDate(BaseTestUtils.getAllOrgCodeList(), LocalDate.of(2023, 1, 1), LocalDate.now());
        // 50000986
        // 50000980
        // 50000986
        // 50000980
        // 50000986
        // 50000986
        // 50000986
        staffCountReqService.listStaffCountByDate(List.of("50000986", "50000980", "50000986"), LocalDate.of(2024, 1, 1), LocalDate.now());
    }

    @Test
    void selectStaffCountByMonthAndOrg() {

        staffCountReqService.selectStaffCountByMonthAndOrg(ContextUtils.getCurrentUser().getId(), BaseTestUtils.getAllOrgCodeList(), 2023, 10);
    }
}