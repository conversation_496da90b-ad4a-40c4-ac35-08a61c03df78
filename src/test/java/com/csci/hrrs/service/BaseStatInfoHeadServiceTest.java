package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class BaseStatInfoHeadServiceTest extends BaseTest {

    @Resource
    private BaseStatInfoHeadService baseStatInfoHeadService;

    @Test
    void getOrInit() {
        baseStatInfoHeadService.getOrInit("50001380", 2024, 6);
    }
}