package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.Batch;
import com.csci.hrrs.model.EmissionFactor;
import com.csci.hrrs.qo.BatchIdPageQO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class EmissionFactorServiceTest extends BaseTest {

    @Autowired
    EmissionFactorService emissionFactorService;

    @Autowired
    BatchService batchService;

    @Test
    void insertSelective() {

        Batch batch = batchService.getOrInitBatch("0ad1cf7c-4550-44f6-bc8b-5d0a2f8eb9ff", 2022, 9);

        for (int i = 0; i < 100; i++) {
            EmissionFactor record = new EmissionFactor();
            // record.setId();
            record.setBatchId(batch.getId());
            record.setCategory("Category" + i);
            record.setEmissionSource("EmissionSource" + i);
            record.setType("Type" + i);
            record.setUnit("Unit" + i);
            record.setEmissionFactor("EmissionFactor" + i);
            record.setEmissionUnit("EmissionUnit" + i);
            record.setEnergyConsumptionFactor("EnergyConsumptionFactor" + i);
            record.setEnergyConsumptionUnit("EnergyConsumptionUnit" + i);

            emissionFactorService.insertSelective(record);
        }
    }

    @Test
    void listEmissionFactorByPage() {
        BatchIdPageQO batchIdPageQO = new BatchIdPageQO();
        batchIdPageQO.setBatchId("f615e434-e2c5-4bdc-a22c-b090d9c43686");
        batchIdPageQO.setPageSize(10);
        batchIdPageQO.setCurPage(1);

        emissionFactorService.listEmissionFactorByPage(batchIdPageQO);
    }
}