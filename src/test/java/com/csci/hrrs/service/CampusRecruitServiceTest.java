package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class CampusRecruitServiceTest extends BaseTest {

    @Resource
    private CampusRecruitService campusRecruitService;

    @Test
    void listByRecoveryDate() {
        campusRecruitService.listByRecoveryDate(LocalDate.now());
    }

    @Test
    void getMaxRecoveryDate() {
        campusRecruitService.getMaxRecoveryDate(LocalDate.now());
    }
}