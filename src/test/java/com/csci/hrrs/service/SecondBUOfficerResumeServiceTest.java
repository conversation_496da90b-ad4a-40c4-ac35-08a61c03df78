package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class SecondBUOfficerResumeServiceTest extends BaseTest {

    @Resource
    private SecondBUOfficerResumeService service;

    @Test
    void list() {
        System.out.println(service.count());
        PageHelper.startPage(2, 10);
        System.out.println(CommonUtils.toJson(service.list()));
    }

}