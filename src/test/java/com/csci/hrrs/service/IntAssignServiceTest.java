package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;

class IntAssignServiceTest extends BaseTest {

    @Resource
    private IntAssignService intAssignService;

    @Test
    void selectHzzCount() {
        intAssignService.selectHzzCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectIntAssignCount() {
        intAssignService.selectIntAssignCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectManagerCount() {
        intAssignService.selectManagerCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectTechOrWorkerCount() {
        intAssignService.selectTechOrWorkerCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDeptCount() {
        intAssignService.selectDeptCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDeptBusiCount() {
        intAssignService.selectDeptBusiCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectEngCompanyCount() {
        intAssignService.selectEngCompanyCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectEngCompanyDeptOrSiteCount() {
        intAssignService.selectEngCompanyDeptOrSiteCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectJiXieCount() {
        intAssignService.selectJiXieCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectZZXHaihongCount() {
        intAssignService.selectZZXHaihongCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrBySubsidiary() {
        intAssignService.selectDistrBySubsidiary(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrBySchoolType() {
        intAssignService.selectDistrBySchoolType(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrByAge() {
        intAssignService.selectDistrByAge(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrByCohlSeniority() {
        intAssignService.selectDistrByCohlSeniority(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectSecondeBULeaderCount() {
        intAssignService.selectSecondeBULeaderCount(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrByPositionInDept() {
        intAssignService.selectDistrByPositionInDept(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectDistrByPositionInBusi() {
        intAssignService.selectDistrByPositionInBusi(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

    @Test
    void selectAvgCohlSeniority() {
        intAssignService.selectAvgCohlSeniority(null, LocalDate.now().withDayOfMonth(1), LocalDate.now());
    }

}