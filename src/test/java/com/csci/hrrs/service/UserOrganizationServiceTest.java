package com.csci.hrrs.service;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.UserOrganization;
import com.csci.hrrs.vo.UserOrganizationVO;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
class UserOrganizationServiceTest extends BaseTest {

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private UserOrganizationService userOrganizationService;
    @Resource
    private OrganizationService organizationService;

    @Test
    void saveUserOrganizationList() {
        List<UserOrganizationVO> lstUserOrganization = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            UserOrganizationVO userOrganization = new UserOrganizationVO();
            userOrganization.setUserId("userId" + i);
            userOrganization.setOrganizationId("orgId" + i);
            lstUserOrganization.add(userOrganization);
        }
        List<UserOrganizationVO> lstResult = userOrganizationService.saveUserOrganizationList(lstUserOrganization);

        log.info("lstUserOrganization: {}", gson.toJson(lstResult));
    }

    @Test
    void listUserOrganizationByUsername() {
        List<UserOrganizationVO> lstResult = userOrganizationService.listUserOrganizationByUsername("tao_li");
        log.info("lstUserOrganization: {}", gson.toJson(lstResult));
    }

    @Test
    void testSaveBatch() {
        List<UserOrganization> lstUserOrganization = new ArrayList<>();
        UserOrganization userOrganization1 = new UserOrganization();
        userOrganization1.setUserId("00634b60-f5e1-4642-9401-ae430afec924");
        userOrganization1.setOrganizationId("1");
        userOrganization1.setOrganizationCode("1");
        lstUserOrganization.add(userOrganization1);

        UserOrganization userOrganization2 = new UserOrganization();
        userOrganization2.setUserId("00634b60-f5e1-4642-9401-ae430afec924");
        userOrganization2.setOrganizationId("2");
        userOrganization2.setOrganizationCode("2");
        lstUserOrganization.add(userOrganization2);

        UserOrganization userOrganization3 = new UserOrganization();
        userOrganization3.setUserId("00634b60-f5e1-4642-9401-ae430afec924");
        userOrganization3.setOrganizationId("3");
        userOrganization3.setOrganizationCode("3");
        lstUserOrganization.add(userOrganization3);

        userOrganizationService.saveBatch(lstUserOrganization);
    }

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Test
    void testSave() {
        UserOrganization userOrganization1 = new UserOrganization();
        userOrganization1.setUserId("00634b60-f5e1-4642-9401-ae430afec924");
        userOrganization1.setOrganizationId("1");
        userOrganization1.setOrganizationCode("1");
        userOrganizationService.save(userOrganization1);
    }

    @Test
    void listUserOrgCodes() {
        userOrganizationService.listUserOrgCodes("00634b60-f5e1-4642-9401-ae430afec924");
    }
}