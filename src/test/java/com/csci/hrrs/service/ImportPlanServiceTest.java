package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.dto.ImportPlanFullDTO;
import com.csci.hrrs.dto.ImportPlanYearlyDTO;
import com.csci.hrrs.model.ImportPlan;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.ResultBean;
import com.csci.hrrs.qo.plan.StaffCountQO;
import com.csci.hrrs.vo.plan.FullCountVO;
import com.csci.hrrs.vo.plan.ImportPlanFullVO;
import com.csci.hrrs.vo.plan.ImportPlanYearlyVO;
import com.csci.hrrs.vo.plan.ManagerBaseVO;
import com.csci.hrrs.vo.plan.OrgBaseVO;
import com.csci.hrrs.vo.plan.OutputValueVO;
import com.csci.hrrs.vo.plan.StaffCountVO;
import com.csci.hrrs.vo.plan.YearCountVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @ClassName: ImportPlanServiceTest
 * @Auther: <EMAIL>
 * @Date: 2025/1/3 上午10:49
 * @Description: 编制导入计划测试类
 */
public class ImportPlanServiceTest extends BaseTest {
    @Resource
    private ImportPlanService importPlanService;
    @Resource
    private OrganizationService organizationService;

    @Test
    public void testQueryOrgInfo() {
        OrgBaseVO orgBaseVO = importPlanService.queryBaseByOrgInfo("00100193");
        System.out.println(orgBaseVO);
    }

    @Test
    void testManageInfo() {
        List<ManagerBaseVO> managerBaseVOS = importPlanService.queryBaseByManagerInfo("李大林");
        System.out.println(managerBaseVOS);
    }

    @Test
    void testSaveFullBaseInfo() {
        ImportPlanFullDTO importPlanDTO = new ImportPlanFullDTO();
        importPlanDTO.setId("6d0eb93c-8b9e-4c9a-a13a-69be77d8d0c1");
        importPlanDTO.setOrgCode("50000986"); // 50000784:启德医院Site B智能工地系统    50000986:启德新急症医院(Site A)
        importPlanDTO.setOrgName("启德新急症医院(Site A)");
        importPlanDTO.setContractAmount(BigDecimal.valueOf(78));
        importPlanDTO.setManagerName("趙震"); // managerName
        importPlanDTO.setManagerEngName("ZHEN_ZHAO"); // managerEngName
        importPlanDTO.setTeamManagerName("李大林");
        importPlanDTO.setTeamManagerEngName("LIDL"); // managerPassword
        importPlanDTO.setProjectType("房屋署工程");
        importPlanDTO.setProjectScale("特大型（30亿以上）");
        importPlanDTO.setAvgOutputSuggested(BigDecimal.valueOf(123));
        importPlanDTO.setAvgOutputReported(BigDecimal.valueOf(100));
        importPlanDTO.setCostPerHundredSuggested(BigDecimal.valueOf(80000000));
        importPlanDTO.setCostPerHundredReported(BigDecimal.valueOf(92500000));
        importPlanDTO.setStartTime("2025-01-01");
        importPlanDTO.setEndTime("2025-05-01");
        importPlanDTO.setSituation("初始化");
        importPlanDTO.setStatus("submitted");
//        importPlanDTO.setPlanType("full");
        ResultBean<ImportPlanFullVO> importPlanFullVOResultBean = importPlanService.saveFullBaseInfo(importPlanDTO);
        System.out.println(importPlanFullVOResultBean.getData());
    }

    @Test
    void testSaveYearBaseInfo() {
        ImportPlanYearlyDTO importPlanDTO = new ImportPlanYearlyDTO();
        importPlanDTO.setId("7dce2aa3-4f7d-4587-b753-244f8568a781");
        importPlanDTO.setOrgCode("50000986"); // 50000784:启德医院Site B智能工地系统    50000986:启德新急症医院(Site A)
        importPlanDTO.setOrgName("启德新急症医院(Site A)");
        importPlanDTO.setContractAmount(BigDecimal.valueOf(78));
        importPlanDTO.setManagerName("趙震"); // managerName
        importPlanDTO.setManagerEngName("ZHEN_ZHAO"); // managerEngName
        importPlanDTO.setTeamManagerName("李大林");
        importPlanDTO.setTeamManagerEngName("LIDL"); // managerPassword
        importPlanDTO.setYear(2025);
        importPlanDTO.setTotalBudgetAfter(BigDecimal.valueOf(70));
        importPlanDTO.setTotalBudgetBefore(BigDecimal.valueOf(78));
        importPlanDTO.setTotalStaffCountAfter(109);
        importPlanDTO.setTotalStaffCountBefore(110);
        importPlanDTO.setStartTime("2024-01-01");
        importPlanDTO.setEndTime("2025-01-02");
        importPlanDTO.setStatus("submitted");
//        importPlanDTO.setPlanType("full");
        ImportPlanYearlyVO importPlanVO = importPlanService.saveYearBaseInfo(importPlanDTO).getData();
        System.out.println(importPlanVO);
    }

    @Test
    void testGetImportPlanFullDraft() {
        // 生成的UUID: 6d0eb93c-8b9e-4c9a-a13a-69be77d8d0c1  orgCode=50000474
        ImportPlanFullVO importPlanFullDraft = importPlanService.getImportPlanFullDraft("50000474").getData();
        System.out.println(importPlanFullDraft);
    }


    @Test
    void testGetImportPlanYearlyDraft() {
        // 生成的UUID: 7dce2aa3-4f7d-4587-b753-244f8568a781
        ImportPlanYearlyVO importPlanYearlyDraft = importPlanService.getImportPlanYearlyDraft("50000986").getData();
        System.out.println(importPlanYearlyDraft);
    }

    @Test
    void testQueryBaseByStaffFullAdjust() {
        StaffCountQO staffCountQO = new StaffCountQO();
        staffCountQO.setOrgCode("50000539");
        staffCountQO.setImportPlanId("22ce2806-fe3d-4c1f-915a-9d69f380cb8a");
        staffCountQO.setStartDate(LocalDate.parse("2022-06-06"));
        staffCountQO.setEndDate(LocalDate.parse("2025-12-31"));
        staffCountQO.setYear(2025);
        staffCountQO.setPlanType("yearly");
        StaffCountVO staffCountVO = importPlanService.queryBaseByStaffFullAdjust(staffCountQO);
        System.out.println(staffCountVO);
    }

    @Test
    void testQueryBaseByStaffYearlyAdjust() {
        // {"orgCode":"00100208","importPlanId":"e72429ee-5e44-455b-a92c-81d5d1a2e1ed","startDate":"2021-03-25","endDate":"2024-12-27","year":"2025"}
        StaffCountQO staffCountQO = new StaffCountQO();
        staffCountQO.setOrgCode("00100208");
        staffCountQO.setImportPlanId("e72429ee-5e44-455b-a92c-81d5d1a2e1ed");
        staffCountQO.setStartDate(LocalDate.parse("2021-03-25"));
        staffCountQO.setEndDate(LocalDate.parse("2024-12-27"));
        staffCountQO.setYear(2025);
        StaffCountVO staffCountVO = importPlanService.queryBaseByStaffYearlyAdjust(staffCountQO);
        System.out.println(staffCountVO);
    }

    @Test
    void getTotalStaffCountFull() {
        Organization organization =  organizationService.getByCode("50000986");;
        ImportPlan importPlan = importPlanService.getById("6d0eb93c-8b9e-4c9a-a13a-69be77d8d0c1"); // full
        FullCountVO fullCountVO = importPlanService.getTotalStaffCountFull(organization.getCode(), organization.getStartDate(), organization.getEndDate(), importPlan);
        System.out.println(fullCountVO);
    }

    @Test
    void getTotalStaffCountYearly() {
        Organization organization =  organizationService.getByCode("50000984");;
        ImportPlan importPlan = importPlanService.getById("30a3c300-590d-4cf3-95e1-696160b6d391"); // yearly
        YearCountVO totalStaffCountYearly = importPlanService.getTotalStaffCountYearly(organization, importPlan);
        System.out.println(totalStaffCountYearly);
    }

    @Test
    void testGetOrgInfo() {
        // {"orgCode":"00100193","importPlanId":"cf2b23f3-6cee-4ddd-9ee2-e65ab1de2672","startDate":"2020-11-30","endDate":"2024-10-31","year":2024}
        StaffCountQO staffCountQO = new StaffCountQO();
        staffCountQO.setOrgCode("00100193");
        staffCountQO.setImportPlanId("cf2b23f3-6cee-4ddd-9ee2-e65ab1de2672");
        staffCountQO.setStartDate(LocalDate.parse("2020-11-30"));
        staffCountQO.setEndDate(LocalDate.parse("2024-10-31"));
        staffCountQO.setYear(2024);
        OutputValueVO importPlanFullVO = importPlanService.queryBaseByPlanFullOutputValue(staffCountQO);
        System.out.println(importPlanFullVO);
    }

    @Test
    void testSaveImportPlanFull() {
        ImportPlanFullDTO importPlanFullDTO = new ImportPlanFullDTO();
        importPlanFullDTO.setOrgCode("50000986");
        importPlanFullDTO.setOrgName("启德新急症医院(Site A)");
        importPlanFullDTO.setContractAmount(BigDecimal.valueOf(78));
        importPlanFullDTO.setManagerName("趙震");
        importPlanFullDTO.setManagerEngName("ZHEN_ZHAO");
        importPlanFullDTO.setTeamManagerEngName("LIDL");
        importPlanFullDTO.setTeamManagerName("李斌");
        importPlanFullDTO.setProjectType("房屋署工程");
        importPlanFullDTO.setProjectScale("特大型（30亿以上）");
        ImportPlanFullVO importPlanFullVO = importPlanService.saveFullImportPlan(importPlanFullDTO).getData();
        System.out.println(importPlanFullVO);
    }
}
