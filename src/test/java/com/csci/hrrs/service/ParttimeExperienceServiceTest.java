package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.dto.ParttimeExperienceDTO;
import com.csci.hrrs.model.ResultPage;
import com.csci.hrrs.util.factory.ObjectMapperFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
class ParttimeExperienceServiceTest extends BaseTest {

    private final ObjectMapper objectMapper = ObjectMapperFactory.getInstance();
    @Resource
    ParttimeExperienceService parttimeExperienceService;

    @Test
    void testQuery() {
        IPage<ParttimeExperienceDTO> result = parttimeExperienceService.page(new Page<>(1, 1));
        try {
            log.info("result: {}", objectMapper.writeValueAsString(result));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testOldPage() {
        PageHelper.startPage(1, 1);
        List<ParttimeExperienceDTO> list = parttimeExperienceService.list();
        try {
            ResultPage<ParttimeExperienceDTO> resultPage = new ResultPage<>(list, ParttimeExperienceDTO.class);
            log.info("result: {}", objectMapper.writeValueAsString(resultPage));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}