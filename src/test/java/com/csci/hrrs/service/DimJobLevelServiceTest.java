package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class DimJobLevelServiceTest extends BaseTest {

    @Resource
    private DimJobLevelService dimJobLevelService;

    @Test
    void testList() {
        PageHelper.startPage(1, 10);
        // dimJobLevelService.list();
        System.out.println(CommonUtils.toJson(dimJobLevelService.list()));
    }
}