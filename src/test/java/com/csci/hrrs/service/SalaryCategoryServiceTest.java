package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.model.SalaryCategory;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("prod")
class SalaryCategoryServiceTest extends BaseTest {

    @Resource
    private SalaryCategoryService salaryCategoryService;

    @Test
    void initSalaryCategory() {
        salaryCategoryService.initCategory();
    }

}