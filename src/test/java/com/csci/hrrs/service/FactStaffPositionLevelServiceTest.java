package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("localprod")
class FactStaffPositionLevelServiceTest extends BaseTest {

    @Resource
    private FactStaffPositionLevelService factStaffPositionLevelService;

    @Test
    void selectPositionLevel() {
        factStaffPositionLevelService.selectPositionLevel();
    }
}