package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.RosterLeaderTitle;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("prod")
class RosterLeaderTitleServiceTest extends BaseTest {

    @Resource
    private RosterLeaderTitleService rosterLeaderTitleService;

    @Test
    void initData() {
        List<RosterLeaderTitle> lstDate = new ArrayList<>();
        lstDate.add(getRecord("00000045", "董事局主席"));

        lstDate.add(getRecord("00017410", "行政总裁"));

        // 00000296	楊衛東
        // 50027891	張杰
        // 00000184	孔祥兆
        // 00009377	羅海川
        // 00000483	趙小琦
        // 00001513	趙紹然
        // 00000824	周文斌
        // 00024779	劉國兵
        // 00000415	黃江
        lstDate.add(getRecord("00000296", "副总裁(级)"));
        lstDate.add(getRecord("50027891", "副总裁(级)"));
        lstDate.add(getRecord("00000184", "副总裁(级)"));
        lstDate.add(getRecord("00009377", "副总裁(级)"));
        lstDate.add(getRecord("00000483", "副总裁(级)"));
        lstDate.add(getRecord("00001513", "副总裁(级)"));
        lstDate.add(getRecord("00000824", "副总裁(级)"));
        lstDate.add(getRecord("00024779", "副总裁(级)"));
        lstDate.add(getRecord("00000415", "副总裁(级)"));

        // 00000044	田樹臣
        // 50042282	陳恒鑌
        // 00002000	張明
        // 00001116	周志軻
        // 50018074	米翔
        // 50041397	張宗軍
        lstDate.add(getRecord("00000044", "助理总裁/专业高管"));
        lstDate.add(getRecord("50042282", "助理总裁/专业高管"));
        lstDate.add(getRecord("00002000", "助理总裁/专业高管"));
        lstDate.add(getRecord("00001116", "助理总裁/专业高管"));
        lstDate.add(getRecord("50018074", "助理总裁/专业高管"));
        lstDate.add(getRecord("50041397", "助理总裁/专业高管"));

        removeExistFrom(lstDate);

        rosterLeaderTitleService.saveBatch(lstDate);
    }

    /**
     * 从List中移除
     *
     * @param dataList 数据
     */
    private void removeExistFrom(List<RosterLeaderTitle> dataList) {
        LambdaQueryWrapper<RosterLeaderTitle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RosterLeaderTitle::getIsDeleted, Boolean.FALSE).in(RosterLeaderTitle::getPernr, dataList.stream().map(RosterLeaderTitle::getPernr).toArray());
        List<RosterLeaderTitle> existList = rosterLeaderTitleService.list(queryWrapper);
        dataList.removeIf(x -> existList.stream().anyMatch(y -> y.getPernr().equals(x.getPernr())));
    }

    private RosterLeaderTitle getRecord(String pernr, String title) {
        RosterLeaderTitle rosterLeaderTitle = new RosterLeaderTitle();
        rosterLeaderTitle.setPernr(pernr);
        rosterLeaderTitle.setTitle(title);
        return rosterLeaderTitle;
    }
}