package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.EmployeeSubGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
class EmployeeSubGroupServiceTest extends BaseTest {

    @Resource
    private EmployeeSubGroupService employeeSubGroupService;

    @Test
    void selectAll() {
        System.out.println(employeeSubGroupService.selectAll());
    }

    @Test
    void sourceEmployeeSubGroups() {
        List<Map<String, String>> lstSubGroups = employeeSubGroupService.sourceEmployeeSubGroups();
        for (Map<String, String> map : lstSubGroups) {
            String employeeSubgroup = map.get("employeeSubgroup");
            String employeeSubgroupText = map.get("employeeSubgroupText");

            if (StringUtils.isBlank(employeeSubgroup) || StringUtils.isBlank(employeeSubgroupText)) {
                log.info("employeeSubgroup: {}, employeeSubgroupText: {}", employeeSubgroup, employeeSubgroupText);
            }

            // 先判断是否已经存在，如果不存在则插入
            if (employeeSubGroupService.isExistByCode(employeeSubgroup)) {
                continue;
            }
            EmployeeSubGroup record = new EmployeeSubGroup();
            // record.setId();
            record.setCode(employeeSubgroup);
            record.setName(employeeSubgroupText);
            record.setSeq(0);
            record.setIsDeleted(Boolean.FALSE);

            // employeeSubGroupService.insertSelective(record);
            employeeSubGroupService.save(record);

        }

    }

    @Test
    void testInsert() {
        EmployeeSubGroup employeeSubGroup = new EmployeeSubGroup();
        // employeeSubGroup.setId();
        employeeSubGroup.setCode("33");
        employeeSubGroup.setName("海外属地/派驻员工");
        // employeeSubGroup.setSeq(0);
        // employeeSubGroup.setIsDeleted(Boolean.FALSE);
        // employeeSubGroup.setCreationTime();
        // employeeSubGroup.setCreateUserId();
        // employeeSubGroup.setCreateUsername();
        // employeeSubGroup.setLastUpdateTime();
        // employeeSubGroup.setLastUpdateUserId();
        // employeeSubGroup.setLastUpdateUsername();
        // employeeSubGroup.setLastUpdateVersion();
        // employeeSubGroupService.insertSelective(employeeSubGroup);
        employeeSubGroupService.save(employeeSubGroup);
    }
}