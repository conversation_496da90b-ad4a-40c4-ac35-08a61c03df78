package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.CertQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class CertDefinitionServiceTest extends BaseTest {

    @Resource
    private CertDefinitionService certDefinitionService;

    @Test
    void listCertType() {
        certDefinitionService.listCertType();
    }

    @Test
    void listCertNoByPage() {
        CertQO certQO = new CertQO();
        certQO.setParentCode("A");
        certDefinitionService.listCertNoByPage(certQO);
    }

    @Test
    void listCertMajorByNo() {
        CertQO certQO = new CertQO();
        certQO.setParentCode("ACN006");
        certDefinitionService.listCertMajorByNo(certQO);
    }
}