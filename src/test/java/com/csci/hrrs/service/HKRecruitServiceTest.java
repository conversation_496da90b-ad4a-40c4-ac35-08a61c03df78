package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.constant.HrrsConsts;
import com.csci.hrrs.qo.HKDashQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class HKRecruitServiceTest extends BaseTest {

    @Resource
    private HKRecruitService hkRecruitService;

    @Test
    void getHKRecruitCount() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        long hkRecruitCount = hkRecruitService.getHKRecruitCount(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1), qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth()));
        System.out.println(hkRecruitCount);
    }

    @Test
    void selectHKRecruitDistrBySubsidiary() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrBySubsidiary(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByManageType() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByManageType(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByTalentType() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByTalentType(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByGender() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByGender(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth()), 1).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByEducation() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByEducation(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth()), HrrsConsts.ManageType.manager).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByCohlSeniority() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByCohlSeniority(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth()), "管理人员").forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByJobType() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByJobType(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDistrByPosition() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDistrByPosition(
                qo.getOrganizationCodeList(),
                qo.getStatDate().withDayOfMonth(1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }

    @Test
    void selectHKRecruitDismissByYearMonth() {
        HKDashQO qo = new HKDashQO();
        qo.setStatDate(LocalDate.now());
        qo.setOrganizationCodeList(List.of("00100022", "00100029"));
        hkRecruitService.selectHKRecruitDismissByYearMonth(
                qo.getOrganizationCodeList(),
                LocalDate.of(2022, 1, 1),
                qo.getStatDate().withDayOfMonth(qo.getStatDate().lengthOfMonth())).forEach(System.out::println);
    }
}