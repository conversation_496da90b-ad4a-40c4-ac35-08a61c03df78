package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;

class BaseHeadSubmitStatusServiceTest extends BaseTest {

    @Resource
    private BaseHeadSubmitStatusService baseHeadSubmitStatusService;

    @Test
    void selectAllSubmittedHeadIdsOfSubOrgs() {
        baseHeadSubmitStatusService.selectAllSubmittedHeadIdsOfSubOrgs("64b8d5e9-315f-47a4-ae9c-5412bd34bcc2", LocalDate.of(2023, 6, 1), BaseHeadSubmitStatusService.TableKey.base_stat_info);
    }
}