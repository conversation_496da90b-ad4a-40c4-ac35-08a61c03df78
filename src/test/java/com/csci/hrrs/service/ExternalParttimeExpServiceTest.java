package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.util.factory.ObjectMapperFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class ExternalParttimeExpServiceTest extends BaseTest {

    @Resource
    private ExternalParttimeExpService externalParttimeExpService;

    private ObjectMapper objectMapper = ObjectMapperFactory.getInstance();

    @Test
    void listExternalParttimeExpByPernr() {
        externalParttimeExpService.listExternalParttimeExpByPernr("50005589").forEach(x -> {
            System.out.println(DemoUtils.toJson(x));
        });
    }
}