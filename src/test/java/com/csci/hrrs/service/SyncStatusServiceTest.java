package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.SyncStatus;
import com.csci.hrrs.model.SyncStatusExample;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class SyncStatusServiceTest extends BaseTest {

    @Resource
    private SyncStatusService syncStatusService;

    @Test
    void selectByExample() {
        PageHelper.startPage(1, 10);
        List<SyncStatus> lstStatus = syncStatusService.selectByExample(new SyncStatusExample());
        log.info("lstStatus: {}", lstStatus);
    }

    @Test
    void findLatestSyncStatus() {
        SyncStatus syncStatus = syncStatusService.findLatestSyncStatus();
        log.info("syncStatus: {}", syncStatus);
    }
}