package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.RawRevenue;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

class RawRevenueServiceTest extends BaseTest {

    @Resource
    private RawRevenueService rawRevenueService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<RawRevenue> list = rawRevenueService.list();
        System.out.println(CommonUtils.toJson(list));
    }

    @Test
    void getBy() {
        RawRevenue rawRevenue = rawRevenueService.getBy("房屋公司", 2024, 5);
        System.out.println(CommonUtils.toJson(rawRevenue));
    }
}