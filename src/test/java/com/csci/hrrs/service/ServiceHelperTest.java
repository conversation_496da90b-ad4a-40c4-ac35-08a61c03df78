package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("prod")
class ServiceHelperTest extends BaseTest {

    @Test
    void isOrganizationLeaf() {
        System.out.println(ServiceHelper.isOrganizationLeaf("1a8428fa-f392-4ce5-9265-feed8b07200e"));
        System.out.println(ServiceHelper.isOrganizationLeaf("231248dd-b3da-4e16-ace1-086f3ef86652"));
    }
}