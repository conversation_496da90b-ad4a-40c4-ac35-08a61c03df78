package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.Area;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("prod")
class AreaServiceTest extends BaseTest {

    @Autowired
    private AreaService areaService;

    @Test
    void insertSelective() {

        Area area = new Area();
        // area.setId();
        area.setCode("HK-MACAU");
        area.setName("香港和澳门");
        area.setSort(1);
        areaService.insertSelective(area);

        area = new Area();
        // area.setId();
        area.setCode("PRC");
        area.setName("中国大陆830+皇姑热电厂");
        area.setSort(1);
        areaService.insertSelective(area);

        area = new Area();
        // area.setId();
        area.setCode("ZJGT");
        area.setName("中建国际投资");
        area.setSort(1);
        areaService.insertSelective(area);
    }
}