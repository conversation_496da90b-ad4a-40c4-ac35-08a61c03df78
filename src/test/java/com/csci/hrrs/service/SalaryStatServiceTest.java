package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.SalaryStatQO;
import com.csci.hrrs.util.BaseTestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

@ActiveProfiles("dev")
class SalaryStatServiceTest extends BaseTest {

    @Resource
    private SalaryStatService salaryStatService;

    @Test
    void list() {
        salaryStatService.list();
    }

    @Test
    void listSalaryStat() {
        SalaryStatQO qo = new SalaryStatQO();
        qo.setYear(2023);
        qo.setOrganizationCode("test1");
        salaryStatService.listSalaryStat(qo);
    }

    @Test
    void listSalaryCountByDate() {
        salaryStatService.listSalaryCountByDate(2024, List.of("00100017"), false, "内地");
    }

    @Test
    void getSalaryDistrByDate() {
        salaryStatService.getSalaryDistrByDate(null, BaseTestUtils.getAllOrgCodeList(), "内地");
    }

    @Test
    void listForQueryPageByHeadIdForCurrentUser() {
        salaryStatService.listByHeadIdForCurrentUser("87de3596-8cb6-43d9-9e24-5d8ff1621a1d", true);
    }

    @Test
    void listFillRecordsForCurrentUser() {
        salaryStatService.listFillRecordsForCurrentUser("87de3596-8cb6-43d9-9e24-5d8ff1621a1d", "");
    }
}