package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.DwOrganization;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DwOrganizationServiceTest extends BaseTest {

    @Resource
    private DwOrganizationService dwOrganizationService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<DwOrganization> list = dwOrganizationService.list();
        System.out.println("size: " + list.size());
    }

}