package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.HRMonthReport;
import com.csci.hrrs.qo.HRMonthReportQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;

class HRMonthReportServiceTest extends BaseTest {

    @Resource
    private HRMonthReportService hrMonthReportService;

    @Test
    void testList() {
        hrMonthReportService.lambdaQuery().orderByAsc(HRMonthReport::getSort).list();
    }

    @Test
    void listByPage() {
        HRMonthReportQO qo = new HRMonthReportQO();
        qo.setRecoverydate(hrMonthReportService.getMaxRecoverydate());
        hrMonthReportService.listByPage(qo);
    }

    @Test
    void getMaxRecoverydate() {
        System.out.println("max date: " + hrMonthReportService.getMaxRecoverydate(LocalDate.now()));
    }
}