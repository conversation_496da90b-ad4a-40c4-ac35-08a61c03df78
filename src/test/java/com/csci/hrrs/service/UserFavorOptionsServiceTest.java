package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.BusinessQO;
import com.csci.hrrs.vo.UserFavorOptionsVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class UserFavorOptionsServiceTest extends BaseTest {

    @Resource
    UserFavorOptionsService userFavorOptionsService;

    @Test
    void deleteUserQueryOptions() {
        userFavorOptionsService.deleteUserQueryOptions("2b9670dc-8235-4166-9ece-bab7bc0c50c8");
    }

    @Test
    void listUserFavorOptions() {
        userFavorOptionsService.listUserFavorOptions(new BusinessQO());
    }

    @Test
    void createUserQueryOptions() {
        userFavorOptionsService.createUserQueryOptions(new UserFavorOptionsVO().setName("test1").setQueryOptions("test 1 options"));
        userFavorOptionsService.createUserQueryOptions(new UserFavorOptionsVO().setName("test2").setQueryOptions("test 2 options"));
        userFavorOptionsService.createUserQueryOptions(new UserFavorOptionsVO().setName("test3").setQueryOptions("test 3 options"));
    }
}