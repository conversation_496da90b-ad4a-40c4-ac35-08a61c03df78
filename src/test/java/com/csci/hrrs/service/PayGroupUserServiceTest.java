package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.PayGroup;
import com.csci.hrrs.model.User;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PayGroupUserServiceTest extends BaseTest {

    @Resource
    PayGroupUserService payGroupUserService;

    @Resource
    private PayGroupService payGroupService;

    @Resource
    private UserService userService;

    @Test
    void addUsersToPayGroup() {
        PayGroup payGroup = payGroupService.getPayGroupByCode("HK-PG-001");
        User user = userService.getUserByUsername("tao_li");

        payGroupUserService.addUsersToPayGroup(List.of(user.getId()), payGroup.getId());
    }
}