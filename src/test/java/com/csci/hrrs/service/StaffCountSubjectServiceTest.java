package com.csci.hrrs.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.dto.SubjectCodeExcelDTO;
import com.csci.hrrs.model.StaffCountSubject;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

class StaffCountSubjectServiceTest extends BaseTest {

    @Resource
    StaffCountSubjectService staffCountSubjectService;

    @Test
    void list() {
        LambdaQueryWrapper<StaffCountSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffCountSubject::getIsDeleted, false).orderByAsc(StaffCountSubject::getSort);
        System.out.println(CommonUtils.toJson(staffCountSubjectService.list(queryWrapper)));
    }

    @Test
    void listSubjectCodeBySource() {
        System.out.println(CommonUtils.toJson(staffCountSubjectService.listSubjectCodeBySource("导入")));
    }

    @Test
    public void excelSubjectCode() {
        String fileName = "E:\\人员编制\\830绩效系统\\830远东香港-编制科目.xlsx";
        int pageSize = 3000; // 根据需要调整此数值
        EasyExcel.read(fileName, SubjectCodeExcelDTO.class, new PageReadListener<SubjectCodeExcelDTO>(
                this::processCreateSubjectCode,
                pageSize // 设置每页大小
        )).sheet().doRead();

    }

    private void processCreateSubjectCode(List<SubjectCodeExcelDTO> subjectCodeExcelDTOS) {
        List<StaffCountSubject> staffCountSubjects = new ArrayList<>();
        for (SubjectCodeExcelDTO subjectCodeExcelDTO : subjectCodeExcelDTOS) {
            StaffCountSubject staffCountSubject = new StaffCountSubject();
            staffCountSubject.setType(subjectCodeExcelDTO.getType());
            staffCountSubject.setUnit(subjectCodeExcelDTO.getUnit());
            staffCountSubject.setCode(subjectCodeExcelDTO.getCode());
            staffCountSubject.setName(subjectCodeExcelDTO.getName());
            staffCountSubject.setSort(subjectCodeExcelDTO.getSort());
            staffCountSubject.setSource(subjectCodeExcelDTO.getSource());
            staffCountSubjects.add(staffCountSubject);
        }
        if (staffCountSubjects.size() > 0) {
            staffCountSubjectService.saveBatch(staffCountSubjects);
        }
    }
}