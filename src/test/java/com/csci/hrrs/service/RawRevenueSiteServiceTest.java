package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.fis.RawRevenueSite;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class RawRevenueSiteServiceTest extends BaseTest {

    @Resource
    private RawRevenueSiteService rawRevenueSiteService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<RawRevenueSite> lstRevenueSite = rawRevenueSiteService.list();
        System.out.println(CommonUtils.toJson(lstRevenueSite));
    }

}