package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.FactOtherEdu;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

class FactOtherEduServiceTest extends BaseTest {

    @Resource
    private FactOtherEduService factOtherEduService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        List<FactOtherEdu> eduList = factOtherEduService.list();
        System.out.println(eduList.size());
        System.out.println(CommonUtils.toJson(eduList));
    }

}