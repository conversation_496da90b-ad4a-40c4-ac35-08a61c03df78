package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class DwMonthReportServiceTest extends BaseTest {

    @Resource
    private DwMonthReportService dwMonthReportService;

    @Test
    void getMaxRecoverydate() {
        dwMonthReportService.getMaxRecoverydate(LocalDate.now());
    }
}