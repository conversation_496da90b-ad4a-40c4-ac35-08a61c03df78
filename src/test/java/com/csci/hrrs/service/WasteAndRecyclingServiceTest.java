package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.annotation.DS;
import com.csci.hrrs.annotation.LogMethod;
import com.csci.hrrs.model.WasteAndRecyclingDetailExample;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.csci.hrrs.constant.DatasourceContextEnum;

@Service
@DS(DatasourceContextEnum.HRRS)
@LogMethod
class WasteAndRecyclingServiceTest extends BaseTest {

    @Autowired
    WasteAndRecyclingService wasteAndRecyclingService;

    @Test
    void selectByExample() {
        wasteAndRecyclingService.selectByExample(new WasteAndRecyclingDetailExample());
    }
}