package com.csci.hrrs.service;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.BaseTest;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class ProjectInitiationServiceTest extends BaseTest {

    @Resource
    private ProjectInitiationService projectInitiationService;

    @Test
    void list() {
        PageHelper.startPage(1, 10);
        System.out.println(CommonUtils.toJson(projectInitiationService.list()));
    }

}