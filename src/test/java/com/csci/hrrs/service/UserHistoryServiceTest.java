package com.csci.hrrs.service;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.User;
import com.csci.hrrs.modelcovt.UserHistoryConverter;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class UserHistoryServiceTest extends BaseTest {

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private UserHistoryService userHistoryService;

    @Resource
    private UserService userService;

    @Test
    void insertSelective() {
        User user = userService.getUserByUsername("tao_li");
        userHistoryService.insertSelective(UserHistoryConverter.convert(user));
    }

    @Test
    void selectByExampleWithBLOBs() {
    }

    @Test
    void selectByExample() {
    }
}