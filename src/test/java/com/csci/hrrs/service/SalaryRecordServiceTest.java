package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.SalaryRecordQO;
import com.csci.hrrs.vo.SalaryRecordVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

class SalaryRecordServiceTest extends BaseTest {

    @Autowired
    private SalaryRecordService salaryRecordService;

    @Test
    void listSalaryRecord() {
        // {"orgCodeList":["00100022"],"employeeCategory":"内派","yearMonth":"2025-03"}
        SalaryRecordQO qo = new SalaryRecordQO();
        qo.setOrgCodeList(Arrays.asList("00100022"));
        qo.setEmployeeCategory("内派");
        qo.setYearMonth("2025-03");
        List<SalaryRecordVO> salaryRecordVOS = salaryRecordService.listSalaryRecord(qo);
        System.out.println(salaryRecordVOS);
    }
}