package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.Form;
import com.csci.hrrs.model.FormExample;
import com.csci.hrrs.model.User;
import com.csci.hrrs.qo.WorkflowControlQO;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.vo.WorkflowControlVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

// @ActiveProfiles("prod")
class WorkflowControlServiceTest extends BaseTest {

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    FormService formService;

    @Test
    void getWorkflowControlByBusinessId() {
        WorkflowControlVO workflowControlVO = workflowControlService.getWorkflowControlByBusinessId("ff20518e-38e7-4fdb-8f40-4c362a39071c");
        assertNotNull(workflowControlVO);
    }

    @Test
    void listWorkflowControlByPage() {

        List<Form> lstForm = formService.selectByExample(new FormExample());
        Form form = lstForm.get(0);

        WorkflowControlQO workflowControlQO = new WorkflowControlQO();
        workflowControlQO.setOrganizationId("1a8428fa-f392-4ce5-9265-feed8b07200e");
        workflowControlQO.setYear(2023);
        workflowControlQO.setFormId(form.getId());
        workflowControlQO.setSubmitStatus(0);
        // workflowControlQO.setOrganizationId("11111111111111111111111111111111");
        workflowControlService.listWorkflowControlByPage(workflowControlQO);
    }

    @Test
    void selectUnSubmitOrganizationOfAmbient() {
        FormService formService = SpringContextHolder.getBean(FormService.class);
        Form form = formService.findFormByCode("ambient");
        UserService userService = SpringContextHolder.getBean(UserService.class);
        User user = userService.getUserByUsername("tao_li");
        WorkflowControlQO workflowControlQO = new WorkflowControlQO();
        workflowControlQO.setYear(2022);
        workflowControlQO.setFormId(form.getId());
        workflowControlQO.setUserId(user.getId());
        workflowControlService.selectUnSubmitOrganization(workflowControlQO);
    }
}