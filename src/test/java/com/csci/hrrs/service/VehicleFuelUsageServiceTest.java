package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.VehicleFuelUsageQO;
import com.csci.hrrs.vo.VehicleFuelUsageVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class VehicleFuelUsageServiceTest extends BaseTest {

    @Resource
    VehicleFuelUsageService vehicleFuelUsageService;

    @Test
    void saveVehicleFuelUsage() {
        VehicleFuelUsageVO vehicleFuelUsageVO = new VehicleFuelUsageVO();
        // vehicleFuelUsageVO.setId();
        vehicleFuelUsageVO.setMonthValue(1);
        vehicleFuelUsageVO.setVehicleType("轿车");
        vehicleFuelUsageVO.setVehicleEmissionStandard("国五");
        vehicleFuelUsageVO.setMileage(new java.math.BigDecimal(1000));
        vehicleFuelUsageVO.setCylinderCapacity(new java.math.BigDecimal(1000));
        vehicleFuelUsageVO.setWeight(new java.math.BigDecimal(1000));
        vehicleFuelUsageVO.setRatedPower(new java.math.BigDecimal(1000));
        vehicleFuelUsageVO.setFuelType("汽油");
        vehicleFuelUsageVO.setFuelUseAmount(new java.math.BigDecimal(1000));
        vehicleFuelUsageVO.setRemark("备注");
        // vehicleFuelUsageVO.setLastUpdateVersion();

        vehicleFuelUsageService.saveVehicleFuelUsage(vehicleFuelUsageVO);

    }

    @Test
    void listVehicleFuelUsage() {
        vehicleFuelUsageService.listVehicleFuelUsage(new VehicleFuelUsageQO());
    }
}