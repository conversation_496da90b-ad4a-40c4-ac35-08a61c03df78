package com.csci.hrrs.service;

import com.csci.common.model.PageableVO;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.BaseBusinessUnit;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

class BaseBusinessUnitServiceTest extends BaseTest {

    @Resource
    private BaseBusinessUnitService baseBusinessunitService;

    @Test
    void listByPage() {
        PageableVO pageableVO = new PageableVO();
        pageableVO.setCurPage(2);
        baseBusinessunitService.listByPage(new PageableVO());
    }

    @Test
    void getMaxRecoverydate() {
        baseBusinessunitService.getMaxRecoverydate();
    }

    @Test
    void listAllLatest() {
        List<BaseBusinessUnit> lstUnit = baseBusinessunitService.listAllLatest();
        System.out.println("size: " + lstUnit.size());
    }

}