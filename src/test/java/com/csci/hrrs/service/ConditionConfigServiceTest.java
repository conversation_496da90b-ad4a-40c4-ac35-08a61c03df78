package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.ConditionConfig;
import com.csci.hrrs.qo.ConditionConfigQO;
import com.csci.hrrs.vo.ConditionConfigVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

class ConditionConfigServiceTest extends BaseTest {

    @Resource
    private ConditionConfigService conditionConfigService;

    @Test
    void testSave() {
        ConditionConfig config = new ConditionConfig();
        // 设置测试数据
        config.setIsDeleted(Boolean.FALSE);

        conditionConfigService.save(config);
    }

    @Test
    void testBatchSave() {
        // 准备测试数据
        ConditionConfigVO config1 = new ConditionConfigVO();
        config1.setProjectType("房屋署工程");
        config1.setProjectScale("特大型（30亿以上）");
        config1.setMonthlyAverageOutput(BigDecimal.valueOf(70));
        config1.setLaborCostPerHundredOutput(BigDecimal.valueOf(5.7));
//        config1.setTypeSort(1);
//        config1.setScaleSort(1);

        ConditionConfigVO config2 = new ConditionConfigVO();
        config2.setProjectType("房屋署工程");
        config2.setProjectScale("大型（20亿-30亿）");
        config2.setMonthlyAverageOutput(BigDecimal.valueOf(60));
        config2.setLaborCostPerHundredOutput(BigDecimal.valueOf(1.7));
//        config2.setTypeSort(1);
//        config2.setScaleSort(2);

        ConditionConfigVO config3 = new ConditionConfigVO();
        config3.setProjectType("房屋署工程");
        config3.setProjectScale("中型（10亿-20亿）");
        config3.setMonthlyAverageOutput(BigDecimal.valueOf(50));
        config3.setLaborCostPerHundredOutput(BigDecimal.valueOf(3.5));
//        config3.setTypeSort(1);
//        config3.setScaleSort(3);

        ConditionConfigVO config4 = new ConditionConfigVO();
        config4.setProjectType("房屋署工程");
        config4.setProjectScale("小型（10亿以下）");
        config4.setMonthlyAverageOutput(BigDecimal.valueOf(40));
        config4.setLaborCostPerHundredOutput(BigDecimal.valueOf(2.5));
//        config4.setTypeSort(1);
//        config4.setScaleSort(4);

        ConditionConfigVO config5 = new ConditionConfigVO();
        config5.setProjectType("私家工程（港铁）");
        config5.setProjectScale("特大型（30亿以上）");
        config5.setMonthlyAverageOutput(BigDecimal.valueOf(60));
        config5.setLaborCostPerHundredOutput(BigDecimal.valueOf(1.7));
//        config5.setTypeSort(2);
//        config5.setScaleSort(1);

        ConditionConfigVO config6 = new ConditionConfigVO();
        config6.setProjectType("私家工程（港铁）");
        config6.setProjectScale("大型（20亿-30亿）");
        config6.setMonthlyAverageOutput(BigDecimal.valueOf(70));
        config6.setLaborCostPerHundredOutput(BigDecimal.valueOf(5.7));
//        config6.setTypeSort(2);
//        config6.setScaleSort(2);

        ConditionConfigVO config7 = new ConditionConfigVO();
        config7.setProjectType("私家工程（港铁）");
        config7.setProjectScale("中型（10亿-20亿）");
        config7.setMonthlyAverageOutput(BigDecimal.valueOf(50));
        config7.setLaborCostPerHundredOutput(BigDecimal.valueOf(3.5));
//        config7.setTypeSort(2);
//        config7.setScaleSort(3);

        ConditionConfigVO config8 = new ConditionConfigVO();
        config8.setProjectType("私家工程（港铁）");
        config8.setProjectScale("小型（10亿以下）");
        config8.setMonthlyAverageOutput(BigDecimal.valueOf(40));
        config8.setLaborCostPerHundredOutput(BigDecimal.valueOf(2.5));
//        config8.setTypeSort(2);
//        config8.setScaleSort(4);

        ConditionConfigVO config9 = new ConditionConfigVO();
        config9.setProjectType("建筑署及半官方工程");
        config9.setProjectScale("特大型（30亿以上）");
        config9.setMonthlyAverageOutput(BigDecimal.valueOf(60));
        config9.setLaborCostPerHundredOutput(BigDecimal.valueOf(1.7));
//        config9.setTypeSort(3);
//        config9.setScaleSort(1);

        ConditionConfigVO config10 = new ConditionConfigVO();
        config10.setProjectType("建筑署及半官方工程");
        config10.setProjectScale("大型（20亿-30亿）");
        config10.setMonthlyAverageOutput(BigDecimal.valueOf(70));
        config10.setLaborCostPerHundredOutput(BigDecimal.valueOf(5.7));
//        config10.setTypeSort(3);
//        config10.setScaleSort(2);

        ConditionConfigVO config17 = new ConditionConfigVO();
        config17.setProjectType("根据新增数据，顺序返回-插入到11行");
        config17.setProjectScale("测试数据");
        config17.setMonthlyAverageOutput(BigDecimal.valueOf(40));
        config17.setLaborCostPerHundredOutput(BigDecimal.valueOf(2.5));

        ConditionConfigVO config11 = new ConditionConfigVO();
        config11.setProjectType("建筑署及半官方工程");
        config11.setProjectScale("中型（10亿-20亿）");
        config11.setMonthlyAverageOutput(BigDecimal.valueOf(50));
        config11.setLaborCostPerHundredOutput(BigDecimal.valueOf(3.5));
//        config11.setTypeSort(3);
//        config11.setScaleSort(3);

        ConditionConfigVO config12 = new ConditionConfigVO();
        config12.setProjectType("建筑署及半官方工程");
        config12.setProjectScale("小型（10亿以下）");
        config12.setMonthlyAverageOutput(BigDecimal.valueOf(40));
        config12.setLaborCostPerHundredOutput(BigDecimal.valueOf(2.5));
//        config12.setTypeSort(3);
//        config12.setScaleSort(4);

        ConditionConfigVO config13 = new ConditionConfigVO();
        config13.setProjectType("私家工程（私人发展商）");
        config13.setProjectScale("特大型（30亿以上）");
        config13.setMonthlyAverageOutput(BigDecimal.valueOf(60));
        config13.setLaborCostPerHundredOutput(BigDecimal.valueOf(1.7));
//        config13.setTypeSort(4);
//        config13.setScaleSort(1);

        ConditionConfigVO config14 = new ConditionConfigVO();
        config14.setProjectType("私家工程（私人发展商）");
        config14.setProjectScale("大型（20亿-30亿）");
        config14.setMonthlyAverageOutput(BigDecimal.valueOf(70));
        config14.setLaborCostPerHundredOutput(BigDecimal.valueOf(5.7));
//        config14.setTypeSort(4);
//        config14.setScaleSort(2);

        ConditionConfigVO config15 = new ConditionConfigVO();
        config15.setProjectType("私家工程（私人发展商）");
        config15.setProjectScale("中型（10亿-20亿）");
        config15.setMonthlyAverageOutput(BigDecimal.valueOf(50));
        config15.setLaborCostPerHundredOutput(BigDecimal.valueOf(3.5));
//        config15.setTypeSort(4);
//        config15.setScaleSort(3);

        ConditionConfigVO config16 = new ConditionConfigVO();
        config16.setProjectType("私家工程（私人发展商）");
        config16.setProjectScale("小型（10亿以下）");
        config16.setMonthlyAverageOutput(BigDecimal.valueOf(40));
        config16.setLaborCostPerHundredOutput(BigDecimal.valueOf(2.5));
//        config16.setTypeSort(4);
//        config16.setScaleSort(4);


        List<ConditionConfigVO> configs = Arrays.asList(config1, config2, config3, config4,
                config5, config6, config7, config8,
                config9, config10,/*config17,*/ config11, config12,
                config13, config14, config15, config16);

        // 调用批量保存的方法
        conditionConfigService.createBatchConditionConfigs(configs);
    }

    @Test
    void testList() {
        conditionConfigService.list();
    }

    @Test
    void testCreat() {
        ConditionConfigVO conditionConfigVO = new ConditionConfigVO();
        conditionConfigVO.setProjectType("房屋署工程");
        conditionConfigVO.setProjectScale("特大型（30亿以上）");
        conditionConfigVO.setMonthlyAverageOutput(BigDecimal.valueOf(70));
        conditionConfigVO.setLaborCostPerHundredOutput(BigDecimal.valueOf(5.7));
        conditionConfigService.createConditionConfig(conditionConfigVO);
    }

    @Test
    void testQuery() {
        ConditionConfigQO qoConditionConfigQO = new ConditionConfigQO();
        qoConditionConfigQO.setProjectType("房屋署工程");
        qoConditionConfigQO.setProjectScale("特大型（30亿以上）");
        ConditionConfigVO conditionConfig = conditionConfigService.getConditionConfig(qoConditionConfigQO);
        System.out.println(conditionConfig);
    }

    @Test
    void testUpdate() {
        ConditionConfigVO conditionConfigVO = new ConditionConfigVO();
        conditionConfigVO.setId("237aad43-2ce1-4e8a-8b91-a0cf8264342e");
        conditionConfigVO.setProjectType("房屋署工程");
        conditionConfigVO.setProjectScale("大型（20亿-30亿）");
        conditionConfigVO.setMonthlyAverageOutput(BigDecimal.valueOf(60));
        conditionConfigVO.setLaborCostPerHundredOutput(BigDecimal.valueOf(1.7));
        conditionConfigService.updateConditionConfig(conditionConfigVO);
    }

    @Test
    void testFindAll() {
        List<ConditionConfigVO> conditionConfigVOS = conditionConfigService.listConditionConfigs();
        System.out.println(conditionConfigVOS);
    }

    @Test
    void testOneProjectType() {
        List<ConditionConfigVO> conditionConfigList = conditionConfigService.getConditionConfigList("房屋署工程", null);
        System.out.println(conditionConfigList);
    }


    @Test
    void testOneProjectScale() {
        List<ConditionConfigVO> conditionConfigList = conditionConfigService.getConditionConfigList(null, "特大型（30亿以上）");
        System.out.println(conditionConfigList);
    }

    @Test
    void testOneAll() {
        List<ConditionConfigVO> conditionConfigList = conditionConfigService.getConditionConfigList(null, null);
        System.out.println(conditionConfigList);
    }

    @Test
    void testOneMonthlyAverageOutput() {
        List<String> projectTypeGroup = conditionConfigService.getProjectTypeGroup();
        System.out.println(projectTypeGroup);
    }

} 