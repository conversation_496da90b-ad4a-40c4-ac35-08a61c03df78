package com.csci.hrrs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.model.FactJobChange;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
class FactJobChangeServiceTest extends BaseTest {

    @Resource
    FactJobChangeService factJobChangeService;

    @Test
    void findPreviousJobChange() {
        LambdaQueryWrapper<FactJobChange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactJobChange::getPernr, "00004196").eq(FactJobChange::getBegindate, "20230801");
        FactJobChange factJobChange = factJobChangeService.getOne(queryWrapper, Boolean.FALSE);
        factJobChangeService.findPreviousJobChange(factJobChange);
    }

    @Test
    void list() {
        // LambdaQueryWrapper<FactJobChange> queryWrapper = new LambdaQueryWrapper<>();
        // queryWrapper.select(FactJobChange::getPernr, FactJobChange::getBegindate, FactJobChange::getEnddate, );
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<FactJobChange> lstJobChange = factJobChangeService.list();
        System.out.println(lstJobChange.size());
        stopWatch.stop();
        log.info("list()方法执行时间：{}(s)", stopWatch.getTotalTimeSeconds());
        // 计算 lstJobChange 占用的内存大小并打印
    }

}