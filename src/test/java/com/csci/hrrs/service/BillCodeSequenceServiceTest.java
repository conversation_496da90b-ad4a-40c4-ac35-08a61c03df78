package com.csci.hrrs.service;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("dev")
class BillCodeSequenceServiceTest extends BaseTest {

    @Resource
    private BillCodeSequenceService billCodeSequenceService;

    @Test
    void getNextSequence() {
        billCodeSequenceService.getNextSequence("test", "test1");
    }
}