package com.csci.hrrs.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.dto.OrgDTO;
import com.csci.hrrs.model.Organization;
import com.csci.hrrs.model.User;
import com.csci.hrrs.qo.KeywordPageQO;
import com.csci.hrrs.qo.KeywordQO;
import com.csci.hrrs.qo.OrganizationPageableQO;
import com.csci.hrrs.qo.OrganizationTreeQO;
import com.csci.hrrs.util.DemoUtils;
import com.csci.hrrs.util.context.RequestContextManager;
import com.csci.hrrs.util.context.impl.RequestContext;
import com.csci.hrrs.util.context.model.UserInfo;
import com.csci.hrrs.vo.OrganizationVO;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@ActiveProfiles("dev")
class OrganizationServiceTest extends BaseTest {
    @Resource
    private UserService userService;

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OrganizationServiceTest.class);

    @Resource
    OrganizationService organizationService;

    @Test
    void generateNo() {
        // organizationService.generateNo("17087670-7555-48d2-a743-59a3ad413f04");
    }

    @Test
    void listOrgByParentId() {
        UserInfo userInfo = new UserInfo();
        userInfo.setId("fc51cc0e-07fe-47ca-8daa-ae2db5ca0c90");
        userInfo.setUsername("tao_li");
        userInfo.setRoles(Collections.singletonList("superAdmin"));
        RequestContextManager.setCurrent(new RequestContext(new HashMap<>(), userInfo));
        organizationService.listOrgByParentId("17087670-7555-48d2-a743-59a3ad413f04", 1, 10);
    }

    @Test
    void listOrganizationTreeByParentId() {
        com.csci.hrrs.model.Organization organization = organizationService.getOrganizationByName("中建香港");
        organizationService.listOrganizationTreeByParentId(organization.getId());
    }


    @Test
    void saveOrganization() {
        OrganizationVO organizationVO = new OrganizationVO();
        organizationVO.setName("中建香港");
        organizationService.saveOrganization(organizationVO);

        organizationVO = new OrganizationVO();
        organizationVO.setName("中建澳門");
        organizationService.saveOrganization(organizationVO);
    }

    @Test
    void listDeptByUserId() {
        User user = userService.getUserByUsername("admin");
        organizationService.listDeptByUserId(user.getId());
    }

    @Test
    void listAllOrgs() {
        KeywordPageQO keywordPageQO = new KeywordPageQO();
        keywordPageQO.setKeyword("中");
        organizationService.listAllOrgs(keywordPageQO);
    }

    @Test
    void listCompanyDeptByPage() {
        KeywordQO keywordQO = new KeywordQO();
        keywordQO.setKeyword("中");
        organizationService.listCompanyDeptByPage(keywordQO);
    }

    @Test
    void listCompanies() {
        organizationService.listTopCompanies();
    }

    @Test
    void findOrganizationChildren() {
        organizationService.findLeafChildrenByOrgId("************************************");
    }

    @Test
    void findLeafChildrenByNo() {
    }

    @Test
    void listCurrentUsersOrganization() {
        KeywordQO keywordQO = new KeywordQO();
        keywordQO.setCurPage(1);
        keywordQO.setPageSize(9999);
        organizationService.listCurrentUsersOrganization(keywordQO);
    }

    @Test
    void listUsersCompanyDept() {

        organizationService.listUsersCompanyDept(null, "19871e94-47ff-4b6f-9e16-0c33c221031e");
    }

    @Test
    void getFullPath() {
        organizationService.generateFullPath(null);
    }

    @Test
    void listOrganization() {
        organizationService.listOrganization(new OrganizationPageableQO());
    }

    @Test
    void getAllExistingCodes() {
        Set<String> lstCodes = organizationService.getAllExistingCodes();
        logger.info("all existing codes: {}", lstCodes);
        logger.info("all existing codes size: {}", lstCodes.size());
    }

    @Test
    void testSelectById() {
        Organization organization = organizationService.getById("0010572c-49c8-4950-9c86-ee74392923cf");
        System.out.println(DemoUtils.toJson(organization));
    }

    @Test
    void getRootCompany() {
        Organization rootCompany = organizationService.getRootCompany();
        System.out.println(DemoUtils.toJson(rootCompany));
    }

    @Test
    void getOrgTreeWithParentAndSubOrgs() {
        organizationService.getOrgTreeWithParentAndSubOrgs(new OrganizationTreeQO().setCodes(List.of("00100035", "10100000")).setKeyword("发展"));
    }

    @Test
    void listUsersOrgCodes() {
        User user = userService.getUserByUsername("tao_li");
        List<String> lstOrgCode = organizationService.listUsersOrgCodes(user.getId());
        System.out.println("lstOrgCode size: " + lstOrgCode.size());
    }

    @Test
    void selectUserOrgs() {
        organizationService.selectUserOrgs("00634b60-f5e1-4642-9401-ae430afec924",null);
    }

    @Test
    void listHKSubsidiary() {
        organizationService.listHKSubsidiary();
    }

    @Test
    void listAllHKSubCompanyOrgs() {
        organizationService.listAllHKSubCompanyOrgs();
    }

    @Test
    void listAllOrganization() {
        organizationService.listAllOrganization();
    }

    @Test
    public void importData() {
        String fileName = "E:\\人员编制\\830绩效系统\\组织机构数据收集20250508.xlsx";

        // 预先加载所有存在的组织数据
        Map<String, Organization> existingOrgs = organizationService.list(
                        new LambdaQueryWrapper<Organization>()
                                .eq(Organization::getIsDeleted, 0)
                                .like(Organization::getNo, "00200002")
                ).stream()
                .collect(Collectors.toMap(Organization::getCode, org -> org));

        EasyExcel.read(fileName, OrgDTO.class, new PageReadListener<OrgDTO>(
                orgDTOS -> processData(orgDTOS, existingOrgs),
                1000 // 设置合适的批处理大小
        )).sheet().doRead();
    }

    private void processData(List<OrgDTO> orgDTOS, Map<String, Organization> existingOrgs) {
        List<Organization> organizationsToUpdate = new ArrayList<>();
        List<Organization> organizationsToAdd = new ArrayList<>();

        for (OrgDTO orgDTO : orgDTOS) {
            Organization existingOrg = existingOrgs.get(orgDTO.getOrgCode());

            if (existingOrg != null) {
                // 更新现有组织
                Organization organization = new Organization();
                organization.setId(existingOrg.getId());
                organization.setCode(orgDTO.getOrgCode());
                organization.setFullName(orgDTO.getFullName());
                organization.setShortName(orgDTO.getShortName());
                organization.setSiteCode(orgDTO.getMatchCode());
                organization.setIsProject(orgDTO.getIsProject());
                organization.setSiteLeaderName(orgDTO.getPrincipal());
                organization.setOnConstruction(orgDTO.getOn_construction());
                organization.setContractAmount(orgDTO.getContractAmount());
                organization.setContractStartDate(orgDTO.getContractStartDate());
                organization.setContractEndDate(orgDTO.getContractEndDate());
                organization.setStartDate(orgDTO.getStartDate());
                organization.setEndDate(orgDTO.getEndDate());

                // 设置修改标志位
                organization.setIsStartDateModified(orgDTO.getStartDate() != null);
                organization.setIsEndDateModified(orgDTO.getEndDate() != null);
                organization.setIsContractStartDateModified(orgDTO.getContractStartDate() != null);
                organization.setIsContractEndDateModified(orgDTO.getContractEndDate() != null);
                organization.setIsContractAmountModified(orgDTO.getContractAmount() != null ? (byte)1 : 0);

                organizationsToUpdate.add(organization);
            } else {
                // 新增不存在的组织
                Organization newOrg = new Organization();
                newOrg.setCode(orgDTO.getOrgCode());
                newOrg.setFullName(orgDTO.getFullName());
                newOrg.setShortName(orgDTO.getShortName());
                newOrg.setSiteCode(orgDTO.getMatchCode());
                newOrg.setIsProject(orgDTO.getIsProject());
                newOrg.setSiteLeaderName(orgDTO.getPrincipal());
                newOrg.setOnConstruction(orgDTO.getOn_construction());
                newOrg.setContractAmount(orgDTO.getContractAmount());
                newOrg.setContractStartDate(orgDTO.getContractStartDate());
                newOrg.setContractEndDate(orgDTO.getContractEndDate());
                newOrg.setStartDate(orgDTO.getStartDate());
                newOrg.setEndDate(orgDTO.getEndDate());

                // 新组织默认修改标志位为true
                newOrg.setIsStartDateModified(true);
                newOrg.setIsEndDateModified(true);
                newOrg.setIsContractStartDateModified(true);
                newOrg.setIsContractEndDateModified(true);
                newOrg.setIsContractAmountModified(orgDTO.getContractAmount() != null ? (byte)1 : 0);

                // 设置其他必要字段
                newOrg.setIsDeleted(false);
                organizationsToAdd.add(newOrg);
            }
        }

        // 批量操作
        if (!organizationsToUpdate.isEmpty()) {
            /*organizationService.updateBatchById(organizationsToUpdate);*/
        }
        if (!organizationsToAdd.isEmpty()) {
            /*organizationService.saveBatch(organizationsToAdd);*/

            // 将新增的组织添加到existingOrgs中，避免后续重复新增
            organizationsToAdd.forEach(org -> existingOrgs.put(org.getCode(), org));
        }
    }
}