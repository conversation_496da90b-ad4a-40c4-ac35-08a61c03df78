package com.csci.hrrs.login;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class Oauth2InvokerTest extends BaseTest {

    @Resource
    private Oauth2Invoker oauth2Invoker;

    @Test
    void requestAccessToken() {
        oauth2Invoker.requestAccessToken("jiandu01", "audit123");
    }

    @Test
    void refreshToken() {
        oauth2Invoker.refreshToken("78e08a237f124c179e62b56d2f1a55b3", "9c3a47c174c64bdd8167240cdb4893cd");
    }

    @Test
    void validateAccessToken() {
        oauth2Invoker.validateAccessToken("0d4a4ee9764f43c49a7d7a95ce86dc68");
    }
}