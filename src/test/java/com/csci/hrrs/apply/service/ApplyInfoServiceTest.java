package com.csci.hrrs.apply.service;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.apply.model.ApplyInfo;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.LocalDate;

class ApplyInfoServiceTest extends BaseTest {

    @Resource
    private ApplyInfoService applyInfoService;

    @Test
    void add() {
        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setName("test001");
        applyInfo.setDescription("测试");
        applyInfo.setStatus("draft");
        applyInfo.setStartDate(LocalDate.now());
        applyInfo.setEndDate(LocalDate.now().plusDays(30));
        applyInfo.setCompleteCount(0);
        applyInfoService.save(applyInfo);
    }

}