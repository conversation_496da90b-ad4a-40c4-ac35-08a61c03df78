package com.csci.hrrs.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;

/**
 * @ClassName: BooleanConverter
 * @Auther: <EMAIL>
 * @Date: 2025/5/12 19:08
 * @Description:
 */
public class BooleanConverter implements Converter<Boolean> {
    @Override
    public Class<Boolean> supportJavaTypeKey() {
        return Boolean.class;
    }

    @Override
    public Boolean convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                     GlobalConfiguration globalConfiguration) {
        if (cellData == null || cellData.getType() == CellDataTypeEnum.EMPTY) {
            return false;
        }

        switch (cellData.getType()) {
            case NUMBER:
                return cellData.getNumberValue().compareTo(BigDecimal.ONE) == 0;
            case STRING:
                String value = cellData.getStringValue().trim();
                return "是".equals(value) || "1".equals(value) || "true".equalsIgnoreCase(value);
            case BOOLEAN:
                return cellData.getBooleanValue();
            default:
                return false;
        }
    }
}