package com.csci.hrrs.converter;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class JobLevelConverterTest extends BaseTest {

    @Test
    void convertJobLevel() {
        System.out.println(JobLevelConverter.convertJobLevel("B1"));
        System.out.println(JobLevelConverter.convertJobLevel("B2"));
        System.out.println(JobLevelConverter.convertJobLevel("C1"));
        System.out.println(JobLevelConverter.convertJobLevel("C2"));
        System.out.println(JobLevelConverter.convertJobLevel("D1"));
        System.out.println(JobLevelConverter.convertJobLevel("D2"));
        System.out.println(JobLevelConverter.convertJobLevel("D3"));
    }
}