package com.csci.hrrs.controller;

import com.csci.common.util.DateUtils;
import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.DashboardQueryRosterQO;
import com.csci.hrrs.qo.JobChangeQO;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class DashboardControllerTest extends BaseTest {

    @Resource
    private DashboardController controller;

    @Test
    void listJobChange() {
        JobChangeQO qo = new JobChangeQO();
        qo.setDate(DateUtils.toLocalDateTime("2024-08").toLocalDate());
        qo.setHireType("自有");
        qo.setType(1);
        qo.setPageSize(50);
        qo.setRosterHeadId("9ee62a73-c779-4558-9dae-52a7032e8a3f");
        qo.setPlatformOrgCodeList(List.of("00100001"));
        controller.listJobChange(qo);
    }

    @Test
    void listRosterDetail() {
        DashboardQueryRosterQO qo = new DashboardQueryRosterQO();
        qo.setDate(LocalDate.of(2024, 8, 1));
        qo.setHireTypeList(List.of("自有"));
        qo.setKeyword("刘霞");
        qo.setPlatformOrgCodeList(List.of("00100001"));
        qo.setRosterHeadId("1c4b0832-3469-496a-9bee-d63e5b4f0009");
        qo.setPageSize(10);
        controller.listRosterDetail(qo);
    }
}