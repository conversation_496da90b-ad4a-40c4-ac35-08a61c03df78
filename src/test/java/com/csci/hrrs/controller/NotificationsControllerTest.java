package com.csci.hrrs.controller;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.NotificationsQO;
import com.csci.hrrs.vo.AddNotificationsVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class NotificationsControllerTest extends BaseTest {

    @Resource
    private NotificationsController controller;

    @Test
    void addNotifications() {
        AddNotificationsVO addNotificationsVO = new AddNotificationsVO();
        addNotificationsVO.setMessage("测试新增消息");
        addNotificationsVO.setSourceSystem("hrrs");

        controller.addNotifications(addNotificationsVO);
    }

    @Test
    void listNotifications() {
        NotificationsQO qo = new NotificationsQO();
        qo.setSourceSystem("hrrs");
        controller.listNotifications(qo);
    }
}