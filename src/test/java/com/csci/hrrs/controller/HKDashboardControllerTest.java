package com.csci.hrrs.controller;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.qo.OrgCodeListQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class HKDashboardControllerTest extends BaseTest {

    @Resource
    private HKDashboardController hkDashboardController;

    @Test
    void getHKOverview() {
        OrgCodeListQO orgCodeListQO = new OrgCodeListQO();
        orgCodeListQO.setOrganizationCodeList(List.of("00100026", "50000896"));

        hkDashboardController.getHKOverview(orgCodeListQO);
    }
}