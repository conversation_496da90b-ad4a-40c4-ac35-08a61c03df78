package com.csci.hrrs.controller;

import com.csci.hrrs.BaseTest;
import com.csci.hrrs.vo.IdVersionVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class AdminLineControllerTest extends BaseTest {

    @Resource
    private AdminLineController controller;

    @Test
    void getOrInit() {
        controller.getOrInit("00100001", null, 2024, 9);
    }

    @Test
    void aggregate() {
        IdVersionVO idVersionVO = new IdVersionVO();
        idVersionVO.setId("0b5b2d9b-5b2f-46a5-8412-fba3a3eb5115");
        idVersionVO.setLastUpdateVersion(1);
        controller.aggregate(idVersionVO);
    }
}