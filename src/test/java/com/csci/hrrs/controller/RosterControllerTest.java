package com.csci.hrrs.controller;

import com.csci.hrrs.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class RosterControllerTest extends BaseTest {

    @Resource
    private RosterController rosterController;

    @Test
    void exportInfoCollection() throws IOException {
        rosterController.exportInfoCollection("ccc6bf97-dfab-4322-8d1d-81c4252a887f", "zh-cn", false, null);
    }

    @Test
    void getRosterDetail() {
        rosterController.getRosterDetail("fd089319-c7be-49c0-90a4-8fb1906f7c32", "zh-cn");
    }
}