package com.csci.hrrs;

import com.csci.common.util.CommonUtils;
import com.csci.hrrs.facade.UserRoleFacade;
import com.csci.hrrs.model.User;
import com.csci.hrrs.service.UserRoleService;
import com.csci.hrrs.service.UserSalaryCategoryAccessService;
import com.csci.hrrs.service.UserService;
import com.csci.hrrs.util.SpringContextHolder;
import com.csci.hrrs.util.TranslateUtils;
import com.csci.hrrs.util.context.RequestContextManager;
import com.csci.hrrs.util.context.impl.RequestContext;
import com.csci.hrrs.util.context.model.UserInfo;
import com.csci.hrrs.vo.RoleVO;
import com.github.houbb.heaven.util.lang.StringUtil;
import com.github.houbb.pinyin.constant.enums.PinyinStyleEnum;
import com.github.houbb.pinyin.util.PinyinHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.List;

@TestPropertySource(properties = "app.scheduling.enable=false")
@SpringBootTest
public class BaseTest {

    @Autowired
    private UserSalaryCategoryAccessService userSalaryCategoryAccessService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserRoleFacade userRoleFacade;

    @Test
    public void contextLoads() {
    }

    @BeforeEach
    public void init() {
        String username = "tao_li";
        UserService userService = SpringContextHolder.getBean(UserService.class);
        User user = userService.getUserByUsername(username);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setName(user.getName());

        List<String> categoryList = userSalaryCategoryAccessService.listUsersSalaryCategoryCode(username);
        userInfo.setSalaryCategoryCodeList(categoryList);

        List<String> roleCodeList = userRoleFacade.listRoleByUsername(username).stream().map(RoleVO::getCode).toList();
        userInfo.setRoles(roleCodeList);

        RequestContextManager.setCurrent(new RequestContext(new HashMap<>(), userInfo));
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void test() {
        /*String md5 = DigestUtils.md5Hex("建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)");
        System.out.println(md5);
        System.out.println(md5.length());*/

        System.out.println(CommonUtils.generateGuid());
        System.out.println(TranslateUtils.convertToPinyin("葉懿斌"));
        System.out.println(PinyinHelper.toPinyin("葉懿斌", PinyinStyleEnum.NORMAL, StringUtil.EMPTY));


        System.out.println(CommonUtils.md5("timpstamp=20240808144130&key=A24B6708D3164F92A2876C710E0745F2"));

        System.out.println("------------------------------------------------");
    }

}
