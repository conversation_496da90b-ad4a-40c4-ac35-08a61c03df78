@echo off

call ./setTag.bat

cd ../../

call mvn clean package

del /S /Q docker\test\lib\*.*

mkdir docker\test\lib\

copy /Y target\*.jar docker\test\lib\

cd docker/test

call env.bat

rem building image =====================
setlocal
call docker build -t %deployVersion% .
if errorlevel 1 (
    goto :error
)
echo build image done ===================
endlocal

rem pushing image ===================
call docker push %deployVersion%
if errorlevel 1 (
    goto :error
)
echo push image done ===================

rmdir /S /Q lib

pause

:error
echo An error occurred during the build or push.
pause
