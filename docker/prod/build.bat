cd ../../

:: set JAVA_HOME=D:\JAVA\openjdk-20+36_windows-x64_bin\jdk-20

:: set Path=%JAVA_HOME%\bin;%Path%

call mvn clean package

del /S /Q docker\prod\lib\*.*

mkdir docker\prod\lib\

copy /Y target\*.jar docker\prod\lib\

cd docker/prod

call env.bat

echo begin to build docker image================
call docker build -t %deployVersion% .
echo end to build docker image================

echo begin to push docker image================
call docker push %deployVersion%
echo end to push docker image================

pause

:: 目前发布在 113 - 10.1.8.114,115 这台服务器上