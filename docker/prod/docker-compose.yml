version: "3.7"

services:
  hrrs:
    image: *********:1080/csci/hrrs-api:prod-v1.0.20
    container_name: hrrs-back
    ports:
      - "20801:8091"
    environment:
      - JASYPT_ENCRYPTOR_PASSWORD=your_actual_password_here
    restart: unless-stopped
    volumes:
      - "./logs:/logs"
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:8091/" ]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    logging:
      driver: "json-file"
      options:
        max-size: "1024m"
        max-file: "3"